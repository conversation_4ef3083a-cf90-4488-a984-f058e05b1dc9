<?php

use Illuminate\Support\Facades\Broadcast;

use App\Models\Order;
use App\Models\User;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
  return (int) $user->id === (int) $id;
});


Broadcast::channel('new-orders', function (User $user) {
  // return $user->sales_person_id === Order::find($orderId)->sales_person_id;

  Log::debug('Channel new-orders authenticated');

  return true;
});