<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\DevelopmentController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });

// Route::middleware(['auth:sanctum', 'verified'])->group(function () {
//     // Get translators that match an assignment
//     Route::get('/translators/matching', [\App\Http\Controllers\Api\TranslatorController::class, 'getMatchingTranslators'])->name('api.translators.matching');
// });

// Calculate Commission Diff
// Route::post('calculate_commission_diff', [DevelopmentController::class, 'calculateCommissionDiff']);
