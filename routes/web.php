<?php

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;
use App\Http\Middleware\VerifyAssignmentPublicAccess;
use App\Http\Middleware\VerifyAssignmentPublicAccessPreMarketBid;
use Inertia\Inertia;

use App\Http\Controllers\TranslationCategoriesController;
use App\Http\Controllers\TranslationLanguagesController;
use App\Http\Controllers\TranslationAssignmentsController;
use App\Http\Controllers\TranslationProfileController;
use App\Http\Controllers\TranslatorApplicationsController;
use App\Http\Controllers\TranslationProductsController;
use App\Http\Controllers\TranslationAssignmentMarketController;
use App\Http\Controllers\MyAssignmentsController;
use App\Http\Controllers\TranslationStoreController;
use App\Http\Controllers\TranslatorsController;
use App\Http\Controllers\SveaCheckoutController;
use App\Http\Controllers\PaymentsController;

use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\FormLanguagesController;
use App\Http\Controllers\FormCategoriesController;
use App\Http\Controllers\FormAssignmentsController;
use App\Http\Controllers\FormTranslatorsController;
use App\Http\Resources\TranslationAssignmentDetailTransformer;
use Softbox\SBX\Webshop\Models\Order;
use App\Models\TranslationAssignment;
use App\Http\Resources\TranslationAssignmentEmailTransformer;
use App\Mail\AssignmentExpiredCustomerEmail;
use App\Models\User;
use App\Mail\AssignmentReminderCustomerEmail;
use App\Mail\OfferReceivedEmail;

use Softbox\SBX\HelloSMS\Jobs\SendSMS;
use Illuminate\Support\Facades\Mail;


/*
|--------------------------------------------------------------------------
| Main Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return redirect('/login');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('Dashboard');
    })->name('dashboard');
});



/*
|--------------------------------------------------------------------------
| Home
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole']], function () {
    Route::get('{marketCode}/{languageCode}/home', function ($marketCode, $languageCode) {
        $roles = Auth::user()->roles;

        $isAdmin = false;
        $isTranslator = false;
        foreach ($roles as $role) {
            if ($role->id == 1) {
                $isAdmin = true;
            }

            if ($role->id == 2) {
                $isTranslator = true;
            }
        }

        if ($isAdmin) {
            return redirect('/' . $marketCode . '/' . $languageCode . '/translation_assignments');
        }

        if ($isTranslator) {
            return redirect('/' . $marketCode . '/' . $languageCode . '/matchande-uppdrag');
        }
    })->name('home');
});



/*
|--------------------------------------------------------------------------
| Translators
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole', 'admin']], function () {
    Route::get('{marketCode}/{languageCode}/translators', [TranslatorsController::class, 'index'])->name('translators');

    Route::get('{marketCode}/{languageCode}/translators/create', [TranslatorsController::class, 'create'])->name('translators.create');
    Route::post('{marketCode}/{languageCode}/translators/store', [TranslatorsController::class, 'store'])->name('translators.store')->middleware([HandlePrecognitiveRequests::class]);

    Route::get('{marketCode}/{languageCode}/translators/{translator}/edit', [TranslatorsController::class, 'edit'])->name('translators.edit');
    Route::put('{marketCode}/{languageCode}/translators/{translator}/update', [TranslatorsController::class, 'update'])->name('translators.update')->middleware([HandlePrecognitiveRequests::class]);
    Route::put('{marketCode}/{languageCode}/translators/{translator}/information/update', [TranslatorsController::class, 'updateInformation'])->name('translators.update.information')->middleware([HandlePrecognitiveRequests::class]);
    Route::put('{marketCode}/{languageCode}/translators/{translator}/leads/update', [TranslatorsController::class, 'updateLeads'])->name('translators.update.leads')->middleware([HandlePrecognitiveRequests::class]);

    Route::delete('{marketCode}/{languageCode}/translators/{translator}/destroy', [TranslatorsController::class, 'destroy'])->name('translators.destroy');
});



/*
|--------------------------------------------------------------------------
| Translation Categories
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole', 'admin']], function () {
    Route::get('{marketCode}/{languageCode}/translation_categories', [TranslationCategoriesController::class, 'index'])->name('translation_categories');

    Route::get('{marketCode}/{languageCode}/translation_categories/create', [TranslationCategoriesController::class, 'create'])->name('translation_categories.create');
    Route::post('{marketCode}/{languageCode}/translation_categories/store', [TranslationCategoriesController::class, 'store'])->name('translation_categories.store')->middleware([HandlePrecognitiveRequests::class]);

    Route::get('{marketCode}/{languageCode}/translation_categories/{translationCategory}/edit', [TranslationCategoriesController::class, 'edit'])->name('translation_categories.edit');
    Route::put('{marketCode}/{languageCode}/translation_categories/{translationCategory}/update', [TranslationCategoriesController::class, 'update'])->name('translation_categories.update')->middleware([HandlePrecognitiveRequests::class]);

    Route::delete('{marketCode}/{languageCode}/translation_categories/{translationCategory}/destroy', [TranslationCategoriesController::class, 'destroy'])->name('translation_categories.destroy');
});



/*
|--------------------------------------------------------------------------
| Translation Languages
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole', 'admin']], function () {
    Route::get('{marketCode}/{languageCode}/translation_languages', [TranslationLanguagesController::class, 'index'])->name('translation_languages');

    Route::get('{marketCode}/{languageCode}/translation_languages/create', [TranslationLanguagesController::class, 'create'])->name('translation_languages.create');
    Route::post('{marketCode}/{languageCode}/translation_languages/store', [TranslationLanguagesController::class, 'store'])->name('translation_languages.store')->middleware([HandlePrecognitiveRequests::class]);

    Route::get('{marketCode}/{languageCode}/translation_languages/{translationLanguage}/edit', [TranslationLanguagesController::class, 'edit'])->name('translation_languages.edit');
    Route::put('{marketCode}/{languageCode}/translation_languages/{translationLanguage}/update', [TranslationLanguagesController::class, 'update'])->name('translation_languages.update')->middleware([HandlePrecognitiveRequests::class]);

    Route::delete('{marketCode}/{languageCode}/translation_languages/{translationLanguage}/destroy', [TranslationLanguagesController::class, 'destroy'])->name('translation_languages.destroy');
});



/*
|--------------------------------------------------------------------------
| Translation Assignments
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole', 'admin']], function () {
    Route::get('{marketCode}/{languageCode}/translation_assignments', [TranslationAssignmentsController::class, 'index'])->name('translation_assignments');
    Route::get('{marketCode}/{languageCode}/translation_assignments/ongoing', [TranslationAssignmentsController::class, 'ongoingAssignments'])->name('translation_assignments.ongoing');
    Route::get('{marketCode}/{languageCode}/translation_assignments/won', [TranslationAssignmentsController::class, 'wonAssignments'])->name('translation_assignments.won');

    // Update bid price
    Route::put('{marketCode}/{languageCode}/translation_assignments/bids/{bid}/update-price', [TranslationAssignmentMarketController::class, 'updateBidPrice'])->name('translation_assignments.bids.update_price');
    Route::get('{marketCode}/{languageCode}/translation_assignments/completed', [TranslationAssignmentsController::class, 'completedAssignments'])->name('translation_assignments.completed');
    Route::get('{marketCode}/{languageCode}/translation_assignments/unwon', [TranslationAssignmentsController::class, 'unwonAssignments'])->name('translation_assignments.unwon');

    Route::get('{marketCode}/{languageCode}/translation_assignments/{assignment}/view', [TranslationAssignmentsController::class, 'view'])->name('translation_assignments.view');

    Route::get('{marketCode}/{languageCode}/translation_assignments/create', [TranslationAssignmentsController::class, 'create'])->name('translation_assignments.create');
    Route::post('{marketCode}/{languageCode}/translation_assignments/store', [TranslationAssignmentsController::class, 'store'])->name('translation_assignments.store')->middleware([HandlePrecognitiveRequests::class]);

    Route::get('{marketCode}/{languageCode}/translation_assignments/{translationAssignment}/edit', [TranslationAssignmentsController::class, 'edit'])->name('translation_assignments.edit');
    Route::put('{marketCode}/{languageCode}/translation_assignments/{translationAssignment}/update', [TranslationAssignmentsController::class, 'update'])->name('translation_assignments.update')->middleware([HandlePrecognitiveRequests::class]);

    Route::post('{marketCode}/{languageCode}/translation_assignments/{translationAssignment}/file/', [TranslationAssignmentsController::class, 'addFile'])->name('translation_assignments.addFile');
    Route::delete('{marketCode}/{languageCode}/translation_assignments/{translationAssignment}/file/{fileID}', [TranslationAssignmentsController::class, 'deleteFile'])->name('translation_assignments.deleteFile')->middleware([HandlePrecognitiveRequests::class]);


    Route::delete('{marketCode}/{languageCode}/translation_assignments/{translationAssignment}/destroy', [TranslationAssignmentsController::class, 'destroy'])->name('translation_assignments.destroy');

    // Approvals
    Route::get('{marketCode}/{languageCode}/translation_assignment_approvals', [TranslationAssignmentsController::class, 'unapprovedAssignments'])->name('translation_assignment_approvals');
    Route::get('{marketCode}/{languageCode}/translation_assignment_approvals/{assignment}/show', [TranslationAssignmentsController::class, 'showUnapprovedAssignment'])->name('translation_assignment_approvals.show');

    Route::post('{marketCode}/{languageCode}/translation_assignment_approvals/{assignment}/approve', [TranslationAssignmentsController::class, 'approveAssignment'])->name('translation_assignment_approvals.approve');

    Route::post('{marketCode}/{languageCode}/translation_assignment_approvals/{assignment}/reject', [TranslationAssignmentsController::class, 'rejectAssignment'])->name('translation_assignment_approvals.reject');

    Route::post('{marketCode}/{languageCode}/translation_assignment_approvals/{assignment}/delete', [TranslationAssignmentsController::class, 'deleteAssignment'])->name('translation_assignment_approvals.delete');

    Route::put('{marketCode}/{languageCode}/translation_assignments/{assignment}/activate', [TranslationAssignmentsController::class, 'activateAssignment'])->name('translation_assignments.activate');
    Route::put('{marketCode}/{languageCode}/translation_assignments/{assignment}/deactivate', [TranslationAssignmentsController::class, 'deactivateAssignment'])->name('translation_assignments.deactivate');

    // Resend Match Email
    Route::post('{marketCode}/{languageCode}/translation_assignment_resend_match_email/{assignment}', [TranslationAssignmentsController::class, 'resendMatchEmail'])->name('translation_assignment_resend_match_email');

    // Add Pre-Market Bid
    Route::put('{marketCode}/{languageCode}/translation_assignments/{translationAssignment}/add_premarket_bid', [TranslationAssignmentMarketController::class, 'addPremarketBid'])->name('translation_assignments.add_premarket_bid');

    // Update Bid Details (Price and/or Delivery Date)
    Route::put('{marketCode}/{languageCode}/translation_assignments/bids/{bid}/update-details', [TranslationAssignmentMarketController::class, 'updateBidPrice'])->name('translation_assignments.bids.update_details');

    // Assign Translator to Assignment
    Route::put('{marketCode}/{languageCode}/translation_assignments/{assignment}/assign-translator', [TranslationAssignmentMarketController::class, 'assignTranslator'])->name('translation_assignments.assign_translator');

    // Get Matching Translators for Assignment
    Route::get('{marketCode}/{languageCode}/translation_assignments/{assignment}/matching-translators', [TranslationAssignmentMarketController::class, 'getMatchingTranslators'])->name('translation_assignments.matching_translators');

    // Close Assignment for Offers
    Route::put('{marketCode}/{languageCode}/translation_assignments/{assignment}/close-for-offers', [TranslationAssignmentMarketController::class, 'closeForOffers'])->name('translation_assignments.close_for_offers');

    // Complete Assignment (Admin)
    Route::post('{marketCode}/{languageCode}/translation_assignments/{assignment}/complete', [TranslationAssignmentsController::class, 'adminCompleteAssignment'])->name('translation_assignments.admin_complete');

    Route::get('{marketCode}/{languageCode}/translation_assignments/pricedata', [TranslationAssignmentsController::class, 'getPriceCalculatorData'])->name('translation_assignments.get_price_calculator_data');
});

/*
|--------------------------------------------------------------------------
| Translation Assignment Market
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole']], function () {
    Route::get('{marketCode}/{languageCode}/matchande-uppdrag', [TranslationAssignmentMarketController::class, 'index'])->name('translation_assignment_market');

    Route::put('{marketCode}/{languageCode}/matchande-uppdrag/{translationAssignment}/make_bid', [TranslationAssignmentMarketController::class, 'makeBid'])->name('translation_assignment_market.make_bid');

    Route::put('{marketCode}/{languageCode}/matchande-uppdrag/{translationAssignment}/show_details', [TranslationAssignmentMarketController::class, 'showDetails'])->name('translation_assignment_market.show_details');

    Route::get('{marketCode}/{languageCode}/matchande-uppdrag/{translationAssignmentID}', [TranslationAssignmentMarketController::class, 'show'])->name('translation_assignment_market.show');

    Route::get('{marketCode}/{languageCode}/matchande-uppdrag/{translationAssignmentID}/download_all_files', [TranslationAssignmentsController::class, 'downloadAllFiles'])->name('translation_assignment_market.download_all_files');
});



/*
|--------------------------------------------------------------------------
| My Translation Assignments
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole']], function () {
    Route::get('{marketCode}/{languageCode}/mina-uppdrag', [MyAssignmentsController::class, 'index'])->name('my_assignments');
    Route::get('{marketCode}/{languageCode}/mina-offererade-uppdrag', [MyAssignmentsController::class, 'offered'])->name('my_assignments.offered');
    Route::get('{marketCode}/{languageCode}/mina-pagaende-uppdrag', [MyAssignmentsController::class, 'ongoing'])->name('my_assignments.ongoing');
    Route::get('{marketCode}/{languageCode}/mina-avslutade-uppdrag', [MyAssignmentsController::class, 'completed'])->name('my_assignments.completed');

    Route::get('{marketCode}/{languageCode}/mina-uppdrag/{translationAssignmentID}', [MyAssignmentsController::class, 'show'])->name('my_assignments.show');
    Route::get('{marketCode}/{languageCode}/visa-uppdrag/{translationAssignmentID}', [MyAssignmentsController::class, 'showMobileDetail'])->name('my_assignments.mobile_detail.show');

    Route::post('{marketCode}/{languageCode}/{translationAssignmentID}/complete', [MyAssignmentsController::class, 'completeAssignment'])->name('my_assignments.complete');
    Route::post('{marketCode}/{languageCode}/{translationAssignmentID}/completeMissingFile', [MyAssignmentsController::class, 'completeMissingFiles'])->name('my_assignments.completeMissingFiles');
});



/*
|--------------------------------------------------------------------------
|  Assignment Customer View
|--------------------------------------------------------------------------
*/

// Route::group(['middleware' => ['verifyAssignmentPublicAccess']], function () {
//     Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}', [TranslationAssignmentsController::class, 'customerView'])->name('assignment_customer_view');
// });

/*
|--------------------------------------------------------------------------
| Profile
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole']], function () {
    Route::get('{marketCode}/{languageCode}/min-profil', [TranslationProfileController::class, 'edit'])->name('translation_profile');
    Route::put('{marketCode}/{languageCode}/min-profil/update', [TranslationProfileController::class, 'update'])->name('translation_profile.update')->middleware([HandlePrecognitiveRequests::class]);
    Route::put('{marketCode}/{languageCode}/min-profil/update_information', [TranslationProfileController::class, 'updateInformation'])->name('translation_profile.update.information')->middleware([HandlePrecognitiveRequests::class]);
});



/*
|--------------------------------------------------------------------------
| Translator Application
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole', 'admin']], function () {
    Route::get('{marketCode}/{languageCode}/translator_applications', [TranslatorApplicationsController::class, 'index'])->name('translator_applications');

    Route::get('{marketCode}/{languageCode}/translator_applications/{translatorApplicationUser}/show', [TranslatorApplicationsController::class, 'show'])->name('translator_applications.show');

    Route::post('{marketCode}/{languageCode}/translator_applications/{translatorApplicationUser}/approve', [TranslatorApplicationsController::class, 'approveTranslatorApplication'])->name('translator_applications.approve');

    Route::post('{marketCode}/{languageCode}/translator_applications/{translatorApplicationUser}/reject', [TranslatorApplicationsController::class, 'rejectTranslatorApplication'])->name('translator_applications.reject');
});

// Public

Route::get('{marketCode}/{languageCode}/skapa-konto', [TranslatorApplicationsController::class, 'create'])->name('translator_applications.create');
Route::post('{marketCode}/{languageCode}/skapa-konto/store', [TranslatorApplicationsController::class, 'store'])->name('translator_applications.store')->middleware([HandlePrecognitiveRequests::class]);



/*
|--------------------------------------------------------------------------
| Products
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'admin']], function () {
    Route::get('{marketCode}/{languageCode}/translation_products', [TranslationProductsController::class, 'index'])->name('translation_products');

    Route::get('{marketCode}/{languageCode}/translation_products/create', [TranslationProductsController::class, 'create'])->name('translation_products.create');
    Route::post('{marketCode}/{languageCode}/translation_products/store', [TranslationProductsController::class, 'store'])->name('translation_products.store');

    Route::get('{marketCode}/{languageCode}/translation_products/{product}/basic_information/edit', [TranslationProductsController::class, 'editBasicInformation'])->name('translation_products.basic_information.edit');
    Route::put('{marketCode}/{languageCode}/translation_products/{product}/basic_information/update', [TranslationProductsController::class, 'updateBasicInformation'])->name('translation_products.basic_information.update');
});



/*
|--------------------------------------------------------------------------
| Store
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified']], function () {
    Route::get('{marketCode}/{languageCode}/translation_store', [TranslationStoreController::class, 'index'])->name('translation_store');

    Route::put('{marketCode}/{languageCode}/translation_store/buy_credits', [TranslationStoreController::class, 'buyCredits'])->name('translation_store.buy_credits');

    // Svea
    Route::get('{marketCode}/{languageCode}/svea/create_order/product/{product}/{discountApplies}', [SveaCheckoutController::class, 'createOrder'])->name('svea.create_order');
    Route::get('{marketCode}/{languageCode}/orderbekraftelse', [SveaCheckoutController::class, 'orderConfirmation'])->name('svea.order_confirmation');
    Route::get('{marketCode}/{languageCode}/svea/push/svea_order_id/{sveaOrderID}', [SveaCheckoutController::class, 'pushOrder'])->name('svea.order_push');
});



/*
|--------------------------------------------------------------------------
| Payments
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified']], function () {
    Route::get('{marketCode}/{languageCode}/betalningar', [PaymentsController::class, 'index'])->name('payments');
    Route::post('{marketCode}/{languageCode}/betalning/{order}/kvitto', [PaymentsController::class, 'resendReceipt'])->name('payments.resend_receipt');
});


/*
|--------------------------------------------------------------------------
| Activity Logs
|--------------------------------------------------------------------------
*/

Route::group(['middleware' => ['web', 'auth:sanctum', 'verified', 'hasUserRole', 'admin']], function () {
    Route::get('api/activity-logs', [ActivityLogController::class, 'index'])->name('activity_logs');
    Route::get('api/activity-logs/assignment/{assignment}', [ActivityLogController::class, 'forAssignment'])->name('activity_logs.assignment');
});



/*
|--------------------------------------------------------------------------
| External Form Support
|--------------------------------------------------------------------------
*/

Route::group(['prefix' => 'form', 'middleware' => ['web']], function () {
    Route::get('languages', [FormLanguagesController::class, 'languages']);
    Route::get('categories', [FormCategoriesController::class, 'categories']);
    Route::post('assignment', [FormAssignmentsController::class, 'store']);
    Route::post('translator', [FormTranslatorsController::class, 'store']);
    Route::post('webhook', [FormAssignmentsController::class, 'webhookStore']);
});

// Public

Route::get('{marketCode}/{languageCode}/nytt-uppdrag', [TranslationAssignmentsController::class, 'show'])->name('assignments');
Route::post('{marketCode}/{languageCode}/nytt-uppdrag/store', [TranslationAssignmentsController::class, 'store'])->name('assignments.store')->middleware([HandlePrecognitiveRequests::class]);
Route::group(['middleware' => [VerifyAssignmentPublicAccess::class]], function () {
    Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}', [TranslationAssignmentsController::class, 'customerView'])->name('assignment');
    Route::put('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/accept', [TranslationAssignmentMarketController::class, 'acceptBid'])->middleware([HandlePrecognitiveRequests::class])->name('translation_assignment_market.accept_bid');
    Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/payment/{translationAssignmentBidID}', [TranslationAssignmentMarketController::class, 'processBidPayment'])->middleware([HandlePrecognitiveRequests::class])->name('translation_assignment_market.process_bid_payment');
    Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/payment/{translationAssignmentBidID}/confirmation', [SveaCheckoutController::class, 'offerOrderConfirmation'])->name('svea.offer_order_confirmation');
    Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/translation/download', [TranslationAssignmentsController::class, 'downloadTranslatedFiles'])->name('assignment.download_translations');
});

Route::group(['middleware' => [VerifyAssignmentPublicAccessPreMarketBid::class]], function () {
    Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/premarket/{premarketBidID}/accept/', [TranslationAssignmentsController::class, 'acceptPreMarketBid'])->middleware([HandlePrecognitiveRequests::class])->name('translation_assignment_market.accept_premarket_bid');
    Route::get('{marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/premarket/{premarketBidID}/decline/', [TranslationAssignmentsController::class, 'declinePreMarketBid'])->middleware([HandlePrecognitiveRequests::class])->name('translation_assignment_market.decline_premarket_bid');
});






/*
|--------------------------------------------------------------------------
| Email
|--------------------------------------------------------------------------
*/
if (App::environment(['local', 'dev', 'staging'])) {
    Route::get('email/template', function () {
        return view('emails.layouts.template');
    });

    Route::get('email/new_assignment', function () {
        $assignment = TranslationAssignment::find(24);
        $user = User::find(1);

        return view('emails.new_assignment')->with(['user' => $user, 'assignment' => TranslationAssignmentEmailTransformer::make($assignment)]);
    });

    Route::get('email/assignment_published', function () {
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_published')->with(['assignment' => $assignment]);
    });

    Route::get('email/welcome', function () {
        $user = User::find(8);

        return view('emails.welcome')->with(['user' => $user, 'password' => '123456']);
    });

    Route::get('email/receipt', function () {
        $order = Order::find(1);

        return view('emails.receipt')->with(['order' => $order]);
    });

    Route::get('email/application_rejected', function () {
        $user = User::find(8);

        return view('emails.application_rejected')->with(['user' => $user]);
    });

    Route::get('email/assignment_rejected', function () {
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_rejected')->with(['assignment' => $assignment]);
    });

    Route::get('email/new_assignment_for_approval', function () {
        $assignment = TranslationAssignment::find(24);

        return view('emails.admin.new_assignment_for_approval')->with(['assignment' => $assignment]);
    });

    Route::get('email/new_translator_for_approval', function () {
        $user = User::find(8);

        return view('emails.admin.new_translator_for_approval')->with(['user' => $user]);
    });

    Route::get('email/new_offer_translation', function () {
        $user = User::find(2);
        $assignment = TranslationAssignment::find(24);

        return view('emails.new_offer')->with(['user' => $user, 'assignment' => $assignment]);
    });

    Route::get('email/assignment_payment_received', function () {

        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_payment_received')->with(['assignment' => $assignment]);
    });

    Route::get('email/assignment_won', function () {
        $user = User::find(2);
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_won')->with(['user' => $user, 'assignment' => $assignment]);
    });

    Route::get('email/assignment_lost', function () {
        $user = User::find(2);
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_lost')->with(['user' => $user, 'assignment' => $assignment]);
    });

    Route::get('email/assignment_completed', function () {
        $user = User::find(2);
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_completed')->with(['user' => $user, 'assignment' => $assignment]);
    });

    Route::get('email/assignment_completed_customer', function () {
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_completed_customer')->with(['assignment' => $assignment]);
    });

    Route::get('email/assignment_reminder_customer', function () {
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_reminder_customer')->with(['assignment' => $assignment]);
    });

    Route::get('email/assignment_expired_customer', function () {
        $assignment = TranslationAssignment::find(24);

        return view('emails.assignment_expired_customer')->with(['assignment' => $assignment]);
    });

    Route::get('email/receipt_email_customer', function () {
        $order = Order::find(19);
        return view('emails.receipt_customer')->with(['order' => $order]);
    });

    Route::get('email/premarket_offer_email_customer', function () {
        $assignment = TranslationAssignment::find(24);
        return view('emails.premarket_offer_customer')->with(['assignment' => $assignment]);
    });

    Route::get('email/assignment_price_changed_customer_email', function () {
        $assignment = TranslationAssignment::find(24);
        return view('emails.assignment_price_changed_customer_email')->with(['assignment' => $assignment]);
    });

    Route::get('email/assignment_files_updated_customer_email', function () {
        $assignment = TranslationAssignment::find(24);
        return view('emails.assignment_files_updated_customer_email')->with(['assignment' => $assignment]);
    });

    Route::get('email/assignment_cancelled_customer_email', function () {
        $assignment = TranslationAssignment::find(24);
        return view('emails.assignment_cancelled')->with(['assignment' => $assignment]);
    });

    // Route::get('reminder_job_test', function () {
    //     $translationAssignmentsQuery = TranslationAssignment::query();
    //     $translationAssignmentsQuery->where('is_approved', true);
    //     $translationAssignmentsQuery->where('is_active', true);
    //     $translationAssignmentsQuery->whereNull('completed_at');
    //     $translationAssignmentsQuery->whereHas('bids', function ($query) {
    //         $query->whereNull('accepted_at');
    //     });

    //     $translationAssignments = $translationAssignmentsQuery
    //         ->orderBy('created_at', 'DESC')->get();

    //     //Send reminder email
    //     foreach ($translationAssignments as $translationAssignment) {
    //         Mail::to($translationAssignment->email)->send(new AssignmentReminderCustomerEmail($translationAssignment));

    //         $messageText = "Påminnelse! Du har mottagit prisförslag från översättare." . PHP_EOL . PHP_EOL;
    //         $messageText .= "Se och besvara:" . PHP_EOL . PHP_EOL . $translationAssignment->assignment_customer_link . ".";

    //         SendSMS::dispatch($translationAssignment->assignment_id, $translationAssignment->phone_no, $messageText, true);
    //     }
    // });

    // Route::get('offer_expired', function () {
    //     $translationAssignmentsQuery = TranslationAssignment::GetOfferClosedAssignments('se');
    //     $translationAssignments = $translationAssignmentsQuery
    //         ->orderBy('approved_at', 'DESC')->get();

    //     foreach ($translationAssignments as $translationAssignment) {
    //         try {

    //             $translationAssignment->is_closed_for_offers = true;
    //             $translationAssignment->save();
    //             Mail::to($translationAssignment->email)->send(new OfferReceivedEmail($translationAssignment));
    //         } catch (\Exception $e) {
    //             Log::error('Error while checking passed offer assignments: ' . $e->getMessage());
    //         }
    //     }
    // });

    // Route::get('translator_count', function () {
    //     $translationAssignment = TranslationAssignment::find(24);
    //     $excludedEmails = ['<EMAIL>', '<EMAIL>'];

    //     $translatorCount = User::forAssignment($translationAssignment, $excludedEmails)->count();

    //     echo "Number of matching translators: " . $translatorCount;
    // });

    // Route::mailPreview('mail-preview');

    // Route::get('expired_jobs', function () {
    //     //Open translation assignments
    //     $translationAssignmentsQuery = TranslationAssignment::query();
    //     $translationAssignmentsQuery->where('is_approved', true);
    //     $translationAssignmentsQuery->where('is_active', true);
    //     $translationAssignmentsQuery->whereNull('completed_at');
    //     $translationAssignmentsQuery->where('approved_at', '<', now()->subDays(8));
    //     $translationAssignments = $translationAssignmentsQuery
    //         ->orderBy('created_at', 'DESC')->get();

    //     foreach ($translationAssignments as $translationAssignment) {

    //         $translationAssignment->is_active = false;
    //         $translationAssignment->save();

    //         Mail::to($translationAssignment->email)->send(new AssignmentExpiredCustomerEmail($translationAssignment));
    //     }
    //     dd($translationAssignments);
    //     dd('Expired jobs updated');
    // });
}
