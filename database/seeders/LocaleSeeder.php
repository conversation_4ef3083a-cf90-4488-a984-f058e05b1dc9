<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Softbox\SBX\Admin\Models\Language;
use Softbox\SBX\Admin\Models\LanguageLocalization;

use Softbox\SBX\Admin\Models\Market;
use Softbox\SBX\Admin\Models\MarketLocalization;

class LocaleSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {

    // Languages
    $language = new Language;
    $language->language_code = 'sv';
    $language->is_available_for_website = true;
    $language->is_website_default = true;
    $language->is_website_fallback = true;
    $language->is_available_for_admin = true;
    $language->is_admin_default = true;
    $language->is_admin_fallback = true;
    $language->created_by_user_id = 1;
    $language->updated_by_user_id = 1;
    $language->save();

    $languageLocalization = new LanguageLocalization;
    $languageLocalization->language_id = $language->id;
    $languageLocalization->language_code = "sv";
    $languageLocalization->name = "Svenska";
    $languageLocalization->created_by_user_id = 1;
    $languageLocalization->updated_by_user_id = 1;
    $languageLocalization->save();


    // Markets
    $market = new Market;
    $market->market_code = 'se';
    $market->is_website_default = true;
    $market->is_website_fallback = true;
    $market->is_admin_default = true;
    $market->is_admin_fallback = true;
    $market->created_by_user_id = 1;
    $market->updated_by_user_id = 1;
    $market->save();

    $marketLocalization = new MarketLocalization;
    $marketLocalization->market_id = $market->id;
    $marketLocalization->language_code = "sv";
    $marketLocalization->name = "Sverige";
    $marketLocalization->created_by_user_id = 1;
    $marketLocalization->updated_by_user_id = 1;
    $marketLocalization->save();
  }
}
