<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Softbox\SBX\Webshop\Models\ProductCategory;
use Softbox\SBX\Webshop\Models\ProductCategoryLocalization;

use Softbox\SBX\Webshop\Models\Product;
use Softbox\SBX\Webshop\Models\ProductLocalization;
use Softbox\SBX\Webshop\Models\ProductMarket;
use Softbox\SBX\Webshop\Models\ProductPrice;

use Carbon\Carbon;

class ProductsSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {

    // Products
    $oneLeadProduct = new Product;
    $oneLeadProduct->product_unit_id = 1;
    $oneLeadProduct->credit_quantity = 1;
    $oneLeadProduct->created_by_user_id = 1;
    $oneLeadProduct->updated_by_user_id = 1;
    $oneLeadProduct->save();

    $oneLeadProductLocalization = new ProductLocalization;
    $oneLeadProductLocalization->product_id = $oneLeadProduct->id;
    $oneLeadProductLocalization->language_code = "sv";
    $oneLeadProductLocalization->name = "1 lead";
    $oneLeadProductLocalization->short_description = "Köp 1 lead för 199 kr inkl moms";
    $oneLeadProductLocalization->long_description = "";
    $oneLeadProductLocalization->created_by_user_id = 1;
    $oneLeadProductLocalization->updated_by_user_id = 1;
    $oneLeadProductLocalization->save();

    $oneLeadProductMarket = new ProductMarket;
    $oneLeadProductMarket->product_id = $oneLeadProduct->id;
    $oneLeadProductMarket->market_code = "se";
    $oneLeadProductMarket->is_active = true;
    $oneLeadProductMarket->vat_rate_id = 1;
    $oneLeadProductMarket->created_by_user_id = 1;
    $oneLeadProductMarket->updated_by_user_id = 1;
    $oneLeadProductMarket->save();

    $oneLeadProductPrice = new ProductPrice;
    $oneLeadProductPrice->price_list_id = 1;
    $oneLeadProductPrice->market_code = "se";
    $oneLeadProductPrice->product_id =  $oneLeadProduct->id;
    $oneLeadProductPrice->currency_id = 1;
    $oneLeadProductPrice->price = 159.20;
    $oneLeadProductPrice->created_by_user_id = 1;
    $oneLeadProductPrice->updated_by_user_id = 1;
    $oneLeadProductPrice->save();


    $fiveLeadsProduct = new Product;
    $fiveLeadsProduct->product_unit_id = 1;
    $fiveLeadsProduct->credit_quantity = 5;
    $fiveLeadsProduct->created_by_user_id = 1;
    $fiveLeadsProduct->updated_by_user_id = 1;
    $fiveLeadsProduct->save();

    $fiveLeadsProductLocalization = new ProductLocalization;
    $fiveLeadsProductLocalization->product_id = $fiveLeadsProduct->id;
    $fiveLeadsProductLocalization->language_code = "sv";
    $fiveLeadsProductLocalization->name = "5 leads";
    $fiveLeadsProductLocalization->short_description = "Köp 5 leads för 179 kr/st inkl moms";
    $fiveLeadsProductLocalization->long_description = "";
    $fiveLeadsProductLocalization->created_by_user_id = 1;
    $fiveLeadsProductLocalization->updated_by_user_id = 1;
    $fiveLeadsProductLocalization->save();

    $fiveLeadsProductMarket = new ProductMarket;
    $fiveLeadsProductMarket->product_id = $fiveLeadsProduct->id;
    $fiveLeadsProductMarket->market_code = "se";
    $fiveLeadsProductMarket->is_active = true;
    $fiveLeadsProductMarket->vat_rate_id = 1;
    $fiveLeadsProductMarket->created_by_user_id = 1;
    $fiveLeadsProductMarket->updated_by_user_id = 1;
    $fiveLeadsProductMarket->save();

    $fiveLeadsProductPrice = new ProductPrice;
    $fiveLeadsProductPrice->price_list_id = 1;
    $fiveLeadsProductPrice->market_code = "se";
    $fiveLeadsProductPrice->product_id =  $fiveLeadsProduct->id;
    $fiveLeadsProductPrice->currency_id = 1;
    $fiveLeadsProductPrice->price = 716.00;
    $fiveLeadsProductPrice->created_by_user_id = 1;
    $fiveLeadsProductPrice->updated_by_user_id = 1;
    $fiveLeadsProductPrice->save();


    $tenLeadsProduct = new Product;
    $tenLeadsProduct->product_unit_id = 1;
    $tenLeadsProduct->credit_quantity = 10;
    $tenLeadsProduct->created_by_user_id = 1;
    $tenLeadsProduct->updated_by_user_id = 1;
    $tenLeadsProduct->save();

    $tenLeadsProductLocalization = new ProductLocalization;
    $tenLeadsProductLocalization->product_id = $tenLeadsProduct->id;
    $tenLeadsProductLocalization->language_code = "sv";
    $tenLeadsProductLocalization->name = "10 leads";
    $tenLeadsProductLocalization->short_description = "Köp 10 leads för 149 kr/st inkl moms";
    $tenLeadsProductLocalization->long_description = "";
    $tenLeadsProductLocalization->created_by_user_id = 1;
    $tenLeadsProductLocalization->updated_by_user_id = 1;
    $tenLeadsProductLocalization->save();

    $tenLeadsProductMarket = new ProductMarket;
    $tenLeadsProductMarket->product_id = $tenLeadsProduct->id;
    $tenLeadsProductMarket->market_code = "se";
    $tenLeadsProductMarket->is_active = true;
    $tenLeadsProductMarket->vat_rate_id = 1;
    $tenLeadsProductMarket->created_by_user_id = 1;
    $tenLeadsProductMarket->updated_by_user_id = 1;
    $tenLeadsProductMarket->save();

    $tenLeadsProductPrice = new ProductPrice;
    $tenLeadsProductPrice->price_list_id = 1;
    $tenLeadsProductPrice->market_code = "se";
    $tenLeadsProductPrice->product_id =  $tenLeadsProduct->id;
    $tenLeadsProductPrice->currency_id = 1;
    $tenLeadsProductPrice->price = 1192.00;
    $tenLeadsProductPrice->created_by_user_id = 1;
    $tenLeadsProductPrice->updated_by_user_id = 1;
    $tenLeadsProductPrice->save();
  }
}
