<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Softbox\SBX\Webshop\Models\ProductUnit;
use Softbox\SBX\Webshop\Models\ProductUnitLocalization;

class ProductUnitsSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {
    $productUnit = new ProductUnit;
    $productUnit->is_default = true;
    $productUnit->created_by_user_id = 1;
    $productUnit->updated_by_user_id = 1;
    $productUnit->save();

    $productUnitLocalization = new ProductUnitLocalization;
    $productUnitLocalization->product_unit_id = $productUnit->id;
    $productUnitLocalization->language_code = 'sv';
    $productUnitLocalization->unit = "st";
    $productUnitLocalization->created_by_user_id = 1;
    $productUnitLocalization->updated_by_user_id = 1;
    $productUnitLocalization->save();
  }
}
