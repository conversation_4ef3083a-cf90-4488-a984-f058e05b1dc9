<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\TranslationCategory;
use App\Models\TranslationCategoryLocalization;
use App\Models\TranslationCategoryMarket;

class TranslationCategorySeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {

    // Translation Categories
    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = 1;
    $translationCategory->updated_by_user_id = 1;
    $translationCategory->save();

    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = "sv";
    $translationCategoryLocalization->name = "Dokument";
    $translationCategoryLocalization->created_by_user_id = 1;
    $translationCategoryLocalization->updated_by_user_id = 1;
    $translationCategoryLocalization->save();

    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = "se";
    $translationCategoryMarket->is_active = true;
    $translationCategoryMarket->created_by_user_id = 1;
    $translationCategoryMarket->updated_by_user_id = 1;
    $translationCategoryMarket->save();


    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = 1;
    $translationCategory->updated_by_user_id = 1;
    $translationCategory->save();

    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = "sv";
    $translationCategoryLocalization->name = "Juridiska dokument";
    $translationCategoryLocalization->created_by_user_id = 1;
    $translationCategoryLocalization->updated_by_user_id = 1;
    $translationCategoryLocalization->save();

    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = "se";
    $translationCategoryMarket->is_active = true;
    $translationCategoryMarket->created_by_user_id = 1;
    $translationCategoryMarket->updated_by_user_id = 1;
    $translationCategoryMarket->save();


    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = 1;
    $translationCategory->updated_by_user_id = 1;
    $translationCategory->save();

    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = "sv";
    $translationCategoryLocalization->name = "Myndighetsbrev";
    $translationCategoryLocalization->created_by_user_id = 1;
    $translationCategoryLocalization->updated_by_user_id = 1;
    $translationCategoryLocalization->save();

    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = "se";
    $translationCategoryMarket->is_active = true;
    $translationCategoryMarket->created_by_user_id = 1;
    $translationCategoryMarket->updated_by_user_id = 1;
    $translationCategoryMarket->save();


    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = 1;
    $translationCategory->updated_by_user_id = 1;
    $translationCategory->save();

    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = "sv";
    $translationCategoryLocalization->name = "Avtal";
    $translationCategoryLocalization->created_by_user_id = 1;
    $translationCategoryLocalization->updated_by_user_id = 1;
    $translationCategoryLocalization->save();

    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = "se";
    $translationCategoryMarket->is_active = true;
    $translationCategoryMarket->created_by_user_id = 1;
    $translationCategoryMarket->updated_by_user_id = 1;
    $translationCategoryMarket->save();


    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = 1;
    $translationCategory->updated_by_user_id = 1;
    $translationCategory->save();

    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = "sv";
    $translationCategoryLocalization->name = "Ekonomisk översättning";
    $translationCategoryLocalization->created_by_user_id = 1;
    $translationCategoryLocalization->updated_by_user_id = 1;
    $translationCategoryLocalization->save();

    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = "se";
    $translationCategoryMarket->is_active = true;
    $translationCategoryMarket->created_by_user_id = 1;
    $translationCategoryMarket->updated_by_user_id = 1;
    $translationCategoryMarket->save();


    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = 1;
    $translationCategory->updated_by_user_id = 1;
    $translationCategory->save();

    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = "sv";
    $translationCategoryLocalization->name = "Medicinsk översättning";
    $translationCategoryLocalization->created_by_user_id = 1;
    $translationCategoryLocalization->updated_by_user_id = 1;
    $translationCategoryLocalization->save();

    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = "se";
    $translationCategoryMarket->is_active = true;
    $translationCategoryMarket->created_by_user_id = 1;
    $translationCategoryMarket->updated_by_user_id = 1;
    $translationCategoryMarket->save();
  }
}
