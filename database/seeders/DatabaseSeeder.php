<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use Illuminate\Database\Seeder;

use Softbox\SBX\Admin\Seeders\SettingTypesSeeder;

class DatabaseSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {
    $this->call(LocaleSeeder::class);
    $this->call(UsersSeeder::class);
    $this->call(ProductUnitsSeeder::class);
    $this->call(WebshopSeeder::class);
    $this->call(ProductsSeeder::class);
    $this->call(SettingTypesSeeder::class);
    $this->call(SettingsSeeder::class);
    $this->call(TranslationCategorySeeder::class);
    $this->call(TranslationLanguageSeeder::class);
    $this->call(TranslationAssignmentsSeeder::class);
  }
}
