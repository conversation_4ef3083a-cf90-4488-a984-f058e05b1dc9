<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Softbox\SBX\Webshop\Models\VatRate;
use Softbox\SBX\Webshop\Models\VatRateLocalization;
use Softbox\SBX\Webshop\Models\VatRateMarket;

use Softbox\SBX\Webshop\Models\Currency;
use Softbox\SBX\Webshop\Models\CurrencyMarket;

use Softbox\SBX\Webshop\Models\PriceList;
use Softbox\SBX\Webshop\Models\PriceListMarket;

class WebshopSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {

    // VAT Rates
    $vatRate25 = new VatRate;
    $vatRate25->created_by_user_id = 1;
    $vatRate25->updated_by_user_id = 1;
    $vatRate25->save();

    $vatRate25Localization = new VatRateLocalization;
    $vatRate25Localization->vat_rate_id = $vatRate25->id;
    $vatRate25Localization->language_code = "sv";
    $vatRate25Localization->name = "Moms (25%)";
    $vatRate25Localization->created_by_user_id = 1;
    $vatRate25Localization->updated_by_user_id = 1;
    $vatRate25Localization->save();

    $vatRate25Market = new VatRateMarket;
    $vatRate25Market->vat_rate_id = $vatRate25->id;
    $vatRate25Market->market_code = "se";
    $vatRate25Market->is_active = true;
    $vatRate25Market->is_default = true;
    $vatRate25Market->rate = 25;
    $vatRate25Market->created_by_user_id = 1;
    $vatRate25Market->updated_by_user_id = 1;
    $vatRate25Market->save();


    // Currencies
    $currencySEK = new Currency;
    $currencySEK->name = "SEK";
    $currencySEK->created_by_user_id = 1;
    $currencySEK->updated_by_user_id = 1;
    $currencySEK->save();

    $currencySEKMarket = new CurrencyMarket;
    $currencySEKMarket->currency_id = $currencySEK->id;
    $currencySEKMarket->market_code = "se";
    $currencySEKMarket->is_active = true;
    $currencySEKMarket->is_default = true;
    $currencySEKMarket->created_by_user_id = 1;
    $currencySEKMarket->updated_by_user_id = 1;
    $currencySEKMarket->save();


    // Price Lists
    $priceListWebshop = new PriceList;
    $priceListWebshop->name = "Webshop";
    $priceListWebshop->created_by_user_id = 1;
    $priceListWebshop->updated_by_user_id = 1;
    $priceListWebshop->save();

    $priceListWebshopMarket = new PriceListMarket;
    $priceListWebshopMarket->price_list_id = $priceListWebshop->id;
    $priceListWebshopMarket->market_code = "se";
    $priceListWebshopMarket->is_active = true;
    $priceListWebshopMarket->is_default = true;
    $priceListWebshopMarket->prices_include_vat = false;
    $priceListWebshopMarket->created_by_user_id = 1;
    $priceListWebshopMarket->updated_by_user_id = 1;
    $priceListWebshopMarket->save();
  }
}