<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Softbox\SBX\Admin\Models\User;
use Softbox\SBX\Admin\Models\Role;
use Softbox\SBX\Admin\Models\RoleLocalization;

use Carbon\Carbon;
use DB;
use Hash;
use Str;

class UsersSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {
    $adminRole = new Role;
    $adminRole->is_admin = true;
    $adminRole->created_by_user_id = 1;
    $adminRole->updated_by_user_id = 1;
    $adminRole->save();

    $adminRoleLocalization = new RoleLocalization;
    $adminRoleLocalization->role_id = $adminRole->id;
    $adminRoleLocalization->language_code = "sv";
    $adminRoleLocalization->name = "Administratör";
    $adminRoleLocalization->created_by_user_id = 1;
    $adminRoleLocalization->updated_by_user_id = 1;
    $adminRoleLocalization->save();

    $translatorRole = new Role;
    $translatorRole->is_admin = false;
    $translatorRole->created_by_user_id = 1;
    $translatorRole->updated_by_user_id = 1;
    $translatorRole->save();

    $translatorRoleLocalization = new RoleLocalization;
    $translatorRoleLocalization->role_id = $translatorRole->id;
    $translatorRoleLocalization->language_code = "sv";
    $translatorRoleLocalization->name = "Översättare";
    $translatorRoleLocalization->created_by_user_id = 1;
    $translatorRoleLocalization->updated_by_user_id = 1;
    $translatorRoleLocalization->save();

    $lasse = new User;
    $translatorID = random_int(1001, 9999);
    while (count(User::where('translator_id', $translatorID)->get()) > 0) {
      $translatorID = random_int(1001, 9999);
    }
    $lasse->translator_id = $translatorID;
    $lasse->first_name = "Lars";
    $lasse->last_name = "Lindholm";
    $lasse->email = "<EMAIL>";
    $lasse->admin_language_code = "sv";
    $lasse->created_by_user_id = 1;
    $lasse->updated_by_user_id = 1;
    $lasse->remember_token = "sdvmkdfgjslkfjwijvsoivj234dc90sfusd0fu043u0";
    $lasse->password = Hash::make('secretLL01');
    $lasse->save();

    $lasse->roles()->attach($adminRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);


    $fv = new User;
    $translatorID = random_int(1001, 9999);
    while (count(User::where('translator_id', $translatorID)->get()) > 0) {
      $translatorID = random_int(1001, 9999);
    }
    $fv->translator_id = $translatorID;
    $fv->first_name = "First";
    $fv->last_name = "Vision";
    $fv->email = "<EMAIL>";
    $fv->admin_language_code = "sv";
    $fv->created_by_user_id = 1;
    $fv->updated_by_user_id = 1;
    $fv->remember_token = "sdvmkdfgjsekfjwijvsoivj48gdc50sfusd0fu043u0";
    $fv->password = Hash::make('FirstVision2014!');
    $fv->save();

    $fv->roles()->attach($adminRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);


    // Translators
    for ($i = 0; $i < 5; $i++) {
      $translator = new User;
      $translatorID = random_int(1001, 9999);
      while (count(User::where('translator_id', $translatorID)->get()) > 0) {
        $translatorID = random_int(1001, 9999);
      }
      $translator->translator_id = $translatorID;
      $translator->company = "Översättare AB";
      $translator->identity_no = "123456-1234";
      $translator->first_name = "Översättare";
      $translator->last_name = "Nr " . ($i + 1);
      $translator->email = "test" . ($i + 1) . "@test.com";
      $translator->phone_no = "123 456 78 90";
      $translator->address_1 = "Testvägen 1";
      $translator->postal_code = "123 45";
      $translator->city = "Teststaden";
      $translator->is_authorized = rand(0, 1);
      $translator->authorization_id = "123456";
      $translator->credit_count = rand(0, 5);
      $translator->admin_language_code = "sv";
      $translator->created_by_user_id = 1;
      $translator->updated_by_user_id = 1;
      $translator->remember_token = "sdvmkdfgjslkfjwijvsoivj234dc90sfusd0fu043u0";
      $translator->password = Hash::make('secretLL01');
      $translator->save();

      if (!$translator->is_authorized) {
        $translator->is_approved = true;
        $translator->save();
      }

      $fromLanguages = rand(1, 3);
      for ($fromLanguagesIndex = 0; $fromLanguagesIndex < $fromLanguages; $fromLanguagesIndex++) {
        DB::table('user_from_translation_languages')->insert([
          ['translation_language_id' => rand(1, 12), 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
        ]);
      }

      $toLanguages = rand(1, 3);
      for ($toLanguagesIndex = 0; $toLanguagesIndex < $toLanguages; $toLanguagesIndex++) {
        DB::table('user_to_translation_languages')->insert([
          ['translation_language_id' => rand(1, 12), 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
        ]);
      }

      $services = rand(1, 3);
      for ($servicesIndex = 0; $servicesIndex < $services; $servicesIndex++) {
        DB::table('user_translation_categories')->insert([
          ['translation_category_id' => rand(1, 6), 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
        ]);
      }

      $translator->roles()->attach($translatorRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);
    }


    $translator = new User;
    $translatorID = random_int(1001, 9999);
    while (count(User::where('translator_id', $translatorID)->get()) > 0) {
      $translatorID = random_int(1001, 9999);
    }
    $translator->translator_id = $translatorID;
    $translator->company = "Softbox AB";
    $translator->identity_no = "556783-8973";
    $translator->first_name = "Lars";
    $translator->last_name = "Lindholm";
    $translator->email = "<EMAIL>";
    $translator->phone_no = "070 324 20 52";
    $translator->address_1 = "Clemenstorget 8";
    $translator->postal_code = "222 21";
    $translator->city = "Lund";
    $translator->is_authorized = true;
    $translator->authorization_id = "123456";
    $translator->credit_count = 2;
    $translator->admin_language_code = "sv";
    $translator->created_by_user_id = 1;
    $translator->updated_by_user_id = 1;
    $translator->remember_token = "sdvmkdfgjslkfjwijvsoivj234dc90sfusd0fu043u0";
    $translator->password = Hash::make('secretLL01');
    $translator->save();

    DB::table('user_from_translation_languages')->insert([
      ['translation_language_id' => 3, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
    ]);

    DB::table('user_to_translation_languages')->insert([
      ['translation_language_id' => 6, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
      ['translation_language_id' => 4, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
      ['translation_language_id' => 2, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
    ]);

    DB::table('user_translation_categories')->insert([
      ['translation_category_id' => 2, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
      ['translation_category_id' => 4, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()],
      ['translation_category_id' => 6, 'user_id' => $translator->id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
    ]);

    $translator->roles()->attach($translatorRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);
  }
}
