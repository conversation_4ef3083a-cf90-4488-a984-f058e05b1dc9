<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\TranslationAssignment;

use Carbon\Carbon;
use DB;
use Str;

class TranslationAssignmentsSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {
    // Translation Assignments
    for ($i = 0; $i < 60; $i++) {
      $fromLanguageID = rand(1, 12);
      $toLanguageID = rand(1, 12);
      while ($toLanguageID == $fromLanguageID) {
        $toLanguageID = rand(1, 12);
      }

      $assignmentRand = rand(0, 1);
      $assignmentType = 'company';
      if ($assignmentRand == 1) {
        $assignmentType = 'personal';
      }

      $translationAssignment = new TranslationAssignment;
      $assignmentID = random_int(100001, 999999);
      while (count(TranslationAssignment::where('assignment_id', $assignmentID)->get()) > 0) {
        $assignmentID = random_int(100001, 999999);
      }
      $translationAssignment->assignment_id = $assignmentID;
      $translationAssignment->is_approved = rand(0, 1);
      $translationAssignment->translation_category_id = rand(1, 6);
      $translationAssignment->from_translation_language_id = $fromLanguageID;
      $translationAssignment->to_translation_language_id = $toLanguageID;
      $translationAssignment->is_authorization_required = rand(0, 1);
      $translationAssignment->number_of_words = rand(250, 5000);
      $translationAssignment->assignment_type = $assignmentType;
      $translationAssignment->first_name = "Test";
      $translationAssignment->last_name = "kund";
      $translationAssignment->company_no = "12345678-1234";
      $translationAssignment->email = "<EMAIL>";
      $translationAssignment->phone_no = "0701234567";
      $translationAssignment->is_email_contact_allowed = true;
      $translationAssignment->is_phone_contact_allowed = false;
      $translationAssignment->notes = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna.";
      $translationAssignment->save();

      $bids = rand(0, 5);
      if ($bids > 0) {
        for ($bidIndex = 0; $bidIndex < $bids; $bidIndex++) {
          DB::table('translation_assignment_bids')->insert([
            ['translation_assignment_id' => $translationAssignment->id, 'user_id' => ($bidIndex + 10), 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
          ]);
        }
      }
    }
  }
}
