<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use Softbox\SBX\Admin\Models\SettingType;
use Softbox\SBX\Admin\Models\Setting;
use Softbox\SBX\Admin\Models\SettingLocalization;
use Softbox\SBX\Admin\Models\SettingMarket;

class SettingsSeeder extends Seeder
{

    /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

    public function run()
    {
        $booleanSettingType = SettingType::where('type', 'boolean')->first();
        $integerSettingType = SettingType::where('type', 'integer')->first();
        $floatSettingType = SettingType::where('type', 'float')->first();
        $stringSettingType = SettingType::where('type', 'string')->first();
        $jsonSettingType = SettingType::where('type', 'json')->first();

        $setting = new Setting;
        $setting->setting_type_id = $integerSettingType->id;
        $setting->is_user_editable = true;
        $setting->created_by_user_id = 1;
        $setting->updated_by_user_id = 1;
        $setting->save();

        $localization = new SettingLocalization;
        $localization->setting_id = $setting->id;
        $localization->language_code = "sv";
        $localization->name = "Max antal bud per uppdrag";
        $localization->created_by_user_id = 1;
        $localization->updated_by_user_id = 1;
        $localization->save();

        $market = new SettingMarket;
        $market->setting_id = $setting->id;
        $market->market_code = "se";
        $market->integer_value = 5;
        $market->created_by_user_id = 1;
        $market->updated_by_user_id = 1;
        $market->save();
    }
}
