<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\TranslationLanguage;
use App\Models\TranslationLanguageLocalization;
use App\Models\TranslationLanguageMarket;

class TranslationLanguageSeeder extends Seeder {

  /*
  |--------------------------------------------------------------------------
  | Run
  |--------------------------------------------------------------------------
  */

  public function run() {

    // Translation Categories
    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Arabiska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Tyska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Svenska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Italienska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Franska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Danska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Norska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Finska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Holländska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Spanska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Grekiska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();


     $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = 1;
    $translationLanguage->updated_by_user_id = 1;
    $translationLanguage->save();

    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = "sv";
    $translationLanguageLocalization->name = "Engelska";
    $translationLanguageLocalization->created_by_user_id = 1;
    $translationLanguageLocalization->updated_by_user_id = 1;
    $translationLanguageLocalization->save();

    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = "se";
    $translationLanguageMarket->is_active = true;
    $translationLanguageMarket->created_by_user_id = 1;
    $translationLanguageMarket->updated_by_user_id = 1;
    $translationLanguageMarket->save();
  }
}
