<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('translation_assignment_bids', function (Blueprint $table) {
            //
            $table->integer('order_id')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('translation_assignment_bids', function (Blueprint $table) {

            $table->dropColumn('order_id');
            //
        });
    }
};
