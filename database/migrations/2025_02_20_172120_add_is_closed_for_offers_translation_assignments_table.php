<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('translation_assignments', function (Blueprint $table) {
            //
            $table->boolean('is_closed_for_offers')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('translation_assignments', function (Blueprint $table) {
            //
            $table->dropColumn('is_closed_for_offers');
        });
    }
};
