<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('translation_assignment_premarket_bids', function (Blueprint $table) {
            $table->bigIncrements('id')->unsigned();
            $table->integer('translation_assignment_id');
            $table->integer('user_id');
            $table->double('price');
            $table->double('price_end_customer');
            $table->tinyInteger('is_accepted')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamps();
        });

        Schema::table('translation_assignment_bids', function (Blueprint $table) {
            //
            $table->dropColumn([
                'is_premarket_bid',
            ]);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('translation_assignment_premarket_bids');

        Schema::table('translation_assignment_bids', function (Blueprint $table) {
            //
            $table->integer('is_premarket_bid')->nullable();
        });
    }
};
