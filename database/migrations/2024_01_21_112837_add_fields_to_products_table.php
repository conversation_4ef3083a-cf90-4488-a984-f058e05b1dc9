<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

  /*
  |--------------------------------------------------------------------------
  | Up
  |--------------------------------------------------------------------------
  */

  public function up() {
    Schema::table('products', function (Blueprint $table) {
      $table->integer('credit_quantity')->default(1);
    });
  }



  /*
  |--------------------------------------------------------------------------
  | Down
  |--------------------------------------------------------------------------
  */

  public function down() {
    Schema::table('products', function (Blueprint $table) {
      $table->dropColumn([
        'credit_quantity'
      ]);
    });
  }
};
