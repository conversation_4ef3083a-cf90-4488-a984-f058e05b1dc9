<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

  /*
  |--------------------------------------------------------------------------
  | Up
  |--------------------------------------------------------------------------
  */

  public function up(): void {
    Schema::create('translation_assignments', function (Blueprint $table) {
      $table->id();
      $table->integer('assignment_id')->unique();
      $table->boolean('is_approved')->default(false);
      $table->integer('translation_category_id');
      $table->integer('from_translation_language_id');
      $table->integer('to_translation_language_id');
      $table->boolean('is_authorization_required')->default(false);
      $table->integer('number_of_words')->nullable();
      $table->enum('assignment_type', ['company', 'personal'])->default('company');
      $table->string('first_name');
      $table->string('last_name')->nullable();
      $table->string('company')->nullable();
      $table->string('company_no')->nullable();
      $table->string('email');
      $table->string('phone_no');
      $table->mediumtext('notes')->nullable();
      $table->boolean('is_email_contact_allowed')->default(false);
      $table->boolean('is_phone_contact_allowed')->default(false);
      $table->timestamps();
    });
  }

  /*
  |--------------------------------------------------------------------------
  | Down
  |--------------------------------------------------------------------------
  */

  public function down(): void {
    Schema::dropIfExists('translation_assignments');
  }
};
