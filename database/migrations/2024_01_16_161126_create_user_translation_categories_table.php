<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

  /*
  |--------------------------------------------------------------------------
  | Up
  |--------------------------------------------------------------------------
  */

  public function up(): void {
    Schema::create('user_translation_categories', function (Blueprint $table) {
      $table->id();
      $table->integer('user_id');
      $table->string('translation_category_id');
      $table->timestamps();
    });
  }

  /*
  |--------------------------------------------------------------------------
  | Down
  |--------------------------------------------------------------------------
  */

  public function down(): void {
    Schema::dropIfExists('user_translation_categories');
  }
};
