<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

  /*
  |--------------------------------------------------------------------------
  | Up
  |--------------------------------------------------------------------------
  */

  public function up() {
    Schema::table('users', function (Blueprint $table) {
      $table->integer('translator_id')->unique();
      $table->integer('credit_count')->default(0);
      $table->boolean('is_authorized')->default(false);
      $table->boolean('discount_used')->default(false);
      $table->string('authorization_id')->nullable();
    });
  }



  /*
  |--------------------------------------------------------------------------
  | Down
  |--------------------------------------------------------------------------
  */

  public function down() {
    Schema::table('users', function (Blueprint $table) {
      $table->dropColumn([
        'translator_id',
        'credit_count',
        'is_authorized',
        'authorization_id'
      ]);
    });
  }
};
