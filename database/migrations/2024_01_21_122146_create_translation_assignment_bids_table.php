<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

  /*
  |--------------------------------------------------------------------------
  | Up
  |--------------------------------------------------------------------------
  */

  public function up(): void {
    Schema::create('translation_assignment_bids', function (Blueprint $table) {
      $table->id();
      $table->integer('translation_assignment_id');
      $table->integer('user_id');
      $table->timestamps();
    });
  }

  /*
  |--------------------------------------------------------------------------
  | Down
  |--------------------------------------------------------------------------
  */

  public function down(): void {
    Schema::dropIfExists('translation_assignment_bids');
  }
};
