<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

  /*
  |--------------------------------------------------------------------------
  | Up
  |--------------------------------------------------------------------------
  */

  public function up(): void {
    Schema::create('translation_language_localizations', function (Blueprint $table) {
      $table->id();
      $table->integer('translation_language_id');
      $table->string('language_code');
      $table->string('name')->nullable();
      $table->integer('created_by_user_id');
      $table->integer('updated_by_user_id');
      $table->timestamps();
    });
  }



  /*
  |--------------------------------------------------------------------------
  | Down
  |--------------------------------------------------------------------------
  */

  public function down(): void {
    Schema::dropIfExists('translation_language_localizations');
  }
};
