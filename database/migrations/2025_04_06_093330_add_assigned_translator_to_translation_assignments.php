<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAssignedTranslatorToTranslationAssignments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('translation_assignments', function (Blueprint $table) {
            $table->unsignedBigInteger('assigned_translator_id')->nullable()->after('assignment_id');
            $table->foreign('assigned_translator_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('translation_assignments', function (Blueprint $table) {
            $table->dropForeign(['assigned_translator_id']);
            $table->dropColumn('assigned_translator_id');
        });
    }
}
