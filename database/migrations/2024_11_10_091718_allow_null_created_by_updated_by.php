<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
            $table->integer('created_by_user_id')->nullable()->change();
            $table->integer('updated_by_user_id')->nullable()->change();
        });

        Schema::table('order_rows', function (Blueprint $table) {
            //
            $table->integer('created_by_user_id')->nullable()->change();
            $table->integer('updated_by_user_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
            $table->integer('created_by_user_id')->change();
            $table->integer('updated_by_user_id')->change();
        });

        Schema::table('order_rows', function (Blueprint $table) {
            //
            $table->integer('created_by_user_id')->change();
            $table->integer('updated_by_user_id')->change();
        });
    }
};
