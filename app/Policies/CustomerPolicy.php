<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;

use App\Models\User;
use App\Models\Customer;

class CustomerPolicy {

  /*
  |---------------------------------------------------------------------
  | Show Customer Policy
  |---------------------------------------------------------------------
  */

  public function show(User $user, Customer $customer): Response {
    if ($user->isAdmin) {
      return Response::allow();
    }

    return $user->sales_person_id === $customer->sales_person_id
      ? Response::allow()
      : Response::deny('Du har inte behörighet att visa denna kund.');
  }
}
