<?php

namespace App\Jobs;

use App\Mail\AssignmentReminderCustomerEmail;
use App\Models\TranslationAssignment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\HelloSMS\Jobs\SendSMS;
use Mail;

class SendBidReminderEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //Open translation assignments
        $translationAssignmentsQuery = TranslationAssignment::query();
        $translationAssignmentsQuery->where('is_approved', true);
        $translationAssignmentsQuery->where('is_active', true);
        $translationAssignmentsQuery->whereNull('completed_at');
        $translationAssignmentsQuery->whereHas('bids', function ($query) {
            $query->whereNull('accepted_at');
        });

        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')->get();

        //Send reminder email
        foreach ($translationAssignments as $translationAssignment) {
            Mail::to($translationAssignment->email)->send(new AssignmentReminderCustomerEmail($translationAssignment));

            // $messageText = "Påminnelse! Du har mottagit prisförslag från översättare." . PHP_EOL . PHP_EOL;
            // $messageText .= "Se och besvara:" . PHP_EOL . PHP_EOL . $translationAssignment->assignment_customer_link . ".";

            // SendSMS::dispatch($translationAssignment->assignment_id, $translationAssignment->phone_no, $messageText, env('HELLO_SMS_TEST_MODE'));
        }
    }
}
