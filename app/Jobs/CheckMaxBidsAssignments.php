<?php

namespace App\Jobs;

use App\Models\TranslationAssignment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class CheckMaxBidsAssignments implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct(TranslationAssignment $assignment)
    {
        $this->assignment = $assignment;
    }


    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Check if this assignment has reached max bids
        if ($this->assignment->hasReachedMaxBids('se')) {
            MarkAssignmentAsClosedForOffers::dispatch($this->assignment);
            return;
        }

        // If max bids not reached, check if all eligible translators have bid
        $excludedEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
        $translatorCount = User::forAssignment($this->assignment, $excludedEmails)->count();

        // If all eligible translators have bid, close for offers
        if ($translatorCount > 0 && $this->assignment->bids()->count() >= $translatorCount) {
            MarkAssignmentAsClosedForOffers::dispatch($this->assignment);
        }
    }
}
