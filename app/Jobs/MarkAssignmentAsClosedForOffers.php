<?php

namespace App\Jobs;

use App\Mail\AssignmentExpiredCustomerEmail;
use App\Models\TranslationAssignment;
use App\Services\ActivityLogService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use App\Mail\OfferReceivedEmail;

use Softbox\SBX\HelloSMS\Jobs\SendSMS;
use Illuminate\Support\Facades\Mail;

class MarkAssignmentAsClosedForOffers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
|--------------------------------------------------------------------------
| Contructor
|--------------------------------------------------------------------------
*/

    public function __construct(TranslationAssignment $assignment)
    {
        $this->assignment = $assignment;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $translationAssignment = $this->assignment;
        try {
            // Mark the assignment as closed for offers
            $translationAssignment->is_closed_for_offers = true;
            $translationAssignment->save();

            // Send email to the customer
            Mail::to($translationAssignment->email)->send(new OfferReceivedEmail($translationAssignment));

            // Log the activity
            ActivityLogService::log(
                'offer_email_sent',
                'Offer received email sent to customer',
                $translationAssignment,
                [
                    'email' => $translationAssignment->email,
                    'assignment_id' => $translationAssignment->assignment_id,
                    'bid_count' => $translationAssignment->bids()->count(),
                ]
            );

            Log::info('Assignment marked as closed for offers and email sent', [
                'assignment_id' => $translationAssignment->id,
                'email' => $translationAssignment->email
            ]);
        } catch (\Exception $e) {
            Log::error('Error while marking assignment as closed for offers: ' . $e->getMessage());
        }
    }
}
