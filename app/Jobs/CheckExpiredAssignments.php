<?php

namespace App\Jobs;

use App\Mail\AssignmentExpiredCustomerEmail;
use App\Models\TranslationAssignment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Softbox\SBX\Admin\Models\Setting;
use Illuminate\Support\Facades\Mail;

class CheckExpiredAssignments implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Get assignment deadline setting (Setting ID 5) for the main market (se)
        $assignmentDeadlineSetting = Setting::find(5);
        $assignmentDeadlineSettingMarket = $assignmentDeadlineSetting->markets()->where('market_code', 'se')->first();

        // Default to 8 days if no setting is found
        $deadlineDays = 8;
        if ($assignmentDeadlineSettingMarket) {
            $deadlineDays = $assignmentDeadlineSettingMarket->integer_value;
        }

        // Find expired translation assignments
        $translationAssignmentsQuery = TranslationAssignment::query();
        $translationAssignmentsQuery->where('is_approved', true);
        $translationAssignmentsQuery->where('is_active', true);
        $translationAssignmentsQuery->where('is_deleted', false); // Only check assignments that aren't already deleted
        $translationAssignmentsQuery->whereNull('completed_at');
        $translationAssignmentsQuery->whereDoesntHave('bids', function ($query) {
            $query->where('is_accepted', true); // Don't expire assignments with accepted bids
        });
        $translationAssignmentsQuery->where('approved_at', '<', now()->subDays($deadlineDays));

        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')->get();

        foreach ($translationAssignments as $translationAssignment) {
            $translationAssignment->is_active = false;
            $translationAssignment->is_deleted = true;
            $translationAssignment->save();

            Mail::to($translationAssignment->email)->send(new AssignmentExpiredCustomerEmail($translationAssignment));
        }
    }
}
