<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Models\TranslationAssignment;
use App\Models\User;

use App\Mail\NewAssignmentEmail;

use Illuminate\Support\Facades\Mail;

class SendNewAssignmentEmails implements ShouldQueue
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;



    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct(TranslationAssignment $assignment)
    {
        $this->assignment = $assignment;
    }



    /*
  |--------------------------------------------------------------------------
  | Job Handler
  |--------------------------------------------------------------------------
  */

    public function handle(): void
    {
        // Check if the assignment has an assigned translator
        if ($this->assignment->assigned_translator_id) {
            // If there's an assigned translator, only send the email to them
            $translator = User::find($this->assignment->assigned_translator_id);

            if ($translator && $translator->is_active && $translator->is_approved) {
                Mail::to($translator)->queue(new NewAssignmentEmail($translator, $this->assignment));

                // Log::channel('slack')->info('SendNewAssignmentEmails - SENT TO ASSIGNED TRANSLATOR', [
                //   'translator' => $translator->id,
                //   'assignment' => $this->assignment->id,
                // ]);
            }
        } else {
            // If there's no assigned translator, send to all matching translators as before
            $translators = User::activeTranslators()->get();

            foreach ($translators as $translator) {
                if ($translator->matchesAssignment($this->assignment)) {
                    Mail::to($translator)->queue(new NewAssignmentEmail($translator, $this->assignment));

                    // Log::channel('slack')->info('SendNewAssignmentEmails - ASSIGNMENT MATCH FOUND', [
                    //   'translator' => $translator->id,
                    //   'assignment' => $this->assignment->id,
                    // ]);
                }
            }
        }
    }
}
