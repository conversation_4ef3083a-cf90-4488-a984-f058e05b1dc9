<?php

namespace App\Jobs;

use App\Mail\AssignmentExpiredCustomerEmail;
use App\Models\TranslationAssignment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;

use Softbox\SBX\HelloSMS\Jobs\SendSMS;
use Mail;

class CheckPassedOfferAssignments implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $translationAssignmentsQuery = TranslationAssignment::GetOfferClosedAssignments('se');
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('approved_at', 'DESC')->get();

        foreach ($translationAssignments as $translationAssignment) {
            try {
                MarkAssignmentAsClosedForOffers::dispatch($translationAssignment);
            } catch (\Exception $e) {
                Log::error('Error while checking passed offer assignments: ' . $e->getMessage());
            }
        }
    }
}
