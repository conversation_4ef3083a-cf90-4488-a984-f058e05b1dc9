<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

use Softbox\SBX\Admin\Events\DeleteLanguage;

class TranslationLanguage extends Model {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use HasFactory;



  /*
  |--------------------------------------------------------------------------
  | Event Dispatchers
  |--------------------------------------------------------------------------
  */

  protected $dispatchesEvents = [
  ];



  /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

  protected $fillable = [
    'created_at',
    'updated_at'
  ];



  /*
  |--------------------------------------------------------------------------
  | Field Casts
  |--------------------------------------------------------------------------
  */

  protected $casts = [
  ];



  /*
  |---------------------------------------------------------------------
  | Query Scopes
  |---------------------------------------------------------------------
  */

  public function scopeActive(Builder $query, string $marketCode): void {
    $query->whereHas('markets', function ($query) use ($marketCode) {
      $query->where('market_code', $marketCode)->where('is_active', true);
    })
    ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
    ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
    ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active');
  }



  /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

  public function localizations() {
    return $this->hasMany(TranslationLanguageLocalization::class);
  }


  public function markets() {
    return $this->hasMany(TranslationLanguageMarket::class);
  }
}
