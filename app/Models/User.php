<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Builder;
use App\Notifications\ResetPasswordNotification;

use Softbox\SBX\Admin\Models\User as SBXUser;
use Softbox\SBX\Webshop\Models\Order;

use Auth;

class User extends SBXUser
{

    /*
  |--------------------------------------------------------------------------
  | Query Scopes
  |--------------------------------------------------------------------------
  */

    public function scopeActiveTranslators(Builder $query): void
    {
        $query->where('is_approved', true)->whereHas('roles', function (Builder $query) {
            $query->where('role_id', 2);
        });
    }


    public function scopeForAssignment($query, TranslationAssignment $assignment, array $excludedEmails = [])
    {
        // Get the IDs of the from and to languages for the assignment
        $fromLanguageId = $assignment->from_translation_language_id;
        $toLanguageId = $assignment->to_translation_language_id;

        // Filter users based on the assignment's languages, service, and authorization
        // Filter users based on the assignment's languages, service, and authorization
        $query->whereHas('fromTranslationLanguages', function ($query) use ($fromLanguageId) {
            $query->where('translation_languages.id', $fromLanguageId);
        })->whereHas('toTranslationLanguages', function ($query) use ($toLanguageId) {
            $query->where('translation_languages.id', $toLanguageId);
        })->whereHas('translationCategories', function ($query) use ($assignment) {
            $query->where('translation_categories.id', $assignment->translation_category_id);
        });

        $query->where('is_approved', true)->whereHas('roles', function (Builder $query) {
            $query->where('role_id', 2);
        });

        // Check authorization
        if ($assignment->is_authorization_required) {
            $query->where('is_authorized', true);
        }

        // Exclude specific users based on email addresses
        if (!empty($excludedEmails)) {
            $query->whereNotIn('email', $excludedEmails);
        }

        return $query;
    }



    /*
  |--------------------------------------------------------------------------
  | Field Casts
  |--------------------------------------------------------------------------
  */

    protected $casts = [
        'is_approved' => 'boolean',
        'is_active' => 'boolean',
        'discount_used' => 'boolean',
        'allow_text_messages' => 'boolean'
    ];




    /*
  |--------------------------------------------------------------------------
  | Custom Reset Password Notification
  |--------------------------------------------------------------------------
  */

    public function sendPasswordResetNotification($token): void
    {
        // $url = 'https://example.com/reset-password?token='.$token;
        $this->notify(new ResetPasswordNotification($this, $token));
    }



    /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

    public function fromTranslationLanguages()
    {
        return $this->belongsToMany(TranslationLanguage::class, 'user_from_translation_languages', 'user_id', 'translation_language_id')->withTimestamps();
    }


    public function fromTranslationLanguageNames()
    {
        return $this->belongsToMany(TranslationLanguage::class, 'user_from_translation_languages', 'user_id', 'translation_language_id')->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')->select('translation_language_localizations.name');
    }


    public function toTranslationLanguages()
    {
        return $this->belongsToMany(TranslationLanguage::class, 'user_to_translation_languages', 'user_id', 'translation_language_id')->withTimestamps();
    }


    public function toTranslationLanguageNames()
    {
        return $this->belongsToMany(TranslationLanguage::class, 'user_to_translation_languages', 'user_id', 'translation_language_id')->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')->select('translation_language_localizations.name');
    }


    public function translationCategories()
    {
        return $this->belongsToMany(TranslationCategory::class, 'user_translation_categories', 'user_id', 'translation_category_id')->withTimestamps();
    }


    public function translationCategoryNames()
    {
        return $this->belongsToMany(TranslationCategory::class, 'user_translation_categories')->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')->select('translation_category_localizations.name');
    }


    public function payments()
    {
        return $this->hasMany(Order::class);
    }



    /*
  |--------------------------------------------------------------------------
  | Computed Properties
  |--------------------------------------------------------------------------
  */

    public function getIsAdminAttribute()
    {
        $roles = Auth::user()->roles;

        $isAdmin = false;
        foreach ($this->roles as $role) {
            if ($role->is_admin) {
                $isAdmin = true;
                break;
            }
        }

        return $isAdmin;
    }


    public function getFromLanguagesListAttribute()
    {
        $languages = [];

        foreach ($this->fromTranslationLanguages as $language) {
            $languageLocalization  = $language->localizations()->where('language_code', 'sv')->first();

            array_push($languages, $languageLocalization->name);
        }

        sort($languages);

        return $languages;
    }


    public function getToLanguagesListAttribute()
    {
        $languages = [];

        foreach ($this->toTranslationLanguages as $language) {
            $languageLocalization  = $language->localizations()->where('language_code', 'sv')->first();

            array_push($languages, $languageLocalization->name);
        }

        sort($languages);

        return $languages;
    }


    public function getTranslationCategoriesListAttribute()
    {
        $categories = [];

        foreach ($this->translationCategories as $category) {
            $categoryLocalization  = $category->localizations()->where('language_code', 'sv')->first();

            array_push($categories, $categoryLocalization->name);
        }

        sort($categories);

        return $categories;
    }



    /*
  |--------------------------------------------------------------------------
  | Custom Methods
  |--------------------------------------------------------------------------
  */

    public function matchesAssignment(TranslationAssignment $assignment)
    {
        // Check if assignment is specifically assigned to another translator
        if ($assignment->assigned_translator_id !== null && $assignment->assigned_translator_id != $this->id) {
            return false;
        }

        // Check active
        $isActive = $assignment->is_active;

        // Check service
        $serviceMatches = $this->translationCategories->pluck('id')->contains($assignment->translation_category_id);

        // Check from language
        $fromLanguageMatches = $this->fromTranslationLanguages->pluck('id')->contains($assignment->from_translation_language_id);

        // Check to language
        $toLanguageMatches = $this->toTranslationLanguages->pluck('id')->contains($assignment->to_translation_language_id);

        // Check Authorized
        $authorizedMatches = false;
        if ($assignment->is_authorization_required) {
            if ($this->is_authorized) {
                $authorizedMatches = true;
            }
        } else {
            $authorizedMatches = true;
        }

        return ($isActive && $serviceMatches && $fromLanguageMatches && $toLanguageMatches && $authorizedMatches);
    }

    public function hasBidOnAssignment(TranslationAssignment $assignment)
    {
        return ($assignment->bids()->where('user_id', $this->id)->first() != NULL);
    }

    public function isAdmin()
    {
        return $this->getIsAdminAttribute();
    }
}
