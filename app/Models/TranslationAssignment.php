<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

use Softbox\SBX\Admin\Events\DeleteLanguage;

use App\Enums\AssignmentType;

use Softbox\SBX\Admin\Models\Setting;
use App\Models\User;

class TranslationAssignment extends Model
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use HasFactory;



    /*
  |--------------------------------------------------------------------------
  | Event Dispatchers
  |--------------------------------------------------------------------------
  */

    protected $dispatchesEvents = [];



    /*
  |--------------------------------------------------------------------------
  | Query Scopes
  |--------------------------------------------------------------------------
  */

    public function scopeUnmatchedAssignmentsForUser(Builder $query, $marketCode, User $user): void
    {
        // Only approved assignments
        $query->where('is_approved', true);

        // Only active assignments
        $query->where('is_active', true);

        // Check that assignment is not closed for offers
        $query->whereNull('is_closed_for_offers');

        // Check if assignment is either unassigned or assigned to this user
        $query->where(function ($query) use ($user) {
            $query->whereNull('assigned_translator_id')
                ->orWhere('assigned_translator_id', $user->id);
        });

        // Check that assignment is not expired
        $bidsDeadlineSetting = Setting::find(4);
        $bidsDeadlineSettingMarket = $bidsDeadlineSetting->markets()->where('market_code', $marketCode)->first();

        if ($bidsDeadlineSettingMarket) {
            $query->whereRaw('DATE_ADD(approved_at, INTERVAL ? DAY) >= NOW()', [$bidsDeadlineSettingMarket->integer_value]);
        } else {
            $query->whereRaw('DATE_ADD(approved_at, INTERVAL 1 DAY) >= NOW()');
        }

        // Check that bid count is not exceeded
        $maxBidsSetting = Setting::find(1);
        $maxBidsSettingMarket = $maxBidsSetting->markets()->where('market_code', $marketCode)->first();
        $maxBids = 3;
        if ($maxBidsSettingMarket) {
            $maxBids = $maxBidsSettingMarket->integer_value;
        }

        $query->has('bids', '<', $maxBids);

        // Check that current user has not made a bid
        $query->whereDoesntHave('bids', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        });

        $query->whereDoesntHave('bids', function ($query) {
            $query->where('is_accepted', true);
        });

        // Check services
        $query->whereIn('translation_category_id',  $user->translationCategories->pluck('id'));

        // Check from language
        $query->whereIn('from_translation_language_id',  $user->fromTranslationLanguages->pluck('id'));

        // Check to language
        $query->whereIn('to_translation_language_id',  $user->toTranslationLanguages->pluck('id'));

        // Check Authorization
        if (!$user->is_authorized) {
            $query->where('is_authorization_required', false);
        }
    }

    public function scopeGetWonAssignmentsForUser(Builder $query, $marketCode, User $user): void
    {
        // Only approved assignments
        $query->where('is_approved', true);

        // Only active assignments
        $query->where('is_active', true);

        // Check that current user has won the bid
        $query->whereHas('bids', function ($query) use ($user) {
            $query->where('user_id', $user->id)->where('is_accepted', true);
        });
    }

    public function scopeGetOfferClosedAssignments(Builder $query, $marketCode): void
    {
        $bidsDeadlineSetting = Setting::find(4);
        $bidsDeadlineSettingMarket = $bidsDeadlineSetting->markets()->where('market_code', $marketCode)->first();

        if ($bidsDeadlineSettingMarket) {
            $query->whereRaw('DATE_ADD(approved_at, INTERVAL ? DAY) <= NOW()', [$bidsDeadlineSettingMarket->integer_value]);
        } else {
            $query->whereRaw('DATE_ADD(approved_at, INTERVAL 1 DAY) <= NOW()');
        }

        $query->whereNull('is_closed_for_offers');
    }

    /**
     * Scope a query to only include assignments that have reached the maximum number of bids
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $marketCode
     * @return void
     */
    public function scopeCheckMaxBidsFullfilled(Builder $query, $marketCode): void
    {
        $maxBidsSetting = Setting::find(1);
        $maxBidsSettingMarket = $maxBidsSetting->markets()->where('market_code', $marketCode)->first();
        $maxBids = 3;
        if ($maxBidsSettingMarket) {
            $maxBids = $maxBidsSettingMarket->integer_value;
        }

        $query->has('bids', '>=', $maxBids);
    }

    /**
     * Check if this specific assignment has reached the maximum number of bids
     *
     * @param  string  $marketCode
     * @return bool
     */
    public function hasReachedMaxBids($marketCode): bool
    {
        $maxBidsSetting = Setting::find(1);
        $maxBidsSettingMarket = $maxBidsSetting->markets()->where('market_code', $marketCode)->first();
        $maxBids = 3;
        if ($maxBidsSettingMarket) {
            $maxBids = $maxBidsSettingMarket->integer_value;
        }

        return $this->bids()->count() >= $maxBids;
    }


    /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

    protected $fillable = [
        'created_at',
        'updated_at',
        'assigned_translator_id'
    ];



    /*
  |--------------------------------------------------------------------------
  | Field Casts
  |--------------------------------------------------------------------------
  */

    protected $casts = [
        'assignment_type' => AssignmentType::class,
        'is_authorization_required' => 'boolean',
        'is_email_contact_allowed' => 'boolean',
        'is_phone_contact_allowed' => 'boolean',
        'is_approved' => 'boolean',
        'is_active' => 'boolean',
        'approved_at' => 'datetime',
        'completed_at' => 'datetime',
    ];



    /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

    public function bids()
    {
        return $this->hasMany(TranslationAssignmentBid::class);
    }

    public function premarket_bid()
    {
        return $this->hasOne(TranslationAssignmentPremarketBid::class);
    }

    public function views()
    {
        return $this->hasMany(TranslationAssignmentView::class);
    }


    public function files()
    {
        return $this->hasMany(TranslationAssignmentFile::class);
    }


    public function category()
    {
        return $this->hasOne(TranslationCategory::class, 'id', 'translation_category_id');
    }


    public function fromLanguage()
    {
        return $this->hasOne(TranslationLanguage::class, 'id', 'from_translation_language_id');
    }


    public function toLanguage()
    {
        return $this->hasOne(TranslationLanguage::class, 'id', 'to_translation_language_id');
    }

    public function assignedTranslator()
    {
        return $this->belongsTo(User::class, 'assigned_translator_id');
    }

    /**
     * Get the activity logs for this assignment.
     */
    public function activityLogs()
    {
        return $this->morphMany(ActivityLog::class, 'loggable');
    }



    /*
  |---------------------------------------------------------------------
  | Custom Attributes
  |---------------------------------------------------------------------
  */

    public function getAuthorizedTextAttribute()
    {
        if ($this->is_authorization_required) {
            return __('oversattare.new_assignment_email_yes_label');
        }

        return __('oversattare.new_assignment_email_no_label');
    }


    public function getAssignmentLinkAttribute()
    {
        return url('/se/sv/matchande-uppdrag/' . $this->assignment_id);
    }


    public function getAssignmentCustomerLinkAttribute()

    {
        return url('/se/sv/customer/' . $this->assignment_id . '/' . $this->email);
    }

    public function getAssignmentPreMarketBidCustomerLinkAttribute()

    {
        return url('/se/sv/customer/' . $this->assignment_id . '/' . $this->email . '/premarket/' . $this->premarket_bid()->first()->id);
    }

    public function getFilteredBids($user)
    {
        if ($user->isAdmin()) {
            // Admin can see all bids
            return $this->bids;
        } else {
            // Non-admins see only their own bids
            return $this->bids()->where('user_id', $user->id)->get();
        }
    }

    public function getPreMarketBid($user)
    {
        if ($user->isAdmin()) {
            return $this->premarket_bid()->first();
        } else {
            return $this->premarket_bid()->exists();
        }
    }

    public function getPreMarketBidWithTax()
    {
        $premarketBid = $this->premarket_bid()->first();
        if ($premarketBid) {
            return $premarketBid->price_end_customer +
                ($premarketBid->price_end_customer * 25 / 100);
        }

        return null;
    }

    public function getBestBid()
    {
        return $this->bids()->orderBy('price_end_customer', 'asc')->first();
    }

    /**
     * Get the bid with the fastest delivery time among all bids except the best bid (by price),
     * but only if it has a faster delivery time than the best bid
     *
     * @return TranslationAssignmentBid|null
     */
    public function getFastestDeliveryBid()
    {
        $bestBid = $this->getBestBid();

        if (!$bestBid) {
            return null;
        }

        // Get the best bid's delivery time
        $bestBidDeliveryTime = strtotime($bestBid->estimated_delivery);

        // Get all bids except the best bid
        $otherBids = $this->bids()
            ->where('id', '!=', $bestBid->id)
            ->get();

        if ($otherBids->isEmpty()) {
            return null;
        }

        // Filter bids to only those with faster delivery than the best bid
        $fasterBids = $otherBids->filter(function ($bid) use ($bestBidDeliveryTime) {
            return strtotime($bid->estimated_delivery) < $bestBidDeliveryTime;
        });

        if ($fasterBids->isEmpty()) {
            return null;
        }

        // Find the bid with the fastest delivery time among the faster bids
        return $fasterBids->sortBy('estimated_delivery')->first();
    }
}
