<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

use Softbox\SBX\Admin\Events\DeleteLanguage;

class TranslationCategory extends Model {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use HasFactory;



  /*
  |--------------------------------------------------------------------------
  | Event Dispatchers
  |--------------------------------------------------------------------------
  */

  protected $dispatchesEvents = [
  ];



  /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

  protected $fillable = [
    'created_at',
    'updated_at'
  ];



  /*
  |--------------------------------------------------------------------------
  | Field Casts
  |--------------------------------------------------------------------------
  */

  protected $casts = [
  ];



  /*
  |---------------------------------------------------------------------
  | Query Scopes
  |---------------------------------------------------------------------
  */

  public function scopeActive(Builder $query, string $marketCode): void {
    $query->whereHas('markets', function ($query) use ($marketCode) {
      $query->where('market_code', $marketCode)->where('is_active', true);
    })
    ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
    ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
    ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active');
  }



  /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

  public function localizations() {
    return $this->hasMany(TranslationCategoryLocalization::class);
  }


  public function markets() {
    return $this->hasMany(TranslationCategoryMarket::class);
  }
}
