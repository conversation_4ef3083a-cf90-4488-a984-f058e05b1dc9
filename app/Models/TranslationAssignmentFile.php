<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class TranslationAssignmentFile extends Model
{

    /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

    protected $fillable = [];



    /*
  |--------------------------------------------------------------------------
  | Casts
  |--------------------------------------------------------------------------
  */

    protected $casts = [];

    /*
  |--------------------------------------------------------------------------
  | Query Scopes
  |--------------------------------------------------------------------------
  */
    public function scopeGetFilesForTranslation(Builder $query): void
    {
        // Only approved assignments
        $query->where('file_type', 0);
    }


    public function scopeGetTranslatedFiles(Builder $query): void
    {
        // Only approved assignments
        $query->where('file_type', 1);
    }
}
