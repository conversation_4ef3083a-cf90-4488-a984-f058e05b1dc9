<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TranslationCategoryMarket extends Model {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use HasFactory;



  /*
  |--------------------------------------------------------------------------
  | Event Dispatchers
  |--------------------------------------------------------------------------
  */

  protected $dispatchesEvents = [
  ];



  /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

  protected $fillable = [
    'created_at',
    'updated_at'
  ];



  /*
  |--------------------------------------------------------------------------
  | Field Casts
  |--------------------------------------------------------------------------
  */

  protected $casts = [
    'is_active' => 'boolean'
  ];



  /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

  public function translationCategory() {
    return $this->belongsTo(TranslationCategory::class);
  }
}
