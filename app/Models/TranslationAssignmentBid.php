<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Softbox\SBX\Admin\Events\DeleteLanguage;
use Softbox\SBX\Webshop\Models\Order;

class TranslationAssignmentBid extends Model
{

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use HasFactory;



  /*
  |--------------------------------------------------------------------------
  | Event Dispatchers
  |--------------------------------------------------------------------------
  */

  protected $dispatchesEvents = [];



  /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

  protected $fillable = [
    'created_at',
    'updated_at',
    'price',
    'price_end_customer',
    'estimated_delivery'
  ];



  /*
  |--------------------------------------------------------------------------
  | Field Casts
  |--------------------------------------------------------------------------
  */

  protected $casts = [
    'accepted_at' => 'datetime',
  ];



  /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

  public function translator()
  {
    return $this->hasOne(User::class, 'id', 'user_id');
  }

  public function order()
  {
    return $this->hasOne(Order::class, 'order_no', 'order_id');
  }

  public function translationAssignment()
  {
    return $this->belongsTo(TranslationAssignment::class, 'translation_assignment_id', 'id');
  }
}
