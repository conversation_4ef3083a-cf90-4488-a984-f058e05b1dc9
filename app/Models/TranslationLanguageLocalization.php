<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TranslationLanguageLocalization extends Model {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use HasFactory;



  /*
  |--------------------------------------------------------------------------
  | Event Dispatchers
  |--------------------------------------------------------------------------
  */

  protected $dispatchesEvents = [
  ];



  /*
  |--------------------------------------------------------------------------
  | Fillable Fields
  |--------------------------------------------------------------------------
  */

  protected $fillable = [
    'created_at',
    'updated_at'
  ];



  /*
  |---------------------------------------------------------------------
  | Relationship Definitions
  |---------------------------------------------------------------------
  */

   public function translationLanguage() {
    return $this->belongsTo(TranslationLanguage::class);
  }
}
