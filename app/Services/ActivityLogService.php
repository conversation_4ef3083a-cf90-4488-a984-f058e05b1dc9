<?php

namespace App\Services;

use App\Models\ActivityLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ActivityLogService
{
    /**
     * Log an activity
     *
     * @param string $action The action performed (e.g., 'email_sent', 'assignment_closed')
     * @param string $description A human-readable description of the activity
     * @param Model $model The model the activity is related to
     * @param array $properties Additional properties to store with the log
     * @param int|null $userId The ID of the user who performed the action (defaults to current authenticated user)
     * @return ActivityLog
     */
    public static function log(string $action, string $description, Model $model, array $properties = [], ?int $userId = null): ActivityLog
    {
        // If no user ID is provided, use the currently authenticated user
        if ($userId === null && Auth::check()) {
            $userId = Auth::id();
        }
        
        return ActivityLog::create([
            'action' => $action,
            'description' => $description,
            'loggable_id' => $model->id,
            'loggable_type' => get_class($model),
            'user_id' => $userId,
            'properties' => $properties,
        ]);
    }
    
    /**
     * Get all activity logs for a specific model
     *
     * @param Model $model
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getLogsForModel(Model $model)
    {
        return ActivityLog::where('loggable_type', get_class($model))
            ->where('loggable_id', $model->id)
            ->orderBy('created_at', 'desc')
            ->get();
    }
    
    /**
     * Get all activity logs for a specific action
     *
     * @param string $action
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getLogsByAction(string $action)
    {
        return ActivityLog::where('action', $action)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
