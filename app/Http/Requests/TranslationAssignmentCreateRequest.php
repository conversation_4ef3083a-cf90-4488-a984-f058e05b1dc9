<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

use App\Enums\AssignmentType;

use App\Models\User;

use Auth;
use Log;

class TranslationAssignmentCreateRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return true;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'translation_category_id' => 'required|numeric',
      'from_translation_language_id' => 'required|numeric',
      'to_translation_language_id' => 'required|numeric',
      'is_authorization_required' => 'required|boolean',
      'number_of_words' => ['nullable', 'numeric', Rule::requiredIf(count($this->assignment_files) == 0)],
      'assignment_type' => ['required', Rule::enum(AssignmentType::class)],
      'first_name' => 'required|string|max:255',
      'email' => 'required|email|max:255',
      'phone_no' => 'required|max:15',
      'notes' => 'nullable|string|max:3000',
      'is_email_contact_allowed' => 'required|boolean|accepted_if:is_phone_contact_allowed,false',
      'is_phone_contact_allowed' => 'required|boolean|accepted_if:is_email_contact_allowed,false',
      'assignment_files' => ['array', 'required_without:number_of_words'],
      'assignment_files.*.file' => [File::types(['png', 'jpg', 'pdf', 'doc', 'docx'])->min('1kb')->max('50mb')],
    ];
  }



  /*
  |--------------------------------------------------------------------------
  | Messages
  |--------------------------------------------------------------------------
  */

  public function messages() {
    return [
      'company_no.required_if' => 'Organisationsnummer måste anges om Företag är valt.',
      'company.required_if' => 'Företagsnamn måste anges om Företag är valt.',
      'company_no.regex' => 'Organisationsnummer måste anges i följande format XXXXXX-XXXX.',
      'is_email_contact_allowed.accepted_if' => 'Minst en kontaktmetod måste väljas.',
      'is_phone_contact_allowed.accepted_if' => 'Minst en kontaktmetod måste väljas.',
      'assignment_files.*.file' => 'Dokumentet måste vara i ett giltigt format och får inte vara större än 50Mb.',
      'number_of_words.required_if' => 'Antal ord måste anges om inga dokument laddas upp.',
      'number_of_words.required' => 'Antal ord måste anges om inga dokument laddas upp.',
      'assignment_files.required_without' => 'Dokument måste laddas upp om antalet ord inte anges.',
      'assignment_files.required' => 'Dokument måste laddas upp om antalet ord inte anges.'
    ];
  }
}
