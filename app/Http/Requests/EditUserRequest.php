<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

use App\Models\User;

use Auth;
use Log;

class EditUserRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return Auth::user()->isAdmin;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'role_id' => 'required',
      'first_name' => 'required|max:255',
      'last_name' => 'required|max:255',
      'email' => 'required|string|email|max:255|unique:users,email,' . $this->user->id,
      'phone_no' => 'max:255',
      'sales_person_id' => 'required'
    ];
  }



  /*
  |--------------------------------------------------------------------------
  | Messages
  |--------------------------------------------------------------------------
  */

  public function messages() {
    return [
      'email.unique' => 'E-postadressen används redan.',
      'required_if' => 'Säljare måste väljas om rollen är Säljare.'
    ];
  }
}
