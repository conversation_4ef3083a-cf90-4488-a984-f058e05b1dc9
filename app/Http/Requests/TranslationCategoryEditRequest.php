<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class TranslationCategoryEditRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return Auth::user()->isAdmin;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'name' => 'required|max:255',
      'is_active' => 'required|boolean'
    ];
  }
}
