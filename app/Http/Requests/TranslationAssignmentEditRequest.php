<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class TranslationAssignmentEditRequest extends FormRequest
{

    /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

    public function authorize(): bool
    {
        return Auth::user()->isAdmin;
    }



    /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

    public function rules(): array
    {

        return [
            'translation_category_id' => 'required|numeric',
            'from_translation_language_id' => 'required|numeric',
            'to_translation_language_id' => 'required|numeric',
            'is_authorization_required' => 'required|boolean',
            'number_of_words' => 'nullable|numeric',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'company_no' => 'nullable|string|max:255',
            'email' => 'required|email|max:255',
            'notes' => 'nullable|string|max:3000'
        ];
    }
}
