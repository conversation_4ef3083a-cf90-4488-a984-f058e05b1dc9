<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class EditProfileContactInformationRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return true;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'company' => 'nullable|max:255',
      'company_no' => 'nullable|max:255',
      'first_name' => 'required|max:255',
      'last_name' => 'required|max:255',
      'address_1' => 'required|max:255',
      'address_2' => 'nullable|max:255',
      'postal_code' => 'required|max:255',
      'city' => 'required|max:255',
      'phone_no' => 'required|max:255',
      'password' => ['exclude_if:password,null', 'required', 'confirmed', Password::min(8)]
    ];
  }



  /*
  |--------------------------------------------------------------------------
  | Messages
  |--------------------------------------------------------------------------
  */

  public function messages() {
    return [
      'email.unique' => 'E-postadressen används redan.',
      'required_if' => 'Säljare måste väljas om rollen är Säljare.'
    ];
  }
}
