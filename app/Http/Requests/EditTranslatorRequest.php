<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class EditTranslatorRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return true;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'company' => 'nullable|max:255',
      'company_no' => 'nullable|max:255',
      'first_name' => 'required|max:255',
      'last_name' => 'required|max:255',
      'address_1' => 'nullable|max:255',
      'address_2' => 'nullable|max:255',
      'postal_code' => 'nullable|max:255',
      'city' => 'nullable|max:255',
      'email' => 'required|string|email|max:255|unique:users,email,' . $this->translator->id,
      'phone_no' => 'required|max:255',
      'authorization_id' => 'nullable|numeric'
    ];
  }



  /*
  |--------------------------------------------------------------------------
  | Messages
  |--------------------------------------------------------------------------
  */

  public function messages() {
    return [
      'email.unique' => 'E-postadressen används redan.',
      'required_if' => 'Säljare måste väljas om rollen är Säljare.'
    ];
  }
}
