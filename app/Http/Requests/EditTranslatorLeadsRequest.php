<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class EditTranslatorLeadsRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return true;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'lead_count' => 'required|numeric'
    ];
  }
}
