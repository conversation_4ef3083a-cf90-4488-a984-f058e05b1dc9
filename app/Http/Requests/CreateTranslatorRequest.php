<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class CreateTranslatorRequest extends FormRequest {

  /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

  public function authorize(): bool {
    return true;
  }



  /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

  public function rules(): array {
    return [
      'first_name' => 'required|max:255',
      'last_name' => 'required|max:255',
      'company' => 'nullable|max:255',
      'company_no' => 'nullable|max:255',
      'address_1' => 'nullable|max:255',
      'address_2' => 'nullable|max:255',
      'postal_code' => 'nullable|max:6',
      'city' => 'nullable|max:255',
      'email' => 'required|string|email|max:255|unique:users',
      'phone_no' => 'required|max:15'
    ];
  }



  /*
  |--------------------------------------------------------------------------
  | Messages
  |--------------------------------------------------------------------------
  */

  public function messages() {
    return [
      'email.unique' => 'E-postadressen används redan.',
      'authorization_id.required_if' => 'Kammarkollegiets id-nummer måste anges för auktoriserade översättare.',
    ];
  }
}
