<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use App\Models\User;

use Auth;
use Log;

class TranslatorApplicationCreateRequest extends FormRequest
{

    /*
  |--------------------------------------------------------------------------
  | Authorize
  |--------------------------------------------------------------------------
  */

    public function authorize(): bool
    {
        return true;
    }



    /*
  |--------------------------------------------------------------------------
  | Rules
  |--------------------------------------------------------------------------
  */

    //   public function rules(): array {
    //     return [
    //       'first_name' => 'required|max:255',
    //       'last_name' => 'required|max:255',
    //       'company' => 'nullable|max:255',
    //       'email' => 'required|string|email|max:255|unique:users',
    //       'phone_no' => 'required|max:20',
    //       'from_translation_languages' => 'required',
    //       'to_translation_languages' => 'required',
    //       'translation_categories' => 'required',
    //       'is_authorized' => 'required|boolean',
    //       'authorization_id' => 'max:255|required_if:is_authorized,true',
    //     ];
    //   }



    /*
  |--------------------------------------------------------------------------
  | Messages
  |--------------------------------------------------------------------------
  */

    public function messages()
    {
        return [
            'email.unique' => 'E-postadressen används redan.',
            'authorization_id.required_if' => 'Kammarkollegiets id-nummer måste anges för auktoriserade översättare.',
            'from_translation_languages.required' => 'Minst ett språk måste anges.',
            'to_translation_languages.required' => 'Minst ett språk måste anges.',
            'translation_categories.required' => 'Minst ett område måste anges.',
            'company_no.regex' => 'Organisationsnummer / personnummer måste anges i följande format (XX)XXXXXX-XXXX.',
            'company_no.required' => 'Organisationsnummer / personnummer måste anges.',
        ];
    }
}
