<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;

use App\Models\TranslationAssignment;
use App\Models\TranslationAssignmentBid;
use App\Models\TranslationAssignmentFile;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Softbox\SBX\Admin\Models\Setting;
use Softbox\SBX\HelloSMS\Jobs\SendSMS;
use App\Jobs\CheckMaxBidsAssignments;
use App\Jobs\MarkAssignmentAsClosedForOffers;

use App\Http\Resources\TranslationAssignmentTransformer;
use App\Http\Resources\TranslationAssignmentDetailTransformer;
use App\Http\Resources\TranslationAssignmentMarketTransformer;
use App\Mail\OfferReceived;
use Softbox\SBX\Admin\Shared\Locale;
use App\Services\ActivityLogService;


use App\Mail\PreMarketPriceCustomerEmail;
use App\Mail\AssignmentPriceChangedCustomerEmail;
use App\Mail\OfferReceivedEmail;

use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Softbox\SBX\Webshop\Models\Product;

class TranslationAssignmentMarketController extends Controller
{

    /*
  |--------------------------------------------------------------------------
  | Get All Translation Assignments
  |--------------------------------------------------------------------------
  */

    public function index(Request $request, $marketCode, $languageCode)
    {
        $unmatchedAssignments = TranslationAssignment::unmatchedAssignmentsForUser($marketCode, Auth::user())->orderBy('created_at', 'DESC')->get();

        return Inertia::render('TranslationAssignmentMarket/TranslationAssignmentMarket', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentMarketTransformer::collection($unmatchedAssignments)
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Make Bid
  |--------------------------------------------------------------------------
  */

    public function makeBid(Request $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {

        if ($translationAssignment->bids()->where('is_accepted', 1)->exists()) {
            //TODO: Return error message
            return redirect()->back()->withErrors([
                'error' => 'Already accepted bid'
            ]);
        };

        $price = $request->input('price');
        $priceEndCustomer = $this->calculateEndCustomerPrice($marketCode, $price, $translationAssignment->assignment_type);

        // Insert the bid
        $bidId = DB::table('translation_assignment_bids')->insertGetId([
            'translation_assignment_id' => $translationAssignment->id,
            'price' => $price,
            'price_end_customer' => $priceEndCustomer,
            'estimated_delivery' => $request->input('delivery_date'),
            'user_id' => Auth::id(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);

        // Check if the bidder is the assigned translator
        $currentUserId = Auth::id();
        if ($translationAssignment->assigned_translator_id == $currentUserId) {
            // If the assigned translator has placed a bid, close the assignment for offers
            MarkAssignmentAsClosedForOffers::dispatch($translationAssignment);
        } else {
            // Otherwise, check if max bids have been reached
            CheckMaxBidsAssignments::dispatch($translationAssignment);
        }

        return redirect()->route('my_assignments.show', ['marketCode' => $marketCode, 'languageCode' => $languageCode, 'translationAssignmentID' => $translationAssignment->assignment_id]);
    }

    public function addPremarketBid(Request $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {

        if ($translationAssignment->is_approved) {
            return redirect()->back()->withErrors([
                'error' => 'Assignment already approved'
            ]);
        };

        if ($translationAssignment->bids()->where('is_accepted', 1)->exists()) {
            //TODO: Return error message
            return redirect()->back()->withErrors([
                'error' => 'Already accepted bid'
            ]);
        };

        if ($translationAssignment->premarket_bid()->where('is_accepted', 1)->exists()) {
            return redirect()->back()->withErrors([
                'error' => 'Premarket bid already added'
            ]);
        }

        $price = $request->input('price');

        $priceEndCustomer = $this->calculateEndCustomerPrice($marketCode, $price, $translationAssignment->assignment_type);
        DB::table('translation_assignment_premarket_bids')->insert([
            ['translation_assignment_id' => $translationAssignment->id, 'price' => $price, 'price_end_customer' => $priceEndCustomer,  'user_id' => Auth::id(), 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
        ]);

        // Notify the customer per email
        Mail::to($translationAssignment)->send(new PreMarketPriceCustomerEmail($translationAssignment));

        return redirect()->route('translation_assignment_approvals', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }


    /*
  |--------------------------------------------------------------------------
  | Show Details
  |--------------------------------------------------------------------------
  */

    public function showDetails(Request $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {
        $hasViewed = DB::table('translation_assignment_views')->where('user_id', Auth::id())->where('translation_assignment_id', $translationAssignment->id)->first();

        if (!$hasViewed) {
            DB::table('translation_assignment_views')->insert([
                ['translation_assignment_id' => $translationAssignment->id, 'user_id' => Auth::id(), 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()]
            ]);
        }

        return Redirect::back();
    }



    /*
  |--------------------------------------------------------------------------
  | Show Translation Assignment
  |--------------------------------------------------------------------------
  */

    public function show(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        $showAssignment = NULL;

        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();

        // Has bid by current user -> show assignment detail
        if ($assignment && $assignment->is_approved) {
            if (Auth::user()->hasBidOnAssignment($assignment)) {
                return redirect()->route('my_assignments.show', ['marketCode' => $marketCode, 'languageCode' => $languageCode, 'translationAssignmentID' => $assignment->assignment_id]);
            }

            if (Auth::user()->matchesAssignment($assignment)) {
                $showAssignment = TranslationAssignmentDetailTransformer::make($assignment);
            }
        }

        $unmatchedAssignments = TranslationAssignment::unmatchedAssignmentsForUser($marketCode, Auth::user())->orderBy('created_at', 'DESC')->get();

        return Inertia::render('TranslationAssignmentMarket/TranslationAssignmentMarket', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentMarketTransformer::collection($unmatchedAssignments),
            'selectedAssignment' => $showAssignment
        ]);
    }

    /*
  |--------------------------------------------------------------------------
  | Accept Translation Assignment Bid
  |--------------------------------------------------------------------------
  */

    public function acceptBid(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        //Check if assignment already has accepted bid
        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        if ($assignment && $assignment->bids()->where('is_accepted', 1)->exists()) {
            //TODO: Return error message
            return redirect()->back()->withErrors([
                'error' => 'Already accepted bid'
            ]);
        } else {
            return Redirect::back();
        }
    }


    public function processBidPayment(Request $request, $marketCode, $languageCode)
    {

        $bid = TranslationAssignmentBid::find($request->route('translationAssignmentBidID'));
        $assignment = TranslationAssignment::where('assignment_id', $request->route('translationAssignmentID'))->first();
        //This is a fixed product for offers
        $product = Product::find(4);

        $paymentGateway = (new SveaCheckoutController)->createOfferOrder($request, $marketCode, $languageCode, $assignment, $product, $bid);

        return view('svea', [
            'paymentGateway' => $paymentGateway,
        ]);
    }

    /*
  |--------------------------------------------------------------------------
  | Update Bid Price
  |--------------------------------------------------------------------------
  */

    /**
     * Update bid details (price and/or delivery date)
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $marketCode
     * @param  string  $languageCode
     * @param  \App\Models\TranslationAssignmentBid  $bid
     * @return \Illuminate\Http\Response
     */
    public function updateBidPrice(Request $request, $marketCode, $languageCode, TranslationAssignmentBid $bid)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Validate request
        $validated = $request->validate([
            'price' => 'sometimes|required|numeric|min:0',
            'estimated_delivery' => 'sometimes|required|date|after:now',
        ]);

        // Check if assignment is completed
        $assignment = $bid->translationAssignment;
        if ($assignment->completed_at) {
            return response()->json(['error' => 'Cannot edit bid details on a completed assignment'], 400);
        }

        // Check if bid is already accepted and has payment
        if ($bid->accepted_at && $bid->order) {
            return response()->json(['error' => 'Cannot edit bid details on a bid that has been paid for'], 400);
        }

        $changes = [];
        $priceChanged = false;
        $deliveryDateChanged = false;

        // Update price if provided
        if (isset($validated['price'])) {
            $priceChanged = true;
            $oldPrice = $bid->price;
            $price = $validated['price'];
            $priceEndCustomer = $this->calculateEndCustomerPrice($marketCode, $price, $assignment->assignment_type);

            $bid->price = $price;
            $bid->price_end_customer = $priceEndCustomer;

            $changes[] = "Price updated from {$oldPrice} to {$price}";
        }

        // Update delivery date if provided
        if (isset($validated['estimated_delivery'])) {
            $deliveryDateChanged = true;
            $oldDeliveryDate = $bid->estimated_delivery;
            $newDeliveryDate = $validated['estimated_delivery'];

            $bid->estimated_delivery = $newDeliveryDate;

            $changes[] = "Delivery date updated from {$oldDeliveryDate} to {$newDeliveryDate}";
        }

        // Save changes if any
        if ($priceChanged || $deliveryDateChanged) {
            $bid->save();

            // Notify the customer per email
            Mail::to($assignment)->send(new AssignmentPriceChangedCustomerEmail($assignment));

            // Log the activity
            ActivityLogService::log(
                'bid_details_updated',
                'Bid details updated: ' . implode(', ', $changes),
                $assignment,
                [
                    'email' => $assignment->email,
                    'assignment_id' => $assignment->assignment_id,
                    'bid_id' => $bid->id,
                    'price_changed' => $priceChanged,
                    'delivery_date_changed' => $deliveryDateChanged,
                ]
            );

            return response()->json([
                'message' => 'Bid details updated successfully',
                'bid' => $bid
            ]);
        }

        return response()->json([
            'message' => 'No changes were made to the bid',
            'bid' => $bid
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Assign Translator to Assignment
  |--------------------------------------------------------------------------
  */

    public function assignTranslator(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Validate request
        $validated = $request->validate([
            'translator_id' => 'nullable|exists:users,id',
        ]);

        // Check if assignment is completed
        if ($assignment->completed_at) {
            return response()->json(['error' => 'Cannot assign translator to a completed assignment'], 400);
        }

        // Update the assignment
        $assignment->assigned_translator_id = $validated['translator_id'];
        $assignment->save();

        return response()->json([
            'message' => $validated['translator_id'] ? 'Translator assigned successfully' : 'Assignment unassigned successfully',
            'assignment' => $assignment
        ]);
    }

    /**
     * Get translators that match an assignment
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $marketCode
     * @param  string  $languageCode
     * @param  \App\Models\TranslationAssignment  $assignment
     * @return \Illuminate\Http\Response
     */
    public function getMatchingTranslators(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Log the assignment details for debugging
        Log::info('Getting matching translators for assignment', [
            'assignment_id' => $assignment->id,
            'from_language_id' => $assignment->from_translation_language_id,
            'to_language_id' => $assignment->to_translation_language_id,
            'category_id' => $assignment->translation_category_id,
        ]);

        // Get all translators first for debugging
        $allTranslators = User::whereHas('roles', function (Builder $query) {
            $query->where('role_id', 2); // Translator role
        })->count();

        Log::info('Total translators found: ' . $allTranslators);

        // Get matching translators
        $translators = User::forAssignment($assignment)
            ->select('id', 'first_name', 'last_name', 'email')
            ->get();

        Log::info('Matching translators found: ' . $translators->count());

        $formattedTranslators = $translators->map(function ($translator) {
            return [
                'id' => $translator->id,
                'name' => $translator->first_name . ' ' . $translator->last_name,
                'email' => $translator->email,
            ];
        });

        return response()->json($formattedTranslators);
    }

    public function calculateEndCustomerPrice($marketCode, $price, $assignmentType)
    {
        $priceMarketAddon = Setting::find(3)->markets()->where('market_code', $marketCode)->first();
        $priceBreaks = json_decode($priceMarketAddon->json_value, true)['markup_levels'][$assignmentType->value];

        $margin = 0.0;
        $fee = 0.0;

        usort($priceBreaks, fn($a, $b) => $b['min_value'] - $a['min_value']);

        foreach ($priceBreaks as $priceBreak) {
            if ($price >= $priceBreak['min_value']) {
                $fee = $priceBreak['fee'];
                if ($priceBreak['type'] === 'fixed') {
                    $margin = $priceBreak['markup'];
                } elseif ($priceBreak['type'] === 'percentage') {
                    $margin = $price * ($priceBreak['markup'] / 100);
                }
                break;
            }
        }

        return $price + $fee + $margin;
    }

    /**
     * Close an assignment for offers
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $marketCode
     * @param  string  $languageCode
     * @param  \App\Models\TranslationAssignment  $assignment
     * @return \Illuminate\Http\Response
     */
    public function closeForOffers(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if assignment is already closed for offers
        if ($assignment->is_closed_for_offers) {
            return response()->json(['error' => 'Assignment is already closed for offers'], 400);
        }

        // Check if assignment is completed
        if ($assignment->completed_at) {
            return response()->json(['error' => 'Cannot close a completed assignment for offers'], 400);
        }

        try {
            // Dispatch the job to mark the assignment as closed for offers
            MarkAssignmentAsClosedForOffers::dispatch($assignment);

            // Log additional activity for admin action
            ActivityLogService::log(
                'admin_closed_for_offers',
                'Assignment manually closed for offers by admin',
                $assignment,
                [
                    'admin_id' => $user->id,
                    'admin_email' => $user->email,
                ]
            );

            return response()->json([
                'message' => 'Assignment closed for offers successfully',
                'assignment' => $assignment
            ]);
        } catch (\Exception $e) {
            // Log the error
            if (class_exists('\\Illuminate\\Support\\Facades\\Log')) {
                \Illuminate\Support\Facades\Log::error('Failed to close assignment for offers', [
                    'error' => $e->getMessage(),
                    'assignment_id' => $assignment->id,
                ]);
            }

            return response()->json([
                'error' => 'An unexpected error occurred while closing the assignment for offers: ' . $e->getMessage()
            ], 500);
        }
    }
}
