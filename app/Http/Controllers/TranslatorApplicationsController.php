<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\Registered;

use Inertia\Inertia;

use Softbox\SBX\Admin\Shared\Locale;

use App\Http\Requests\TranslatorApplicationCreateRequest;

use App\Models\User;
use Softbox\SBX\Admin\Models\Role;
use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;

use App\Http\Resources\TranslationCategoryListTransformer;
use App\Http\Resources\TranslationLanguageListTransformer;
use App\Http\Resources\TranslatorApplicationListTransformer;
use App\Http\Resources\TranslatorApplicationTransformer;

use App\Mail\WelcomeEmail;
use App\Mail\Admin\NewTranslatorForApprovalEmail;
use App\Mail\ApplicationRejectedEmail;

use Arr;
use Auth;
use Carbon\Carbon;
use Hash;
use Log;
use Mail;

class TranslatorApplicationsController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get All Translator Applications
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {

    // Search
    $filters['search'] = $request->search;
    $search = $request->search;

    // Start Query
    $translatorApplicationsQuery = User::query();

    // Search
    $translatorApplicationsQuery
      ->when($request->search, function($query, $search) {
        $query->where(function ($query) use ($search) {
          $query->where('first_name', 'like', "%{$search}%")->orWhere('last_name', 'like', "%{$search}%")->orWhere('company', 'like', "%{$search}%")->orWhere('email', 'like', "%{$search}%")->orWhere('translator_id', 'like', "%{$search}%");
        });
      });

    // Not admin
    $translatorApplicationsQuery->whereHas('roles', function ($query) {
      $query->where('user_roles.role_id', 2);
    });

    // Not approved
    $translatorApplicationsQuery->where('is_approved', false);

    // Resolve query
    $translatorApplications = $translatorApplicationsQuery
     ->orderBy('created_at', 'DESC')
      ->orderBy('last_name')
      ->orderBy('first_name')
      ->paginate(20)
      ->onEachSide(2)
      ->withQueryString();

    return Inertia::render('TranslatorApplication/TranslatorApplications', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translatorApplications' => TranslatorApplicationListTransformer::collection($translatorApplications),
      'filters' => $filters
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Show Customer Application
  |--------------------------------------------------------------------------
  */

  public function show($marketCode, $languageCode, User $translatorApplicationUser) {
    return Inertia::render('TranslatorApplication/ShowTranslatorApplication', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translatorApplication' => TranslatorApplicationTransformer::make($translatorApplicationUser)
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Create Translator Application
  |--------------------------------------------------------------------------
  */

  public function create(Request $request, $marketCode, $languageCode) {
    $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
    $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

    return Inertia::render('TranslatorApplication/CreateTranslatorApplication', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
      'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
    ]);
  }


  public function store(TranslatorApplicationCreateRequest $request, $marketCode, $languageCode) {
    $generatedPassword = Str::random(8);

    $translatorApplicationUser = new User;
    $translatorID = random_int(1001, 9999);
    while (count(User::where('translator_id', $translatorID)->get()) > 0) {
      $translatorID = random_int(1001, 9999);
    }
    $translatorApplicationUser->translator_id = $translatorID;
    $translatorApplicationUser->company = $request->company;
    $translatorApplicationUser->identity_no = $request->company_no;
    $translatorApplicationUser->first_name = $request->first_name;
    $translatorApplicationUser->last_name = $request->last_name;
    $translatorApplicationUser->email = $request->email;
    $translatorApplicationUser->phone_no = $request->phone_no;
    $translatorApplicationUser->address_1 = $request->address_1;
    $translatorApplicationUser->address_2 = $request->address_2;
    $translatorApplicationUser->postal_code = $request->postal_code;
    $translatorApplicationUser->city = $request->city;
    $translatorApplicationUser->password = Hash::make($generatedPassword);
    $translatorApplicationUser->is_authorized = $request->is_authorized;
    $translatorApplicationUser->authorization_id = $request->authorization_id;
    $translatorApplicationUser->created_by_user_id = 1;
    $translatorApplicationUser->updated_by_user_id = 1;
    $translatorApplicationUser->save();

    // Add From Languages
    $fromLanguages = Arr::pluck($request->from_translation_languages, 'id');
    $translatorApplicationUser->fromTranslationLanguages()->attach($fromLanguages, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    // Add To Languages
    $toLanguages = Arr::pluck($request->to_translation_languages, 'id');
    $translatorApplicationUser->toTranslationLanguages()->attach($toLanguages, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    // Add Categories
    $categories = Arr::pluck($request->translation_categories, 'id');
    $translatorApplicationUser->translationCategories()->attach($categories, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    $translatorRole = Role::find(2);
    $translatorApplicationUser->roles()->attach($translatorRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);

    // event(new Registered($translatorApplicationUser));

    Mail::to(env('ADMIN_EMAIL'))->send(new NewTranslatorForApprovalEmail($translatorApplicationUser));

    return Redirect::back();
  }



  /*
  |--------------------------------------------------------------------------
  | Approve Translator Application
  |--------------------------------------------------------------------------
  */

  public function approveTranslatorApplication(Request $request, $marketCode, $languageCode, User $translatorApplicationUser) {
    $generatedPassword = Str::random(8);

    $translatorApplicationUser->is_approved = true;
    $translatorApplicationUser->credit_count = 4;
    $translatorApplicationUser->password =  Hash::make($generatedPassword);
    $translatorApplicationUser->updated_by_user_id = Auth::id();
    $translatorApplicationUser->save();

    // Welcome Email
    // if (App::environment() == 'production') {
      Mail::to($translatorApplicationUser)->send(new WelcomeEmail($translatorApplicationUser, $generatedPassword));
    // }

    return redirect()->route('translators', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }



  /*
  |--------------------------------------------------------------------------
  | Reject Translator Application
  |--------------------------------------------------------------------------
  */

  public function rejectTranslatorApplication($marketCode, $languageCode, User $translatorApplicationUser) {
    Mail::to($translatorApplicationUser)->send(new ApplicationRejectedEmail($translatorApplicationUser));

    $translatorApplicationUser->delete();

    return redirect()->route('translators', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }
}
