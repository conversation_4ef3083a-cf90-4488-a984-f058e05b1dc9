<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;

use App\Models\TranslationCategory;
use App\Http\Resources\TranslationCategoryListTransformer;

class FormCategoriesController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get Active Translation Categories
  |--------------------------------------------------------------------------
  */

  public function categories(Request $request) {
    $categories = TranslationCategory::active('se')->orderBy('name')->get();

    return TranslationCategoryListTransformer::collection($categories);
  }
}
