<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;

use App\Http\Requests\TranslationLanguageCreateRequest;
use App\Http\Requests\TranslationLanguageEditRequest;

use App\Models\TranslationLanguage;
use App\Models\TranslationLanguageLocalization;
use App\Models\TranslationLanguageMarket;

use App\Http\Resources\TranslationLanguageTransformer;
use App\Http\Resources\TranslationLanguageListTransformer;

use Softbox\SBX\Admin\Shared\Locale;

use Auth;
use Carbon\Carbon;
use Log;

class TranslationLanguagesController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get All Translation Languages
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {

    // Search
    $filters['search'] = $request->search;
    $search = $request->search;

    // Active
    $isActive = true;
    $filters['active'] = $isActive;
    if ($request->has('active')) {
      if ($request->active == 'true') {
        $isActive = true;
        $filters['active'] = true;
      } else {
        $isActive = false;
        $filters['active'] = false;
      }
    }

    // Start Query
    $translationLanguagesQuery = TranslationLanguage::query();

    // Search
    $translationLanguagesQuery
      ->when($request->search, function($query, $search) use ($languageCode) {
        $query->whereHas('localizations', function ($query) use ($languageCode, $search) {
          $query->where('language_code', $languageCode)->where('name', 'like', "%{$search}%");
        });
      });

    // Active
    $translationLanguagesQuery->whereHas('markets', function ($query) use ($marketCode, $isActive) {
      $query->where('market_code', $marketCode)->where('is_active', $isActive);
    });

    $translationLanguagesQuery
      ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
      ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
      ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active');

    // Resolve query
    $translationLanguages = $translationLanguagesQuery
      ->orderBy('name')
      ->paginate(20)
      ->onEachSide(2)
      ->withQueryString();

    return Inertia::render('TranslationLanguages/TranslationLanguages', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages),
      'filters' => $filters
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Create Translation Language
  |--------------------------------------------------------------------------
  */

  public function create(Request $request, $marketCode, $languageCode) {
    return Inertia::render('TranslationLanguages/CreateTranslationLanguage', [
      'locale' => Locale::resolve($marketCode, $languageCode)
    ]);
  }


  public function store(TranslationLanguageCreateRequest $request, $marketCode, $languageCode) {

    // Create Translation Language
    $translationLanguage = new TranslationLanguage;
    $translationLanguage->created_by_user_id = Auth::id();
    $translationLanguage->updated_by_user_id = Auth::id();
    $translationLanguage->save();

    // Create Translation Language Localization
    $translationLanguageLocalization = new TranslationLanguageLocalization;
    $translationLanguageLocalization->translation_language_id = $translationLanguage->id;
    $translationLanguageLocalization->language_code = $languageCode;
    $translationLanguageLocalization->name = $request->name;
    $translationLanguageLocalization->created_by_user_id = Auth::id();
    $translationLanguageLocalization->updated_by_user_id = Auth::id();
    $translationLanguageLocalization->save();

    // Create Translation Language Market
    $translationLanguageMarket = new TranslationLanguageMarket;
    $translationLanguageMarket->translation_language_id = $translationLanguage->id;
    $translationLanguageMarket->market_code = $marketCode;
    $translationLanguageMarket->is_active = $request->is_active;
    $translationLanguageMarket->created_by_user_id = Auth::id();
    $translationLanguageMarket->updated_by_user_id = Auth::id();
    $translationLanguageMarket->save();

    return redirect()->route('translation_languages', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }



  /*
  |--------------------------------------------------------------------------
  | Edit Translation Language
  |--------------------------------------------------------------------------
  */

  public function edit($marketCode, $languageCode, TranslationLanguage $translationLanguage) {
    $resolvedTranslationLanguage = TranslationLanguage::where('translation_languages.id', $translationLanguage->id)
      ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
      ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
      ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
      ->first();

    return Inertia::render('TranslationLanguages/EditTranslationLanguage', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translationLanguage' => TranslationLanguageTransformer::make($resolvedTranslationLanguage)
    ]);
  }


  public function update(TranslationLanguageEditRequest $request, $marketCode, $languageCode, TranslationLanguage $translationLanguage) {

    // Update Translation Language
    $translationLanguage->updated_by_user_id = Auth::id();
    $translationLanguage->save();

    // Update Translation Language Localization
    $localization = TranslationLanguageLocalization::where('translation_language_id', $translationLanguage->id)->where('language_code', $languageCode)->first();

    if ($localization) {
      $localization->name = $request->name;
      $localization->updated_by_user_id = Auth::id();
      $localization->save();
    }

    // Update Translation Language Market
    $market = TranslationLanguageMarket::where('translation_language_id', $translationLanguage->id)->where('market_code', $marketCode)->first();

    if ($market) {
      $market->is_active = $request->is_active;
      $market->updated_by_user_id = Auth::id();
      $market->save();
    }

    return redirect()->route('translation_languages', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }



  /*
  |--------------------------------------------------------------------------
  | Delete Translation Language
  |--------------------------------------------------------------------------
  */

  public function destroy(Request $request, $marketCode, $languageCode, TranslationLanguage $translationLanguage) {
    $localization = TranslationLanguageLocalization::where('translation_language_id', $translationLanguage->id)->where('language_code', $languageCode)->first();
    $market = TranslationLanguageMarket::where('translation_language_id', $translationLanguage->id)->where('market_code', $marketCode)->first();

    if ($localization) {
      $localization->delete();
    }

    if ($market) {
      $market->delete();
    }

    $translationLanguage->delete();

    return Redirect::back()->with('success', 'Translation Language deleted.');
  }
}
