<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;

use Inertia\Inertia;

use Softbox\SBX\Webshop\Events\CreateProduct;
use Softbox\SBX\Webshop\Events\DeleteProduct;

use Softbox\SBX\Admin\Models\Language;
use Softbox\SBX\Admin\Models\Market;

use Softbox\SBX\Webshop\Models\Product;
use Softbox\SBX\Webshop\Models\ProductLocalization;
use Softbox\SBX\Webshop\Models\ProductMarket;

use Softbox\SBX\Webshop\Models\ProductUnit;
use Softbox\SBX\Webshop\Models\VatRate;

use App\Http\Resources\TranslationProductBasicInformationTransformer;
use Softbox\SBX\Webshop\Resources\ProductProductCategoriesTransformer;
use Softbox\SBX\Webshop\Resources\ProductPhotoTransformer;
use Softbox\SBX\Webshop\Resources\ProductSEOTransformer;
use App\Http\Resources\TranslationProductListTransformer;
use Softbox\SBX\Webshop\Resources\ProductUnitTransformer;

use Softbox\SBX\Admin\Shared\Locale;

use Auth;
use DB;
use Log;

class TranslationProductsController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get All Products
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {

    // Filters
    $filters['search'] = $request->search;

    $selectedFilter = NULL;
    if ($request->has('selectedFilter')) {
      $selectedFilter = $request->selectedFilter;
      $filters['selectedFilter'] = $request->selectedFilter;
    } else {
      $selectedFilter = 'all';
      $filters['selectedFilter'] = 'all';
    }

    $isActive = ($selectedFilter != 'inactive');

    $products = Product::query()
      ->when($request->search, function($query, $search) use ($languageCode) {
        $query->whereHas('localizations', function ($query) use ($languageCode, $search) {
          $query->where('language_code', $languageCode)->where('name', 'like', "%{$search}%");
        })->OrWhere('article_no', 'like', "%{$search}%");
      })
      ->when($selectedFilter != 'all', function($query) use ($marketCode, $isActive) {
        $query->whereHas('markets', function ($query) use ($marketCode, $isActive) {
          $query->where('market_code', $marketCode)->where('is_active', $isActive);
        });
      })
      ->paginate(25)
      ->onEachSide(2)
      ->withQueryString();

    return Inertia::render('TranslationProducts/Products', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'products' => TranslationProductListTransformer::collection($products),
      'editRoute' => config('sbxwebshop.product_edit_route', 'products.basic_information.edit'),
      'createRoute' => config('sbxwebshop.product_create_route', 'products.create'),
      'filters' => $filters
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Create Product
  |--------------------------------------------------------------------------
  */

  public function create(Request $request, $marketCode, $languageCode) {
    $productUnits = ProductUnit::all();
    $defaultProductUnit = ProductUnit::where('is_default', true)->first();

    return Inertia::render('TranslationProducts/CreateProduct', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'productUnits' => ProductUnitTransformer::collection($productUnits),
      'defaultProductUnitID' => $defaultProductUnit->id
    ]);
  }


  public function store(Request $request, $marketCode, $languageCode) {
    $request->validate([
      'name' => 'required|max:255',
      'short_description' => 'max:255',
      'product_unit_id' => 'required'
    ]);

    $product = new Product;
    $product->product_unit_id = $request->product_unit_id;
    $product->created_by_user_id = Auth::id();
    $product->updated_by_user_id = Auth::id();
    $product->save();

    // Add all languages
    $languages = Language::all();
    foreach($languages as $language) {
      $productLocalization = new ProductLocalization;
      $productLocalization->product_id = $product->id;
      $productLocalization->language_code = $language->language_code;
      if ($language->language_code == $languageCode) {
        $productLocalization->name = $request->name;
        $productLocalization->manufacturer = $request->manufacturer;
        $productLocalization->packet_size = $request->packet_size;
        $productLocalization->short_description = $request->short_description;
        $productLocalization->long_description = $request->long_description;
      }
      $productLocalization->created_by_user_id = Auth::id();
      $productLocalization->updated_by_user_id = Auth::id();
      $productLocalization->save();
    }

    $defaultVatRate = VatRate::whereHas('markets', function (Builder $query) use ($marketCode) {
      $query->where('market_code', $marketCode)->where('is_default', true);
    })->first();

    $vatRateID = NULL;
    if ($defaultVatRate) {
      $vatRateID = $defaultVatRate->id;
    }

    // Add all markets
    $markets = Market::all();
    foreach($markets as $market) {
      $productMarket = new ProductMarket;
      $productMarket->product_id = $product->id;
      $productMarket->market_code = $market->market_code;
      if ($market->market_code == $marketCode) {
        $productMarket->vat_rate_id = $vatRateID;
      }
      $productMarket->created_by_user_id = Auth::id();
      $productMarket->updated_by_user_id = Auth::id();
      $productMarket->save();
    }

    CreateProduct::dispatch($product);

    return redirect()->route(config('sbxwebshop.return_route', 'products'), ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }



  /*
  |--------------------------------------------------------------------------
  | Edit Basic Information
  |--------------------------------------------------------------------------
  */

  public function editBasicInformation($marketCode, $languageCode, Product $product) {
    return Inertia::render('TranslationProducts/EditBasicInformation', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'product' => new TranslationProductBasicInformationTransformer($product),
      'pageComponents' => config('sbxwebshop.product_page_components', [])
    ]);
  }


  public function updateBasicInformation(Request $request, $marketCode, $languageCode, Product $product) {
    $request->validate([
      'name' => 'required|max:255',
      'short_description' => 'max:255'
    ]);

    $localization = $product->localizations()->where('language_code', $languageCode)->first();

    $product->article_no = $request->article_no;
    $product->updated_by_user_id = Auth::id();
    $product->save();

    $localization->name = $request->name;
    $localization->short_description = $request->short_description;
    $localization->long_description = $request->long_description;
    $localization->updated_by_user_id = Auth::id();
    $localization->save();

    return Redirect::back()->with('success', 'Product basic information updated.');
  }
}
