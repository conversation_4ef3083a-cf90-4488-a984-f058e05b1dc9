<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;

use App\Models\TranslationAssignment;
use App\Models\TranslationAssignmentFile;

use App\Http\Resources\TranslationAssignmentTransformer;
use App\Http\Resources\TranslationAssignmentDetailTransformer;

use App\Mail\AssignmentCompletedCustomerEmail;
use App\Mail\AssignmentCompletedEmail;
use App\Mail\AssignmentFilesUpdatedCustomerEmail;
use Softbox\SBX\Admin\Shared\Locale;
use Softbox\SBX\HelloSMS\Jobs\SendSMS;

use Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Log;
use Storage;

class MyAssignmentsController extends Controller
{

    /*
  |--------------------------------------------------------------------------
  | Get All Translation Assignments
  |--------------------------------------------------------------------------
  */

    public function index(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only approved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Check that current user has made a bid
        $translationAssignmentsQuery->whereHas('bids', function ($query) {
            $query->where('user_id', Auth::id());
        });

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")->orWhere('first_name', 'like', "%{$search}%")->orWhere('last_name', 'like', "%{$search}%")->orWhere('company', 'like', "%{$search}%")->orWhere('company_no', 'like', "%{$search}%");
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('MyAssignments/MyAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentDetailTransformer::collection($translationAssignments),
            'filters' => $filters
        ]);
    }

    public function offered(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only approved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Check that current user has made a bid
        $translationAssignmentsQuery->whereHas('bids', function ($query) {
            $query->where('user_id', Auth::id());
        });

        // Check that the assignment does not have a approved bid
        $translationAssignmentsQuery->whereDoesntHave('bids', function ($query) {
            $query->where('is_accepted', true);
        });


        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")->orWhere('first_name', 'like', "%{$search}%")->orWhere('last_name', 'like', "%{$search}%")->orWhere('company', 'like', "%{$search}%")->orWhere('company_no', 'like', "%{$search}%");
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->where('completed_at', NULL)
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('MyAssignments/MyAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentDetailTransformer::collection($translationAssignments),
            'filters' => $filters
        ]);
    }


    public function ongoing(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::GetWonAssignmentsForUser($marketCode, Auth::user());

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->where('completed_at', NULL)
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('MyAssignments/OngoingAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentDetailTransformer::collection($translationAssignments),
            'filters' => $filters
        ]);
    }

    public function completed(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::GetWonAssignmentsForUser($marketCode, Auth::user());

        // Only approved assignments
        $translationAssignmentsQuery->where('is_approved', true);
        $translationAssignmentsQuery->whereNotNull('completed_at');

        // Check that current user has made a bid
        $translationAssignmentsQuery->whereHas('bids', function ($query) {
            $query->where('user_id', Auth::id())->where('is_accepted', true);
        });

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")->orWhere('first_name', 'like', "%{$search}%")->orWhere('last_name', 'like', "%{$search}%")->orWhere('company', 'like', "%{$search}%")->orWhere('company_no', 'like', "%{$search}%");
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('MyAssignments/CompletedAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentDetailTransformer::collection($translationAssignments),
            'filters' => $filters
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Show Translation Assignment
  |--------------------------------------------------------------------------
  */

    public function show(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only approved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Check that current user has made a bid
        $translationAssignmentsQuery->whereHas('bids', function ($query) {
            $query->where('user_id', Auth::id());
        });

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")->orWhere('first_name', 'like', "%{$search}%")->orWhere('last_name', 'like', "%{$search}%")->orWhere('company', 'like', "%{$search}%")->orWhere('company_no', 'like', "%{$search}%");
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        $selectedAssignment = NULL;
        $translationAssignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        if ($translationAssignment) {
            if ($translationAssignment->bids()->where('user_id', Auth::id())->first()) {
                $selectedAssignment = $translationAssignment;
            }
        }

        return Inertia::render('MyAssignments/MyAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignments' => TranslationAssignmentDetailTransformer::collection($translationAssignments),
            'selectedAssignment' => TranslationAssignmentDetailTransformer::make($selectedAssignment),
            'filters' => $filters
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Show Mobile Detail
  |--------------------------------------------------------------------------
  */

    public function showMobileDetail(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        // Search
        $filters['search'] = "";

        $translationAssignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();

        return Inertia::render('MyAssignments/ShowMobileDetail', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignment' => TranslationAssignmentDetailTransformer::make($translationAssignment),
            'filters' => $filters
        ]);
    }



    public function completeAssignment(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        //TODO: Proper validation split and handling
        // Check that the assignments accepted bid belongs to the user and that the assignment is not already completed
        $translationAssignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        if ($translationAssignment && $translationAssignment->bids()->where('user_id', Auth::id())->where('is_accepted', true)->first() && !$translationAssignment->completed_at) {

            // Process the files that are uploaded with the request
            if ($request->hasFile('files')) {
                $files = $request->file('files');
                foreach ($files as $file) {
                    $this->addFileToAssignment($file, $translationAssignment);
                }
            }

            // Mark assignment as completed and send notifications
            $this->markAssignmentAsCompleted($translationAssignment, Auth::user());

            return http_response_code(200);
        } else {
            return response()->json(['error' => 'Assignment not found or not allowed'], 403);
        }
    }

    public function completeMissingFiles(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        $translationAssignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        if ($translationAssignment && $translationAssignment->bids()->where('user_id', Auth::id())->where('is_accepted', true)->first() && $translationAssignment->completed_at) {
            // Process the files that are uploaded with the request
            if ($request->hasFile('files')) {
                $files = $request->file('files');
                foreach ($files as $file) {
                    try {
                        $this->addFileToAssignment($file, $translationAssignment);
                    } catch (\Exception $e) {
                        return response()->json(['error' => 'Error uploading file'], 500);
                    }
                }
                Mail::to($translationAssignment->email)->send(new AssignmentFilesUpdatedCustomerEmail($translationAssignment));
                return http_response_code(200);
            }
        } else {
            return response()->json(['error' => 'Assignment not found or not allowed'], 403);
        }
    }

    private function addFileToAssignment($file, $translationAssignment)
    {
        $storedFile = Storage::putFile('public/assignments', $file);

        $name = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        try {
            $translationAssignmentFile = new TranslationAssignmentFile();
            $translationAssignmentFile->translation_assignment_id = $translationAssignment->id;
            $translationAssignmentFile->filename = str_replace("public/assignments/", "", $storedFile);
            $translationAssignmentFile->original_filename = $name;
            $translationAssignmentFile->file_type = 1;
            $translationAssignmentFile->save();
        } catch (\Exception $e) {
            Log::error($e);
            throw new \Exception("Error saving file");
        }
    }

    /**
     * Mark an assignment as completed and send all necessary notifications
     *
     * @param TranslationAssignment $translationAssignment The assignment to mark as completed
     * @param User $user The user who completed the assignment
     * @return void
     */
    private function markAssignmentAsCompleted($translationAssignment, $user)
    {
        $translationAssignment->completed_at = Carbon::now();
        $translationAssignment->save();

        // Notify the customer that the assignment is completed
        Mail::to($translationAssignment->email)->send(new AssignmentCompletedCustomerEmail($translationAssignment));

        // Notify the customer per sms
        $messageText = "Din översättning är klar och kan laddas ner. Originalet skickas till angiven adress." . PHP_EOL . PHP_EOL;
        $messageText .= "Ladda ner översättningen:" . PHP_EOL . PHP_EOL . $translationAssignment->getAssignmentCustomerLinkAttribute() . ".";
        SendSMS::dispatch($translationAssignment->assignment_id, $translationAssignment->phone_no, $messageText, env('HELLO_SMS_TEST_MODE'));

        // Notify the translator that the assignment is completed and they can provide an invoice
        if ($user) {
            Mail::to($user)->send(new AssignmentCompletedEmail($translationAssignment));
        }

        Mail::to(env('ADMIN_EMAIL'))->send(new AssignmentCompletedCustomerEmail($translationAssignment));
    }
}
