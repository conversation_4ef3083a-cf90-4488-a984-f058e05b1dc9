<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Shared\Locale;

use Softbox\SBX\Admin\Models\Language;

use Softbox\SBX\Webshop\Models\Order;

use App\Http\Resources\PaymentTransformer;

use App\Mail\ReceiptEmail;

use Auth;
use Carbon\Carbon;
use Log;
use Mail;

class PaymentsController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get All Payments
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {

    // Search
    $filters['search'] = $request->search;
    $search = $request->search;

    // Start Query
    $paymentsQuery = Order::query();

    // Only users orders
    $paymentsQuery->where('user_id', Auth::id());

    // Only completed orders
    $paymentsQuery->where('is_completed', true);

    // Search
    $paymentsQuery->when($request->search, function($query, $search) {
      $query->where(function ($query) use ($search) {
        $query->where('order_no', 'like', "%{$search}%")->orWhere('created_at', 'like', "%{$search}%")->orWhere('order_total', 'like', "%{$search}%");
      });
    });

    // Resolve query
    $payments = $paymentsQuery
      ->orderBy('created_at', 'DESC')
      ->paginate(20)
      ->onEachSide(2)
      ->withQueryString();

    return Inertia::render('Payments/Payments', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'payments' => PaymentTransformer::collection($payments),
      'filters' => $filters
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Get All Payments
  |--------------------------------------------------------------------------
  */

  public function resendReceipt(Request $request, $marketCode, $languageCode, Order $order) {
    // Send Receipt Email
    Mail::to(Auth::user())->send(new ReceiptEmail(Auth::user(), $order));

    return Redirect::back()->with('success', 'Receipt sent.');
  }
}
