<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Http\File;

use App\Http\Requests\TranslationAssignmentCreateFormRequest;

use App\Http\Resources\TranslationAssignmentDetailTransformer;

use App\Mail\Admin\NewAssignmentForApprovalEmail;

use App\Models\TranslationAssignment;
use App\Models\TranslationAssignmentFile;

use Mail;
use Storage;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class FormAssignmentsController extends Controller
{

    /*
  |--------------------------------------------------------------------------
  | Create Assignment
  |--------------------------------------------------------------------------
  */

    public function store(TranslationAssignmentCreateFormRequest $request)
    {

        // Create Translation Assignment
        $translationAssignment = new TranslationAssignment;
        $assignmentID = random_int(100001, 999999);
        while (count(TranslationAssignment::where('assignment_id', $assignmentID)->get()) > 0) {
            $assignmentID = random_int(100001, 999999);
        }
        $translationAssignment->assignment_id = $assignmentID;
        $translationAssignment->translation_category_id = $request->translation_category_id;
        $translationAssignment->from_translation_language_id = $request->from_translation_language_id;
        $translationAssignment->to_translation_language_id = $request->to_translation_language_id;
        $translationAssignment->is_authorization_required = $request->is_authorization_required;
        $translationAssignment->number_of_words = $request->number_of_words;
        $translationAssignment->assignment_type = $request->assignment_type;
        $translationAssignment->first_name = $request->first_name;
        $translationAssignment->last_name = $request->last_name;
        $translationAssignment->company = $request->company;
        $translationAssignment->company_no = $request->company_no;
        $translationAssignment->email = $request->email;
        $translationAssignment->phone_no = $request->phone_no;
        $translationAssignment->notes = $request->notes;
        $translationAssignment->is_email_contact_allowed = $request->is_email_contact_allowed;
        $translationAssignment->is_phone_contact_allowed = $request->is_phone_contact_allowed;
        $translationAssignment->save();

        // Files
        if ($request->has('assignment_files')) {
            dump($request->assignment_files);
            foreach ($request->assignment_files as $index => $file) {
                $storedFile = Storage::putFile('public/assignments', $file);

                $name = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();

                $assignmentFile = new TranslationAssignmentFile;
                $assignmentFile->translation_assignment_id = $translationAssignment->id;
                $assignmentFile->filename = str_replace("public/assignments/", "", $storedFile);
                $assignmentFile->original_filename = $name;
                $assignmentFile->save();
            }
        }

        Mail::to(env('ADMIN_EMAIL'))->send(new NewAssignmentForApprovalEmail($translationAssignment));

        return TranslationAssignmentDetailTransformer::make($translationAssignment);
    }

    public function webhookStore(TranslationAssignmentCreateFormRequest $request)
    {
        // Create Translation Assignment

        $translationAssignment = new TranslationAssignment;
        $assignmentID = random_int(100001, 999999);
        while (count(TranslationAssignment::where('assignment_id', $assignmentID)->get()) > 0) {
            $assignmentID = random_int(100001, 999999);
        }

        $translationAssignment->assignment_id = $assignmentID;
        $translationAssignment->translation_category_id = 8;
        $translationAssignment->from_translation_language_id = $request->select_1;
        $translationAssignment->to_translation_language_id = $request->select_2;
        $translationAssignment->assignment_type = $request->radio_2;
        $translationAssignment->is_authorization_required = is_null($request->radio_1) ? 0 : 1;
        $translationAssignment->first_name = $request->name_1_first_name;
        $translationAssignment->email = $request->email_1;
        $translationAssignment->phone_no = $request->phone_1;
        $translationAssignment->notes = $request->textarea_1;
        $translationAssignment->last_delivery = $request->date_1;
        $translationAssignment->save();

        if ($request->radio_2 == 'company') {
            $translationAssignment->company = $request->name_1_first_name;
        }

        if ($request->upload_1) {
            //explode to see if there are multiple files
            $files = explode(',', $request->upload_1);

            foreach ($files as $key => $value) {
                $fileData = $this->downloadAndSaveTempFileWithStream(trim($value));
                $storedFile = Storage::putFile('public/assignments', $fileData);

                $name = $request->forminator_multifile_hidden['upload_1'][$key]['file_name'];

                $assignmentFile = new TranslationAssignmentFile;
                $assignmentFile->translation_assignment_id = $translationAssignment->id;
                $assignmentFile->filename = str_replace("public/assignments/", "", $storedFile);
                $assignmentFile->original_filename = $name;
                $assignmentFile->save();
            }
        }

        return http_response_code(200);
    }


    private function downloadAndSaveTempFileWithStream($url)
    {

        // Use a read stream from the URL
        $readStream = fopen($url, 'r');

        if ($readStream === false) {
            throw new Exception("Unable to open URL: $url");
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'url-file-');

        file_put_contents($tempFile, $readStream);

        fclose($readStream);

        return $tempFile;
    }
}
