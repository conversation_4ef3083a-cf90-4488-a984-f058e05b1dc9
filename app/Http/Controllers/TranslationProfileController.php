<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;
use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;

use App\Http\Requests\EditProfileContactInformationRequest;

use App\Models\User;

use App\Http\Resources\TranslationProfileTransformer;
use App\Http\Resources\TranslationCategoryListTransformer;
use App\Http\Resources\TranslationLanguageListTransformer;

use Softbox\SBX\Admin\Shared\Locale;

use Arr;
use Auth;
use Carbon\Carbon;
use Hash;
use Log;

class TranslationProfileController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Edit Profile
  |--------------------------------------------------------------------------
  */

  public function edit($marketCode, $languageCode) {
    $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
    $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

    return Inertia::render('Profile/EditProfile', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'user' => TranslationProfileTransformer::make(Auth::user()),
      'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
      'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
    ]);
  }


  public function update(EditProfileContactInformationRequest $request, $marketCode, $languageCode) {
    Auth::user()->company = $request->company;
    Auth::user()->identity_no = $request->company_no;
    Auth::user()->first_name = $request->first_name;
    Auth::user()->last_name = $request->last_name;
    Auth::user()->address_1 = $request->address_1;
    Auth::user()->address_2 = $request->address_2;
    Auth::user()->postal_code = $request->postal_code;
    Auth::user()->city = $request->city;
    Auth::user()->phone_no = $request->phone_no;
    Auth::user()->allow_text_messages = $request->allow_text_messages;
    if ($request->password != NULL) {
      Auth::user()->password = Hash::make($request->password);
    }
    Auth::user()->updated_by_user_id = Auth::id();
    Auth::user()->save();

    return redirect()->route('translation_profile', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }


  public function updateInformation(Request $request, $marketCode, $languageCode) {
    Auth::user()->updated_by_user_id = Auth::id();
    Auth::user()->save();

    // From Languages
    $fromLanguages = Arr::pluck($request->from_translation_languages, 'id');
    Auth::user()->fromTranslationLanguages()->sync($fromLanguages, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    // To Languages
    $toLanguages = Arr::pluck($request->to_translation_languages, 'id');
    Auth::user()->toTranslationLanguages()->sync($toLanguages, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    // Categories
    $categories = Arr::pluck($request->translation_categories, 'id');
    Auth::user()->translationCategories()->sync($categories, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    return redirect()->route('translation_profile', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }
}
