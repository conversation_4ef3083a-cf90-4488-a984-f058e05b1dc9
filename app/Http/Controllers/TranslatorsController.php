<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;

use App\Http\Requests\EditTranslatorRequest;
use App\Http\Requests\EditTranslatorLeadsRequest;
use App\Http\Requests\CreateTranslatorRequest;

use App\Models\User;
use Softbox\SBX\Admin\Models\Role;
use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;

use App\Http\Resources\TranslatorTransformer;
use App\Http\Resources\TranslatorListTransformer;
use App\Http\Resources\TranslationCategoryListTransformer;
use App\Http\Resources\TranslationLanguageListTransformer;

use Softbox\SBX\Admin\Shared\Locale;

use Arr;
use Auth;
use Carbon\Carbon;
use Hash;

class TranslatorsController extends Controller
{

    /*
  |--------------------------------------------------------------------------
  | Get All Translation Categories
  |--------------------------------------------------------------------------
  */

    public function index(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translatorsQuery = User::query();

        // Only apprived
        $translatorsQuery->where('is_approved', true);

        // Search
        $translatorsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('first_name', 'like', "%{$search}%")->orWhere('last_name', 'like', "%{$search}%")->orWhere('company', 'like', "%{$search}%")->orWhere('email', 'like', "%{$search}%")->orWhere('translator_id', 'like', "%{$search}%");
            });
        });

        // Authorized
        if ($request->has('authorized')) {
            $filters['authorized'] = $request->authorized;
            $authorized = $request->authorized;
        } else {
            $filters['authorized'] = 'all';
            $authorized = 'all';
        }

        if ($authorized != 'all') {
            if ($authorized == 'authorized') {
                $translatorsQuery->where('is_authorized', true);
            }

            if ($authorized == 'unauthorized') {
                $translatorsQuery->where('is_authorized', false);
            }
        }


        // From Language
        if ($request->has('from_language')) {
            $filters['from_language'] = $request->from_language;
            $fromLanguage = $request->from_language;
        } else {
            $filters['from_language'] = 'all';
            $fromLanguage = 'all';
        }

        if ($fromLanguage != 'all') {
            $translatorsQuery->whereHas('fromTranslationLanguages', function ($query) use ($marketCode, $fromLanguage) {
                $query->where('translation_language_id', $fromLanguage);
            });
        }

        // To Language
        if ($request->has('to_language')) {
            $filters['to_language'] = $request->to_language;
            $toLanguage = $request->to_language;
        } else {
            $filters['to_language'] = 'all';
            $toLanguage = 'all';
        }

        if ($toLanguage != 'all') {
            $translatorsQuery->whereHas('toTranslationLanguages', function ($query) use ($marketCode, $toLanguage) {
                $query->where('translation_language_id', $toLanguage);
            });
        }

        // Category
        if ($request->has('category')) {
            $filters['category'] = $request->category;
            $category = $request->category;
        } else {
            $filters['category'] = 'all';
            $category = 'all';
        }

        if ($category != 'all') {
            $translatorsQuery->whereHas('translationCategories', function ($query) use ($marketCode, $category) {
                $query->where('translation_category_id', $category);
            });
        }


        // Resolve query
        $translators = $translatorsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();


        $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
        $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

        return Inertia::render('Translators/Translators', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translators' => TranslatorListTransformer::collection($translators),
            'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
            'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages),
            'filters' => $filters
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Create Translation Category
  |--------------------------------------------------------------------------
  */

    public function create(Request $request, $marketCode, $languageCode)
    {
        $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
        $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

        return Inertia::render('Translators/CreateTranslator', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
            'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
        ]);
    }


    public function store(CreateTranslatorRequest $request, $marketCode, $languageCode)
    {
        $translator = new User;
        $translatorID = random_int(1001, 9999);
        while (count(User::where('translator_id', $translatorID)->get()) > 0) {
            $translatorID = random_int(1001, 9999);
        }
        $translator->translator_id = $translatorID;
        $translator->company = $request->company;
        $translator->identity_no = $request->company_no;
        $translator->first_name = $request->first_name;
        $translator->last_name = $request->last_name;
        $translator->email = $request->email;
        $translator->phone_no = $request->phone_no;
        $translator->address_1 = $request->address_1;
        $translator->address_2 = $request->address_2;
        $translator->postal_code = $request->postal_code;
        $translator->city = $request->city;
        $translator->password = Hash::make($request->password);
        $translator->is_authorized = $request->is_authorized;
        $translator->authorization_id = $request->authorization_id;
        $translator->created_by_user_id = 1;
        $translator->updated_by_user_id = 1;
        $translator->save();

        // Add From Languages
        $translator->fromTranslationLanguages()->attach($request->from_translation_languages, [
            'updated_at' => Carbon::now(),
            'created_at' => Carbon::now()
        ]);

        // Add To Languages
        $translator->toTranslationLanguages()->attach($request->to_translation_languages, [
            'updated_at' => Carbon::now(),
            'created_at' => Carbon::now()
        ]);

        // Add Categories
        $translator->translationCategories()->attach($request->translation_categories, [
            'updated_at' => Carbon::now(),
            'created_at' => Carbon::now()
        ]);

        $translatorRole = Role::find(2);
        $translator->roles()->attach($translatorRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);

        return redirect()->route('translators', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Edit Translation Category
  |--------------------------------------------------------------------------
  */

    public function edit($marketCode, $languageCode, User $translator)
    {
        $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
        $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

        return Inertia::render('Translators/EditTranslator', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'user' => TranslatorTransformer::make($translator),
            'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
            'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
        ]);
    }


    public function update(EditTranslatorRequest $request, $marketCode, $languageCode, User $translator)
    {
        $translator->company = $request->company;
        $translator->identity_no = $request->company_no;
        $translator->first_name = $request->first_name;
        $translator->last_name = $request->last_name;
        $translator->address_1 = $request->address_1;
        $translator->address_2 = $request->address_2;
        $translator->postal_code = $request->postal_code;
        $translator->city = $request->city;
        $translator->email = $request->email;
        $translator->phone_no = $request->phone_no;
        $translator->is_authorized = $request->is_authorized;
        $translator->authorization_id = $request->authorization_id;
        $translator->allow_text_messages = $request->allow_text_messages;
        $translator->updated_by_user_id = Auth::id();
        $translator->save();

        return redirect()->route('translators', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }


    public function updateInformation(Request $request, $marketCode, $languageCode, User $translator)
    {
        $translator->updated_by_user_id = Auth::id();
        $translator->save();

        // From Languages - Convert IDs to strings since the migration uses string type
        $fromLanguages = array_map('strval', Arr::pluck($request->from_translation_languages, 'id'));
        $translator->fromTranslationLanguages()->sync($fromLanguages);

        // To Languages - Convert IDs to strings since the migration uses string type
        $toLanguages = array_map('strval', Arr::pluck($request->to_translation_languages, 'id'));
        $translator->toTranslationLanguages()->sync($toLanguages);

        // Categories - Convert IDs to strings since the migration uses string type
        $categories = array_map('strval', Arr::pluck($request->translation_categories, 'id'));
        $translator->translationCategories()->sync($categories);

        return redirect()->route('translators', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }


    public function updateLeads(EditTranslatorLeadsRequest $request, $marketCode, $languageCode, User $translator)
    {
        $translator->credit_count = $translator->credit_count + $request->lead_count;
        $translator->updated_by_user_id = Auth::id();
        $translator->save();

        return redirect()->route('translators', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Delete Translator
  |--------------------------------------------------------------------------
  */

    public function destroy(Request $request, $marketCode, $languageCode, User $translator)
    {
        $translator->delete();

        return Redirect::back()->with('success', 'Translator deleted.');
    }
}
