<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use App\Models\TranslationAssignment;
use App\Services\ActivityLogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityLogController extends Controller
{
    /**
     * Display a listing of activity logs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Only allow admins to access this API
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Apply filters if provided
        $query = ActivityLog::with('user');

        if ($request->has('action')) {
            $query->where('action', $request->action);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('description', 'like', "%{$search}%")
                    ->orWhere('action', 'like', "%{$search}%");
            });
        }

        // Get paginated results
        $logs = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 20));

        return response()->json($logs);
    }

    /**
     * Display activity logs for a specific assignment.
     *
     * @param  \App\Models\TranslationAssignment  $assignment
     * @return \Illuminate\Http\JsonResponse
     */
    public function forAssignment(TranslationAssignment $assignment)
    {
        // Only allow admins to access this API
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $logs = ActivityLogService::getLogsForModel($assignment);

        return response()->json([
            'logs' => $logs,
            'assignment_id' => $assignment->id,
            'assignment_reference' => $assignment->assignment_id
        ]);
    }
}
