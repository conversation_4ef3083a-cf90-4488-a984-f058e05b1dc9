<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;

use Softbox\SBX\Webshop\Models\Product;
use Softbox\SBX\Admin\Models\Setting;

use App\Http\Resources\TranslationProductStoreTransformer;

use Softbox\SBX\Admin\Shared\Locale;

use Auth;
use Carbon\Carbon;
use Log;

class TranslationStoreController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Translation Store
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {
    $products = Product::all();

    $discountActiveSetting = Setting::find(2);
    $discountActiveSettingMarket = $discountActiveSetting->markets()->where('market_code', $request->marketCode)->first();
    $discountActive = $discountActiveSettingMarket->boolean_value;

    return Inertia::render('TranslationStore/TranslationStore', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'discountActive' => $discountActive,
      'discountUsed' => Auth::user()->discount_used,
      'products' => TranslationProductStoreTransformer::collection($products)
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Buy Credits
  |--------------------------------------------------------------------------
  */

  public function buyCredits(Request $request, $marketCode, $languageCode) {
    $product = Product::find($request->product_id);

    if ($product) {
      Auth::user()->credit_count = Auth::user()->credit_count + $product->credit_quantity;
      Auth::user()->save();
    }

    return Redirect::back()->with('success', 'Credits purchased.');
  }
}
