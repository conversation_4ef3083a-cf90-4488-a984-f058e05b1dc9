<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Shared\Locale;

use Softbox\SBX\Admin\Models\Language;
use Softbox\SBX\Webshop\Models\VatRateMarket;
use Softbox\SBX\Admin\Models\Setting;

use App\Http\Requests\TranslationAssignmentCreateRequest;
use App\Http\Requests\TranslationAssignmentEditRequest;
use App\Http\Resources\CustomerViewAssignmentTransformer;
use App\Models\TranslationAssignment;
use App\Models\TranslationAssignmentFile;
use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;
use App\Models\User;

use App\Http\Resources\TranslationAssignmentTransformer;
use App\Http\Resources\TranslationAssignmentDetailTransformer;
use App\Http\Resources\TranslationAssignmentListTransformer;
use App\Http\Resources\TranslationCategoryListTransformer;
use App\Http\Resources\TranslationLanguageListTransformer;

use App\Jobs\SendNewAssignmentEmails;
use Softbox\SBX\HelloSMS\Jobs\SendSMS;

use App\Mail\AssignmentPublishedEmail;
use App\Mail\Admin\NewAssignmentForApprovalEmail;
use App\Mail\AssignmentRejectedEmail;
use App\Mail\PreMarketPriceCustomerEmail;
use App\Services\ActivityLogService;

use Auth;
use Carbon\Carbon;
use Log;
use Mail;

use Storage;
use Str;

class TranslationAssignmentsController extends Controller
{

    /*
  |--------------------------------------------------------------------------
  | Get All Translation Assignments
  |--------------------------------------------------------------------------
  */

    public function index(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only approved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%")
                    ->orWhere('company_no', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_no', 'like', "%{$search}%")
                    ->orWhereHas('fromLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('TranslationAssignments/TranslationAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignments' => TranslationAssignmentListTransformer::collection($translationAssignments),
            'filters' => $filters
        ]);
    }


    public function unapprovedAssignments(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only unapproved assignments
        $translationAssignmentsQuery->where('is_approved', false);

        // Only not deleted assignments
        $translationAssignmentsQuery->where('is_deleted', false);

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%")
                    ->orWhere('company_no', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_no', 'like', "%{$search}%")
                    ->orWhereHas('fromLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('TranslationAssignments/UnapprovedTranslationAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignments' => TranslationAssignmentListTransformer::collection($translationAssignments),
            'filters' => $filters,
            'pageTitle' => 'Uppdrag som behöver godkännas'
        ]);
    }


    public function ongoingAssignments(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only approved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Only not deleted assignments
        $translationAssignmentsQuery->where('is_deleted', false);

        // Only active assignments
        $translationAssignmentsQuery->where('is_active', true);

        // Only not completed assignments
        $translationAssignmentsQuery->whereNull('completed_at');

        // Exclude assignments where bids have been won (accepted)
        $translationAssignmentsQuery->whereDoesntHave('bids', function ($query) {
            $query->where('is_accepted', true);
        });

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%")
                    ->orWhere('company_no', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_no', 'like', "%{$search}%")
                    ->orWhereHas('fromLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('TranslationAssignments/TranslationAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignments' => TranslationAssignmentListTransformer::collection($translationAssignments),
            'filters' => $filters,
            'pageTitle' => 'Pågående uppdrag'
        ]);
    }

    public function wonAssignments(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only unapproved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Only not deleted assignments
        $translationAssignmentsQuery->where('is_deleted', false);

        $translationAssignmentsQuery->where('is_closed_for_offers', true);

        $translationAssignmentsQuery->whereHas('bids', function ($query) {
            $query->where('is_accepted', true);
        });

        $translationAssignmentsQuery->whereNull('completed_at');

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%")
                    ->orWhere('company_no', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_no', 'like', "%{$search}%")
                    ->orWhereHas('fromLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('TranslationAssignments/TranslationAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignments' => TranslationAssignmentListTransformer::collection($translationAssignments),
            'filters' => $filters,
            'pageTitle' => 'Vunna uppdrag'
        ]);
    }

    public function completedAssignments(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only unapproved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Only not deleted assignments
        $translationAssignmentsQuery->where('is_deleted', false);

        $translationAssignmentsQuery->where('is_closed_for_offers', true);

        $translationAssignmentsQuery->whereNotNull('completed_at');

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%")
                    ->orWhere('company_no', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_no', 'like', "%{$search}%")
                    ->orWhereHas('fromLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('TranslationAssignments/TranslationAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignments' => TranslationAssignmentListTransformer::collection($translationAssignments),
            'filters' => $filters,
            'pageTitle' => 'Avslutade uppdrag'
        ]);
    }

    public function unwonAssignments(Request $request, $marketCode, $languageCode)
    {

        // Search
        $filters['search'] = $request->search;
        $search = $request->search;

        // Start Query
        $translationAssignmentsQuery = TranslationAssignment::query();

        // Only unapproved assignments
        $translationAssignmentsQuery->where('is_approved', true);

        // Only not deleted assignments
        $translationAssignmentsQuery->where('is_deleted', false);

        $translationAssignmentsQuery->where('is_closed_for_offers', true);

        $translationAssignmentsQuery->whereDoesntHave('bids', function ($query) {
            $query->where('is_accepted', true);
        });

        // Search
        $translationAssignmentsQuery->when($request->search, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('assignment_id', 'like', "%{$search}%")
                    ->orWhere('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%")
                    ->orWhere('company_no', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_no', 'like', "%{$search}%")
                    ->orWhereHas('fromLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('toLanguage.localizations', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        });

        // Resolve query
        $translationAssignments = $translationAssignmentsQuery
            ->orderBy('created_at', 'DESC')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->paginate(20)
            ->onEachSide(2)
            ->withQueryString();

        return Inertia::render('TranslationAssignments/TranslationAssignments', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignments' => TranslationAssignmentListTransformer::collection($translationAssignments),
            'pageTitle' => 'Nekade uppdrag',
            'filters' => $filters
        ]);
    }




    /*
  |--------------------------------------------------------------------------
  | View Translation Assignment
  |--------------------------------------------------------------------------
  */

    public function view(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        return Inertia::render('TranslationAssignments/ViewAssignment', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignment' => TranslationAssignmentDetailTransformer::make($assignment)
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Create Translation Assignment
  |--------------------------------------------------------------------------
  */

    public function create(Request $request, $marketCode, $languageCode)
    {
        $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
        $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

        return Inertia::render('TranslationAssignments/CreateTranslationAssignment', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
            'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
        ]);
    }


    public function show(Request $request, $marketCode, $languageCode)
    {
        $translationCategories = TranslationCategory::active($marketCode)->orderBy('name')->get();
        $translationLanguages = TranslationLanguage::active($marketCode)->orderBy('name')->get();

        return Inertia::render('TranslationAssignments/CreateTranslationAssignmentPublic', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
            'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
        ]);
    }


    public function store(TranslationAssignmentCreateRequest $request, $marketCode, $languageCode)
    {

        // Create Translation Assignment
        $translationAssignment = new TranslationAssignment;
        $assignmentID = random_int(100001, 999999);
        while (count(TranslationAssignment::where('assignment_id', $assignmentID)->get()) > 0) {
            $assignmentID = random_int(100001, 999999);
        }
        $translationAssignment->assignment_id = $assignmentID;
        $translationAssignment->translation_category_id = $request->translation_category_id;
        $translationAssignment->from_translation_language_id = $request->from_translation_language_id;
        $translationAssignment->to_translation_language_id = $request->to_translation_language_id;
        $translationAssignment->is_authorization_required = $request->is_authorization_required;
        $translationAssignment->number_of_words = $request->number_of_words;
        $translationAssignment->assignment_type = $request->assignment_type;
        $translationAssignment->first_name = $request->first_name;
        $translationAssignment->last_name = $request->last_name;
        $translationAssignment->company = $request->company;
        $translationAssignment->company_no = $request->company_no;
        $translationAssignment->email = $request->email;
        $translationAssignment->phone_no = $request->phone_no;
        $translationAssignment->notes = $request->notes;
        $translationAssignment->is_email_contact_allowed = $request->is_email_contact_allowed;
        $translationAssignment->is_phone_contact_allowed = $request->is_phone_contact_allowed;
        $translationAssignment->save();

        // Files
        if ($request->has('assignment_files')) {
            $files = $request->file('assignment_files');
            foreach ($request->assignment_files as $index => $fileObject) {
                $file = $fileObject['file'];

                $storedFile = Storage::putFile('public/assignments', $file);

                $name = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();

                $assignmentFile = new TranslationAssignmentFile;
                $assignmentFile->translation_assignment_id = $translationAssignment->id;
                $assignmentFile->filename = str_replace("public/assignments/", "", $storedFile);
                $assignmentFile->original_filename = $name;
                $assignmentFile->save();
            }
        }

        Mail::to(env('ADMIN_EMAIL'))->send(new NewAssignmentForApprovalEmail($translationAssignment));

        return Redirect::back()->with('success', 'Translation Assignment created.');

        // return redirect()->route('translation_assignment_approvals', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Edit Translation Assignment
  |--------------------------------------------------------------------------
  */

    public function edit($marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {
        $translationCategories = TranslationCategory::join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
            ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
            ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active')
            ->get();

        $translationLanguages = TranslationLanguage::join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->get();

        return Inertia::render('TranslationAssignments/EditTranslationAssignment', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'translationAssignment' => TranslationAssignmentTransformer::make($translationAssignment),
            'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
            'translationLanguages' => TranslationLanguageListTransformer::collection($translationLanguages)
        ]);
    }


    public function update(TranslationAssignmentEditRequest $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {

        // Update Translation Assignment
        $translationAssignment->translation_category_id = $request->translation_category_id;
        $translationAssignment->from_translation_language_id = $request->from_translation_language_id;
        $translationAssignment->to_translation_language_id = $request->to_translation_language_id;
        $translationAssignment->is_authorization_required = $request->is_authorization_required;
        $translationAssignment->number_of_words = $request->number_of_words;
        if ($request->assignment_type) {
            $translationAssignment->assignment_type = 'company';
        } else {
            $translationAssignment->assignment_type = 'personal';
        }
        $translationAssignment->first_name = $request->first_name;
        $translationAssignment->last_name = $request->last_name;
        $translationAssignment->company = $request->company;
        $translationAssignment->company_no = $request->company_no;
        $translationAssignment->email = $request->email;
        $translationAssignment->phone_no = $request->phone_no;
        $translationAssignment->notes = $request->notes;
        $translationAssignment->is_email_contact_allowed = $request->is_email_contact_allowed;
        $translationAssignment->is_phone_contact_allowed = $request->is_phone_contact_allowed;
        $translationAssignment->save();

        return redirect()->route('translation_assignments', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Delete Translation Assignment
  |--------------------------------------------------------------------------
  */

    public function destroy(Request $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {
        $translationAssignment->delete();

        return Redirect::back()->with('success', 'Translation Assignment deleted.');
    }



    /*
  |--------------------------------------------------------------------------
  | Show Unapproved Assignment
  |--------------------------------------------------------------------------
  */

    public function showUnapprovedAssignment($marketCode, $languageCode, TranslationAssignment $assignment)
    {
        return Inertia::render('TranslationAssignments/ShowUnapprovedAssignment', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignment' => TranslationAssignmentDetailTransformer::make($assignment)
        ]);
    }



    /*
  |--------------------------------------------------------------------------
  | Approve Assignment
  |--------------------------------------------------------------------------
  */

    public function approveAssignment(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        $assignment->is_approved = true;
        $assignment->approved_at = Carbon::now();
        $assignment->save();

        // Assignment Published Email
        Mail::to($assignment)->send(new AssignmentPublishedEmail($assignment));

        SendNewAssignmentEmails::dispatch($assignment);

        $translators = User::activeTranslators()->get();

        foreach ($translators as $translator) {
            if ($translator->allow_text_messages && $translator->matchesAssignment($assignment)) {
                $messageText = "Det finns ett nytt uppdrag som matchar din profil. " . PHP_EOL . PHP_EOL;
                $messageText .= "Läs och besvara:" . PHP_EOL . PHP_EOL . $assignment->assignmentLink . ".";

                SendSMS::dispatch($translator->id, $translator->phone_no, $messageText, env('HELLO_SMS_TEST_MODE'));
            }
        }

        return redirect()->route('translation_assignments', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Reject Assignment
  |--------------------------------------------------------------------------
  */

    public function rejectAssignment($marketCode, $languageCode, TranslationAssignment $assignment)
    {
        Mail::to($assignment)->send(new AssignmentRejectedEmail($assignment));

        $assignment->delete();

        return redirect()->route('translation_assignments', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Delete Assignment
  |--------------------------------------------------------------------------
  */

    public function deleteAssignment($marketCode, $languageCode, TranslationAssignment $assignment)
    {
        $assignment->delete();

        return redirect()->route('translation_assignments', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }

    //This is used to keep the assignment in the system but mark it as deleted
    public function markAsDeleteAssignment($marketCode, $languageCode, TranslationAssignment $assignment)
    {
        $assignment->is_deleted = true;
        $assignment->is_active = false;
        $assignment->save();

        return redirect()->route('translation_assignments', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }



    /*
  |--------------------------------------------------------------------------
  | Assignment Activation
  |--------------------------------------------------------------------------
  */

    public function activateAssignment(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        $assignment->is_active = true;
        $assignment->save();

        return Redirect::back()->with('success', 'Assignment activated.');
    }


    public function deactivateAssignment(Request $request, $assignmentCode, $languageCode, TranslationAssignment $assignment)
    {
        $assignment->is_active = false;
        $assignment->save();

        return Redirect::back()->with('success', 'Assignment deactivated.');
    }

    /*
  |--------------------------------------------------------------------------
  | Complete Assignment (Admin)
  |--------------------------------------------------------------------------
  */

    /**
     * Complete an assignment from the admin interface without requiring files
     *
     * @param Request $request
     * @param string $marketCode
     * @param string $languageCode
     * @param TranslationAssignment $assignment
     * @return \Illuminate\Http\RedirectResponse
     */
    public function adminCompleteAssignment(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        // Check if user is admin
        $user = Auth::user();
        if (!$user || !$user->roles->contains(function ($role) {
            return $role->is_admin;
        })) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if assignment is already completed
        if ($assignment->completed_at) {
            return redirect()->back()->withErrors([
                'error' => 'Assignment is already completed'
            ]);
        }

        // Get the translator who won the bid
        $winningBid = $assignment->bids()->where('is_accepted', true)->first();
        $translator = $winningBid ? User::find($winningBid->user_id) : null;

        // Mark assignment as completed
        $assignment->completed_at = Carbon::now();
        $assignment->save();

        // Notify the customer that the assignment is completed
        Mail::to($assignment->email)->send(new \App\Mail\AssignmentCompletedCustomerEmail($assignment));

        // Notify the customer per sms
        $messageText = "Din översättning är klar och kan laddas ner. Originalet skickas till angiven adress." . PHP_EOL . PHP_EOL;
        $messageText .= "Ladda ner översättningen:" . PHP_EOL . PHP_EOL . $assignment->getAssignmentCustomerLinkAttribute() . ".";
        SendSMS::dispatch($assignment->assignment_id, $assignment->phone_no, $messageText, env('HELLO_SMS_TEST_MODE'));

        // Notify the translator that the assignment is completed and they can provide an invoice
        if ($translator) {
            Mail::to($translator)->send(new \App\Mail\AssignmentCompletedEmail($assignment));
        }

        Mail::to(env('ADMIN_EMAIL'))->send(new \App\Mail\AssignmentCompletedCustomerEmail($assignment));

        return redirect()->route('translation_assignments.view', [
            'marketCode' => $marketCode,
            'languageCode' => $languageCode,
            'assignment' => $assignment->id
        ])->with('success', 'Assignment marked as completed');
    }



    /*
  |--------------------------------------------------------------------------
  | Resend Match Email
  |--------------------------------------------------------------------------
  */

    public function resendMatchEmail(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment)
    {
        SendNewAssignmentEmails::dispatch($assignment);

        return redirect()->route('translation_assignments', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
    }


    /*
  |--------------------------------------------------------------------------
  | Customer view for active assigment with link
  |--------------------------------------------------------------------------
  */


    public function customerView(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        $defaultVatRate = VatRateMarket::where('market_code', $marketCode)->where('is_default', true)->where('is_active', true)->first();

        // Log customer visit
        if ($assignment) {
            ActivityLogService::log(
                'customer_visit',
                'Customer visited their assignment page',
                $assignment,
                [
                    'assignment_id' => $assignment->assignment_id,
                    'customer_email' => $assignment->email,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->header('User-Agent')
                ]
            );
        }

        return Inertia::render('OffersView/CustomerView', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'assignment' => CustomerViewAssignmentTransformer::make($assignment),
            'market_vat_rate' => $defaultVatRate->rate,
        ]);
    }


    public function downloadTranslatedFiles(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        $files = $assignment->files()->GetTranslatedFiles()->get();

        $zip = new \ZipArchive;
        $zipFileName = 'public/assignments/' . $assignment->assignment_id . '.zip';
        $zip->open(storage_path('app/' . $zipFileName), \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        foreach ($files as $file) {
            $zip->addFile(storage_path('app/public/assignments/' . $file->filename), $file->original_filename);
        }

        $zip->close();

        return response()->download(storage_path('app/' . $zipFileName));
    }

    public function downloadOriginalFiles(Request $request, $marketCode, $languageCode, $translationAssignmentID)
    {
        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        $files = $assignment->files()->GetFilesForTranslation()->get();

        $zip = new \ZipArchive;
        $zipFileName = 'public/assignments/' . $assignment->assignment_id . '_original.zip';
        $zip->open(storage_path('app/' . $zipFileName), \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        foreach ($files as $file) {
            $zip->addFile(storage_path('app/public/assignments/' . $file->filename), $file->original_filename);
        }

        $zip->close();

        return response()->download(storage_path('app/' . $zipFileName));
    }

    /**
     * Store a single assignment file
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param TranslationAssignment $translationAssignment
     * @param int $fileType File type (0 = Files for translation, 1 = Translated files)
     * @return TranslationAssignmentFile
     * @throws \Exception
     */
    private function storeAssignmentFile(\Illuminate\Http\UploadedFile $file, TranslationAssignment $translationAssignment, int $fileType = 0): TranslationAssignmentFile
    {
        try {
            // Validate file
            if (!$file->isValid()) {
                throw new \Exception('Invalid file upload');
            }

            // Store file
            $storedFile = Storage::putFile('public/assignments', $file);
            if (!$storedFile) {
                throw new \Exception('Failed to store file');
            }

            // Create database record
            $translationAssignmentFile = new TranslationAssignmentFile();
            $translationAssignmentFile->translation_assignment_id = $translationAssignment->id;
            $translationAssignmentFile->filename = str_replace("public/assignments/", "", $storedFile);
            $translationAssignmentFile->original_filename = $file->getClientOriginalName();
            $translationAssignmentFile->file_type = $fileType;
            $translationAssignmentFile->save();

            return $translationAssignmentFile;
        } catch (\Exception $e) {
            // Clean up stored file if database operation failed
            if (isset($storedFile)) {
                Storage::delete($storedFile);
            }

            Log::error('Failed to store assignment file', [
                'error' => $e->getMessage(),
                'assignment_id' => $translationAssignment->id,
                'original_filename' => $file->getClientOriginalName(),
            ]);

            throw $e;
        }
    }

    /**
     * Add one or multiple files to an assignment
     *
     * @param Request $request
     * @param string $marketCode
     * @param string $languageCode
     * @param TranslationAssignment $translationAssignment
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addFile(Request $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment)
    {
        try {
            $successCount = 0;
            $errorCount = 0;

            // Get file type (0 = Files for translation, 1 = Translated files)
            $fileType = $request->input('file_type', 0);

            if ($request->hasFile('files')) {
                // Handle multiple files
                $files = $request->file('files');
                foreach ($files as $file) {
                    try {
                        $this->storeAssignmentFile($file, $translationAssignment, $fileType);
                        $successCount++;
                    } catch (\Exception $e) {
                        $errorCount++;
                        continue; // Continue with next file even if one fails
                    }
                }
            } else if ($request->hasFile('file')) {
                // Handle single file
                try {
                    $this->storeAssignmentFile($request->file('file'), $translationAssignment, $fileType);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                }
            } else {
                return response()->json([
                    'error' => 'No files were uploaded'
                ], 400);
            }

            // Prepare response message
            if ($errorCount > 0 && $successCount > 0) {
                return response()->json([
                    'success' => "{$successCount} file(s) uploaded successfully",
                    'error' => "{$errorCount} file(s) failed to upload"
                ], 207); // 207 Multi-Status
            } else if ($errorCount > 0) {
                return response()->json([
                    'error' => 'Failed to upload file(s)'
                ], 400);
            }

            return response()->json([
                'success' => 'File(s) added successfully',
                'count' => $successCount
            ], 200);
        } catch (\Exception $e) {
            // Log the error
            if (class_exists('\\Illuminate\\Support\\Facades\\Log')) {
                \Illuminate\Support\Facades\Log::error('Failed to process file upload', [
                    'error' => $e->getMessage(),
                    'assignment_id' => $translationAssignment->id,
                ]);
            }

            return response()->json([
                'error' => 'An unexpected error occurred while uploading files: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteFile(Request $request, $marketCode, $languageCode, TranslationAssignment $translationAssignment, $fileID)
    {
        // Check if file belongs to assignment
        if (!$translationAssignment->files()->where('id', $fileID)->exists()) {
            return redirect()->back()->withErrors([
                'error' => 'File not found'
            ]);
        }
        $file = TranslationAssignmentFile::find($fileID);
        //Remove file from storage
        Storage::delete('public/assignments/' . $file->filename);
        $file->delete();

        return Redirect::back()->with('success', 'File deleted.');
    }

    public function getPriceCalculatorData($marketCode)
    {
        $priceMarketAddon = Setting::find(3)->markets()->where('market_code', $marketCode)->first();

        return response(
            $priceMarketAddon->json_value,
        );
    }

    public function acceptPreMarketBid(Request $request, $marketCode, $languageCode, $translationAssignmentID, $customerEmail, $premarketBidID)

    {
        //Check if assignment already has accepted bid
        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        if ($assignment && $assignment->premarket_bid()->where('is_accepted', 1)->exists()) {
            return redirect()->back()->withErrors([
                'error' => 'Already accepted bid'
            ]);
        } else {
            try {
                $bid = $assignment->premarket_bid()->where('id', $premarketBidID)->first();
                $bid->is_accepted = true;
                $bid->accepted_at = Carbon::now();
                $bid->save();
            } catch (\Exception $e) {
                return redirect()->back()->withErrors([
                    'error' => 'Bid not found, unable to approve assigment'
                ]);
            } finally {
                $this->approveAssignment($request, $marketCode, $languageCode, $assignment);
            }

            return view('premarket', ['messageTitle' => 'Din offerförfrågan skickas nu vidare', 'message' => 'Vi har matchat dina dokument med kvalificerade översättare, som nu arbetar med att ta fram ett prisförslag åt dig.']);
        }
    }

    public function declinePreMarketBid(Request $request, $marketCode, $languageCode, $translationAssignmentID, $customerEmail, $premarketBidID)
    {
        //Check if assignment already has accepted bid
        $assignment = TranslationAssignment::where('assignment_id', $translationAssignmentID)->first();
        if ($assignment && $assignment->premarket_bid()->where('is_accepted', 1)->exists()) {
            return redirect()->back()->withErrors([
                'error' => 'Already accepted bid'
            ]);
        } else {
            try {
                $bid = $assignment->premarket_bid()->where('id', $premarketBidID)->first();
                $bid->is_accepted = false;
                $bid->accepted_at = Carbon::now();
                $bid->save();
                $this->markAsDeleteAssignment($request, $marketCode, $languageCode, $assignment);
            } catch (\Exception $e) {
                return redirect()->back()->withErrors([
                    'error' => 'Unable to deactivate assignment'
                ]);
            } finally {
                Mail::to('<EMAIL>')->send(new AssignmentRejectedEmail($assignment));
            }

            return view('premarket', ['messageTitle' => 'Tack för ditt svar.', 'message' => 'Ditt uppdrags förfrågan har nu blivit inaktiverat.']);
        }
    }
}
