<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;

use App\Http\Requests\TranslatorApplicationCreateRequest;

use App\Models\User;
use Softbox\SBX\Admin\Models\Role;

use App\Http\Resources\TranslatorApplicationTransformer;

use App\Mail\Admin\NewTranslatorForApprovalEmail;

use Arr;
use Carbon\Carbon;
use Hash;
use Mail;
use Str;

class FormTranslatorsController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Create Translator Application
  |--------------------------------------------------------------------------
  */

  public function store(TranslatorApplicationCreateRequest $request) {
    $generatedPassword = Str::random(8);

    $translatorApplicationUser = new User;
    $translatorID = random_int(1001, 9999);
    while (count(User::where('translator_id', $translatorID)->get()) > 0) {
      $translatorID = random_int(1001, 9999);
    }
    $translatorApplicationUser->translator_id = $translatorID;
    $translatorApplicationUser->company = $request->company;
    $translatorApplicationUser->identity_no = $request->company_no;
    $translatorApplicationUser->first_name = $request->first_name;
    $translatorApplicationUser->last_name = $request->last_name;
    $translatorApplicationUser->email = $request->email;
    $translatorApplicationUser->phone_no = $request->phone_no;
    $translatorApplicationUser->address_1 = $request->address_1;
    $translatorApplicationUser->address_2 = $request->address_2;
    $translatorApplicationUser->postal_code = $request->postal_code;
    $translatorApplicationUser->city = $request->city;
    $translatorApplicationUser->password = Hash::make($generatedPassword);
    $translatorApplicationUser->is_authorized = $request->is_authorized;
    $translatorApplicationUser->authorization_id = $request->authorization_id;
    $translatorApplicationUser->created_by_user_id = 1;
    $translatorApplicationUser->updated_by_user_id = 1;
    $translatorApplicationUser->save();

    // Add From Languages
    $fromLanguages = $request->from_translation_languages;
    $translatorApplicationUser->fromTranslationLanguages()->attach($fromLanguages, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    // Add To Languages
    $toLanguages = $request->to_translation_languages;
    $translatorApplicationUser->toTranslationLanguages()->attach($toLanguages, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    // Add Categories
    $categories = $request->translation_categories;
    $translatorApplicationUser->translationCategories()->attach($categories, [
      'updated_at' => Carbon::now(),
      'created_at' => Carbon::now()
    ]);

    $translatorRole = Role::find(2);
    $translatorApplicationUser->roles()->attach($translatorRole, ['created_by_user_id' => 1, 'updated_by_user_id' => 1]);

    Mail::to(env('ADMIN_EMAIL'))->send(new NewTranslatorForApprovalEmail($translatorApplicationUser));

    return TranslatorApplicationTransformer::make($translatorApplicationUser);
  }
}
