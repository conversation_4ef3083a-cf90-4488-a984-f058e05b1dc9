<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

use Inertia\Inertia;

use Softbox\SBX\Admin\Models\Language;

use App\Http\Requests\TranslationCategoryCreateRequest;
use App\Http\Requests\TranslationCategoryEditRequest;

use App\Models\TranslationCategory;
use App\Models\TranslationCategoryLocalization;
use App\Models\TranslationCategoryMarket;

use App\Http\Resources\TranslationCategoryTransformer;
use App\Http\Resources\TranslationCategoryListTransformer;

use Softbox\SBX\Admin\Shared\Locale;

use Auth;
use Carbon\Carbon;
use Log;

class TranslationCategoriesController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get All Translation Categories
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {

    // Search
    $filters['search'] = $request->search;
    $search = $request->search;

    // Active
    $isActive = true;
    $filters['active'] = $isActive;
    if ($request->has('active')) {
      if ($request->active == 'true') {
        $isActive = true;
        $filters['active'] = true;
      } else {
        $isActive = false;
        $filters['active'] = false;
      }
    }

    // Start Query
    $translationCategoriesQuery = TranslationCategory::query();

    // Search
    $translationCategoriesQuery
      ->when($request->search, function($query, $search) use ($languageCode) {
        $query->whereHas('localizations', function ($query) use ($languageCode, $search) {
          $query->where('language_code', $languageCode)->where('name', 'like', "%{$search}%");
        });
      });

    // Active
    $translationCategoriesQuery->whereHas('markets', function ($query) use ($marketCode, $isActive) {
      $query->where('market_code', $marketCode)->where('is_active', $isActive);
    });

    $translationCategoriesQuery
      ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
      ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
      ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active');

    // Resolve query
    $translationCategories = $translationCategoriesQuery
      ->orderBy('name')
      ->paginate(20)
      ->onEachSide(2)
      ->withQueryString();

    return Inertia::render('TranslationCategories/TranslationCategories', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translationCategories' => TranslationCategoryListTransformer::collection($translationCategories),
      'filters' => $filters
    ]);
  }



  /*
  |--------------------------------------------------------------------------
  | Create Translation Category
  |--------------------------------------------------------------------------
  */

  public function create(Request $request, $marketCode, $languageCode) {
    return Inertia::render('TranslationCategories/CreateTranslationCategory', [
      'locale' => Locale::resolve($marketCode, $languageCode)
    ]);
  }


  public function store(TranslationCategoryCreateRequest $request, $marketCode, $languageCode) {

    // Create Translation Category
    $translationCategory = new TranslationCategory;
    $translationCategory->created_by_user_id = Auth::id();
    $translationCategory->updated_by_user_id = Auth::id();
    $translationCategory->save();

    // Create Translation Category Localization
    $translationCategoryLocalization = new TranslationCategoryLocalization;
    $translationCategoryLocalization->translation_category_id = $translationCategory->id;
    $translationCategoryLocalization->language_code = $languageCode;
    $translationCategoryLocalization->name = $request->name;
    $translationCategoryLocalization->created_by_user_id = Auth::id();
    $translationCategoryLocalization->updated_by_user_id = Auth::id();
    $translationCategoryLocalization->save();

    // Create Translation Category Market
    $translationCategoryMarket = new TranslationCategoryMarket;
    $translationCategoryMarket->translation_category_id = $translationCategory->id;
    $translationCategoryMarket->market_code = $marketCode;
    $translationCategoryMarket->is_active = $request->is_active;
    $translationCategoryMarket->created_by_user_id = Auth::id();
    $translationCategoryMarket->updated_by_user_id = Auth::id();
    $translationCategoryMarket->save();

    return redirect()->route('translation_categories', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }



  /*
  |--------------------------------------------------------------------------
  | Edit Translation Category
  |--------------------------------------------------------------------------
  */

  public function edit($marketCode, $languageCode, TranslationCategory $translationCategory) {
    $resolvedTranslationCategory = TranslationCategory::where('translation_categories.id', $translationCategory->id)
      ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
      ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
      ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active')
      ->first();

    return Inertia::render('TranslationCategories/EditTranslationCategory', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'translationCategory' => TranslationCategoryTransformer::make($resolvedTranslationCategory)
    ]);
  }


  public function update(TranslationCategoryEditRequest $request, $marketCode, $languageCode, TranslationCategory $translationCategory) {

    // Update Translation Cateogry
    $translationCategory->updated_by_user_id = Auth::id();
    $translationCategory->save();

    // Update Translation Cateogry Localization
    $localization = TranslationCategoryLocalization::where('translation_category_id', $translationCategory->id)->where('language_code', $languageCode)->first();

    if ($localization) {
      $localization->name = $request->name;
      $localization->updated_by_user_id = Auth::id();
      $localization->save();
    }

    // Update Translation Cateogry Market
    $market = TranslationCategoryMarket::where('translation_category_id', $translationCategory->id)->where('market_code', $marketCode)->first();

    if ($market) {
      $market->is_active = $request->is_active;
      $market->updated_by_user_id = Auth::id();
      $market->save();
    }

    return redirect()->route('translation_categories', ['marketCode' => $marketCode, 'languageCode' => $languageCode]);
  }



  /*
  |--------------------------------------------------------------------------
  | Delete Translation Category
  |--------------------------------------------------------------------------
  */

  public function destroy(Request $request, $marketCode, $languageCode, TranslationCategory $translationCategory) {
    $localization = TranslationCategoryLocalization::where('translation_category_id', $translationCategory->id)->where('language_code', $languageCode)->first();
    $market = TranslationCategoryMarket::where('translation_category_id', $translationCategory->id)->where('market_code', $marketCode)->first();

    if ($localization) {
      $localization->delete();
    }

    if ($market) {
      $market->delete();
    }

    $translationCategory->delete();

    return Redirect::back()->with('success', 'Translation category deleted.');
  }
}
