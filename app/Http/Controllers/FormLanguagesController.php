<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;

use App\Models\TranslationLanguage;
use App\Http\Resources\TranslationLanguageListTransformer;

class FormLanguagesController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Get Active Translation Languages
  |--------------------------------------------------------------------------
  */

  public function languages(Request $request) {
    $languages = TranslationLanguage::active('se')->orderBy('name')->get();

    return TranslationLanguageListTransformer::collection($languages);
  }
}
