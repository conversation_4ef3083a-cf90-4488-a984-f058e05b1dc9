<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

use Inertia\Inertia;

use Softbox\SBX\Admin\Shared\Locale;

use Softbox\SBX\Admin\Models\Language;

use Softbox\SBX\Webshop\Models\Product;
use Softbox\SBX\Webshop\Models\Currency;
use Softbox\SBX\Webshop\Models\Order;
use Softbox\SBX\Webshop\Models\OrderRow;
use Softbox\SBX\Admin\Models\Setting;
use Softbox\SBX\HelloSMS\Jobs\SendSMS;

use App\Http\Resources\OrderConfirmationTransformer;
use App\Models\TranslationAssignment;
use App\Models\TranslationAssignmentBid;

use App\Mail\ReceiptCustomerEmail;
use App\Mail\AssignmentPaymentReceivedCustomerEmail;
use App\Mail\AssignmentRejectedEmail;
use App\Mail\AssignmentWonEmail;
use App\Mail\AssignmentLostEmail;

use App;
use Auth;
use Carbon\Carbon;
use Log;
use Mail;
use PhpParser\Node\Expr\Assign;
use Url;


class SveaCheckoutController extends Controller
{

    /*
  |--------------------------------------------------------------------------
  | Create Order
  |--------------------------------------------------------------------------
  */

    //TODO: Remove this function
    public function createOrder(Request $request, $marketCode, $languageCode, Product $product, $discountApplies)
    {
        $productLocalization = $product->localizations()->where('language_code', $request->languageCode)->first();
        $productMarket = $product->markets()->where('market_code', $request->marketCode)->first();

        $currency = Currency::whereHas('markets', function (Builder $query) use ($marketCode) {
            $query->where('market_code', $marketCode)->where('is_default', true)->where('is_active', true);
        })->first();

        $checkoutMerchantId = env('SVEA_MERCHANT_ID');
        $checkoutSecret = env('SVEA_CHECKOUT_SECRET');
        $baseUrl = \Svea\Checkout\Transport\Connector::TEST_BASE_URL;
        if (App::environment() == 'production') {
            $baseUrl = \Svea\Checkout\Transport\Connector::PROD_BASE_URL;
        }

        $price = intval($product->shownPrice($marketCode, $currency) * 100);

        // Verify Discount
        $discount = false;
        if ($discountApplies == 1) {
            $discountActiveSetting = Setting::find(2);
            $discountActiveSettingMarket = $discountActiveSetting->markets()->where('market_code', $request->marketCode)->first();
            $discountActive = $discountActiveSettingMarket->boolean_value;

            if ($discountActive) {
                if (!Auth::user()->discount_used) {
                    Auth::user()->discount_used = true;
                    Auth::user()->save();

                    // Apply 15% discount
                    $discount = true;
                    $price = $price * 0.85;
                }
            }
        }

        $email = '<EMAIL>';
        $postalCode = '99999';

        if (App::environment() == 'production') {
            $email = Auth::user()->email;
            $postalCode = Auth::user()->postal_code;
        }

        try {
            $connector = \Svea\Checkout\Transport\Connector::init($checkoutMerchantId, $checkoutSecret, $baseUrl);
            $checkoutClient = new \Svea\Checkout\CheckoutClient($connector);

            $data = [
                "countryCode" => "SE",
                "currency" => "SEK",
                "locale" => "sv-SE",
                "clientOrderNumber" => rand(10000, 30000000),
                "merchantData" => "Översättare.nu",

                "cart" => [
                    "items" => [
                        [
                            "articleNumber" => $product->id,
                            "name" => $productLocalization->name,
                            "quantity" => 100,
                            "unitPrice" => $price,
                            "vatPercent" => 2500,
                            "unit" => "st",
                            "temporaryReference" => $product->id,
                            "merchantData" => "Lead quantity: " . $product->credit_quantity
                        ],
                    ]
                ],

                "presetValues" => [
                    [
                        "typeName" => "emailAddress",
                        "value" => $email,
                        "isReadonly" => false
                    ],

                    [
                        "typeName" => "postalCode",
                        "value" => $postalCode,
                        "isReadonly" => false
                    ]
                ],

                "merchantSettings" => [
                    "termsUri" => env('SVEA_MERCHANT_TERMS_URI'),
                    "checkoutUri" => env('SVEA_MERCHANT_CHECKOUT_URI'),
                    "confirmationUri" => env('SVEA_MERCHANT_CONFIRMATION_URI'),
                    "pushUri" => env('SVEA_MERCHANT_PUSH_URI'),
                ]
            ];

            // Log::channel('slack')->info('SveaCheckoutController - DATA', [
            //   'data' => $data
            // ]);

            $response = $checkoutClient->create($data);

            $guiSnippet = $response['Gui']['Snippet'];
            $orderId = $response['OrderId'];
            $orderStatus = $response['Status'];

            session(['order_id' => $orderId]);
            session(['discount' => $discount]);
            session(['order_status' => $orderStatus]);

            // Log::channel('slack')->info('SveaCheckoutController - SESSION VARIABLES START', [
            //   'orderId' => $orderId,
            //   'orderStatus' => $orderStatus
            // ]);

            echo $guiSnippet;
        } catch (\Svea\Checkout\Exception\SveaApiException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Api errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (\Svea\Checkout\Exception\SveaConnectorException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Conn errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (\Svea\Checkout\Exception\SveaInputValidationException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Input data errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (Exception $ex) {
            Log::channel('slack')->info('SveaCheckoutController - General errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        }
    }

    public function createOfferOrder(Request $request, $marketCode, $languageCode, TranslationAssignment $assignment, Product $product, TranslationAssignmentBid $bid)
    {
        $productLocalization = $product->localizations()->where('language_code', $request->languageCode)->first();
        $productMarket = $product->markets()->where('market_code', $request->marketCode)->first();

        $currency = Currency::whereHas('markets', function (Builder $query) use ($marketCode) {
            $query->where('market_code', $marketCode)->where('is_default', true)->where('is_active', true);
        })->first();

        $vatRate = $product->shownVATRate($marketCode);

        $checkoutMerchantId = env('SVEA_MERCHANT_ID');
        $checkoutSecret = env('SVEA_CHECKOUT_SECRET');
        $baseUrl = \Svea\Checkout\Transport\Connector::TEST_BASE_URL;
        if (App::environment() == 'production') {
            $baseUrl = \Svea\Checkout\Transport\Connector::PROD_BASE_URL;
        }

        $email = '<EMAIL>';
        $postalCode = '99999';
        if (App::environment() == 'production') {
            $email = $assignment->email;
            //$postalCode = Auth::user()->postal_code;
        }

        try {
            $connector = \Svea\Checkout\Transport\Connector::init($checkoutMerchantId, $checkoutSecret, $baseUrl);
            $checkoutClient = new \Svea\Checkout\CheckoutClient($connector);

            $data = [
                "countryCode" => "SE",
                "currency" => "SEK",
                "locale" => "sv-SE",
                "clientOrderNumber" => $assignment->assignment_id,
                "merchantData" => "Översättare.nu",

                "cart" => [
                    "items" => [
                        [
                            "articleNumber" => $product->id,
                            "name" => $productLocalization->name,
                            "quantity" => 100, //unit in minor
                            "unitPrice" => intval($bid->price_end_customer * (1 + $vatRate / 100) * 100), //unit in minor with VAT
                            "vatPercent" => intval($vatRate * 100), //unit in minor
                            "unit" => "st",
                            "temporaryReference" => $bid->id,
                            "merchantData" => "{\"bid_id\":" . $bid->id . "}"
                        ],
                    ]
                ],

                "presetValues" => [
                    [
                        "typeName" => "emailAddress",
                        "value" => $email,
                        "isReadonly" => false
                    ],
                    [
                        "typeName" => "postalCode",
                        "value" => $postalCode,
                        "isReadonly" => false
                    ],
                ],

                "merchantSettings" => [
                    "termsUri" => env('SVEA_MERCHANT_TERMS_URI'),
                    "checkoutUri" => url()->current(),
                    "confirmationUri" => url("/se/sv/customer/$assignment->assignment_id/$assignment->email/payment/$bid->id/confirmation"),
                    "pushUri" => env('SVEA_MERCHANT_PUSH_URI'),
                ]
            ];

            // Log::channel('slack')->info('SveaCheckoutController - DATA', [
            //   'data' => $data
            // ]);

            $response = $checkoutClient->create($data);

            $guiSnippet = $response['Gui']['Snippet'];
            $orderId = $response['OrderId'];
            $orderStatus = $response['Status'];

            session(['order_id' => $orderId]);
            session(['order_status' => $orderStatus]);

            // Log::channel('slack')->info('SveaCheckoutController - SESSION VARIABLES START', [
            //   'orderId' => $orderId,
            //   'orderStatus' => $orderStatus
            // ]);

            echo $guiSnippet;
        } catch (\Svea\Checkout\Exception\SveaApiException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Api errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
            return Redirect::back()->with('error', 'Något gick fel. Vänligen försök igen.');
        } catch (\Svea\Checkout\Exception\SveaConnectorException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Conn errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
            return Redirect::back()->with('error', 'Något gick fel. Vänligen försök igen.');
        } catch (\Svea\Checkout\Exception\SveaInputValidationException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Input data errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
            return Redirect::back()->with('error', 'Något gick fel. Vänligen försök igen.');
        } catch (Exception $ex) {
            Log::channel('slack')->info('SveaCheckoutController - General errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
            return Redirect::back()->with('error', 'Något gick fel. Vänligen försök igen.');
        }
    }

    /*
  |--------------------------------------------------------------------------
  | Order Confirmation
  |--------------------------------------------------------------------------
  */

    public function orderConfirmation(Request $request, $marketCode, $languageCode)
    {
        $orderId = session('order_id');
        $discount = session('discount');
        $sveaOrder = NULL;

        // Log::channel('slack')->info('SveaCheckoutController - SESSION VARIABLES CONFIRMATION', [
        //   'orderId' => $orderId
        // ]);

        $checkoutMerchantId = env('SVEA_MERCHANT_ID');
        $checkoutSecret = env('SVEA_CHECKOUT_SECRET');
        $baseUrl = \Svea\Checkout\Transport\Connector::TEST_BASE_URL;
        if (App::environment() == 'production') {
            $baseUrl = \Svea\Checkout\Transport\Connector::PROD_BASE_URL;
        }

        try {
            $connector = \Svea\Checkout\Transport\Connector::init($checkoutMerchantId, $checkoutSecret, $baseUrl);
            $checkoutClient = new \Svea\Checkout\CheckoutClient($connector);

            $data = ['orderId' => $orderId];

            $sveaOrderResponse = $checkoutClient->get($data);

            $sveaOrder = $sveaOrderResponse;
        } catch (\Svea\Checkout\Exception\SveaApiException $ex) {
            Log::channel('stack')->info('SveaCheckoutController - Api errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (\Svea\Checkout\Exception\SveaConnectorException $ex) {
            Log::channel('stack')->info('SveaCheckoutController - Conn errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (\Svea\Checkout\Exception\SveaInputValidationException $ex) {
            Log::channel('stack')->info('SveaCheckoutController - Input data errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (Exception $ex) {
            Log::channel('stack')->info('SveaCheckoutController - General errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        }

        $displayedOrder = NULL;

        if ($sveaOrder) {
            // Check if order already exists
            $existingOrder = Order::where('external_payment_reference', $sveaOrder['OrderId'])->first();
            $displayedOrder = $existingOrder;

            if (!$existingOrder) {
                // Resolve Currency
                $currency = Currency::whereHas('markets', function (Builder $query) use ($marketCode) {
                    $query->where('market_code', $marketCode)->where('is_default', true)->where('is_active', true);
                })->first();

                // Resolve Product
                $product = Product::query()
                    ->join('product_markets', function (JoinClause $join) use ($marketCode) {
                        $join->on('products.id', '=', 'product_markets.product_id')
                            ->where('product_markets.market_code', $marketCode);
                    })
                    ->join('product_localizations', function (JoinClause $join) use ($languageCode) {
                        $join->on('products.id', '=', 'product_localizations.product_id')
                            ->where('product_localizations.language_code', $languageCode);
                    })
                    ->select('products.*', 'product_markets.is_active', 'product_markets.vat_rate_id', 'product_localizations.name')
                    ->where('products.id', $sveaOrder['Cart']['Items'][0]['ArticleNumber'])
                    ->first();

                // Create Order
                $unitPrice = $product->shownPrice($marketCode, $currency);
                $unitVAT = $product->shownVAT($marketCode, $currency);

                $vatRate = $product->shownVATRate($marketCode);

                $order = new Order;
                $orderNo = Order::all()->max('order_no') + 1;
                if ($orderNo < 10000) {
                    $orderNo = 10000;
                }
                $order->order_no = $orderNo;
                $order->ordered_at = Carbon::now();
                $order->confirmed_at = Carbon::now();
                $order->is_completed = true;
                $order->user_id = Auth::id();
                $order->created_by_user_id = Auth::id();
                $order->updated_by_user_id = Auth::id();
                $order->first_name = Auth::user()->first_name;
                $order->last_name = Auth::user()->last_name;
                $order->email = Auth::user()->email;
                $order->phone_no = Auth::user()->phone_no;
                $order->company_name = Auth::user()->company;
                $order->address_line_1 = Auth::user()->address_1;
                $order->address_line_2 = Auth::user()->address_2;
                $order->postal_code = Auth::user()->postal_code;
                $order->city = Auth::user()->city;
                $order->country_code = Auth::user()->country_code;
                $order->vat_included = true;
                $order->currency_id = $currency->id;
                $order->currency = $currency->name;
                $order->order_total = $unitPrice;
                $order->order_total_vat = $unitVAT;
                $order->is_invoice_order = ($sveaOrder['PaymentType'] == 'INVOICE');
                $order->is_delivered = true;
                $order->external_payment_reference = $sveaOrder['OrderId'];
                $order->external_order_json = json_encode($sveaOrder);
                $order->save();

                $orderRow = new OrderRow;
                $orderRow->order_id = $order->id;
                $orderRow->product_id = $product->id;
                $orderRow->product_name = $product->name;
                $orderRow->product_meta_1 = $product->credit_quantity;
                $orderRow->vat_rate_id = $product->vat_rate_id;
                $orderRow->vat_rate = $vatRate;
                $orderRow->quantity = 1;
                $orderRow->unit_price = $unitPrice;
                $orderRow->unit_vat = $unitVAT;
                $orderRow->row_total = $unitPrice;
                $orderRow->row_total_vat = $unitVAT;
                $orderRow->created_by_user_id = Auth::id();
                $orderRow->updated_by_user_id = Auth::id();
                $orderRow->save();

                $displayedOrder = $order;

                // Update User Lead Count
                Auth::user()->credit_count = Auth::user()->credit_count + $product->credit_quantity;
                Auth::user()->updated_by_user_id = Auth::id();
                Auth::user()->save();

                // Send Receipt Email
                Mail::to(Auth::user())->send(new ReceiptEmail(Auth::user(), $order));
            }
        }

        return Inertia::render('TranslationStore/OrderConfirmation', [
            'locale' => Locale::resolve($marketCode, $languageCode),
            'order' => OrderConfirmationTransformer::make($displayedOrder)
        ]);
    }


    public function offerOrderConfirmation(Request $request, $marketCode, $languageCode)
    {
        $orderId = session('order_id');
        $sveaOrder = NULL;

        // Log::channel('slack')->info('SveaCheckoutController - SESSION VARIABLES CONFIRMATION', [
        //   'orderId' => $orderId
        // ]);

        $checkoutMerchantId = env('SVEA_MERCHANT_ID');
        $checkoutSecret = env('SVEA_CHECKOUT_SECRET');
        $baseUrl = \Svea\Checkout\Transport\Connector::TEST_BASE_URL;

        if (App::environment() == 'production') {
            $baseUrl = \Svea\Checkout\Transport\Connector::PROD_BASE_URL;
        }

        try {
            $connector = \Svea\Checkout\Transport\Connector::init($checkoutMerchantId, $checkoutSecret, $baseUrl);
            $checkoutClient = new \Svea\Checkout\CheckoutClient($connector);

            $data = ['orderId' => $orderId];

            $sveaOrderResponse = $checkoutClient->get($data);

            $sveaOrder = $sveaOrderResponse;
        } catch (\Svea\Checkout\Exception\SveaApiException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Api errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (\Svea\Checkout\Exception\SveaConnectorException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Conn errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (\Svea\Checkout\Exception\SveaInputValidationException $ex) {
            Log::channel('slack')->info('SveaCheckoutController - Input data errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        } catch (Exception $ex) {
            Log::channel('slack')->info('SveaCheckoutController - General errors', [
                'ErrorMessage' => $ex->getMessage(),
                'ErrorCode' => $ex->getCode()
            ]);
        }

        $displayedOrder = NULL;

        if ($sveaOrder) {
            // Check if order already exists
            $existingOrder = Order::where('external_payment_reference', $sveaOrder['OrderId'])->first();
            $displayedOrder = $existingOrder;

            if (!$existingOrder) {
                // Resolve Currency
                $currency = Currency::whereHas('markets', function (Builder $query) use ($marketCode) {
                    $query->where('market_code', $marketCode)->where('is_default', true)->where('is_active', true);
                })->first();

                // Resolve Product
                $product = Product::query()
                    ->join('product_markets', function (JoinClause $join) use ($marketCode) {
                        $join->on('products.id', '=', 'product_markets.product_id')
                            ->where('product_markets.market_code', $marketCode);
                    })
                    ->join('product_localizations', function (JoinClause $join) use ($languageCode) {
                        $join->on('products.id', '=', 'product_localizations.product_id')
                            ->where('product_localizations.language_code', $languageCode);
                    })
                    ->select('products.*', 'product_markets.is_active', 'product_markets.vat_rate_id', 'product_localizations.name')
                    ->where('products.id', $sveaOrder['Cart']['Items'][0]['ArticleNumber'])
                    ->first();

                // Create Order
                $unitVAT = $product->shownVAT($marketCode, $currency);
                $vatRate = $product->shownVATRate($marketCode);
                $orderTotal = $this->calculateOrderTotal($sveaOrder['Cart']['Items']);

                $order = new Order;
                $order->order_no = $sveaOrder['ClientOrderNumber'];
                $order->ordered_at = Carbon::now();
                $order->confirmed_at = Carbon::now();
                $order->is_completed = true;

                // Get data from the Svea Order
                $order->first_name = $sveaOrder['BillingAddress']['FirstName'];
                $order->last_name = $sveaOrder['BillingAddress']['LastName'];
                $order->email = $sveaOrder['EmailAddress'];
                $order->phone_no = $sveaOrder['PhoneNumber'];
                $order->company_name = $sveaOrder['Customer']['IsCompany'] ? $sveaOrder['BillingAddress']['FullName'] : '';
                $order->address_line_1 = $sveaOrder['BillingAddress']['StreetAddress'];
                $order->address_line_2 = $sveaOrder['BillingAddress']['StreetAddress2'];
                $order->postal_code = $sveaOrder['BillingAddress']['PostalCode'];
                $order->city = $sveaOrder['BillingAddress']['City'];
                $order->country_code = $sveaOrder['BillingAddress']['CountryCode'];

                $order->delivery_full_name = $sveaOrder['ShippingAddress']['FullName'];
                $order->delivery_address_line_1 = $sveaOrder['ShippingAddress']['StreetAddress'];
                $order->delivery_address_line_2 = $sveaOrder['ShippingAddress']['StreetAddress2'];
                $order->delivery_postal_code = $sveaOrder['ShippingAddress']['PostalCode'];
                $order->delivery_city = $sveaOrder['ShippingAddress']['City'];
                $order->delivery_country_code = $sveaOrder['ShippingAddress']['CountryCode'];

                $order->vat_included = true;
                $order->currency_id = $currency->id;
                $order->currency = $currency->name;
                $order->order_total = $orderTotal;
                $order->order_total_vat = round($orderTotal - $orderTotal / (1 + $vatRate / 100), 2);
                $order->is_invoice_order = ($sveaOrder['PaymentType'] == 'INVOICE');
                $order->is_delivered = true;
                $order->external_payment_reference = $sveaOrder['OrderId'];
                $order->external_order_json = json_encode($sveaOrder);
                $order->save();

                foreach ($sveaOrder['Cart']['Items'] as $item) {

                    $unitPrice = $item['UnitPrice'] / 100;
                    $unitVAT = round($unitPrice - $unitPrice / (1 + $vatRate / 100), 2);

                    $orderRow = new OrderRow;
                    $orderRow->order_id = $order->id;
                    $orderRow->product_id = $item['ArticleNumber'];
                    $orderRow->product_name = $item['Name'];
                    $orderRow->product_meta_1 = $item['MerchantData'];
                    $orderRow->vat_rate_id = $product->vat_rate_id;
                    $orderRow->vat_rate = $vatRate;
                    $orderRow->quantity = $item['Quantity'] / 100;
                    $orderRow->unit_price = $unitPrice;
                    $orderRow->unit_vat = $unitVAT;
                    $orderRow->row_total = ($item['UnitPrice'] * ($item['Quantity'] / 100)) / 100;
                    $orderRow->row_total_vat = $unitVAT * ($item['Quantity'] / 100);
                    $orderRow->save();
                }

                $displayedOrder = $order;
                $assignment = TranslationAssignment::where('assignment_id', $sveaOrder['ClientOrderNumber'])->first();

                // Update the bid to accepted
                $bidId = json_decode($sveaOrder['Cart']['Items'][0]['MerchantData'])->bid_id;
                $bid = TranslationAssignmentBid::find($bidId);
                $bid->is_accepted = true;
                $bid->order_id = $sveaOrder['ClientOrderNumber'];
                $bid->accepted_at = Carbon::now();
                $bid->save();

                $translator = $bid->translator;
                // EMAIL: Notify the translator that the bid has been accepted
                Mail::to($translator->email)->send(new AssignmentWonEmail($assignment));
                Mail::to("<EMAIL>")->send(new AssignmentWonEmail($assignment));
                Mail::to("<EMAIL>")->send(new AssignmentWonEmail($assignment));

                // SMS: Notify the translator that the bid has been accepted
                if ($translator->allow_text_messages) {
                    $messageText = "Du har blivit tilldelad ett nytt översättningsuppdrag." . PHP_EOL . PHP_EOL;
                    $messageText .= "Logga in för detaljer:" . PHP_EOL . PHP_EOL . $assignment->assignmentLink . ".";

                    SendSMS::dispatch($translator->id, $translator->phone_no, $messageText, env('HELLO_SMS_TEST_MODE'));
                }


                // EMAIL: Send receipt to the customer
                Mail::to($assignment->email)->send(new ReceiptCustomerEmail(Order::where("external_payment_reference", $sveaOrder['OrderId'])->first()));
                // EMAIL: Notify the translators where their bid was rejected
                $rejectedBids = TranslationAssignmentBid::where('translation_assignment_id', $assignment->id)->whereNull('is_accepted')->get();
                foreach ($rejectedBids as $rejectedBid) {
                    Mail::to($rejectedBid->translator->email)->send(new AssignmentLostEmail($assignment));
                }
            }

            $assignment = TranslationAssignment::where('assignment_id', $sveaOrder['ClientOrderNumber'])->first();
            $bidId = json_decode($sveaOrder['Cart']['Items'][0]['MerchantData'])->bid_id;
            $bid = TranslationAssignmentBid::find($bidId);
        }

        return redirect()->route('assignment', ['marketCode' => $marketCode, 'languageCode' => $languageCode, 'translationAssignmentID' => $request->route('translationAssignmentID'), 'customerEmail' => $request->route('customerEmail')]);
    }

    /*
  |--------------------------------------------------------------------------
  | Push Order
  |--------------------------------------------------------------------------
  */

    public function pushOrder(Request $request, $marketCode, $languageCode, $sveaOrderID)
    {
        Log::channel('slack')->info('SveaCheckoutController - pushOrder', [
            'sveaOrderID' => $sveaOrderID
        ]);
    }


    /*
  |--------------------------------------------------------------------------
  | Helpers
  |--------------------------------------------------------------------------
  */

    private function calculateOrderTotal($orderRows)
    {
        $total = 0;

        foreach ($orderRows as $item) {
            $total += ($item['UnitPrice'] * ($item['Quantity'] / 100)) / 100;
        }

        return $total;
    }
}
