<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Inertia\Inertia;

use Softbox\SBX\Admin\Shared\Locale;

use App\Models\User;

use App\Http\Resources\UserTransformer;

use Log;

class UsersController extends Controller {

  /*
  |--------------------------------------------------------------------------
  | Index
  |--------------------------------------------------------------------------
  */

  public function index(Request $request, $marketCode, $languageCode) {
    $users = User::paginate(20)
      ->onEachSide(2)
      ->withQueryString();

    return Inertia::render('Users/Users', [
      'locale' => Locale::resolve($marketCode, $languageCode),
      'users' => UserTransformer::collection($users)
    ]);
  }
}
