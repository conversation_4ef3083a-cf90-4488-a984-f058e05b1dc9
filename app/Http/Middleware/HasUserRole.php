<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

use Auth;

class HasUserRole {

  /*
  |--------------------------------------------------------------------------
  | Handle
  |--------------------------------------------------------------------------
  */

  public function handle(Request $request, Closure $next) {
    $roles = Auth::user()->roles;

    $rolesCheck = false;
    foreach($roles as $role) {
      if ($role->id == 1 || $role->id == 2) {
        $rolesCheck = true;
        break;
      }
    }

    if ($rolesCheck) {
      return $next($request);
    }

    return redirect('/');
  }
}
