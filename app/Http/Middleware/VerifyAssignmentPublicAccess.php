<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\TranslationAssignment;

class VerifyAssignmentPublicAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        $assignment = TranslationAssignment::where('assignment_id', $request->route('translationAssignmentID'))->where('email', $request->route('customerEmail'))->where('is_active', 1)->first();

        if (!$assignment) {
            return response()->json(['message' => 'Assignment not found'], 404);
        }

        return $next($request);
    }
}
