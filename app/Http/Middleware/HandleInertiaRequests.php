<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Inertia\Middleware;

use App\Models\TranslationAssignment;
use Softbox\SBX\Admin\Models\Setting;
use App\Models\User;

use Auth;

class HandleInertiaRequests extends Middleware {

  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  protected $rootView = 'app';



  /*
  |--------------------------------------------------------------------------
  | Version
  |--------------------------------------------------------------------------
  */

  public function version(Request $request): ?string {
    return parent::version($request);
  }



  /*
  |--------------------------------------------------------------------------
  | Store
  |--------------------------------------------------------------------------
  */

  public function share(Request $request): array {
    $isAdmin = false;
    $credits = 0;
    $assignmentCount = 0;

    if (Auth::check()) {
      $roles = Auth::user()->roles;

      if (count($roles) > 0) {
        $role = $roles[0];

        if ($role->is_admin) {
          $isAdmin = true;
        }
      }

      $credits = Auth::user()->credit_count;

      $unmatchedAssignments = TranslationAssignment::unmatchedAssignmentsForUser('se', Auth::user())->get();

      $assignmentCount = count($unmatchedAssignments);
    }

    $unapprovedAssignmentCount = 0;
    $unapprovedTranslatorCount = 0;

    if ($isAdmin) {
      // Unapproved Translators
      $translatorApplicationsQuery = User::query();
      $translatorApplicationsQuery->whereHas('roles', function ($query) {
        $query->where('user_roles.role_id', 2);
      });
      $translatorApplicationsQuery->where('is_approved', false);
      $translatorApplications = $translatorApplicationsQuery->get();

      $unapprovedTranslatorCount = count($translatorApplications);

      // UnapprovedAssignments
      $translationAssignmentsQuery = TranslationAssignment::query();
      $translationAssignmentsQuery->where('is_approved', false);
      $translationAssignments = $translationAssignmentsQuery->get();

      $unapprovedAssignmentCount = count($translationAssignments);
    }

    return array_merge(parent::share($request), [
      'page_info' => [
        'title_label' => 'Page Title'
      ],
      'flash' => [
        'authorization' => fn () => $request->session()->get('authorization'),
        'svea_checkout_response' => fn () => $request->session()->get('svea_checkout_response')
      ],
      'isAdmin' => $isAdmin,
      'credits' => $credits,
      'assignment_count' => $assignmentCount,
      'unapproved_assignments_count' => $unapprovedAssignmentCount,
      'unapproved_translators_count' => $unapprovedTranslatorCount
    ]);
  }
}
