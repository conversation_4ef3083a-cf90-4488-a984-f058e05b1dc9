<?php

namespace App\Http\Middleware;

use Illuminate\Http\Middleware\TrustHosts as Middleware;

class TrustHosts extends Middleware {

  /*
  |--------------------------------------------------------------------------
  | Hosts
  |--------------------------------------------------------------------------
  */

  public function hosts() {
    return [
      $this->allSubdomainsOfApplicationUrl(),
    ];
  }
}
