<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\TranslationAssignment;

class VerifyAssignmentPublicAccessPreMarketBid
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        $assignment = TranslationAssignment::where('assignment_id', $request->route('translationAssignmentID'))->where('email', $request->route('customerEmail'))->where('is_active', 1)->where('is_approved', 0)->first();

        if (!$assignment) {
            return response()->json(['message' => 'Assignment not found'], 404);
        }

        if ($assignment->premarket_bid()->whereNotNull('is_accepted')->first()) {
            return response()->json(['message' => 'Premarket bid already approved'], 403);
        }

        return $next($request);
    }
}
