<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Log;

class TranslationCategoryShowTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $localization = $this->localizations()->where('language_code', $request->languageCode)->first();

    return [
      'id' => $this->id,
      'name' => $localization->name
    ];
  }
}
