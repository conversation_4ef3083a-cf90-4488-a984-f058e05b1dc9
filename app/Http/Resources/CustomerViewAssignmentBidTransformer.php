<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationLanguage;

use Arr;
use Log;

class CustomerViewAssignmentBidTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'is_authorized' => $this->translator->is_authorized,
            'price' => $this->price_end_customer,
            'estimated_delivery' => $this->estimated_delivery,
            'is_accepted' => $this->is_accepted,
            'accepted_at' => $this->accepted_at ? $this->accepted_at->format('Y-m-d H:i') : null,
            'payment_complete' => $this->order ? $this->order->is_completed : false,
            'payment_information' => $this->order ? PaymentTransformer::make($this->order) : null,
            'created_at' => $this->created_at->format('Y-m-d H:i')
        ];
    }
}
