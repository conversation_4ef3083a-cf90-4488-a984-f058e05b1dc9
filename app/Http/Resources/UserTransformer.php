<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Log;

class UserTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $role = $this->roles[0];
    $roleLocalization = $role->localizations()->where('language_code', $request->languageCode)->first();

    return [
      'id' => $this->id,
      'translator_id' => $this->translator_id,
      'first_name' => $this->first_name,
      'last_name' => $this->last_name,
      'name' => $this->first_name . ' ' . $this->last_name,
      'email' => $this->email,
      'phone_no' => $this->phone_no,
      'sales_person_id' => $this->sales_person_id,
      'role_id' => $role->id,
      'role_name' => $roleLocalization->name
    ];
  }
}
