<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;
use Softbox\SBX\Admin\Models\Setting;

use DB;
use Log;

class TranslationAssignmentFileTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    return [
      'id' => $this->id,
      'filename' => $this->filename,
      'original_filename' => $this->original_filename,
      'url' => url('assignments/' . $this->filename)
    ];
  }
}
