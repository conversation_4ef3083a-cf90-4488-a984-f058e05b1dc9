<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Log;

class TranslationAssignmentTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'assignment_id' => $this->assignment_id,
            'translation_category_id' => $this->translation_category_id,
            'from_translation_language_id' => $this->from_translation_language_id,
            'to_translation_language_id' => $this->to_translation_language_id,
            'is_authorization_required' => (bool)$this->is_authorization_required,
            'number_of_words' => (string)$this->number_of_words,
            'assignment_type' => $this->assignment_type,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'company' => $this->company,
            'company_no' => $this->company_no,
            'email' => $this->email,
            'phone_no' => $this->phone_no,
            'notes' => $this->notes,
            'is_email_contact_allowed' => $this->is_email_contact_allowed,
            'is_phone_contact_allowed' => $this->is_phone_contact_allowed,
            'last_delivery_date' => $this->last_delivery,
            'files' => TranslationAssignmentFileTransformer::collection($this->files)
        ];
    }
}
