<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Log;

class TranslationProfileTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $role = $this->roles[0];

    return [
      'id' => $this->id,
      'translator_id' => $this->translator_id,
      'company' => $this->company,
      'company_no' => $this->identity_no,
      'first_name' => $this->first_name,
      'last_name' => $this->last_name,
      'name' => $this->first_name . ' ' . $this->last_name,
      'address_1' => $this->address_1,
      'address_2' => $this->address_2,
      'postal_code' => $this->postal_code,
      'city' => $this->city,
      'email' => $this->email,
      'phone_no' => $this->phone_no,
      'allow_text_messages' => $this->allow_text_messages,
      'from_translation_languages' => TranslationLanguageShowTransformer::collection($this->fromTranslationLanguages),
      'to_translation_languages' => TranslationLanguageShowTransformer::collection($this->toTranslationLanguages),
      'translation_categories' => TranslationCategoryShowTransformer::collection($this->translationCategories)
    ];
  }
}
