<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Database\Eloquent\Builder;

use Softbox\SBX\Webshop\Models\Currency;

use Log;

class TranslationProductStoreTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $marketCode = $request->marketCode;

    $localization = $this->localizations()->where('language_code', $request->languageCode)->first();
    $market = $this->markets()->where('market_code', $request->marketCode)->first();
    $currency = Currency::whereHas('markets', function (Builder $query) use ($marketCode) {
      $query->where('market_code', $marketCode)->where('is_default', true)->where('is_active', true);
    })->first();

    return [
      'id' => $this->id,
      'name' => $localization->name,
      'short_description' => $localization->short_description,
      'price' => $this->shownPrice($marketCode, $currency),
      'credit_quantity' => $this->credit_quantity
    ];
  }
}
