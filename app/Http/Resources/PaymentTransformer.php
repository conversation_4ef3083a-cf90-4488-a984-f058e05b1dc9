<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Auth;

class PaymentTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'date' => $this->confirmed_at->format('Y-m-d H:i'),
            'price' => $this->order_total,
            'vat' => $this->order_total_vat,
            'rows' => OrderRowTransformer::collection($this->rows),
            'billing_address' => [
                'name' => $this->first_name . ' ' . $this->last_name,
                'address' => $this->address_line_1 . ' ' . $this->address_line_2,
                'zip' => $this->postal_code,
                'city' => $this->city,
                'country' => $this->country_code,
            ],
            'delivery_address' => [
                'name' => $this->delivery_full_name,
                'address' => $this->delivery_address_line_1 . ' ' . $this->delivery_address_line_2,
                'zip' => $this->delivery_postal_code,
                'city' => $this->delivery_city,
                'country' => $this->delivery_country_code,
            ],
        ];
    }
}
