<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;

use Log;

class TranslationAssignmentEmailTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        $translationCategory = TranslationCategory::where('translation_categories.id', $this->translation_category_id)
            ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
            ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
            ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active')
            ->first();

        $fromLanguage = TranslationLanguage::where('translation_languages.id', $this->from_translation_language_id)
            ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->first();

        $toLanguage = TranslationLanguage::where('translation_languages.id', $this->to_translation_language_id)
            ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->first();

        return [
            'id' => $this->id,
            'assignment_id' => $this->assignment_id,
            'category' => $translationCategory->name,
            'from_language' => "$fromLanguage->name",
            'to_language' => $toLanguage->name,
            'is_authorization_required' => (bool)$this->is_authorization_required
        ];
    }
}
