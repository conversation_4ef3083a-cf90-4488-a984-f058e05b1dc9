<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TranslatorTransformer extends JsonResource
{

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request)
  {
    return [
      'id' => $this->id,
      'translator_id' => $this->translator_id,
      'company' => $this->company,
      'company_no' => $this->identity_no,
      'first_name' => $this->first_name,
      'last_name' => $this->last_name,
      'name' => $this->first_name . ' ' . $this->last_name,
      'address_1' => $this->address_1,
      'address_2' => $this->address_2,
      'postal_code' => $this->postal_code,
      'city' => $this->city,
      'email' => $this->email,
      'phone_no' => $this->phone_no,
      'is_authorized' => (bool)$this->is_authorized,
      'authorization_id' => $this->authorization_id,
      'from_translation_languages' => TranslationLanguageListTransformer::collection($this->fromTranslationLanguages),
      'to_translation_languages' => TranslationLanguageListTransformer::collection($this->toTranslationLanguages),
      'translation_categories' => TranslationCategoryListTransformer::collection($this->translationCategories),
      'leads_count' => $this->credit_count,
      'allow_text_messages' => $this->allow_text_messages
    ];
  }
}
