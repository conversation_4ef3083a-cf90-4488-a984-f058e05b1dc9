<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;
use Softbox\SBX\Admin\Models\Setting;

use DB;
use Log;

class CustomerViewAssignmentTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        $translationCategory = TranslationCategory::where('translation_categories.id', $this->translation_category_id)
            ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
            ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
            ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active')
            ->first();

        $fromLanguage = TranslationLanguage::where('translation_languages.id', $this->from_translation_language_id)
            ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->first();

        $toLanguage = TranslationLanguage::where('translation_languages.id', $this->to_translation_language_id)
            ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->first();

        $maxBidsSetting = Setting::find(1);
        $maxBidsSettingMarket = $maxBidsSetting->markets()->where('market_code', $request->marketCode)->first();
        $maxBids = 10;
        if ($maxBidsSettingMarket) {
            $maxBids = $maxBidsSettingMarket->integer_value;
        }

        $bidsLeft = $maxBids - count($this->bids);
        $bidsDeadlineSetting = Setting::find(4);
        $bidsDeadlineSettingMarket = $bidsDeadlineSetting->markets()->where('market_code', $request->marketCode)->first();


        if ($bidsDeadlineSettingMarket) {
            $bidsDeadline = $this->approved_at->addDays($bidsDeadlineSettingMarket->integer_value);
        } else {
            $bidsDeadline = $this->approved_at->addDays(3);
        }

        $assignmentDeadlineSetting = Setting::find(5);
        $assignmentDeadlineSettingMarket = $assignmentDeadlineSetting->markets()->where('market_code', $request->marketCode)->first();

        if ($assignmentDeadlineSettingMarket) {
            $assignmentDeadline = $this->approved_at->addDays($assignmentDeadlineSettingMarket->integer_value);
        } else {
            $assignmentDeadline = $this->approved_at->addDays(6);
        }

        $bidAccepted = $this->bids->where('is_accepted', 1)->count() > 0;
        $bestBid = $this->getBestBid();

        return [
            'id' => $this->id,
            'assignment_id' => $this->assignment_id,
            'assignment_type' => $this->assignment_type,
            'created_date' => $this->created_at->format('Y-m-d'),
            'created_time' => $this->created_at->format('H:i'),
            'approved_at' => $this->approved_at ? $this->approved_at->format('Y-m-d H:i') : null,
            'completed_at' => $this->completed_at ? $this->completed_at->format('Y-m-d H:i') : null,
            'deadline' => $assignmentDeadline->format('Y-m-d H:i'),
            'translation_category_id' => $this->translation_category_id,
            'translation_category' => $translationCategory->name,
            'from_translation_language_id' => $this->from_translation_language_id,
            'from_translation_language' => $fromLanguage->name,
            'to_translation_language_id' => $this->to_translation_language_id,
            'to_translation_language' => $toLanguage->name,
            'is_authorization_required' => (bool)$this->is_authorization_required,
            'number_of_words' => (string)$this->number_of_words,
            'notes' => $this->notes,
            'bid_count' => $bidAccepted ? null : count($this->bids),
            'bids_left' => $bidAccepted ? null : $bidsLeft,
            'bids_allowed' => $maxBids,
            'bids_deadline' => $bidAccepted ? null : $bidsDeadline->format('Y-m-d H:i'),
            'last_delivery_date' => $this->last_delivery,
            'files_count' => count($this->files),
            'bid_accepted' => $bidAccepted,
            'accepted_bid' => $bidAccepted ? CustomerViewAssignmentBidTransformer::make($this->bids->where('is_accepted', 1)->first()) : null,
            'answered_by' => $bidAccepted ? null : CustomerViewAssignmentBidTransformer::make($bestBid),
            'next_best_bid' => $bidAccepted ? null : CustomerViewAssignmentBidTransformer::make($this->getFastestDeliveryBid()),
            'translated_files' => TranslationAssignmentFileTransformer::collection($this->files()->GetTranslatedFiles()->get()),
            'customer_email' => $this->email
        ];
    }
}
