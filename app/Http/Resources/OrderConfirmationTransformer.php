<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Auth;

class OrderConfirmationTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    return [
      'id' => $this->id,
      'order_no' => $this->order_no,
      'user_credit_count' => Auth::user()->credit_count,
      'rows' => OrderRowTransformer::collection($this->rows)
    ];
  }
}
