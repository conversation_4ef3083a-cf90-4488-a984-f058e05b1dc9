<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationLanguage;

use Arr;
use Log;

class TranslatorListTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $fromLanguages = Arr::pluck($this->fromTranslationLanguageNames, 'name');
    $toLanguages = Arr::pluck($this->toTranslationLanguageNames, 'name');
    $categories = Arr::pluck($this->translationCategoryNames, 'name');

    sort($fromLanguages);
    sort($toLanguages);
    sort($categories);

    $fromLanguages = implode(', ', $fromLanguages);
    $toLanguages = implode(', ', $toLanguages);
    $categories = implode(', ', $categories);

    return [
      'id' => $this->id,
      'translator_id' => $this->translator_id,
      'company' => $this->company,
      'company_no' => $this->identity_no,
      'first_name' => $this->first_name,
      'last_name' => $this->last_name,
      'name' => $this->first_name . ' ' . $this->last_name,
      'address_1' => $this->address_1,
      'address_2' => $this->address_2,
      'postal_code' => $this->postal_code,
      'city' => $this->city,
      'email' => $this->email,
      'phone_no' => $this->phone_no,
      'is_authorized' => $this->is_authorized,
      'authorization_id' => $this->authorization_id,
      'from_translation_languages' => $fromLanguages,
      'to_translation_languages' => $toLanguages,
      'translation_categories' => $categories,
      'created_at' => $this->created_at->format('Y-m-d H:i'),
      'credit_count' => $this->credit_count
    ];
  }
}
