<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;
use Softbox\SBX\Admin\Models\Setting;

use Auth;
use DB;
use Log;

class OrderRowTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'product_name' => $this->product_name,
            'credit_count' => $this->product_meta_1,
            'product_meta' => $this->product_meta_1,
            'vat_rate' => $this->vat_rate,
            'quantity' => $this->quantity,
            'unit_price' => $this->unit_price,
            'unit_vat' => $this->unit_vat,
            'row_total' => $this->row_total,
            'row_total_vat' => $this->row_total_vat
        ];
    }
}
