<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;

use Log;

class TranslationAssignmentListTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $translationCategory = TranslationCategory::where('translation_categories.id', $this->translation_category_id)
      ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
      ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
      ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active')
      ->first();

    $fromLanguage = TranslationLanguage::where('translation_languages.id', $this->from_translation_language_id)
      ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
      ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
      ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
      ->first();

    $toLanguage = TranslationLanguage::where('translation_languages.id', $this->to_translation_language_id)
      ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
      ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
      ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
      ->first();


    return [
      'id' => $this->id,
      'assignment_id' => $this->assignment_id,
      'translation_category_id' => $this->translation_category_id,
      'translation_category' => $translationCategory->name,
      'from_translation_language_id' => $this->from_translation_language_id,
      'from_translation_language' => $fromLanguage->name,
      'to_translation_language_id' => $this->to_translation_language_id,
      'to_translation_language' => $toLanguage->name,
      'is_authorization_required' => (bool)$this->is_authorization_required,
      'authorization_text' => $this->authorizedText,
      'number_of_words' => (string)$this->number_of_words,
      'first_name' => $this->first_name,
      'last_name' => $this->last_name,
      'company' => $this->company,
      'company_no' => $this->company_no,
      'email' => $this->email,
      'phone_no' => $this->phone_no,
      'notes' => $this->notes,
      'number_of_bids' => count($this->bids),
      'number_of_views' => count($this->views),
      'is_active' => $this->is_active,
      'assignment_type' => $this->assignment_type,
      'created_at' => $this->created_at->format('Y-m-d H:i')
    ];
  }
}
