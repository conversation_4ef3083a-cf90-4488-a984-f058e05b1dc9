<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Log;

class TranslationProductListTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $localization = $this->localizations()->where('language_code', $request->languageCode)->first();
    $market = $this->markets()->where('market_code', $request->marketCode)->first();

    return [
      'id' => $this->id,
      'name' => $localization->name,
      'is_active' => (bool)$market->is_active
    ];
  }
}
