<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationLanguage;

use Arr;
use Log;

class TranslationAssignmentViewTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    return [
      'id' => $this->id,
      'translator_id' => $this->translator->translator_id,
      'company' => $this->translator->company,
      'first_name' => $this->translator->first_name,
      'last_name' => $this->translator->last_name,
      'name' => $this->translator->first_name . ' ' . $this->translator->last_name,
      'email' => $this->translator->email,
      'is_authorized' => $this->translator->is_authorized,
      'created_at' => $this->created_at->format('Y-m-d H:i')
    ];
  }
}
