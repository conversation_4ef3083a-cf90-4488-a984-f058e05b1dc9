<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;

use Log;

class TranslatorApplicationTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    return [
      'id' => $this->id,
      'translator_id' => $this->translator_id,
      'company' => $this->company,
      'company_no' => $this->identity_no,
      'first_name' => $this->first_name,
      'last_name' => $this->last_name,
      'address_1' => $this->address_1,
      'address_2' => $this->address_2,
      'postal_code' => $this->postal_code,
      'city' => $this->city,
      'email' => $this->email,
      'phone_no' => $this->phone_no,
      'authorization_id' => $this->authorization_id,
      'services' => TranslationCategoryShowTransformer::collection($this->translationCategories),
      'from_languages' => TranslationLanguageShowTransformer::collection($this->fromTranslationLanguages),
      'to_languages' => TranslationLanguageShowTransformer::collection($this->toTranslationLanguages)
    ];
  }
}
