<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use App\Models\TranslationCategory;
use App\Models\TranslationLanguage;
use Softbox\SBX\Admin\Models\Setting;

use Auth;
use DB;
use Log;

class TranslationAssignmentMarketTransformer extends JsonResource
{

    /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

    public function toArray($request)
    {
        $translationCategory = TranslationCategory::where('translation_categories.id', $this->translation_category_id)
            ->join('translation_category_localizations', 'translation_categories.id', '=', 'translation_category_localizations.translation_category_id')
            ->join('translation_category_markets', 'translation_categories.id', '=', 'translation_category_markets.translation_category_id')
            ->select('translation_categories.*', 'translation_category_localizations.name', 'translation_category_markets.is_active')
            ->first();

        $fromLanguage = TranslationLanguage::where('translation_languages.id', $this->from_translation_language_id)
            ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->first();

        $toLanguage = TranslationLanguage::where('translation_languages.id', $this->to_translation_language_id)
            ->join('translation_language_localizations', 'translation_languages.id', '=', 'translation_language_localizations.translation_language_id')
            ->join('translation_language_markets', 'translation_languages.id', '=', 'translation_language_markets.translation_language_id')
            ->select('translation_languages.*', 'translation_language_localizations.name', 'translation_language_markets.is_active')
            ->first();

        $maxBidsSetting = Setting::find(1);
        $maxBidsSettingMarket = $maxBidsSetting->markets()->where('market_code', $request->marketCode)->first();
        $maxBids = 3;
        if ($maxBidsSettingMarket) {
            $maxBids = $maxBidsSettingMarket->integer_value;
        }

        $bidsLeft = $maxBids - count($this->bids);

        // Start Query
        $userHasBid = $this->bids()->where('user_id', Auth::id())->first();

        return [
            'id' => $this->id,
            'assignment_id' => $this->assignment_id,
            'assignment_type' => $this->assignment_type,
            'created_date' => $this->created_at->format('Y-m-d'),
            'created_time' => $this->created_at->format('H:i'),
            'approved_date' => $this->approved_at ? $this->approved_at->format('Y-m-d') : NULL,
            'translation_category_id' => $this->translation_category_id,
            'translation_category' => $translationCategory->name,
            'from_translation_language_id' => $this->from_translation_language_id,
            'from_translation_language' => $fromLanguage->name,
            'to_translation_language_id' => $this->to_translation_language_id,
            'to_translation_language' => $toLanguage->name,
            'is_authorization_required' => (bool)$this->is_authorization_required,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'company' => $this->company,
            'company_no' => $this->company_no,
            'email' => $this->email,
            'notes' => $this->notes,
            'bid_count' => count($this->bids),
            'bids_left' => $bidsLeft,
            'biding_closed_date' => $this->approved_at ? $this->approved_at->addDays(3)->format('Y-m-d') : NULL,
            'user_has_bid' => (bool)($userHasBid != NULL),
            'last_delivery_date' => $this->last_delivery,
            'files' => TranslationAssignmentFileTransformer::collection($this->files)
        ];
    }
}
