<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

use Log;

class TranslationProductBasicInformationTransformer extends JsonResource {

  /*
  |--------------------------------------------------------------------------
  | Transform
  |--------------------------------------------------------------------------
  */

  public function toArray($request) {
    $localization = $this->localizations()->where('language_code', $request->languageCode)->first();

    return [
      'id' => $this->id,
      'product_unit_id' => $this->product_unit_id,
      'name' => $localization->name,
      'short_description' => $localization->short_description,
      'long_description' => $localization->long_description
    ];
  }
}
