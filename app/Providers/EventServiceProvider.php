<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider {

  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  protected $listen = [
    Registered::class => [
      SendEmailVerificationNotification::class,
    ],
  ];



  /*
  |--------------------------------------------------------------------------
  | Boot
  |--------------------------------------------------------------------------
  */

  public function boot() {
  }



  /*
  |--------------------------------------------------------------------------
  | Discover Events
  |--------------------------------------------------------------------------
  */

  public function shouldDiscoverEvents() {
    return false;
  }
}
