<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;


class AppServiceProvider extends ServiceProvider
{

    /*
  |--------------------------------------------------------------------------
  | Register
  |--------------------------------------------------------------------------
  */

    public function register()
    {
        if ($this->app->environment('local') || $this->app->environment('staging') && class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /*
  |--------------------------------------------------------------------------
  | Boot
  |--------------------------------------------------------------------------
  */

    public function boot() {}
}
