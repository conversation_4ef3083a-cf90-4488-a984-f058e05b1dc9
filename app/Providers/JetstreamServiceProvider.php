<?php

namespace App\Providers;

use App\Actions\Jetstream\DeleteUser;
use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Jetstream\Jetstream;

class JetstreamServiceProvider extends ServiceProvider {

  /*
  |--------------------------------------------------------------------------
  | Register
  |--------------------------------------------------------------------------
  */

  public function register() {
  }



  /*
  |--------------------------------------------------------------------------
  | Boot
  |--------------------------------------------------------------------------
  */

  public function boot() {
    $this->configurePermissions();

    Jetstream::deleteUsersUsing(DeleteUser::class);
  }



  /*
  |--------------------------------------------------------------------------
  | Permissions
  |--------------------------------------------------------------------------
  */

  protected function configurePermissions() {
    Jetstream::defaultApiTokenPermissions(['read']);

    Jetstream::permissions([
      'create',
      'read',
      'update',
      'delete',
    ]);
  }
}
