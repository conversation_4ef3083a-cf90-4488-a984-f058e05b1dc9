<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Laravel\Horizon\Horizon;
use Laravel\Horizon\HorizonApplicationServiceProvider;

class HorizonServiceProvider extends HorizonApplicationServiceProvider
{

    /*
  |--------------------------------------------------------------------------
  | Boot
  |--------------------------------------------------------------------------
  */

    public function boot()
    {
        parent::boot();

        // Horizon::routeSmsNotificationsTo('15556667777');
        // Horizon::routeMailNotificationsTo('<EMAIL>');
        // Horizon::routeSlackNotificationsTo('slack-webhook-url', '#channel');

        // Horizon::night();
    }



    /*
  |--------------------------------------------------------------------------
  | Gate
  |--------------------------------------------------------------------------
  */

    protected function gate()
    {
        Gate::define('viewHorizon', function ($user) {
            return in_array($user->email, [
                '<EMAIL>',
                '<EMAIL>'
            ]);
        });
    }
}
