<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\Mailgun\Models\UserEmail;

class AssignmentPublishedEmail extends Mailable {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use Queueable, SerializesModels;



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  public $assignment;



  /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

  public function __construct($assignment) {
    $this->assignment = $assignment;
  }



  /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

  public function envelope() {
    return new Envelope(
      subject: __('oversattare.assignment_published_email_subject'),
      tags: ['assignment_published_email'],
      metadata: [
        'assignment_id' => $this->assignment->id
      ],
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

  public function content() {
    return new Content(
      view: 'emails.assignment_published'
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

  public function attachments() {
    return [];
  }
}
