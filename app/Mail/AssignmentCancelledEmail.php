<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AssignmentCancelledEmail extends Mailable
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use Queueable, SerializesModels;



    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct($assignment)
    {
        $this->assignment = $assignment;
    }



    /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

    public function envelope()
    {
        return new Envelope(
            subject: __('oversattare.cancelled_assignment_email_subject') . '#' . $this->assignment->assignment_id,
            tags: ['assignment_cancelled_email'],
            metadata: []
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

    public function content()
    {
        return new Content(
            view: 'emails.assignment_cancelled'
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

    public function attachments()
    {
        return [];
    }
}
