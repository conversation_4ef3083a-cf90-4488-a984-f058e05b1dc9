<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PreMarketPriceCustomerEmail extends Mailable
{
    use Queueable, SerializesModels;

    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;


    /**
     * Create a new message instance.
     */
    public function __construct($assignment)
    {
        $this->assignment = $assignment;
    }
    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: __('oversattare.new_offer_email_subject'),
            tags: ['assignment_pre_market_offer_received_email'],
            metadata: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content()
    {
        return new Content(
            view: 'emails.premarket_offer_customer',
            with: ['premarketBid' => $this->assignment->premarket_bid()->first()]

        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
