<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\Mailgun\Models\UserEmail;
use App\Models\User;

class ReceiptEmail extends Mailable {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use Queueable, SerializesModels;



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  public $user;
  public $order;



  /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

  public function __construct(User $user, $order = NULL) {
    $this->user = $user;
    $this->order = $order;
  }



  /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

  public function envelope() {
    $email = new UserEmail;
    $email->user_id = $this->user->id;
    $email->type = "ReceiptEmail";
    $email->save();

    return new Envelope(
      subject: __('oversattare.receipt_email_subject'),
      tags: ['receipt_email'],
      metadata: [
        'user_id' => $this->user->id,
        'email_id' => $email->id
      ],
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

  public function content() {
    return new Content(
      view: 'emails.receipt'
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

  public function attachments() {
    return [];
  }
}
