<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AssignmentLostEmail extends Mailable
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use Queueable, SerializesModels;



    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct($assignment)
    {
        $this->assignment = $assignment;
    }



    /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

    public function envelope()
    {
        return new Envelope(
            subject: __('oversattare.lost_assignment_email_subject') . '#' . $this->assignment->assignment_id,
            tags: ['assignment_lost_email'],
            metadata: []
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

    public function content()
    {
        return new Content(
            view: 'emails.assignment_lost'
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

    public function attachments()
    {
        return [];
    }
}
