<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\Mailgun\Models\UserEmail;
use App\Models\User;

class NewAssignmentEmail extends Mailable {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use Queueable, SerializesModels;



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  public $user;
  public $assignment;



  /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

  public function __construct(User $user, $assignment = NULL) {
    $this->user = $user;
    $this->assignment = $assignment;
  }



  /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

  public function envelope() {
    $email = new UserEmail;
    $email->user_id = $this->user->id;
    $email->type = "NewAssignmentEmail";
    $email->save();

    return new Envelope(
      subject: __('oversattare.new_assignment_email_subject') . ' ' . $this->assignment->category->localizations()->where('language_code', 'sv')->first()->name,
      tags: ['new_assignment_email'],
      metadata: [
        'user_id' => $this->user->id,
        'email_id' => $email->id
      ],
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

  public function content() {
    return new Content(
      view: 'emails.new_assignment'
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

  public function attachments() {
    return [];
  }
}
