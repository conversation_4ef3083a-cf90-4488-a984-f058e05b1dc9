<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use App\Models\User;

class AssignmentExpiredCustomerEmail extends Mailable
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use Queueable, SerializesModels;



    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct($assignment)
    {
        $this->assignment = $assignment;
    }



    /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

    public function envelope()
    {
        return new Envelope(
            subject: __('oversattare.assignment_expired_customer_email_subject'),
            tags: ['application_expired_email'],
            metadata: []
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

    public function content()
    {
        return new Content(
            view: 'emails.assignment_expired_customer'
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

    public function attachments()
    {
        return [];
    }
}
