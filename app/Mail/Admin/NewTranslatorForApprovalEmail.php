<?php

namespace App\Mail\Admin;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\Mailgun\Models\UserEmail;
use App\Models\User;

class NewTranslatorForApprovalEmail extends Mailable {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use Queueable, SerializesModels;



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  public $user;



  /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

  public function __construct($user) {
    $this->user = $user;
  }



  /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

  public function envelope() {
    return new Envelope(
      subject: __('oversattare.new_translator_for_approval_email_subject'),
      tags: ['new_translator_for_approval_email'],
      metadata: [
        'user_id' => $this->user->id
      ],
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

  public function content() {
    return new Content(
      view: 'emails.admin.new_translator_for_approval'
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

  public function attachments() {
    return [];
  }
}
