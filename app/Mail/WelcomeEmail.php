<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\Mailgun\Models\UserEmail;
use App\Models\User;

class WelcomeEmail extends Mailable {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use Queueable, SerializesModels;



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  public $user;
  public $password;



  /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

  public function __construct(User $user, $password = NULL) {
    $this->user = $user;
    $this->password = $password;
  }



  /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

  public function envelope() {
    $email = new UserEmail;
    $email->user_id = $this->user->id;
    $email->type = "WelcomeEmail";
    $email->save();

    return new Envelope(
      subject: __('oversattare.welcome_email_subject'),
      tags: ['welcome_email'],
      metadata: [
        'user_id' => $this->user->id,
        'email_id' => $email->id
      ],
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

  public function content() {
    return new Content(
      view: 'emails.welcome'
    );
  }



  /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

  public function attachments() {
    return [];
  }
}
