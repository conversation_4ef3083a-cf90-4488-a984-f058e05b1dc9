<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AssignmentPreMarketBidRejectedEmail extends Mailable
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use Queueable, SerializesModels;


    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $assignment;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct($assignment)
    {
        $this->assignment = $assignment;
    }



    /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

    public function envelope()
    {
        return new Envelope(
            subject: __('oversattare.assignment_premarket_bid_rejected_subject'),
            tags: ['assignment_premarket_rejected_email'],
            metadata: []
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

    public function content()
    {
        return new Content(
            view: 'emails.assignment_premarket_bid_rejected'
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

    public function attachments()
    {
        return [];
    }
}
