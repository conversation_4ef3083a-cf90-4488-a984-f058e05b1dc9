<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use Softbox\SBX\Mailgun\Models\UserEmail;
use App\Models\User;

class ReceiptCustomerEmail extends Mailable
{

    /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

    use Queueable, SerializesModels;



    /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

    public $order;



    /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

    public function __construct($order = NULL)
    {
        $this->order = $order;
    }



    /*
  |--------------------------------------------------------------------------
  | Envelope
  |--------------------------------------------------------------------------
  */

    public function envelope()
    {

        return new Envelope(
            subject: __('oversattare.receipt_email_subject'),
            tags: ['receipt_email'],

        );
    }



    /*
  |--------------------------------------------------------------------------
  | Content
  |--------------------------------------------------------------------------
  */

    public function content()
    {
        return new Content(
            view: 'emails.receipt'
        );
    }



    /*
  |--------------------------------------------------------------------------
  | Attachments
  |--------------------------------------------------------------------------
  */

    public function attachments()
    {
        return [];
    }
}
