<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

use App\Models\User;

class ResetPasswordNotification extends Notification {

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  use Queueable;



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  public $user;
  public $token;



  /*
  |--------------------------------------------------------------------------
  | Contructor
  |--------------------------------------------------------------------------
  */

  public function __construct(User $user, $token) {
    $this->user = $user;
    $this->token = $token;
  }



  /*
  |--------------------------------------------------------------------------
  | Via
  |--------------------------------------------------------------------------
  */

  public function via(object $notifiable): array {
    return ['mail'];
  }



  /*
  |--------------------------------------------------------------------------
  | Mail
  |--------------------------------------------------------------------------
  */

  public function toMail(object $notifiable): MailMessage {
    return (new MailMessage)
      ->subject(__('oversattare.reset_password_email_subject'))
      ->view(
        'emails.reset_password', ['resetURL' => url('/reset-password/' . $this->token . '?email=' . $this->user->email)]
      );
  }



  /*
  |--------------------------------------------------------------------------
  | Array Conversion
  |--------------------------------------------------------------------------
  */

  public function toArray(object $notifiable): array {
    return [];
  }
}
