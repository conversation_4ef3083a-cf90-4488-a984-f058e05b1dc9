<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{

    /*
  |--------------------------------------------------------------------------
  | Svhedule
  |--------------------------------------------------------------------------
  */

    protected function schedule(Schedule $schedule)
    {
        //$schedule->command('cache:prune-stale-tags')->hourly();
        $schedule->command('app:send-reminder-for-assignment-with-bids')->dailyAt('08:30');
        $schedule->command('app:check-for-expired-assignments')->hourly();
        $schedule->command('app:check-for-passed-offer-assignments')->everyMinute();
    }



    /*
  |--------------------------------------------------------------------------
  | Commands
  |--------------------------------------------------------------------------
  */

    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
