<?php

namespace App\Console\Commands;

use App\Jobs\CheckExpiredAssignments;
use Illuminate\Console\Command;

class CheckForExpiredAssignments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-for-expired-assignments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for assignments that are expired and send reminder emails.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        CheckExpiredAssignments::dispatch();
        $this->info('Deactivated expired assignments and sent expiration emails.');
    }
}
