<?php

namespace App\Console\Commands;

use App\Jobs\CheckPassedOfferAssignments;
use Illuminate\Console\Command;

class CheckForPassedOfferAssignments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-for-passed-offer-assignments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for assignments that the offer window expired';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        CheckPassedOfferAssignments::dispatch();
        $this->info('Send bid to user.');
    }
}
