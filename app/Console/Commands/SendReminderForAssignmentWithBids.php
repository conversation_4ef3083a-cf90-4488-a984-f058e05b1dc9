<?php

namespace App\Console\Commands;

use App\Jobs\SendBidReminderEmail;
use Illuminate\Console\Command;

class SendReminderForAssignmentWithBids extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-reminder-for-assignment-with-bids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for assignments with bids and send reminders to customers.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        SendBidReminderEmail::dispatch();
        $this->info('Reminder emails & sms dispatched for open assignments.');
    }
}
