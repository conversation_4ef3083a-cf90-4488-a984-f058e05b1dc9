<?php

namespace Tests\Feature;

use Tests\TestCase;
use Mockery;
use Softbox\SBX\Admin\Models\Setting;
use App\Http\Controllers\TranslationAssignmentMarketController;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use App\Models\TranslationAssignment;
use App\Models\TranslationAssignmentBid;
use App\Mail\PreMarketPriceCustomerEmail;
use Softbox\SBX\HelloSMS\Jobs\SendSMS;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TranslationAssignmentMarketControllerTest extends TestCase
{
    public function testCalculateEndCustomerPrice()
    {
        $controller = new TranslationAssignmentMarketController();

        // Mock the Setting model
        $settingMock = Mockery::mock(Setting::class);

        $this->partialMock(Setting::class, function ($mock) use ($settingMock) {
            $mock->shouldReceive('find')->with(3)->andReturn($settingMock);
        });

        $marketQueryMock = Mockery::mock();
        $settingMock->shouldReceive('markets')->andReturn($marketQueryMock);

        $marketQueryMock->shouldReceive('where')->with('market_code', 'se')->andReturnSelf();
        $marketQueryMock->shouldReceive('first')->andReturn((object)[
            'json_value' => json_encode([
                'markup_levels' => [
                    'consumer' => [
                        ['type' => 'percentage', 'fee' => 325, 'markup' => 0, 'min_value' => 0],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 30, 'min_value' => 501],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 25, 'min_value' => 1001],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 20, 'min_value' => 2001],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 18, 'min_value' => 3001],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 15, 'min_value' => 5001],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 12, 'min_value' => 7501],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 11, 'min_value' => 10001],
                        ['type' => 'percentage', 'fee' => 175, 'markup' => 10, 'min_value' => 15001]
                    ],
                    'company' => [
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 0, 'min_value' => 0],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 30, 'min_value' => 501],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 25, 'min_value' => 1001],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 20, 'min_value' => 2001],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 18, 'min_value' => 3001],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 15, 'min_value' => 5001],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 12, 'min_value' => 7501],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 11, 'min_value' => 10001],
                        ['type' => 'percentage', 'fee' => 400, 'markup' => 10, 'min_value' => 15001]
                    ]
                ]
            ])
        ]);

        $this->app->instance(Setting::class, $settingMock);

        $testCasesConsumer = [
            300 => 625,
            501 => 826.3,
            1001 => 1426.25,
            2001 => 2576.2,
            3001 => 3716.18,
            5001 => 5926.15,
            7501 => 8576.12,
            10001 => 11276.11,
            15001 => 16676.1,
        ];

        // Iterate through test cases
        foreach ($testCasesConsumer as $price => $expectedResult) {
            $calculatedPrice = $controller->calculateEndCustomerPrice('se', $price, 'consumer');
            $this->assertEquals($expectedResult, $calculatedPrice, "Failed for price: $price");
        }

        $testCasesCompany = [
            300 => 700,
            501 => 1051.3,
            1001 => 1651.25,
            2001 => 2801.2,
            3001 => 3941.18,
            5001 => 6151.15,
            7501 => 8801.12,
            10001 => 11501.11,
            15001 => 16901.1,
        ];

        foreach ($testCasesCompany as $price => $expectedResult) {
            $calculatedPrice = $controller->calculateEndCustomerPrice('se', $price, 'company');
            $this->assertEquals($expectedResult, $calculatedPrice, "Failed for price: $price");
        }
    }

    public function testAddPremarketBid()
    {
        $controller = new TranslationAssignmentMarketController();

        // Mock the TranslationAssignment model
        $translationAssignmentMock = Mockery::mock(TranslationAssignment::class);
        $translationAssignmentMock->shouldReceive('is_approved')->andReturn(false);
        $translationAssignmentMock->shouldReceive('bids')->andReturnSelf();
        $translationAssignmentMock->shouldReceive('premarket_bids')->andReturnSelf();
        $translationAssignmentMock->shouldReceive('where')->with('is_accepted', 1)->andReturnSelf();
        $translationAssignmentMock->shouldReceive('exists')->andReturn(false);
        $translationAssignmentMock->shouldReceive('id')->andReturn(1);

        // Mock the Request
        $response = $this->put(route('translation_assignments.add_premarket_bid', ['marketCode' => 'se', 'languageCode' => 'sv', 'translationAssignment' => 1]), [
            'price' => 1000
        ]);

        // Mock the Auth facade
        Auth::shouldReceive('id')->andReturn(1);

        // Mock the DB facade
        DB::shouldReceive('table')->with('translation_assignment_premarket_bids')->andReturnSelf();
        DB::shouldReceive('insert')->andReturn(true);

        // Mock the Mail facade
        Mail::shouldReceive('to')->andReturnSelf();
        Mail::shouldReceive('send')->andReturn(true);

        // Assert the response
        $response->assertStatus(302);
    }

    public function testMakeBid()
    {
        $controller = new TranslationAssignmentMarketController();

        // Mock the TranslationAssignment model
        $translationAssignmentMock = Mockery::mock(TranslationAssignment::class);
        $translationAssignmentMock->shouldReceive('bids')->andReturnSelf();
        $translationAssignmentMock->shouldReceive('where')->with('is_accepted', 1)->andReturnSelf();
        $translationAssignmentMock->shouldReceive('exists')->andReturn(false);
        $translationAssignmentMock->shouldReceive('id')->andReturn(1);
        $translationAssignmentMock->shouldReceive('assignment_type')->andReturn('consumer');
        $translationAssignmentMock->shouldReceive('assignment_id')->andReturn(1);
        $translationAssignmentMock->shouldReceive('phone_no')->andReturn('1234567890');
        $translationAssignmentMock->shouldReceive('getAssignmentCustomerLinkAttribute')->andReturn('http://example.com');

        // Mock the Request
        $requestMock = Mockery::mock(Request::class);
        $requestMock->shouldReceive('input')->with('price')->andReturn(1000);
        $requestMock->shouldReceive('input')->with('delivery_date')->andReturn('2023-10-10');

        // Mock the Auth facade
        Auth::shouldReceive('id')->andReturn(1);
        Auth::shouldReceive('user')->andReturn((object)['id' => 1]);

        // Mock the DB facade
        DB::shouldReceive('table')->with('translation_assignment_bids')->andReturnSelf();
        DB::shouldReceive('insert')->andReturn(true);

        // Mock the Mail facade
        Mail::shouldReceive('to')->andReturnSelf();
        Mail::shouldReceive('send')->andReturn(true);

        // Mock the Queue facade
        Queue::fake();

        // Call the method
        $response = $controller->makeBid($requestMock, 'se', 'en', $translationAssignmentMock);

        // Assert the response
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('my_assignments.show', ['marketCode' => 'se', 'languageCode' => 'en', 'translationAssignmentID' => 1]), $response->getTargetUrl());

        // Assert SMS was dispatched
        Queue::assertPushed(SendSMS::class, function ($job) {
            return $job->phone_no === '1234567890';
        });
    }
}
