<?php

/**
 * Test script to verify the many-to-many relationship fix
 * Run this with: php test_relationship_fix.php
 */

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\TranslationLanguage;
use App\Models\TranslationCategory;

// Simulate the data structure that would come from the Vue component
$testData = [
    'from_translation_languages' => [
        ['id' => '1', 'name' => 'Swedish'],
        ['id' => '2', 'name' => 'English']
    ],
    'to_translation_languages' => [
        ['id' => '3', 'name' => 'German'],
        ['id' => '4', 'name' => 'French']
    ],
    'translation_categories' => [
        ['id' => '1', 'name' => 'Legal'],
        ['id' => '2', 'name' => 'Medical']
    ]
];

echo "Testing relationship data processing...\n\n";

// Test the Arr::pluck functionality
echo "From Languages IDs: ";
$fromLanguages = array_map('strval', array_column($testData['from_translation_languages'], 'id'));
print_r($fromLanguages);

echo "To Languages IDs: ";
$toLanguages = array_map('strval', array_column($testData['to_translation_languages'], 'id'));
print_r($toLanguages);

echo "Categories IDs: ";
$categories = array_map('strval', array_column($testData['translation_categories'], 'id'));
print_r($categories);

echo "\nTest completed successfully!\n";
echo "The data structure is now properly formatted for sync() operations.\n";
