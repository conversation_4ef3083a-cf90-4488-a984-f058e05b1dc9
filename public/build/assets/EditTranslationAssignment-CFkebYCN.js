import{_ as A,S as B,c as p,w as u,r,b as g,e as _,d as a,h as f,t as b,F as w,i as X,j as C,g as U,k as T}from"./app-Cm2beRkj.js";import{u as F}from"./index-BKm97uF2.js";import{S as O}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as L}from"./SBXSelect-RMXlX9En.js";import{S as k}from"./SBXInput-C8dZEgZe.js";import{S as N}from"./SBXToggle-CrVi0Yuw.js";import{S as j}from"./SBXTextArea-D09nQvWc.js";import q from"./FileRow-C_c_9UoD.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";import"./PaperClipIcon-7jOC84nb.js";const z={components:{SBXDefaultPageLayout:O,SBXSelect:L,SBXInput:k,SBXToggle:N,SBXTextArea:j,SBXButton:B,FileRow:q},props:{translationAssignment:Object,translationCategories:Object,translationLanguages:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("Assignment",this.translationAssignment.data)},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:F("put",this.route("translation_assignments.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.translationAssignment.data.id]),{translation_category_id:this.translationAssignment.data.translation_category_id,from_translation_language_id:this.translationAssignment.data.from_translation_language_id,to_translation_language_id:this.translationAssignment.data.to_translation_language_id,is_authorization_required:this.translationAssignment.data.is_authorization_required,number_of_words:this.translationAssignment.data.number_of_words,assignment_type:this.translationAssignment.data.assignment_type=="company",first_name:this.translationAssignment.data.first_name,last_name:this.translationAssignment.data.last_name,company:this.translationAssignment.data.company,company_no:this.translationAssignment.data.company_no,email:this.translationAssignment.data.email,phone_no:this.translationAssignment.data.phone_no,notes:this.translationAssignment.data.notes,is_email_contact_allowed:this.translationAssignment.data.is_email_contact_allowed,is_phone_contact_allowed:this.translationAssignment.data.is_phone_contact_allowed})}},computed:{pageTitle(){return`${this.$t("translations.translation_assignments.edit_title")}`},categoryOptions(){var e=[];e.push({value:null,text:this.$t("translations.translation_assignments.category_prompt"),enabled:!1});for(var n=0;n<this.translationCategories.data.length;n++){let s=this.translationCategories.data[n];e.push({value:s.id,text:s.name})}return e},languageOptions(){var e=[];e.push({value:null,text:this.$t("translations.translation_assignments.language_prompt"),enabled:!1});for(var n=0;n<this.translationLanguages.data.length;n++){let s=this.translationLanguages.data[n];e.push({value:s.id,text:s.name})}return e}},methods:{update(){this.form.processing||this.form.submit({preserveScroll:!0,preserveState:!1,onSuccess:e=>{console.log("Page Updated",e)},onError:e=>{console.log("Page Errors",e)}})}}},P={class:"grid grid-cols-2 gap-4"},D={key:0},E={class:"mt-4 text-lg font-semibold text-oversattare-text-black"};function V(e,n,s,I,t,i){const d=r("SBXSelect"),m=r("SBXToggle"),l=r("SBXInput"),v=r("FileRow"),y=r("SBXTextArea"),S=r("SBXButton"),h=r("SBXDefaultPageLayout");return g(),p(h,null,{default:u(()=>[_("form",{onSubmit:n[25]||(n[25]=T((...o)=>i.update&&i.update(...o),["prevent"]))},[_("div",P,[_("div",null,[a(d,{model:t.form.translation_category_id,"onUpdate:model":n[0]||(n[0]=o=>t.form.translation_category_id=o),items:i.categoryOptions,valueFieldName:"value",label:e.$t("translations.translation_assignments.category_label"),onChange:n[1]||(n[1]=o=>t.form.validate("translation_category_id")),error:t.form.errors.translation_category_id},null,8,["model","items","label","error"]),a(d,{model:t.form.from_translation_language_id,"onUpdate:model":n[2]||(n[2]=o=>t.form.from_translation_language_id=o),items:i.languageOptions,valueFieldName:"value",label:e.$t("translations.translation_assignments.from_language_label"),onChange:n[3]||(n[3]=o=>t.form.validate("from_translation_language_id")),error:t.form.errors.from_translation_language_id},null,8,["model","items","label","error"]),a(d,{model:t.form.to_translation_language_id,"onUpdate:model":n[4]||(n[4]=o=>t.form.to_translation_language_id=o),items:i.languageOptions,valueFieldName:"value",label:e.$t("translations.translation_assignments.to_language_label"),onChange:n[5]||(n[5]=o=>t.form.validate("to_translation_language_id")),error:t.form.errors.to_translation_language_id},null,8,["model","items","label","error"]),a(m,{model:t.form.is_authorization_required,"onUpdate:model":n[6]||(n[6]=o=>t.form.is_authorization_required=o),label:e.$t("translations.translation_assignments.is_authorization_required_label"),class:"mt-4"},null,8,["model","label"]),a(l,{model:t.form.number_of_words,"onUpdate:model":n[7]||(n[7]=o=>t.form.number_of_words=o),label:e.$t("translations.translation_assignments.number_of_words_label"),onChange:n[8]||(n[8]=o=>t.form.validate("number_of_words")),error:t.form.errors.number_of_words},null,8,["model","label","error"]),s.translationAssignment.data.files.length>0?(g(),f("div",D,[_("h4",E,b(e.$t("translations.my_assignments.assignment_detail.files_label")),1),(g(!0),f(w,null,X(s.translationAssignment.data.files,o=>(g(),p(v,{file:o},null,8,["file"]))),256))])):C("",!0),a(m,{model:t.form.assignment_type,"onUpdate:model":n[9]||(n[9]=o=>t.form.assignment_type=o),label:e.$t("translations.translation_assignments.company_label"),class:"mt-4"},null,8,["model","label"]),a(l,{model:t.form.first_name,"onUpdate:model":n[10]||(n[10]=o=>t.form.first_name=o),label:e.$t("translations.translation_assignments.first_name_label"),onChange:n[11]||(n[11]=o=>t.form.validate("first_name")),error:t.form.errors.first_name},null,8,["model","label","error"]),a(l,{model:t.form.last_name,"onUpdate:model":n[12]||(n[12]=o=>t.form.last_name=o),label:e.$t("translations.translation_assignments.last_name_label"),onChange:n[13]||(n[13]=o=>t.form.validate("last_name")),error:t.form.errors.last_name},null,8,["model","label","error"]),a(l,{model:t.form.company,"onUpdate:model":n[14]||(n[14]=o=>t.form.company=o),label:e.$t("translations.translation_assignments.company_label"),onChange:n[15]||(n[15]=o=>t.form.validate("company")),error:t.form.errors.company},null,8,["model","label","error"]),a(l,{model:t.form.company_no,"onUpdate:model":n[16]||(n[16]=o=>t.form.company_no=o),label:e.$t("translations.translation_assignments.company_no_label"),onChange:n[17]||(n[17]=o=>t.form.validate("company_no")),error:t.form.errors.company_no},null,8,["model","label","error"]),a(l,{model:t.form.email,"onUpdate:model":n[18]||(n[18]=o=>t.form.email=o),label:e.$t("translations.translation_assignments.email_label"),onChange:n[19]||(n[19]=o=>t.form.validate("email")),error:t.form.errors.email},null,8,["model","label","error"]),a(l,{model:t.form.phone_no,"onUpdate:model":n[20]||(n[20]=o=>t.form.phone_no=o),label:e.$t("translations.translation_assignments.phone_no_label"),onChange:n[21]||(n[21]=o=>t.form.validate("phone_no")),error:t.form.errors.phone_no},null,8,["model","label","error"]),a(y,{model:t.form.notes,"onUpdate:model":n[22]||(n[22]=o=>t.form.notes=o),label:e.$t("translations.translation_assignments.notes_label"),rows:5,error:t.form.errors.notes},null,8,["model","label","error"]),a(m,{model:t.form.is_email_contact_allowed,"onUpdate:model":n[23]||(n[23]=o=>t.form.is_email_contact_allowed=o),label:e.$t("translations.translation_assignments.contact_option_email_label"),class:"mt-4",error:t.form.errors.is_email_contact_allowed},null,8,["model","label","error"]),a(m,{model:t.form.is_phone_contact_allowed,"onUpdate:model":n[24]||(n[24]=o=>t.form.is_phone_contact_allowed=o),label:e.$t("translations.translation_assignments.contact_option_phone_label"),class:"mt-4",error:t.form.errors.is_phone_contact_allowed},null,8,["model","label","error"]),a(S,{class:"mt-4"},{default:u(()=>[U(b(e.$t("translations.translation_assignments.update_button")),1)]),_:1})])])],32)]),_:1})}const nn=A(z,[["render",V]]);export{nn as default};
