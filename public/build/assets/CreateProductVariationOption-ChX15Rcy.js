import{_,S as b,c as g,w as p,r as u,b as h,d as o,e as a,g as d,t as m,k as B}from"./app-Cm2beRkj.js";import{S as P}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S}from"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const F={components:{SBXDefaultPageLayout:P,SBXInput:S,SBXButton:b},props:{productVariationID:Number,productPricePerKg:Number,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("productVariationID: ",this.productVariationID)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:"",quantity:parseFloat(1).toFixed(2),price_adjustment:parseFloat(0).toFixed(2)})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_variations.option_create_title")}`},calculatedPrice(){return parseFloat(parseFloat(parseFloat(this.form.quantity)*this.productPricePerKg)).toFixed(2)},adjustedPrice(){return parseFloat(parseFloat(this.calculatedPrice)+parseFloat(this.form.price_adjustment)).toFixed(2)}},methods:{create(){this.form.processing||this.form.post(this.route("product_variations.options.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productVariationID]))}}},j={class:"mt-4"};function x(s,e,l,y,r,i){const n=u("SBXInput"),f=u("SBXButton"),c=u("SBXDefaultPageLayout");return h(),g(c,null,{default:p(()=>[o(c,null,{default:p(()=>[a("form",{onSubmit:e[3]||(e[3]=B((...t)=>i.create&&i.create(...t),["prevent"]))},[o(n,{model:r.form.name,"onUpdate:model":e[0]||(e[0]=t=>r.form.name=t),label:s.$t("sbxwebshop.product_variations.name_label"),error:l.errors.name},null,8,["model","label","error"]),o(n,{model:r.form.quantity,"onUpdate:model":e[1]||(e[1]=t=>r.form.quantity=t),label:s.$t("sbxwebshop.product_variations.option_quantity_label"),error:l.errors.quantity},null,8,["model","label","error"]),o(n,{model:r.form.price_adjustment,"onUpdate:model":e[2]||(e[2]=t=>r.form.price_adjustment=t),label:s.$t("sbxwebshop.product_variations.option_price_adjustment_label"),error:l.errors.price_adjustment},null,8,["model","label","error"]),a("p",j,[e[4]||(e[4]=a("strong",null,"Pris före justering: ",-1)),d(m(i.calculatedPrice)+" kr",1)]),a("p",null,[e[5]||(e[5]=a("strong",null,"Pris: ",-1)),d(m(i.adjustedPrice)+" kr",1)]),o(f,{class:"mt-4"},{default:p(()=>[d(m(s.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})]),_:1})}const V=_(F,[["render",x]]);export{V as default};
