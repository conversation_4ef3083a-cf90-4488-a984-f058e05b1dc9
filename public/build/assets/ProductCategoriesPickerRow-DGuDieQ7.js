import{r as C}from"./CheckIcon-CSfnhiPS.js";import{_,S as g,h as o,d as t,w as i,g as u,t as l,j as m,r as p,b as s}from"./app-Cm2beRkj.js";const k={components:{SBXButton:g,CheckIcon:C},props:{productCategory:Object,pickedProductCategories:Array},computed:{selected(){return this.pickedProductCategories.findIndex(c=>c.id==this.productCategory.id)!=-1}},methods:{selectRow(){this.selected?this.$emit("productCategoryUnpicked",this.productCategory):this.$emit("productCategoryPicked",this.productCategory)}}},y={key:0},f={key:1};function w(c,r,a,x,B,e){const d=p("CheckIcon"),n=p("SBXButton");return s(),o("div",{onClick:r[0]||(r[0]=(...h)=>e.selectRow&&e.selectRow(...h)),class:"mb-2 flex items-center cursor-pointer"},[e.selected?m("",!0):(s(),o("div",y,[t(n,{class:"mr-2",size:"s",variant:"secondary"},{default:i(()=>[t(d,{class:"text-white w-4 h-4"})]),_:1}),u(l(a.productCategory.name),1)])),e.selected?(s(),o("div",f,[t(n,{class:"mr-2",size:"s",variant:"success"},{default:i(()=>[t(d,{class:"text-white w-4 h-4"})]),_:1}),u(l(a.productCategory.name),1)])):m("",!0)])}const S=_(k,[["render",w]]);export{S as default};
