import{_ as p,S as _,c as f,w as l,r as d,b,e,t as r,l as n,m as i,d as g,g as k,k as h}from"./app-Cm2beRkj.js";import{S as x}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const B={components:{SBXDefaultPageLayout:x,SBXButton:_},props:{market:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.market.data.name,market_code:this.market.data.market_code})}},computed:{pageTitle(){return`${this.$t("sbxcms.global.edit")} - ${this.market.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("markets.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.market.data.id]))}}},y={class:"mt-4"},S={for:"market_code",class:"block text-sm font-medium text-gray-700"},$={class:"mt-1"},v={class:"mt-4"},w={for:"name",class:"block text-sm font-medium text-gray-700"},X={class:"mt-1"};function D(o,t,T,V,s,m){const c=d("SBXButton"),u=d("SBXDefaultPageLayout");return b(),f(u,null,{default:l(()=>[e("form",{onSubmit:t[2]||(t[2]=h((...a)=>m.update&&m.update(...a),["prevent"]))},[e("div",y,[e("label",S,r(o.$t("sbxadmin.markets.market_code_label")),1),e("div",$,[n(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>s.form.market_code=a),type:"text",name:"market_code",id:"market_code",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[i,s.form.market_code]])])]),e("div",v,[e("label",w,r(o.$t("sbxadmin.markets.name_label"))+" ("+r(o.$page.props.locale.selected_language_code)+")",1),e("div",X,[n(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>s.form.name=a),type:"text",name:"name",id:"name",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[i,s.form.name]])])]),g(c,{class:"mt-4"},{default:l(()=>[k(r(o.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const N=p(B,[["render",D]]);export{N as default};
