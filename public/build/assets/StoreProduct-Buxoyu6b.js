import{_ as d,h as i,e as t,t as r,n as l,b as n}from"./app-Cm2beRkj.js";const u={components:{},props:{product:Object,selectedProductID:Number},computed:{mainClass(){var e="mt-2 p-2 flex justify-between w-3/5 cursor-pointer";return this.selectedProductID==this.product.id&&(e+=" bg-gray-200"),e}},methods:{selectProduct(){this.$emit("selectProduct",this.product)}}},m={class:"flex flex-col"},p={class:"text-xl font-medium text-gray-900"},_={class:"text-sm text-gray-900"},x={class:"ml-16 text-xl font-medium text-gray-900 text-right"};function f(e,o,s,h,g,c){return n(),i("div",{onClick:o[0]||(o[0]=(...a)=>c.selectProduct&&c.selectProduct(...a)),class:l(c.mainClass)},[t("div",m,[t("p",p,r(s.product.name),1),t("p",_,r(s.product.short_description),1)]),t("p",x,r(s.product.price)+" kr",1)],2)}const y=d(u,[["render",f]]);export{y as default};
