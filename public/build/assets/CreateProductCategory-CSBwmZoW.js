import{_ as d,S as _,c as f,w as p,r,b as g,e as b,d as a,g as B,t as S,k as h}from"./app-Cm2beRkj.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as x}from"./SBXInput-C8dZEgZe.js";import{S as w}from"./SBXTextArea-D09nQvWc.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const T={components:{SBXDefaultPageLayout:X,SBXInput:x,SBXTextArea:w,SBXButton:_},props:{errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:null,long_description:null})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_categories.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("product_categories.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}};function $(s,e,l,y,o,n){const i=r("SBXInput"),m=r("SBXTextArea"),c=r("SBXButton"),u=r("SBXDefaultPageLayout");return g(),f(u,null,{default:p(()=>[b("form",{onSubmit:e[2]||(e[2]=h((...t)=>n.create&&n.create(...t),["prevent"]))},[a(i,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:s.$t("sbxwebshop.product_categories.name_label"),error:l.errors.name},null,8,["model","label","error"]),a(m,{model:o.form.long_description,"onUpdate:model":e[1]||(e[1]=t=>o.form.long_description=t),label:s.$t("sbxwebshop.product_categories.description_label"),rows:10,error:l.errors.long_description},null,8,["model","label","error"]),a(c,{class:"mt-4"},{default:p(()=>[B(S(s.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const A=d(T,[["render",$]]);export{A as default};
