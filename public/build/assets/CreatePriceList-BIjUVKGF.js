import{_ as f,S as d,c,w as p,r,b,e as g,d as s,g as B,t as S,k as X}from"./app-Cm2beRkj.js";import{S as h}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as v}from"./SBXInput-C8dZEgZe.js";import{S as w}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const T={components:{SBXDefaultPageLayout:h,SBXInput:v,SBXToggle:w,SBXButton:d},props:{errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:null,is_active:!0,is_default:!1,prices_include_vat:!1})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.price_lists.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("price_lists.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}};function x(l,e,n,$,o,i){const m=r("SBXInput"),a=r("SBXToggle"),u=r("SBXButton"),_=r("SBXDefaultPageLayout");return b(),c(_,null,{default:p(()=>[g("form",{onSubmit:e[4]||(e[4]=X((...t)=>i.create&&i.create(...t),["prevent"]))},[s(m,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:l.$t("sbxwebshop.price_lists.name_label"),error:n.errors.name},null,8,["model","label","error"]),s(a,{model:o.form.is_active,"onUpdate:model":e[1]||(e[1]=t=>o.form.is_active=t),label:l.$t("sbxwebshop.price_lists.is_active_label")},null,8,["model","label"]),s(a,{model:o.form.is_default,"onUpdate:model":e[2]||(e[2]=t=>o.form.is_default=t),label:l.$t("sbxwebshop.price_lists.is_default_label")},null,8,["model","label"]),s(a,{model:o.form.prices_include_vat,"onUpdate:model":e[3]||(e[3]=t=>o.form.prices_include_vat=t),label:l.$t("sbxwebshop.price_lists.prices_include_vat_label")},null,8,["model","label"]),s(u,{class:"mt-4"},{default:p(()=>[B(S(l.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const I=f(T,[["render",x]]);export{I as default};
