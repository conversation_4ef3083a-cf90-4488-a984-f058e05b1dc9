import{s as u,ac as b,h as s,d as o,C as t,w as m,F as r,b as a,af as V,e as d,g as c,k as p,n as w,I as C}from"./app-Cm2beRkj.js";import{A as h}from"./AuthenticationCard-BbMgTIf9.js";import{_ as B}from"./AuthenticationCardLogo-DOAxyrlM.js";import{_ as v,a as k}from"./TextInput-Dx25uC_E.js";import{_ as y}from"./InputLabel-dwoQhTuq.js";import{_ as I}from"./PrimaryButton-7FvKQ4S5.js";const $={class:"mb-4 text-sm text-gray-600"},A={key:0},N={key:1},T={class:"flex items-center justify-end mt-4"},F={layout:null},R=Object.assign(F,{__name:"TwoFactorChallenge",setup(j){const l=u(!1),e=b({code:"",recovery_code:""}),f=u(null),_=u(null),g=async()=>{l.value^=!0,await C(),l.value?(f.value.focus(),e.code=""):(_.value.focus(),e.recovery_code="")},x=()=>{e.post(route("two-factor.login"))};return(U,n)=>(a(),s(r,null,[o(t(V),{title:"Översättare.nu | Bekräftelse 2-faktors autentisering"}),o(h,null,{logo:m(()=>[o(B)]),default:m(()=>[d("div",$,[l.value?(a(),s(r,{key:1},[c(" Bekräfta åtkomst till ditt konto genom att ange en av dina nödkoder. ")],64)):(a(),s(r,{key:0},[c(" Bekräfta åtkomst till ditt konto genom att ange koden som tillhandahålls av din autentiseringsapplikation. ")],64))]),d("form",{onSubmit:p(x,["prevent"])},[l.value?(a(),s("div",N,[o(y,{for:"recovery_code",value:"Nödkod"}),o(v,{id:"recovery_code",ref_key:"recoveryCodeInput",ref:f,modelValue:t(e).recovery_code,"onUpdate:modelValue":n[1]||(n[1]=i=>t(e).recovery_code=i),type:"text",class:"mt-1 block w-full",autocomplete:"one-time-code"},null,8,["modelValue"]),o(k,{class:"mt-2",message:t(e).errors.recovery_code},null,8,["message"])])):(a(),s("div",A,[o(y,{for:"code",value:"Kod"}),o(v,{id:"code",ref_key:"codeInput",ref:_,modelValue:t(e).code,"onUpdate:modelValue":n[0]||(n[0]=i=>t(e).code=i),type:"text",inputmode:"numeric",class:"mt-1 block w-full",autofocus:"",autocomplete:"one-time-code"},null,8,["modelValue"]),o(k,{class:"mt-2",message:t(e).errors.code},null,8,["message"])])),d("div",T,[d("button",{type:"button",class:"text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer",onClick:p(g,["prevent"])},[l.value?(a(),s(r,{key:1},[c(" Använd en engångskod ")],64)):(a(),s(r,{key:0},[c(" Använd en nödkod ")],64))]),o(I,{class:w(["ml-4",{"opacity-25":t(e).processing}]),disabled:t(e).processing},{default:m(()=>n[2]||(n[2]=[c(" Logga in ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}});export{R as default};
