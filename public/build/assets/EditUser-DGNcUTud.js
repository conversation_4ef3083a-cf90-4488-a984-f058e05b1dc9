import{_,S as g,c as b,w as d,r as i,b as S,e as l,d as r,g as B,t as v,k as c}from"./app-Cm2beRkj.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as h}from"./SBXSelect-RMXlX9En.js";import{S as x}from"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const U={components:{SBXDefaultPageLayout:X,SBXButton:g,SBXSelect:h,SBXInput:x},props:{user:Object,roles:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("User",this.user),console.log("Roles",this.roles)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({role_id:this.user.data.role_id,first_name:this.user.data.first_name,last_name:this.user.data.last_name,email:this.user.data.email})}},computed:{pageTitle(){return`${this.$t("sbxadmin.users.edit_title")}`}},methods:{update(){this.form.processing||this.form.put(this.route("users.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]))}}},k={class:"grid grid-cols-2 gap-4"},y={class:"grid grid-cols-2 gap-4"},D={class:"grid grid-cols-2 gap-4"},N={class:"grid grid-cols-2 gap-4"};function T(o,e,a,j,s,m){const u=i("SBXSelect"),n=i("SBXInput"),p=i("SBXButton"),f=i("SBXDefaultPageLayout");return S(),b(f,null,{default:d(()=>[l("form",{onSubmit:e[4]||(e[4]=c((...t)=>m.update&&m.update(...t),["prevent"]))},[l("div",k,[l("div",null,[r(u,{model:s.form.role_id,"onUpdate:model":e[0]||(e[0]=t=>s.form.role_id=t),items:a.roles.data,labelFieldName:"name",label:o.$t("sbxadmin.users.role_label")},null,8,["model","items","label"])]),e[5]||(e[5]=l("div",null,null,-1))]),l("div",y,[l("div",null,[r(n,{model:s.form.first_name,"onUpdate:model":e[1]||(e[1]=t=>s.form.first_name=t),label:o.$t("sbxadmin.users.first_name_label"),error:a.errors.first_name},null,8,["model","label","error"])]),e[6]||(e[6]=l("div",null,null,-1))]),l("div",D,[l("div",null,[r(n,{model:s.form.last_name,"onUpdate:model":e[2]||(e[2]=t=>s.form.last_name=t),label:o.$t("sbxadmin.users.last_name_label"),error:a.errors.last_name},null,8,["model","label","error"])]),e[7]||(e[7]=l("div",null,null,-1))]),l("div",N,[l("div",null,[r(n,{model:s.form.email,"onUpdate:model":e[3]||(e[3]=t=>s.form.email=t),label:o.$t("sbxadmin.users.email_label"),error:a.errors.email},null,8,["model","label","error"])]),e[8]||(e[8]=l("div",null,null,-1))]),r(p,{class:"mt-4"},{default:d(()=>[B(v(o.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const V=_(U,[["render",T]]);export{V as default};
