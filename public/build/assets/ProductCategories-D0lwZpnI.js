import{_ as d,h as c,d as s,e as t,F as n,r as a,b as p}from"./app-Cm2beRkj.js";import{S as u}from"./SBXDataTable-C66uxM7K.js";import{S as g}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const _={components:{SBXDataTable:u,SBXFilterBar:g},props:{productCategories:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.product_categories.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.product_categories.title")},data(){return{columns:[{key:"name",label:this.$t("sbxwebshop.product_categories.name_label")}]}}},m={class:"flex flex-col"},b={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},h={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},f={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function x(e,w,o,B,l,D){const r=a("SBXFilterBar"),i=a("SBXDataTable");return p(),c(n,null,[s(r,{filters:o.filters,searchRoute:"product_categories",placeholder:e.$t("sbxwebshop.global.search")},null,8,["filters","placeholder"]),t("div",m,[t("div",b,[t("div",h,[t("div",f,[s(i,{columns:l.columns,items:o.productCategories.data,showAddButton:!0,addButtonText:e.$t("sbxwebshop.product_categories.new_product_unit_button"),addRoute:"product_categories.create",showEditButton:!0,editRoute:"product_categories.edit",showDeleteButton:!0,deleteRoute:"product_categories.destroy",deleteDialogTitle:e.$t("sbxwebshop.product_categories.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.product_categories.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.product_categories.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxwebshop.global.cancel"),paginator:o.productCategories.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const F=d(_,[["render",x]]);export{F as default};
