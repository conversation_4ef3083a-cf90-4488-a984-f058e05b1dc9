import{_,S as c,c as b,w as d,r as s,b as f,e as y,d as n,g,t as v,k as S}from"./app-Cm2beRkj.js";import{S as h}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as B}from"./SBXInput-C8dZEgZe.js";import{S as X}from"./SBXSelect-RMXlX9En.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const w={components:{SBXDefaultPageLayout:h,SBXInput:B,SBXSelect:X,SBXButton:c},props:{products:Object,changeTypes:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({product_id:null,product_inventory_change_type_id:null,value:null,notes:null})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_inventory.inventory_list.create_row_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("product_inventory.inventory_list.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}};function x(l,e,r,T,o,a){const i=s("SBXSelect"),p=s("SBXInput"),m=s("SBXButton"),u=s("SBXDefaultPageLayout");return f(),b(u,null,{default:d(()=>[y("form",{onSubmit:e[4]||(e[4]=S((...t)=>a.create&&a.create(...t),["prevent"]))},[n(i,{model:o.form.product_id,"onUpdate:model":e[0]||(e[0]=t=>o.form.product_id=t),items:r.products.data,labelFieldName:"long_name",label:l.$t("sbxwebshop.product_inventory.inventory_list.product_name_label"),error:r.errors.product_id},null,8,["model","items","label","error"]),n(i,{model:o.form.product_inventory_change_type_id,"onUpdate:model":e[1]||(e[1]=t=>o.form.product_inventory_change_type_id=t),items:r.changeTypes.data,labelFieldName:"name",label:l.$t("sbxwebshop.product_inventory.inventory_list.change_type_label"),error:r.errors.product_inventory_change_type_id},null,8,["model","items","label","error"]),n(p,{model:o.form.value,"onUpdate:model":e[2]||(e[2]=t=>o.form.value=t),label:l.$t("sbxwebshop.product_inventory.inventory_list.value_label"),error:r.errors.value},null,8,["model","label","error"]),n(p,{model:o.form.notes,"onUpdate:model":e[3]||(e[3]=t=>o.form.notes=t),label:l.$t("sbxwebshop.product_inventory.inventory_list.notes_label"),error:r.errors.notes},null,8,["model","label","error"]),n(m,{class:"mt-4"},{default:d(()=>[g(v(l.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const U=_(w,[["render",x]]);export{U as default};
