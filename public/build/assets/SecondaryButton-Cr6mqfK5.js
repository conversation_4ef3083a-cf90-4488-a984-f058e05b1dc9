import{b as o,h as r,J as n}from"./app-Cm2beRkj.js";const s=["type"],i={__name:"SecondaryButton",props:{type:{type:String,default:"button"}},setup(e){return(t,a)=>(o(),r("button",{type:e.type,class:"inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:ring focus:ring-blue-200 active:text-gray-800 active:bg-gray-50 disabled:opacity-25 transition"},[n(t.$slots,"default")],8,s))}};export{i as _};
