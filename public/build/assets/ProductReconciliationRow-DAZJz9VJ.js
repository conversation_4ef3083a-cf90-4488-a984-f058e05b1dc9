import{_ as d,h as c,e as t,t as s,l as p,m as u,n as x,b as l}from"./app-Cm2beRkj.js";import{S as m}from"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const y={components:{SBXInput:m},props:{product:Object,key:String,rowIdx:Number},data(){return{quantity:0}},computed:{adjustedQuantity(){return this.quantity==""?parseInt(this.product.stock):parseInt(this.product.stock)+parseInt(this.quantity)}}},g={class:"whitespace-nowrap text-left py-2 pl-4 pr-3 text-xs sm:text-sm font-medium text-gray-900 sm:pl-3"},h={class:"text-sm font-semibold"},_={class:"text-xs"},f={class:"text-xs"},w={class:"whitespace-nowrap text-right px-3 py-2 text-xs sm:text-sm text-gray-500"},b={class:"whitespace-nowrap text-right px-3 py-2 text-xs sm:text-sm text-gray-500"},I={class:"hidden sm:table-cell whitespace-nowrap text-right px-3 py-2 text-xs sm:text-sm text-gray-500"};function k(a,n,e,q,r,i){return l(),c("tr",{class:x(e.rowIdx%2===0?void 0:"bg-gray-50")},[t("td",g,[t("div",null,[t("p",h,s(e.product.name),1),t("p",_,s(e.product.product_meta_2),1),t("p",f,s(e.product.product_meta_1),1)])]),t("td",w,s(e.product.stock),1),t("td",b,[p(t("input",{"onUpdate:modelValue":n[0]||(n[0]=o=>r.quantity=o),onChange:n[1]||(n[1]=o=>a.$emit("productQuantityChanged",e.product.id,parseInt(r.quantity))),type:"text",class:"w-16 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 text-right"},null,544),[[u,r.quantity]])]),t("td",I,s(i.adjustedQuantity),1)],2)}const j=d(y,[["render",k]]);export{j as default};
