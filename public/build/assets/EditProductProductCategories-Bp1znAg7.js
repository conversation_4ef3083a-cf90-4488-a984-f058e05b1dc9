import{_ as x,a9 as P,S as w,c as y,w as a,r as i,b as u,d as r,e as c,g as m,t as p,k as B,l as I,v as C,h as f,i as X,F as D}from"./app-Cm2beRkj.js";import{r as O}from"./PlusIcon-Dn95PRNa.js";import{S as N}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import T from"./ProductEditorsDropdown-PputzcmH.js";import{S as F}from"./SBXTable-CaSPMjSb.js";import{S as E}from"./SBXItemPicker-C2ZbAwhV.js";import{S as M}from"./SBXNotification-Bm69sG5a.js";import"./debounce-Bpn6Ai4k.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const j=(t,e)=>t.map(l=>l[e]),A={components:{PlusIcon:O,XMarkIcon:P,SBXDefaultPageLayout:N,ProductEditorsDropdown:T,SBXTable:F,SBXButton:w,SBXItemPicker:E,SBXNotification:M},props:{product:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{columns:[{key:"name",label:this.$t("sbxwebshop.products.product_category_label")},{key:"actions",headeralignment:"right",class:"justify-end"}],pickerColumns:[{key:"name",label:this.$t("sbxwebshop.product_categories.name_label")}],form:this.$inertia.form({product_categories:this.product.data.product_categories}),isPickerOpen:!1,pickerFilters:{active:"all"},filterOptions:[{text:"Alla produkter",value:"all"},{text:"Aktiva",value:"active"},{text:"Inaktiva",value:"inactive"}],showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`},excludedProductCategoryIDs(){var t=j(this.form.product_categories,"id");return t}},methods:{update(){this.form.processing||this.form.put(this.route("products.product_categories.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:t=>{this.showSaveSuccess=!0}})},removeCategory(t){let e=this.form.product_categories.findIndex(l=>l.id==t);this.form.product_categories.splice(e,1)},pickProductCategories(t){for(var e=0;e<t.length;e++)this.form.product_categories.push(t[e]);this.isPickerOpen=!1},saveNotificationCancelled(){this.showSaveSuccess=!1}}},L={class:"flex flex-col mt-4"},V={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},$={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},z={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},K={class:"flex"},R={class:"flex justify-end"},U=["value"];function q(t,e,l,G,o,n){const _=i("ProductEditorsDropdown"),g=i("PlusIcon"),d=i("SBXButton"),h=i("XMarkIcon"),v=i("SBXTable"),k=i("SBXNotification"),b=i("SBXItemPicker"),S=i("SBXDefaultPageLayout");return u(),y(S,null,{default:a(()=>[r(_,{pages:l.pageComponents,currentPage:"product_categories",productID:l.product.data.id},null,8,["pages","productID"]),c("div",L,[c("div",V,[c("div",$,[c("div",z,[r(v,{columns:o.columns,items:o.form.product_categories},{"header-actions":a(()=>[r(d,{onClick:e[0]||(e[0]=s=>o.isPickerOpen=!0)},{default:a(()=>[m(p(t.$t("sbxwebshop.products.add_product_category_button")),1),r(g,{class:"h-5 w-5 ml-2 mr-0 text-white"})]),_:1})]),actions:a(s=>[c("div",K,[r(d,{onClick:H=>n.removeCategory(s.item.id),size:"s",variant:"danger",class:"ml-2"},{default:a(()=>[r(h,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])])]),_:1},8,["columns","items"])])])])]),c("form",{onSubmit:e[1]||(e[1]=B((...s)=>n.update&&n.update(...s),["prevent"]))},[r(d,{class:"mt-4"},{default:a(()=>[m(p(t.$t("sbxwebshop.global.save")),1)]),_:1})],32),r(k,{show:o.showSaveSuccess,onNotificationCancelled:n.saveNotificationCancelled},null,8,["show","onNotificationCancelled"]),r(b,{title:t.$t("sbxwebshop.product_categories_picker.title"),multiple:!0,isOpen:o.isPickerOpen,itemsRoute:"product.product_categories.picker",columns:o.pickerColumns,exclude:n.excludedProductCategoryIDs,filters:o.pickerFilters,onCancelItemPicker:e[3]||(e[3]=s=>o.isPickerOpen=!1),onItemPickerPickedItems:n.pickProductCategories},{filtersArea:a(()=>[c("div",R,[I(c("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>o.pickerFilters.active=s),class:"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(u(!0),f(D,null,X(o.filterOptions,s=>(u(),f("option",{value:s.value},p(s.text),9,U))),256))],512),[[C,o.pickerFilters.active]])])]),_:1},8,["title","isOpen","columns","exclude","filters","onItemPickerPickedItems"])]),_:1})}const ie=x(A,[["render",q]]);export{ie as default};
