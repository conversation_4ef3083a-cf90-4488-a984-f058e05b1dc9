import{_ as P,a9 as S,S as w,c as y,w as l,r as c,b as I,d as s,e as d,g as m,t as _,l as B,m as X,k as C}from"./app-Cm2beRkj.js";import{r as D}from"./PlusIcon-Dn95PRNa.js";import{S as N}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import O from"./ProductEditorsDropdown-PputzcmH.js";import{S as T}from"./SBXTable-CaSPMjSb.js";import{S as q}from"./SBXItemPicker-C2ZbAwhV.js";import{S as $}from"./SBXNotification-Bm69sG5a.js";import"./debounce-Bpn6Ai4k.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const M=(t,e)=>t.map(o=>o[e]),V={components:{PlusIcon:D,XMarkIcon:S,SBXDefaultPageLayout:N,ProductEditorsDropdown:O,SBXTable:T,SBXButton:w,SBXItemPicker:q,SBXNotification:$},props:{product:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("Product: ",this.product)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{columns:[{key:"quantityslot",label:this.$t("sbxwebshop.included_products.quantity_label")},{key:"product_name",label:this.$t("sbxwebshop.included_products.product_label")},{key:"actions",headeralignment:"right",class:"justify-end"}],pickerColumns:[{key:"long_name",label:this.$t("sbxwebshop.included_products.name_label")}],form:this.$inertia.form({included_products:this.product.data.included_products}),isPickerOpen:!1,pickerFilters:{active:"all"},filterOptions:[{text:"Alla produkter",value:"all"},{text:"Aktiva",value:"active"},{text:"Inaktiva",value:"inactive"}],showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`},excludedProductIDs(){var t=M(this.form.included_products,"included_product_id");return t}},methods:{update(){this.form.processing||this.form.put(this.route("products.included_products.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:t=>{this.showSaveSuccess=!0}})},removeProduct(t){let e=this.form.included_products.findIndex(o=>o.id==t);this.form.included_products.splice(e,1)},pickProducts(t){for(var e=0;e<t.length;e++){let o=t[e],u={product_id:this.product.data.id,included_product_id:o.id,quantity:1,product_name:o.long_name};this.form.included_products.push(u)}this.isPickerOpen=!1},saveNotificationCancelled(){this.showSaveSuccess=!1}}},E={class:"flex flex-col mt-4"},j={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},A={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},L={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},F={class:"flex"},U=["onUpdate:modelValue"],z={class:"flex"};function K(t,e,o,u,i,n){const f=c("ProductEditorsDropdown"),h=c("PlusIcon"),a=c("SBXButton"),g=c("XMarkIcon"),b=c("SBXTable"),k=c("SBXNotification"),v=c("SBXItemPicker"),x=c("SBXDefaultPageLayout");return I(),y(x,null,{default:l(()=>[s(f,{pages:o.pageComponents,currentPage:"included_products",productID:o.product.data.id},null,8,["pages","productID"]),d("div",E,[d("div",j,[d("div",A,[d("div",L,[s(b,{columns:i.columns,items:i.form.included_products},{"header-actions":l(()=>[s(a,{onClick:e[0]||(e[0]=r=>i.isPickerOpen=!0)},{default:l(()=>[m(_(t.$t("sbxwebshop.included_products.add_products_button")),1),s(h,{class:"h-5 w-5 ml-2 mr-0 text-white"})]),_:1})]),quantityslot:l(r=>[d("div",F,[B(d("input",{"onUpdate:modelValue":p=>r.item.quantity=p,class:"w-16 rounded-md border border-gray-300 mt-4 px-3 py-2 shadow-sm focus-within:border-blue-600 focus-within:ring-1 focus-within:ring-blue-500 block p-0 text-gray-900 placeholder-gray-500 focus:ring-0 sm:text-sm text-center"},null,8,U),[[X,r.item.quantity]])])]),actions:l(r=>[d("div",z,[s(a,{onClick:p=>n.removeProduct(r.item.id),size:"s",variant:"danger",class:"ml-2"},{default:l(()=>[s(g,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])])]),_:1},8,["columns","items"])])])])]),d("form",{onSubmit:e[1]||(e[1]=C((...r)=>n.update&&n.update(...r),["prevent"]))},[s(a,{class:"mt-4"},{default:l(()=>[m(_(t.$t("sbxwebshop.global.save")),1)]),_:1})],32),s(k,{show:i.showSaveSuccess,onNotificationCancelled:n.saveNotificationCancelled},null,8,["show","onNotificationCancelled"]),s(v,{title:t.$t("sbxwebshop.included_products.picker_title"),multiple:!0,isOpen:i.isPickerOpen,itemsRoute:"product.included_products.picker",columns:i.pickerColumns,exclude:n.excludedProductIDs,filters:i.pickerFilters,onCancelItemPicker:e[2]||(e[2]=r=>i.isPickerOpen=!1),onItemPickerPickedItems:n.pickProducts},null,8,["title","isOpen","columns","exclude","filters","onItemPickerPickedItems"])]),_:1})}const oe=P(V,[["render",K]]);export{oe as default};
