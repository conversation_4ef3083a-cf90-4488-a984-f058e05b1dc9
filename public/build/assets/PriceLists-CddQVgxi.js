import{_ as d,h as n,d as l,e as t,F as p,r as o,b as c}from"./app-Cm2beRkj.js";import{S as _}from"./SBXDataTable-C66uxM7K.js";import{S as m}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const b={components:{SBXDataTable:_,SBXFilterBar:m},props:{priceLists:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.price_lists.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.price_lists.title")},data(){return{columns:[{key:"name",label:this.$t("sbxwebshop.price_lists.name_label")}]}}},u={class:"flex flex-col"},h={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},g={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},f={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function x(e,w,s,B,i,D){const a=o("SBXFilterBar"),r=o("SBXDataTable");return c(),n(p,null,[l(a,{filters:s.filters,searchRoute:"price_lists",placeholder:e.$t("sbxwebshop.global.search")},null,8,["filters","placeholder"]),t("div",u,[t("div",h,[t("div",g,[t("div",f,[l(r,{columns:i.columns,items:s.priceLists.data,showAddButton:!0,addButtonText:e.$t("sbxwebshop.price_lists.new_price_list_button"),addRoute:"price_lists.create",showEditButton:!0,editRoute:"price_lists.edit",showDeleteButton:!0,deleteRoute:"price_lists.destroy",deleteDialogTitle:e.$t("sbxwebshop.price_lists.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.price_lists.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.price_lists.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxwebshop.global.cancel"),paginator:s.priceLists.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const L=d(b,[["render",x]]);export{L as default};
