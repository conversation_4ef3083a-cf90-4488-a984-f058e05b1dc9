import{_ as n,a9 as c,S as u,c as p,w as m,r as a,b as _,d,e}from"./app-Cm2beRkj.js";import{r as g}from"./PlusIcon-Dn95PRNa.js";import{S as b}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import h from"./ProductEditorsDropdown-PputzcmH.js";import f from"./ProductCategoriesPicker-DMcJynDV.js";import{S as D}from"./SBXDataTable-C66uxM7K.js";import"./ProductCategoriesPickerRow-DGuDieQ7.js";import"./CheckIcon-CSfnhiPS.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";const w={components:{PlusIcon:g,XMarkIcon:c,SBXDefaultPageLayout:b,ProductEditorsDropdown:h,ProductCategoriesPicker:f,SBXDataTable:D,SBXButton:u},props:{product:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("product: ",this.product)},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{columns:[{key:"name",label:this.$t("sbxwebshop.product_variations.name_label")}]}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`}},methods:{addProductVariation(){}}},x={class:"flex flex-col mt-4"},v={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},B={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},T={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function P(t,$,o,S,r,y){const s=a("ProductEditorsDropdown"),i=a("SBXDataTable"),l=a("SBXDefaultPageLayout");return _(),p(l,null,{default:m(()=>[d(s,{currentPage:"product_variations",productID:o.product.data.id},null,8,["productID"]),e("div",x,[e("div",v,[e("div",B,[e("div",T,[d(i,{columns:r.columns,items:o.product.data.product_variations,showAddButton:!0,addButtonText:t.$t("sbxwebshop.product_variations.create_title"),addRoute:"product_variations.create",addRouteAdditionalIDs:[o.product.data.id],showEditButton:!0,editRoute:"product_variations.edit",showDeleteButton:!0,deleteRoute:"product_variations.destroy",deleteDialogTitle:t.$t("sbxwebshop.product_variations.delete_dialog_title"),deleteDialogMessage:t.$t("sbxwebshop.product_variations.delete_dialog_message"),deleteDialogOKText:t.$t("sbxwebshop.product_variations.delete_dialog_ok"),deleteDialogCancelText:t.$t("sbxwebshop.global.cancel")},null,8,["columns","items","addButtonText","addRouteAdditionalIDs","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText"])])])])])]),_:1})}const K=n(w,[["render",P]]);export{K as default};
