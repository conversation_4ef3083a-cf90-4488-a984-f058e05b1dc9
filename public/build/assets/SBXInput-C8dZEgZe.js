import{_ as d,b as o,h as s,e as i,t as a,l as c,K as m,j as u}from"./app-Cm2beRkj.js";import{r as p}from"./ExclamationCircleIcon-C4_TqLjZ.js";const f={components:{ExclamationCircleIcon:p},props:{type:{type:String,default:"text"},label:{type:String,required:!0},model:{type:String,default:null},placeholder:{type:String,required:!1,default:null},instruction:{type:String,required:!1,default:null},error:{type:String,required:!1,default:null}},watch:{model(r,e){this.currentValue=r}},data(){return{currentValue:this.model}}},y={class:"relative rounded-md border border-gray-300 mt-4 px-3 py-2 shadow-sm focus-within:border-blue-600 focus-within:ring-1 focus-within:ring-blue-500"},g={class:"block text-xs font-semibold text-gray-900"},h=["type","placeholder"],x={key:0,class:"mt-1 ml-1 text-xs text-gray-500"},b={key:1,class:"mt-1 text-xs text-red-600"};function _(r,e,t,S,l,V){return o(),s("div",null,[i("div",y,[i("label",g,a(t.label),1),c(i("input",{onInput:e[0]||(e[0]=n=>r.$emit("update:model",l.currentValue)),onChange:e[1]||(e[1]=n=>r.$emit("change",l.currentValue)),"onUpdate:modelValue":e[2]||(e[2]=n=>l.currentValue=n),type:t.type,class:"block w-full border-0 p-0 pr-10 text-gray-900 placeholder-gray-500 focus:ring-0 sm:text-sm",placeholder:t.placeholder},null,40,h),[[m,l.currentValue]])]),t.instruction?(o(),s("p",x,a(t.instruction),1)):u("",!0),t.error?(o(),s("p",b,a(t.error),1)):u("",!0)])}const k=d(f,[["render",_]]);export{k as S};
