import{_ as g,h as n,d as m,e as t,w as i,F as f,r as _,b as r,t as s,g as o}from"./app-Cm2beRkj.js";import{S as b}from"./SBXDataTable-C66uxM7K.js";import{S as $}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const x={components:{SBXDataTable:b,SBXFilterBar:$},props:{translatorApplications:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translator_applications.admin.title"),console.log("translatorApplications",this.translatorApplications.data)},updated(){this.$page.props.page_info.admin_title_label=this.$t("translations.translator_applications.title")},data(){return{columns:[{key:"customerslot",label:this.$t("translations.translators.translator_label")},{key:"contactslot",label:this.$t("translations.translator_applications.admin.contact_label")},{key:"infoslot",label:this.$t("translations.translators.other_info_label")},{key:"created_at",label:this.$t("translations.translator_applications.admin.date_label")}],translatorFilters:{search:this.filters.search}}},methods:{changeFilters(){this.$inertia.get(this.route("translator_applications",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translatorFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.translatorFilters.search=e}}},B={class:"flex flex-col"},v={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},y={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},S={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},k={class:"text-sm font-semibold"},F={class:"text-sm font-semibold"},V={class:"text-sm font-semibold"},A={class:"text-sm"},C={class:"text-sm"},T={class:"text-sm"},X={class:"text-sm"},D=["href"],z={class:"text-sm"},E=["href"],N={class:"text-sm"},j={class:"text-sm"},O={class:"text-sm"},R={key:0,class:"text-sm"},q={key:1,class:"text-sm"};function G(e,l,c,H,p,d){const h=_("SBXFilterBar"),u=_("SBXDataTable");return r(),n(f,null,[m(h,{onSearchValueChanged:d.searchValueChanged,filters:p.translatorFilters,searchRoute:"translator_applications",placeholder:e.$t("translations.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),t("div",B,[t("div",v,[t("div",y,[t("div",S,[m(u,{columns:p.columns,items:c.translatorApplications.data,showViewButton:!0,viewRoute:"translator_applications.show",showAddButton:!1,showEditButton:!1,showDeleteButton:!1,paginator:c.translatorApplications.meta},{customerslot:i(a=>[t("div",null,[t("p",k,s(e.$t("translations.global.customer_id"))+": "+s(a.item.translator_id),1),t("p",F,s(a.item.company),1),t("p",V,s(a.item.first_name)+" "+s(a.item.last_name),1),t("p",A,s(a.item.address_1),1),t("p",C,s(a.item.address_2),1),t("p",T,s(a.item.postal_code)+" "+s(a.item.city),1)])]),contactslot:i(a=>[t("div",null,[t("p",X,[l[0]||(l[0]=t("strong",null,"E-post: ",-1)),t("a",{href:`mailto:${a.item.email}`},s(a.item.email),9,D)]),t("p",z,[l[1]||(l[1]=t("strong",null,"Telefon: ",-1)),t("a",{href:`tel:${a.item.phone_no}`},s(a.item.phone_no),9,E)])])]),infoslot:i(a=>[t("div",null,[t("p",N,[t("strong",null,s(e.$t("translations.translators.from_label"))+": ",1),o(s(a.item.from_translation_languages),1)]),t("p",j,[t("strong",null,s(e.$t("translations.translators.to_label"))+": ",1),o(s(a.item.to_translation_languages),1)]),t("p",O,[t("strong",null,s(e.$t("translations.translators.categories_label"))+": ",1),o(s(a.item.translation_categories),1)]),a.item.is_authorized?(r(),n("p",R,[t("strong",null,s(e.$t("translations.translators.authorized_id_label"))+": ",1),o(s(a.item.authorization_id),1)])):(r(),n("p",q,[t("strong",null,s(e.$t("translations.translators.authorized_id_label"))+": ",1),l[2]||(l[2]=o(" -"))]))])]),_:1},8,["columns","items","paginator"])])])])])],64)}const W=g(x,[["render",G]]);export{W as default};
