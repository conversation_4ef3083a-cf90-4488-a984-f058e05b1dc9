import{_ as d,h as n,d as o,e as t,F as m,r as s,b as c}from"./app-Cm2beRkj.js";import{S as u}from"./SBXDataTable-C66uxM7K.js";import{S as p}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const g={components:{SBXDataTable:u,SBXFilterBar:p},props:{markets:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxadmin.markets.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxadmin.markets.title")},data(){return{columns:[{key:"name",label:this.$t("sbxadmin.markets.name_label"),sortable:!0,sortDirection:"desc",sortByFormatted:!0,filterByFormatted:!0}]}},methods:{}},_={class:"flex flex-col"},b={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},h={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},k={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function f(e,x,a,B,l,D){const r=s("SBXFilterBar"),i=s("SBXDataTable");return c(),n(m,null,[o(r,{filters:a.filters,searchRoute:"markets",placeholder:e.$t("sbxadmin.global.search")},null,8,["filters","placeholder"]),t("div",_,[t("div",b,[t("div",h,[t("div",k,[o(i,{columns:l.columns,items:a.markets.data,showAddButton:!0,addButtonText:e.$t("sbxadmin.markets.new_market_button"),addRoute:"markets.create",showEditButton:!0,editRoute:"markets.edit",showDeleteButton:!0,deleteRoute:"markets.destroy",deleteDialogTitle:e.$t("sbxadmin.markets.delete_dialog_title"),deleteDialogMessage:e.$t("sbxadmin.markets.delete_dialog_message"),deleteDialogOKText:e.$t("sbxadmin.markets.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxadmin.global.cancel"),paginator:a.markets.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const O=d(g,[["render",f]]);export{O as default};
