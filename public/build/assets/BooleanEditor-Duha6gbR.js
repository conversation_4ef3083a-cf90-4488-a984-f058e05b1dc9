import{_ as m,S as _,c as p,w as r,r as i,b as f,e,l as b,ab as g,t as a,d as h,g as v,k as x}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const y={components:{SBXDefaultPageLayout:B,SBXButton:_},props:{setting:Object},remember:"form",data(){return{form:this.$inertia.form({boolean_value:this.setting.data.boolean_value})}},computed:{},methods:{update(){this.form.processing||this.form.put(this.route("settings.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.setting.data.id]))}}},S={class:"relative flex items-start mt-4"},k={class:"flex items-center h-5"},X={class:"ml-3 text-sm"},w={for:"boolean_value",class:"font-medium text-gray-700"},D={id:"boolean_value-description",class:"text-gray-500"},V={class:"sr-only"};function C(d,t,s,L,n,l){const c=i("SBXButton"),u=i("SBXDefaultPageLayout");return f(),p(u,null,{default:r(()=>[e("form",{onSubmit:t[1]||(t[1]=x((...o)=>l.update&&l.update(...o),["prevent"]))},[e("div",S,[e("div",k,[b(e("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>n.form.boolean_value=o),"aria-describedby":"boolean_value-description",name:"boolean_value",type:"checkbox",class:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"},null,512),[[g,n.form.boolean_value]])]),e("div",X,[e("label",w,a(s.setting.data.name),1),e("span",D,[e("span",V,a(s.setting.data.name),1)])])]),h(c,{class:"mt-4"},{default:r(()=>[v(a(d.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const M=m(y,[["render",C]]);export{M as default};
