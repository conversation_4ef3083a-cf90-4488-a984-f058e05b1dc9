import{_,S as b,c as g,w as p,r as a,b as S,e as i,d as l,g as v,t as B,k as h}from"./app-Cm2beRkj.js";import{u as X}from"./index-BKm97uF2.js";import{S as k}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as y}from"./SBXSelect-RMXlX9En.js";import{S as P}from"./SBXInput-C8dZEgZe.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const C={components:{SBXDefaultPageLayout:k,SBXButton:b,SBXSelect:y,SBXInput:P},props:{user:Object,roles:Object,salesPeople:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:X("put",this.route("sydfisk_users.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{role_id:this.user.data.role_id,first_name:this.user.data.first_name,last_name:this.user.data.last_name,email:this.user.data.email,phone_no:this.user.data.phone_no,sales_person_id:this.user.data.sales_person_id})}},computed:{pageTitle(){return`${this.$t("sbxadmin.users.edit_title")}`},salesPeopleOptions(){var s=[];s.push({id:null,name:"Ingen säljare vald"});for(var e=0;e<this.salesPeople.data.length;e++){let t=this.salesPeople.data[e];s.push({id:t.id,name:t.name})}return s}},methods:{update(){this.form.processing||this.form.submit()}}},U={class:"grid grid-cols-2 gap-4"};function O(s,e,t,j,o,m){const d=a("SBXSelect"),n=a("SBXInput"),u=a("SBXButton"),f=a("SBXDefaultPageLayout");return S(),g(f,null,{default:p(()=>[i("form",{onSubmit:e[12]||(e[12]=h((...r)=>m.update&&m.update(...r),["prevent"]))},[i("div",U,[i("div",null,[l(d,{model:o.form.role_id,"onUpdate:model":e[0]||(e[0]=r=>o.form.role_id=r),items:t.roles.data,labelFieldName:"name",label:s.$t("sbxadmin.users.role_label"),onChange:e[1]||(e[1]=r=>o.form.validate("role_id")),error:o.form.errors.role_id},null,8,["model","items","label","error"]),l(d,{model:o.form.sales_person_id,"onUpdate:model":e[2]||(e[2]=r=>o.form.sales_person_id=r),items:m.salesPeopleOptions,labelFieldName:"name",label:s.$t("sydfisk.sydfisk_users.sales_person_label"),onChange:e[3]||(e[3]=r=>o.form.validate("sales_person_id")),error:o.form.errors.sales_person_id},null,8,["model","items","label","error"]),l(n,{model:o.form.first_name,"onUpdate:model":e[4]||(e[4]=r=>o.form.first_name=r),label:s.$t("sbxadmin.users.first_name_label"),onChange:e[5]||(e[5]=r=>o.form.validate("first_name")),error:o.form.errors.first_name},null,8,["model","label","error"]),l(n,{model:o.form.last_name,"onUpdate:model":e[6]||(e[6]=r=>o.form.last_name=r),label:s.$t("sbxadmin.users.last_name_label"),onChange:e[7]||(e[7]=r=>o.form.validate("last_name")),error:o.form.errors.last_name},null,8,["model","label","error"]),l(n,{model:o.form.email,"onUpdate:model":e[8]||(e[8]=r=>o.form.email=r),label:s.$t("sbxadmin.users.email_label"),onChange:e[9]||(e[9]=r=>o.form.validate("email")),error:o.form.errors.email},null,8,["model","label","error"]),l(n,{model:o.form.phone_no,"onUpdate:model":e[10]||(e[10]=r=>o.form.phone_no=r),label:s.$t("sydfisk.sydfisk_users.phone_no_label"),onChange:e[11]||(e[11]=r=>o.form.validate("phone_no")),error:o.form.errors.phone_no},null,8,["model","label","error"]),l(u,{class:"mt-4"},{default:p(()=>[v(B(s.$t("sydfisk.sydfisk_users.update_button")),1)]),_:1})])])],32)]),_:1})}const L=_(C,[["render",O]]);export{L as default};
