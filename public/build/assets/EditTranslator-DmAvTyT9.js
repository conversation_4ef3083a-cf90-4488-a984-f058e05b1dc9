import{_ as k,S as T,c as f,w as _,r as c,b as i,e as r,l as X,v as U,h as u,i as B,t as m,F as C,n as w,d as n,g as h,j as p,k as I}from"./app-Cm2beRkj.js";import{u as S}from"./index-BKm97uF2.js";import{S as z}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as N}from"./SBXSelect-RMXlX9En.js";import{S as D}from"./SBXInput-C8dZEgZe.js";import{S as V}from"./SBXComboBox-BrrwJIQ0.js";import{S as j}from"./SBXAlert-DwVKPUUm.js";import{S as A}from"./SBXToggle-CrVi0Yuw.js";import{S as O}from"./StepsDivider-BRzfDQoa.js";import{S as P}from"./SBXGenericNotification-BrDLezmQ.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckIcon-DN9uS3Am.js";import"./use-controllable-D9fh3JbV.js";import"./label-D4lfsZnZ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const E={components:{SBXDefaultPageLayout:z,SBXButton:T,SBXToggle:A,SBXSelect:N,SBXInput:D,SBXComboBox:V,StepsDivider:O,SBXGenericNotification:P,SBXAlert:j},props:{user:Object,translationLanguages:Object,translationCategories:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle;for(var s=[],o=0;o<this.user.data.from_translation_languages.length;o++){let d=this.user.data.from_translation_languages[o];for(var a=0;a<this.translationLanguages.data.length;a++)if(this.translationLanguages.data[a].id==d.id){s.push(this.translationLanguages.data[a]);break}console.log("User:",this.user.data)}this.informationForm.from_translation_languages=s;for(var b=[],o=0;o<this.user.data.to_translation_languages.length;o++){let l=this.user.data.to_translation_languages[o];for(var a=0;a<this.translationLanguages.data.length;a++)if(this.translationLanguages.data[a].id==l.id){b.push(this.translationLanguages.data[a]);break}}this.informationForm.to_translation_languages=b;for(var t=[],o=0;o<this.user.data.translation_categories.length;o++){let l=this.user.data.translation_categories[o];for(var a=0;a<this.translationCategories.data.length;a++)if(this.translationCategories.data[a].id==l.id){t.push(this.translationCategories.data[a]);break}}this.informationForm.translation_categories=t},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{tabs:[{id:"contact_info",name:this.$t("translations.profile.contact_info_tab")},{id:"information",name:this.$t("translations.profile.information_tab")},{id:"leads",name:this.$t("translations.profile.leads_tab")}],selectedTab:"contact_info",profileSaved:!1,contactInfoForm:S("put",this.route("translators.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{company:this.user.data.company,company_no:this.user.data.company_no,first_name:this.user.data.first_name,last_name:this.user.data.last_name,address_1:this.user.data.address_1,address_2:this.user.data.address_2,postal_code:this.user.data.postal_code,city:this.user.data.city,email:this.user.data.email,phone_no:this.user.data.phone_no,is_authorized:this.user.data.is_authorized,authorization_id:this.user.data.authorization_id,allow_text_messages:this.user.data.allow_text_messages}),informationForm:S("put",this.route("translators.update.information",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{from_translation_languages:[],to_translation_languages:[],translation_categories:[]}),leadsForm:S("put",this.route("translators.update.leads",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{lead_count:""})}},computed:{pageTitle(){return`${this.$t("translations.translators.edit_title")}`}},methods:{updateContactInfo(){var s=this;this.contactInfoForm.processing||this.contactInfoForm.submit({preserveScroll:!0,onSuccess:o=>{s.profileSaved=!0,s.contactInfoForm.password=null,s.contactInfoForm.password_confirmation=null,setTimeout(function(){s.profileSaved=!1},3e3)}})},updateInformation(){var s=this;this.informationForm.processing||this.informationForm.submit({preserveScroll:!0,onSuccess:o=>{s.profileSaved=!0,setTimeout(function(){s.profileSaved=!1},3e3)}})},updateLeads(){var s=this;this.leadsForm.processing||this.leadsForm.submit({preserveScroll:!0,onSuccess:o=>{s.profileSaved=!0,setTimeout(function(){s.profileSaved=!1},3e3)}})}}},M={class:"pt-8 sm:pt-0"},G={class:"sm:hidden"},q=["value"],H={class:"hidden sm:block"},J={class:"-mb-px flex space-x-8","aria-label":"Tabs"},K=["onClick","aria-current"],Q={key:0,class:"mt-8 mb-8 sm:mb-0"},R={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},W={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Y={class:"grid grid-cols-1 gap-4"},Z={class:"grid grid-cols-1 gap-4"},x={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},$={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},oo={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},to={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},eo={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},so={key:1,class:"mt-8 mb-8 sm:mb-0"},ro={class:"mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4"},no={class:"text-sm font-semibold"},ao={class:"mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4"},lo={class:"text-sm font-semibold"},io={class:"mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4"},mo={class:"text-sm font-semibold"},uo={key:2,class:"mt-8 mb-8 sm:mb-0"},co={class:"p-8 border rounded-2xl"},po={class:"text-5xl text-center font-semibold"},go={class:"text-xl text-center"},fo={class:"grid grid-cols-1 sm:grid-cols-3 gap-4"};function _o(s,o,a,b,t,d){const l=c("SBXInput"),y=c("SBXToggle"),g=c("SBXButton"),v=c("SBXAlert"),F=c("SBXComboBox"),L=c("SBXDefaultPageLayout");return i(),f(L,null,{default:_(()=>[r("div",M,[r("div",G,[X(r("select",{"onUpdate:modelValue":o[0]||(o[0]=e=>t.selectedTab=e),id:"tabs",name:"tabs",class:"block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"},[(i(!0),u(C,null,B(t.tabs,e=>(i(),u("option",{key:e.id,value:e.id},m(e.name),9,q))),128))],512),[[U,t.selectedTab]])]),r("div",H,[r("nav",J,[(i(!0),u(C,null,B(t.tabs,e=>(i(),u("p",{onClick:bo=>t.selectedTab=e.id,key:e.name,class:w([e.id==t.selectedTab?"border-oversattare-blue text-oversattare-blue":"border-transparent text-oversattare-text-black hover:border-gray-300 hover:text-gray-700","whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium cursor-pointer select-none"]),"aria-current":e.id==t.selectedTab?"page":void 0},m(e.name),11,K))),128))])])]),t.selectedTab=="contact_info"?(i(),u("div",Q,[r("form",{onSubmit:o[25]||(o[25]=I((...e)=>d.updateContactInfo&&d.updateContactInfo(...e),["prevent"]))},[r("div",R,[n(l,{model:t.contactInfoForm.company,"onUpdate:model":o[1]||(o[1]=e=>t.contactInfoForm.company=e),label:s.$t("translations.profile.company_label"),onChange:o[2]||(o[2]=e=>t.contactInfoForm.validate("company")),error:t.contactInfoForm.errors.company},null,8,["model","label","error"]),n(l,{model:t.contactInfoForm.company_no,"onUpdate:model":o[3]||(o[3]=e=>t.contactInfoForm.company_no=e),label:s.$t("translations.profile.company_no_label"),onChange:o[4]||(o[4]=e=>t.contactInfoForm.validate("company_no")),error:t.contactInfoForm.errors.company_no},null,8,["model","label","error"])]),r("div",W,[n(l,{model:t.contactInfoForm.first_name,"onUpdate:model":o[5]||(o[5]=e=>t.contactInfoForm.first_name=e),label:s.$t("translations.profile.first_name_label"),onChange:o[6]||(o[6]=e=>t.contactInfoForm.validate("first_name")),error:t.contactInfoForm.errors.first_name,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),n(l,{model:t.contactInfoForm.last_name,"onUpdate:model":o[7]||(o[7]=e=>t.contactInfoForm.last_name=e),label:s.$t("translations.profile.last_name_label"),onChange:o[8]||(o[8]=e=>t.contactInfoForm.validate("last_name")),error:t.contactInfoForm.errors.last_name,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),r("div",Y,[n(l,{model:t.contactInfoForm.address_1,"onUpdate:model":o[9]||(o[9]=e=>t.contactInfoForm.address_1=e),label:s.$t("translations.profile.address_1_label"),onChange:o[10]||(o[10]=e=>t.contactInfoForm.validate("address_1")),error:t.contactInfoForm.errors.address_1,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),r("div",Z,[n(l,{model:t.contactInfoForm.address_2,"onUpdate:model":o[11]||(o[11]=e=>t.contactInfoForm.address_2=e),label:s.$t("translations.profile.address_2_label"),onChange:o[12]||(o[12]=e=>t.contactInfoForm.validate("address_2")),error:t.contactInfoForm.errors.address_2},null,8,["model","label","error"])]),r("div",x,[n(l,{model:t.contactInfoForm.postal_code,"onUpdate:model":o[13]||(o[13]=e=>t.contactInfoForm.postal_code=e),label:s.$t("translations.profile.postal_code_label"),onChange:o[14]||(o[14]=e=>t.contactInfoForm.validate("postal_code")),error:t.contactInfoForm.errors.postal_code,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),n(l,{model:t.contactInfoForm.city,"onUpdate:model":o[15]||(o[15]=e=>t.contactInfoForm.city=e),label:s.$t("translations.profile.city_label"),onChange:o[16]||(o[16]=e=>t.contactInfoForm.validate("city")),error:t.contactInfoForm.errors.city,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),r("div",$,[n(l,{model:t.contactInfoForm.email,"onUpdate:model":o[17]||(o[17]=e=>t.contactInfoForm.email=e),label:s.$t("sbxadmin.profile.email_label"),onChange:o[18]||(o[18]=e=>t.contactInfoForm.validate("email")),error:t.contactInfoForm.errors.email,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),n(l,{model:t.contactInfoForm.phone_no,"onUpdate:model":o[19]||(o[19]=e=>t.contactInfoForm.phone_no=e),label:s.$t("translations.profile.phone_no_label"),onChange:o[20]||(o[20]=e=>t.contactInfoForm.validate("phone_no")),error:t.contactInfoForm.errors.phone_no,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),r("div",oo,[n(y,{model:t.contactInfoForm.is_authorized,"onUpdate:model":o[21]||(o[21]=e=>t.contactInfoForm.is_authorized=e),label:s.$t("translations.profile.is_authorized_label"),class:"mt-4"},null,8,["model","label"])]),r("div",to,[n(l,{model:t.contactInfoForm.authorization_id,"onUpdate:model":o[22]||(o[22]=e=>t.contactInfoForm.authorization_id=e),label:s.$t("translations.profile.authorization_id_label"),onChange:o[23]||(o[23]=e=>t.contactInfoForm.validate("authorization_id")),error:t.contactInfoForm.errors.authorization_id},null,8,["model","label","error"])]),r("div",eo,[n(y,{model:t.contactInfoForm.allow_text_messages,"onUpdate:model":o[24]||(o[24]=e=>t.contactInfoForm.allow_text_messages=e),label:s.$t("translations.profile.sms_label"),class:"mt-4"},null,8,["model","label"])]),n(g,{class:"mt-4",enabled:!t.contactInfoForm.processing},{default:_(()=>[h(m(s.$t("translations.profile.update_contact_info_button")),1)]),_:1},8,["enabled"]),t.profileSaved?(i(),f(v,{key:0,title:s.$t("translations.profile.saved_title"),message:s.$t("translations.profile.saved_message"),class:"mt-4"},null,8,["title","message"])):p("",!0)],32)])):p("",!0),t.selectedTab=="information"?(i(),u("div",so,[r("form",{onSubmit:o[29]||(o[29]=I((...e)=>d.updateInformation&&d.updateInformation(...e),["prevent"]))},[r("div",ro,[r("div",null,[r("p",no,m(s.$t("translations.profile.from_languages_label")),1),n(F,{model:t.informationForm.from_translation_languages,"onUpdate:model":o[26]||(o[26]=e=>t.informationForm.from_translation_languages=e),items:a.translationLanguages.data,valueFieldName:"id",labelFieldName:"name",selectedLabel:s.$t("translations.translator_applications.selected_languages_prompt"),error:t.informationForm.errors.from_translation_languages},null,8,["model","items","selectedLabel","error"])])]),r("div",ao,[r("div",null,[r("p",lo,m(s.$t("translations.profile.to_languages_label")),1),n(F,{model:t.informationForm.to_translation_languages,"onUpdate:model":o[27]||(o[27]=e=>t.informationForm.to_translation_languages=e),items:a.translationLanguages.data,valueFieldName:"id",labelFieldName:"name",selectedLabel:s.$t("translations.translator_applications.selected_languages_prompt"),error:t.informationForm.errors.to_translation_languages},null,8,["model","items","selectedLabel","error"])])]),r("div",io,[r("div",null,[r("p",mo,m(s.$t("translations.profile.categories_label")),1),n(F,{model:t.informationForm.translation_categories,"onUpdate:model":o[28]||(o[28]=e=>t.informationForm.translation_categories=e),items:a.translationCategories.data,valueFieldName:"id",labelFieldName:"name",selectedLabel:s.$t("translations.translator_applications.selected_categories_prompt"),error:t.informationForm.errors.translation_categories},null,8,["model","items","selectedLabel","error"])])]),n(g,{class:"mt-8",enabled:!t.contactInfoForm.processing},{default:_(()=>[h(m(s.$t("translations.profile.update_information_button")),1)]),_:1},8,["enabled"]),t.profileSaved?(i(),f(v,{key:0,title:s.$t("translations.profile.saved_title"),message:s.$t("translations.profile.saved_message"),class:"mt-4"},null,8,["title","message"])):p("",!0)],32)])):p("",!0),t.selectedTab=="leads"?(i(),u("div",uo,[r("div",co,[r("p",po,m(a.user.data.leads_count),1),r("p",go,m(s.$t("translations.profile.current_lead_count_label")),1)]),r("form",{onSubmit:o[32]||(o[32]=I((...e)=>d.updateLeads&&d.updateLeads(...e),["prevent"]))},[r("div",fo,[n(l,{model:t.leadsForm.lead_count,"onUpdate:model":o[30]||(o[30]=e=>t.leadsForm.lead_count=e),label:s.$t("translations.profile.lead_count_label"),onChange:o[31]||(o[31]=e=>t.leadsForm.validate("lead_count")),error:t.leadsForm.errors.lead_count},null,8,["model","label","error"])]),n(g,{class:"mt-4",enabled:!t.leadsForm.processing},{default:_(()=>[h(m(s.$t("translations.profile.update_leads_button")),1)]),_:1},8,["enabled"]),t.profileSaved?(i(),f(v,{key:0,title:s.$t("translations.profile.saved_title"),message:s.$t("translations.profile.saved_message"),class:"mt-4"},null,8,["title","message"])):p("",!0)],32)])):p("",!0)]),_:1})}const jo=k(E,[["render",_o]]);export{jo as default};
