import{_ as b,s as v,B as k,Y as w,h as i,d as u,w as C,e as t,t as a,g as x,F as j,i as V,j as r,c as f,r as m,b as n}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import D from"./OffersView-B_7AmLou.js";import O from"./PaymentView-CWJ-tCxA.js";import{r as P,a as T,b as I,y as S,g as L,A as N}from"./CheckIcon-De50fY7n.js";import"./index-BKm97uF2.js";import"./client-BWFz6ICJ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const z={components:{SBXDefaultPageLayout:B,OffersView:D,PaymentView:O,CheckIcon:P,CheckCircleIcon:T,DocumentTextIcon:I,RadioGroup:S,RadioGroupOption:L,Disclosure:N},props:{assignment:Object,errors:Object},layout:null,setup(l){const s=v([{id:"01",name:"Granskning",href:"#",status:""},{id:"02",name:"Offert",href:"#",status:""},{id:"03",name:"Betalning",href:"#",status:""},{id:"04",name:"Översättning påbörjat",href:"#",status:""},{id:"05",name:"Uppdraget slutfört",href:"#",status:""}]),e=k(()=>s.value.map((o,g)=>{if(g===0)o.status=l.assignment.data.approved_at?"complete":"current";else{const _=s.value[g-1];o.status=_.status==="complete"?c(o)?"complete":"current":"upcoming"}return o}));function c(o){return o.id==="02"||o.id==="03"?l.assignment.data.bid_accepted:!1}return w(()=>{s.value=e.value,document.body.classList.add("bg-oversattare-green-light")}),{steps:s,updatedSteps:e}},data(){return{pageTitle:"Create Translation Assignment"}},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle}},G={class:"flex-1 bg-oversattare-green-light h-screen"},M={class:"py-4"},U={class:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8"},q={class:"min-h-full py-12 sm:px-6 lg:px-8 bg-grey"},A={class:"flex flex-col md:flex-row justify-between max-w-7xl py-3 px-1 sm:px-3 lg:px-4"},F={class:"text-3xl font-bold leading-tight tracking-tight text-gray-800"},R={class:"flex justify-end items-end text-sm text-gray-500"},E={class:"ml-1 font-bold"},X={class:"sm:mx-auto sm:w-full"},Y={class:"bg-white px-3 py-3 shadow sm:w-full sm:rounded-lg sm:px-6"},H={class:"bg-white justify-center"},J={class:"flex gap-0 mb-1"},K={class:"flex-none content-center border-r px-4 py-12 sm:px-6 lg:px-8"},Q={class:"flex justify-center","aria-label":"Progress"},W={role:"list",class:"space-y-6"},Z={key:0,class:"group"},$={class:"flex items-start"},tt={class:"relative flex h-5 w-5 flex-shrink-0 items-center justify-center"},st={class:"ml-3 text-sm font-medium text-gray-500"},et={key:1,class:"flex items-start","aria-current":"step"},at={class:"ml-3 text-sm font-medium text-green-700"},nt={key:2,class:"group"},it={class:"flex items-start"},lt={class:"ml-3 text-sm font-medium text-gray-500"},ot={class:"grid grid-cols-1 md:grid-cols-2 px-4 sm:px-6 lg:px-8"},dt={class:"py-3 sm:col-span-1 sm:px-0"},rt={class:"text-sm font-semibold leading-6 text-gray-900"},mt={class:"mt-1 text-sm leading-6 text-gray-700"},ct={class:"py-3 sm:col-span-1 sm:px-0"},gt={class:"text-sm font-semibold leading-6 text-gray-900"},_t={class:"mt-1 text-sm leading-6 text-gray-700"},ut={class:"py-3 sm:col-span-1 sm:px-0"},xt={class:"text-sm font-semibold leading-6 text-gray-900"},ft={class:"mt-1 text-sm leading-6 text-gray-700"},ht={class:"py-3 sm:col-span-1 sm:px-0"},pt={class:"text-sm font-semibold leading-6 text-gray-900"},yt={key:0,class:"text-sm font-semibold leading-6 text-green-700"},bt={key:1,class:"text-sm font-semibold leading-6 text-red-700"},vt={class:"py-3 sm:px-0"},kt={class:"text-sm font-semibold leading-6 text-gray-900"},wt={key:0,class:"mt-1 text-sm leading-6 text-gray-700"},Ct={key:1,class:"mt-1 text-sm leading-6 text-gray-700"},jt={class:"py-3 sm:px-0"},Vt={class:"text-sm font-semibold leading-6 text-gray-900"},Bt={key:0,class:"mt-1 text-sm leading-6 text-gray-700"},Dt={key:1,class:"mt-1 text-sm leading-6 text-gray-700"},Ot={key:2,class:"my-4 p-12 border-t border-dashed border-gray-300"},Pt={class:"text-center"},Tt={class:"mt-1 text-xs text-gray-500"};function It(l,s,e,c,o,g){const _=m("Disclosure"),h=m("CheckCircleIcon"),p=m("OffersView"),y=m("PaymentView");return n(),i("main",G,[u(_,{as:"nav",class:"bg-white shadow"},{default:C(()=>s[0]||(s[0]=[t("div",{class:"mx-auto px-2 sm:px-6 lg:px-8"},[t("div",{class:"flex h-16 items-center"},[t("div",{class:"inset-y-0 flex items-center"},[t("div",{class:"flex flex-shrink-0 items-center pl-2 sm:pl-0"},[t("img",{class:"h-8 w-auto items-center",src:"/graphics/oversattare_nu.png",alt:"Översättare.nu"})])])])],-1)])),_:1}),t("div",M,[t("div",U,[t("div",q,[t("div",A,[t("h1",F,"Uppdrag #"+a(e.assignment.data.assignment_id),1),t("div",R,[s[1]||(s[1]=x(" Uppdrag skapad ")),t("span",E,a(e.assignment.data.created_date),1)])]),t("div",X,[t("div",Y,[t("div",H,[t("div",J,[t("div",K,[t("nav",Q,[t("ol",W,[(n(!0),i(j,null,V(c.steps,d=>(n(),i("li",{key:d.name},[d.status==="complete"?(n(),i("div",Z,[t("span",$,[t("span",tt,[u(h,{class:"h-full w-full text-green-600","aria-hidden":"true"})]),t("span",st,a(d.name),1)])])):d.status==="current"?(n(),i("div",et,[s[2]||(s[2]=t("span",{class:"animate-pulse relative flex h-5 w-5 flex-shrink-0 items-center justify-center","aria-hidden":"true"},[t("span",{class:"absolute h-4 w-4 rounded-full bg-green-200"}),t("span",{class:"relative block h-2 w-2 rounded-full bg-green-600"})],-1)),t("span",at,a(d.name),1)])):(n(),i("div",nt,[t("div",it,[s[3]||(s[3]=t("div",{class:"relative flex h-5 w-5 flex-shrink-0 items-center justify-center","aria-hidden":"true"},[t("div",{class:"h-2 w-2 rounded-full bg-gray-300"})],-1)),t("p",lt,a(d.name),1)])]))]))),128))])])]),t("dl",ot,[t("div",dt,[t("dt",rt,a(l.$t("translations.my_assignments.assignment_detail.to_language_label")),1),t("dd",mt,a(e.assignment.data.from_translation_language),1)]),t("div",ct,[t("dt",gt,a(l.$t("translations.my_assignments.assignment_detail.from_language_label")),1),t("dd",_t,a(e.assignment.data.to_translation_language),1)]),t("div",ut,[t("dt",xt,a(l.$t("translations.my_assignments.assignment_detail.category_label")),1),t("dd",ft,a(e.assignment.data.translation_category),1)]),t("div",ht,[t("dt",pt,a(l.$t("translations.my_assignments.assignment_detail.authorization_label")),1),e.assignment.data.is_authorization_required?(n(),i("dd",yt,a(l.$t("translations.global.yes")),1)):r("",!0),e.assignment.data.is_authorization_required?r("",!0):(n(),i("dd",bt,a(l.$t("translations.global.no")),1))]),t("div",vt,[t("dt",kt,a(l.$t("translations.my_assignments.assignment_detail.file_count_label")),1),e.assignment.data.files_count!=null&&e.assignment.data.files_count>0?(n(),i("dd",wt,a(e.assignment.data.files_count),1)):(n(),i("dd",Ct," -"))]),t("div",jt,[t("dt",Vt,a(l.$t("translations.my_assignments.assignment_detail.latest_delivery_date_label")),1),e.assignment.data.last_delivery_date?(n(),i("dd",Bt,a(e.assignment.data.last_delivery_date),1)):(n(),i("dd",Dt,"-"))])])]),e.assignment.data.bid_accepted?r("",!0):(n(),f(p,{key:0,assignment:e.assignment},null,8,["assignment"])),e.assignment.data.bid_accepted?(n(),f(y,{key:1,bid:e.assignment.data.accepted_bid},null,8,["bid"])):r("",!0),e.assignment.data.bid_accepted?(n(),i("div",Ot,[t("div",Pt,[s[5]||(s[5]=t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor","aria-hidden":"true",viewBox:"2.5 2.5 19 19"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802"})],-1)),s[6]||(s[6]=t("h2",{class:"mt-3 text-lg font-semibold text-gray-900"},"Översättningen är påbörjad",-1)),s[7]||(s[7]=t("p",{class:"mt-1 text-sm text-gray-500"},"Din översättare har fått betalningsbekräftelsen och har nu påbörjat översättningsprocessen. ",-1)),t("p",Tt,[s[4]||(s[4]=x("Preliminär leverans är beräknad till ")),t("strong",null,a(e.assignment.data.accepted_bid.estimated_delivery),1)])])])):r("",!0)])])])])])])])}const Ft=b(z,[["render",It]]);export{Ft as default};
