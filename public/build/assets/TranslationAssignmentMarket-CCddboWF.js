import{h as o,e as s,b as t,_ as f,S as k,aj as b,c as n,w as c,r as a,F as y,i as B,j as r,g as v,t as m,d as w}from"./app-Cm2beRkj.js";import{S as x}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import A from"./AssignmentCard-BZMgGTgd.js";import"./index-BKm97uF2.js";import"./client-BWFz6ICJ.js";import"./sweetalert2.all-i0W-sCgv.js";import"./UsersIcon-ClDae0qo.js";import"./PaperClipIcon-7jOC84nb.js";function C(i,d){return t(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9zm3.75 11.625a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"})])}const S={components:{SBXDefaultPageLayout:x,AssignmentCard:A,SBXButton:k,DocumentMagnifyingGlassIcon:C},props:{assignments:Object,selectedAssignment:{type:Object,default:null},filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignment_market.title")},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignment_market.title")},methods:{showAll(){b.get(this.route("translation_assignment_market",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},D={class:"mt-6 mb-6"},j={key:0},X={class:"grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-4"},L={key:1},M={type:"button",class:"relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"},V={class:"mt-2 block text-base font-semibold text-gray-400"};function $(i,d,e,N,O,g){const l=a("AssignmentCard"),_=a("SBXButton"),u=a("DocumentMagnifyingGlassIcon"),p=a("SBXDefaultPageLayout");return t(),n(p,null,{default:c(()=>[s("div",D,[e.assignments.data.length>0?(t(),o("div",j,[s("div",X,[e.selectedAssignment?r("",!0):(t(!0),o(y,{key:0},B(e.assignments.data,h=>(t(),n(l,{assignment:h},null,8,["assignment"]))),256)),e.selectedAssignment?(t(),n(l,{key:1,assignment:e.selectedAssignment.data},null,8,["assignment"])):r("",!0)]),e.selectedAssignment?(t(),n(_,{key:0,onClick:g.showAll,class:"mt-4"},{default:c(()=>[v(m(i.$t("translations.translation_assignment_market.show_all_button")),1)]),_:1},8,["onClick"])):r("",!0)])):(t(),o("div",L,[s("button",M,[w(u,{class:"mx-auto h-12 w-12 text-gray-400 stroke-1"}),s("span",V,m(i.$t("translations.translation_assignment_market.no_assignments")),1)])]))])]),_:1})}const q=f(S,[["render",$]]);export{q as default};
