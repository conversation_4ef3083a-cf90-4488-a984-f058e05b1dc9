import a from"./ApiTokenManager-VCq84RPR.js";import{_ as o}from"./AppLayout-C4Ym-gUG.js";import{c as r,w as i,b as m,e,d as l}from"./app-Cm2beRkj.js";import"./ActionMessage-B_UncuA7.js";import"./DialogModal-CZeDHxad.js";import"./SectionTitle-zHGmFabz.js";import"./Checkbox-D_37U_LQ.js";import"./DangerButton-CnZ0_ogC.js";import"./FormSection-78brDZIO.js";import"./TextInput-Dx25uC_E.js";import"./InputLabel-dwoQhTuq.js";import"./PrimaryButton-7FvKQ4S5.js";import"./SecondaryButton-Cr6mqfK5.js";import"./SectionBorder-BOdnOtA6.js";const n={class:"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8"},w={__name:"Index",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(s){return(p,t)=>(m(),r(o,{title:"API Tokens"},{header:i(()=>t[0]||(t[0]=[e("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," API Tokens ",-1)])),default:i(()=>[e("div",null,[e("div",n,[l(a,{tokens:s.tokens,"available-permissions":s.availablePermissions,"default-permissions":s.defaultPermissions},null,8,["tokens","available-permissions","default-permissions"])])])]),_:1}))}};export{w as default};
