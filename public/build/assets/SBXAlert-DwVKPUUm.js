import{b as H,h as K,e as k,p as V,s as P,B as y,al as v,an as D,aw as Y,ax as w,ay as Z,H as ee,am as te,ao as z,O as J,Q as oe,V as ae,R as le,L as F,T as q,F as ne,M as ue,ap as S,Y as Q,x as X,az as M,a6 as U,aA as ie,ar as se,Z as re,I as A,G as pe,aq as de,W as j,X as I,_ as ve,r as ce,d as be,t as G}from"./app-Cm2beRkj.js";import{d as fe,e as me}from"./use-controllable-D9fh3JbV.js";function xe(o,c){return H(),K("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[k("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z","clip-rule":"evenodd"})])}function De(o,c){return H(),K("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[k("path",{"fill-rule":"evenodd",d:"M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z","clip-rule":"evenodd"})])}function ge(o,c){return o===c}var Oe=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Oe||{}),he=(o=>(o[o.Single=0]="Single",o[o.Multi=1]="Multi",o))(he||{}),ye=(o=>(o[o.Pointer=0]="Pointer",o[o.Other=1]="Other",o))(ye||{});let W=Symbol("ComboboxContext");function L(o){let c=pe(W,null);if(c===null){let f=new Error(`<${o} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(f,L),f}return c}let Ae=V({name:"Combobox",emits:{"update:modelValue":o=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>ge},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},name:{type:String},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(o,{slots:c,attrs:f,emit:C}){let e=P(1),t=P(null),p=P(null),h=P(null),g=P(null),d=P({static:!1,hold:!1}),a=P([]),m=P(null),R=P(1),B=P(!1);function x(n=s=>s){let s=m.value!==null?a.value[m.value]:null,r=de(n(a.value.slice()),b=>S(b.dataRef.domRef)),i=s?r.indexOf(s):null;return i===-1&&(i=null),{options:r,activeOptionIndex:i}}let _=y(()=>o.multiple?1:0),N=y(()=>o.nullable),[E,u]=fe(y(()=>o.modelValue),n=>C("update:modelValue",n),y(()=>o.defaultValue)),l={comboboxState:e,value:E,mode:_,compare(n,s){if(typeof o.by=="string"){let r=o.by;return(n==null?void 0:n[r])===(s==null?void 0:s[r])}return o.by(n,s)},nullable:N,inputRef:p,labelRef:t,buttonRef:h,optionsRef:g,disabled:y(()=>o.disabled),options:a,change(n){u(n)},activeOptionIndex:y(()=>{if(B.value&&m.value===null&&a.value.length>0){let n=a.value.findIndex(s=>!s.dataRef.disabled);if(n!==-1)return n}return m.value}),activationTrigger:R,optionsPropsRef:d,closeCombobox(){B.value=!1,!o.disabled&&e.value!==1&&(e.value=1,m.value=null)},openCombobox(){if(B.value=!0,o.disabled||e.value===0)return;let n=a.value.findIndex(s=>{let r=v(s.dataRef.value);return D(_.value,{0:()=>l.compare(v(l.value.value),v(r)),1:()=>v(l.value.value).some(i=>l.compare(v(i),v(r)))})});n!==-1&&(m.value=n),e.value=0},goToOption(n,s,r){if(B.value=!1,o.disabled||g.value&&!d.value.static&&e.value===1)return;let i=x();if(i.activeOptionIndex===null){let O=i.options.findIndex($=>!$.dataRef.disabled);O!==-1&&(i.activeOptionIndex=O)}let b=Y(n===w.Specific?{focus:w.Specific,id:s}:{focus:n},{resolveItems:()=>i.options,resolveActiveIndex:()=>i.activeOptionIndex,resolveId:O=>O.id,resolveDisabled:O=>O.dataRef.disabled});m.value=b,R.value=r??1,a.value=i.options},selectOption(n){let s=a.value.find(i=>i.id===n);if(!s)return;let{dataRef:r}=s;u(D(_.value,{0:()=>r.value,1:()=>{let i=v(l.value.value).slice(),b=v(r.value),O=i.findIndex($=>l.compare(b,v($)));return O===-1?i.push(b):i.splice(O,1),i}}))},selectActiveOption(){if(l.activeOptionIndex.value===null)return;let{dataRef:n,id:s}=a.value[l.activeOptionIndex.value];u(D(_.value,{0:()=>n.value,1:()=>{let r=v(l.value.value).slice(),i=v(n.value),b=r.findIndex(O=>l.compare(i,v(O)));return b===-1?r.push(i):r.splice(b,1),r}})),l.goToOption(w.Specific,s)},registerOption(n,s){let r={id:n,dataRef:s},i=x(b=>[...b,r]);if(m.value===null){let b=s.value.value;D(_.value,{0:()=>l.compare(v(l.value.value),v(b)),1:()=>v(l.value.value).some(O=>l.compare(v(O),v(b)))})&&(i.activeOptionIndex=i.options.indexOf(r))}a.value=i.options,m.value=i.activeOptionIndex,R.value=1},unregisterOption(n){let s=x(r=>{let i=r.findIndex(b=>b.id===n);return i!==-1&&r.splice(i,1),r});a.value=s.options,m.value=s.activeOptionIndex,R.value=1}};Z([p,h,g],()=>l.closeCombobox(),y(()=>e.value===0)),ee(W,l),te(y(()=>D(e.value,{0:z.Open,1:z.Closed})));let T=y(()=>l.activeOptionIndex.value===null?null:a.value[l.activeOptionIndex.value].dataRef.value);return()=>{let{name:n,disabled:s,...r}=o,i={open:e.value===0,disabled:s,activeIndex:l.activeOptionIndex.value,activeOption:T.value,value:E.value};return J(ne,[...n!=null&&E.value!=null?me({[n]:E.value}).map(([b,O])=>J(oe,ae({features:le.Hidden,key:b,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:b,value:O}))):[],F({theirProps:{...f,...q(r,["modelValue","defaultValue","nullable","multiple","onUpdate:modelValue","by"])},ourProps:{},slot:i,slots:c,attrs:f,name:"Combobox"})])}}}),Ee=V({name:"ComboboxLabel",props:{as:{type:[Object,String],default:"label"}},setup(o,{attrs:c,slots:f}){let C=L("ComboboxLabel"),e=`headlessui-combobox-label-${j()}`;function t(){var p;(p=S(C.inputRef))==null||p.focus({preventScroll:!0})}return()=>{let p={open:C.comboboxState.value===0,disabled:C.disabled.value},h={id:e,ref:C.labelRef,onClick:t};return F({ourProps:h,theirProps:o,slot:p,attrs:c,slots:f,name:"ComboboxLabel"})}}}),Ve=V({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"}},setup(o,{attrs:c,slots:f,expose:C}){let e=L("ComboboxButton"),t=`headlessui-combobox-button-${j()}`;C({el:e.buttonRef,$el:e.buttonRef});function p(d){e.disabled.value||(e.comboboxState.value===0?e.closeCombobox():(d.preventDefault(),e.openCombobox()),A(()=>{var a;return(a=S(e.inputRef))==null?void 0:a.focus({preventScroll:!0})}))}function h(d){switch(d.key){case I.ArrowDown:d.preventDefault(),d.stopPropagation(),e.comboboxState.value===1&&e.openCombobox(),A(()=>{var a;return(a=e.inputRef.value)==null?void 0:a.focus({preventScroll:!0})});return;case I.ArrowUp:d.preventDefault(),d.stopPropagation(),e.comboboxState.value===1&&(e.openCombobox(),A(()=>{e.value.value||e.goToOption(w.Last)})),A(()=>{var a;return(a=e.inputRef.value)==null?void 0:a.focus({preventScroll:!0})});return;case I.Escape:if(e.comboboxState.value!==0)return;d.preventDefault(),e.optionsRef.value&&!e.optionsPropsRef.value.static&&d.stopPropagation(),e.closeCombobox(),A(()=>{var a;return(a=e.inputRef.value)==null?void 0:a.focus({preventScroll:!0})});return}}let g=ue(y(()=>({as:o.as,type:c.type})),e.buttonRef);return()=>{var d,a;let m={open:e.comboboxState.value===0,disabled:e.disabled.value,value:e.value.value},R={ref:e.buttonRef,id:t,type:g.value,tabindex:"-1","aria-haspopup":!0,"aria-controls":(d=S(e.optionsRef))==null?void 0:d.id,"aria-expanded":e.disabled.value?void 0:e.comboboxState.value===0,"aria-labelledby":e.labelRef.value?[(a=S(e.labelRef))==null?void 0:a.id,t].join(" "):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:h,onClick:p};return F({ourProps:R,theirProps:o,slot:m,attrs:c,slots:f,name:"ComboboxButton"})}}}),Fe=V({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function}},emits:{change:o=>!0},setup(o,{emit:c,attrs:f,slots:C,expose:e}){let t=L("ComboboxInput"),p=`headlessui-combobox-input-${j()}`;e({el:t.inputRef,$el:t.inputRef});let h=P(t.value.value),g=()=>{var u;let l=t.value.value;return S(t.inputRef)?typeof o.displayValue<"u"?(u=o.displayValue(l))!=null?u:"":typeof l=="string"?l:"":""},d=P(""),a=!1;function m(u){let l=S(t.inputRef);!l||(l.value=u,a=!0,l.dispatchEvent(new Event("input",{bubbles:!0})),a=!1)}Q(()=>{X([t.value,d],()=>{h.value=g()},{flush:"sync",immediate:!0}),X([h,t.comboboxState],([u,l],[T,n])=>{let s=S(t.inputRef);!s||(n===0&&l===1?m(u):u!==T&&(s.value=u))},{immediate:!0})});let R=P(!1);function B(){R.value=!0}function x(){setTimeout(()=>{R.value=!1})}function _(u){switch(u.key){case I.Backspace:case I.Delete:if(t.mode.value!==0||!t.nullable.value)return;let l=u.currentTarget;requestAnimationFrame(()=>{if(l.value===""){t.change(null);let T=S(t.optionsRef);T&&(T.scrollTop=0),t.goToOption(w.Nothing)}});break;case I.Enter:if(t.comboboxState.value!==0||R.value)return;if(u.preventDefault(),u.stopPropagation(),t.activeOptionIndex.value===null){t.closeCombobox();return}t.selectActiveOption(),t.mode.value===0&&t.closeCombobox();break;case I.ArrowDown:return u.preventDefault(),u.stopPropagation(),D(t.comboboxState.value,{0:()=>t.goToOption(w.Next),1:()=>t.openCombobox()});case I.ArrowUp:return u.preventDefault(),u.stopPropagation(),D(t.comboboxState.value,{0:()=>t.goToOption(w.Previous),1:()=>{t.openCombobox(),A(()=>{t.value.value||t.goToOption(w.Last)})}});case I.Home:case I.PageUp:return u.preventDefault(),u.stopPropagation(),t.goToOption(w.First);case I.End:case I.PageDown:return u.preventDefault(),u.stopPropagation(),t.goToOption(w.Last);case I.Escape:if(t.comboboxState.value!==0)return;u.preventDefault(),t.optionsRef.value&&!t.optionsPropsRef.value.static&&u.stopPropagation(),t.closeCombobox();break;case I.Tab:if(t.comboboxState.value!==0)return;t.mode.value===0&&t.selectActiveOption(),t.closeCombobox();break}}function N(u){c("change",u)}function E(u){a||t.openCombobox(),c("change",u)}return()=>{var u,l,T,n,s,r;let i={open:t.comboboxState.value===0},b={"aria-controls":(u=t.optionsRef.value)==null?void 0:u.id,"aria-expanded":t.disabled.value?void 0:t.comboboxState.value===0,"aria-activedescendant":t.activeOptionIndex.value===null||(l=t.options.value[t.activeOptionIndex.value])==null?void 0:l.id,"aria-multiselectable":t.mode.value===1?!0:void 0,"aria-labelledby":(s=(T=S(t.labelRef))==null?void 0:T.id)!=null?s:(n=S(t.buttonRef))==null?void 0:n.id,id:p,onCompositionstart:B,onCompositionend:x,onKeydown:_,onChange:N,onInput:E,role:"combobox",type:(r=f.type)!=null?r:"text",tabIndex:0,ref:t.inputRef},O=q(o,["displayValue"]);return F({ourProps:b,theirProps:O,slot:i,attrs:f,slots:C,features:M.RenderStrategy|M.Static,name:"ComboboxInput"})}}}),Le=V({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(o,{attrs:c,slots:f,expose:C}){let e=L("ComboboxOptions"),t=`headlessui-combobox-options-${j()}`;C({el:e.optionsRef,$el:e.optionsRef}),U(()=>{e.optionsPropsRef.value.static=o.static}),U(()=>{e.optionsPropsRef.value.hold=o.hold});let p=ie(),h=y(()=>p!==null?p.value===z.Open:e.comboboxState.value===0);return se({container:y(()=>S(e.optionsRef)),enabled:y(()=>e.comboboxState.value===0),accept(g){return g.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:g.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(g){g.setAttribute("role","none")}}),()=>{var g,d,a,m;let R={open:e.comboboxState.value===0},B={"aria-activedescendant":e.activeOptionIndex.value===null||(g=e.options.value[e.activeOptionIndex.value])==null?void 0:g.id,"aria-labelledby":(m=(d=S(e.labelRef))==null?void 0:d.id)!=null?m:(a=S(e.buttonRef))==null?void 0:a.id,id:t,ref:e.optionsRef,role:"listbox"},x=q(o,["hold"]);return F({ourProps:B,theirProps:x,slot:R,attrs:c,slots:f,features:M.RenderStrategy|M.Static,visible:h.value,name:"ComboboxOptions"})}}}),Ne=V({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1}},setup(o,{slots:c,attrs:f,expose:C}){let e=L("ComboboxOption"),t=`headlessui-combobox-option-${j()}`,p=P(null);C({el:p,$el:p});let h=y(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===t:!1),g=y(()=>D(e.mode.value,{0:()=>e.compare(v(e.value.value),v(o.value)),1:()=>v(e.value.value).some(x=>e.compare(v(x),v(o.value)))})),d=y(()=>({disabled:o.disabled,value:o.value,domRef:p}));Q(()=>e.registerOption(t,d)),re(()=>e.unregisterOption(t)),U(()=>{e.comboboxState.value===0&&(!h.value||e.activationTrigger.value!==0&&A(()=>{var x,_;return(_=(x=S(p))==null?void 0:x.scrollIntoView)==null?void 0:_.call(x,{block:"nearest"})}))});function a(x){if(o.disabled)return x.preventDefault();e.selectOption(t),e.mode.value===0&&e.closeCombobox()}function m(){if(o.disabled)return e.goToOption(w.Nothing);e.goToOption(w.Specific,t)}function R(){o.disabled||h.value||e.goToOption(w.Specific,t,0)}function B(){o.disabled||!h.value||e.optionsPropsRef.value.hold||e.goToOption(w.Nothing)}return()=>{let{disabled:x}=o,_={active:h.value,selected:g.value,disabled:x},N={id:t,ref:p,role:"option",tabIndex:x===!0?void 0:-1,"aria-disabled":x===!0?!0:void 0,"aria-selected":g.value,disabled:void 0,onClick:a,onFocus:m,onPointermove:R,onMousemove:R,onPointerleave:B,onMouseleave:B};return F({ourProps:N,theirProps:o,slot:_,attrs:f,slots:c,name:"ComboboxOption"})}}});const Ce={components:{CheckCircleIcon:xe},props:{title:{type:String,default:null},message:{type:String,default:null},variant:{type:String,default:"success"}}},Re={class:"rounded-md bg-green-200 p-4"},Se={class:"flex"},Ie={class:"flex-shrink-0"},Pe={class:"ml-3"},we={class:"text-sm font-medium text-green-800"},_e={class:"mt-1 text-sm text-green-700"};function Be(o,c,f,C,e,t){const p=ce("CheckCircleIcon");return H(),K("div",Re,[k("div",Se,[k("div",Ie,[be(p,{class:"h-5 w-5 text-green-400","aria-hidden":"true"})]),k("div",Pe,[k("h3",we,G(f.title),1),k("div",_e,[k("p",null,G(f.message),1)])])])])}const je=ve(Ce,[["render",Be]]);export{Ae as A,Fe as B,Ee as F,Ve as L,Ne as N,je as S,Le as j,De as r};
