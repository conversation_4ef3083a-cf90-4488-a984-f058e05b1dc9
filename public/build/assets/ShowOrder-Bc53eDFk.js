import{_ as p,p as x,c as g,w as c,S as u,r as y,b as r,e as t,t as s,g as d,h as l,j as n,i as _,n as f,F as h}from"./app-Cm2beRkj.js";import{S as w}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const v=x({components:{SBXDefaultPageLayout:w,SBXButton:u},props:{order:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.orders.show_order_title"),console.log("Order: ",this.order)},data(){return{}},computed:{shownName(){return this.order.data.source=="Bestäälningsformulär"?this.order.data.name:this.order.data.source=="Zettle"?"Okänd kund":this.order.data.source=="Webshop"?this.order.data.name:this.order.data.name}},methods:{formatAmount(e){return e.toLocaleString("SE-sv",{minimumFractionDigits:2,maximumFractionDigits:2})}}}),b={class:"grid grid-cols-3 gap-4"},k={class:"mt-1 max-w-2xl text-sm text-gray-500"},B={class:"mt-1 max-w-2xl text-sm text-gray-500"},S={class:"grid grid-cols-3 gap-8"},D={class:"mt-12"},$={class:"mt-1 max-w-2xl text-sm text-gray-500"},A={class:"mt-1 max-w-2xl text-sm text-gray-500"},C={class:"mt-1 max-w-2xl text-sm text-gray-500"},L={class:"mt-1 max-w-2xl text-sm text-gray-500"},T={class:"mt-1 max-w-2xl text-sm text-gray-500"},E={class:"mt-1 max-w-2xl text-sm text-gray-500"},F={class:"mt-1 max-w-2xl text-sm text-gray-500"},K={class:"mt-12"},N={class:"mt-1 max-w-2xl text-sm text-gray-500"},O={class:"mt-1 max-w-2xl text-sm text-gray-500"},P={class:"mt-1 max-w-2xl text-sm text-gray-500"},X={class:"mt-1 max-w-2xl text-sm text-gray-500"},V={class:"mt-1 max-w-2xl text-sm text-gray-500"},j={class:"grid grid-cols-2 gap-4"},q={class:"mt-12"},z={key:0,class:"text-lg leading-6 font-medium text-gray-900"},U={class:"mt-1 max-w-2xl text-sm text-gray-500"},W={class:"mt-12"},Z={key:0,class:"text-lg leading-6 font-medium text-gray-900"},G={class:"mt-1 max-w-2xl text-sm text-gray-500"},H={class:"flex flex-col mt-12"},I={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},J={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},M={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},Q={class:"min-w-full divide-y divide-gray-200"},R={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Y={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},tt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-right text-gray-900"},et={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-right text-gray-900"},st={class:"px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900"};function at(e,a,ot,rt,dt,lt){const i=y("SBXDefaultPageLayout");return r(),g(i,null,{default:c(()=>[t("div",b,[t("div",null,[a[0]||(a[0]=t("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"Beställning #",-1)),t("p",k,s(e.order.data.order_no),1)]),t("div",null,[a[1]||(a[1]=t("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"Datum",-1)),t("p",B,s(e.order.data.ordered_at),1)]),a[2]||(a[2]=t("div",null,null,-1))]),t("div",S,[t("div",D,[a[5]||(a[5]=t("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"Kund",-1)),t("p",$,s(e.order.data.company_name),1),t("p",A,s(e.order.data.first_name)+" "+s(e.order.data.last_name),1),t("p",C,s(e.order.data.address_line_1),1),t("p",L,s(e.order.data.address_line_2),1),t("p",T,s(e.order.data.postal_code)+" "+s(e.order.data.city),1),t("p",E,[a[3]||(a[3]=t("span",{class:"font-bold"},"E-post:",-1)),d(" "+s(e.order.data.email),1)]),t("p",F,[a[4]||(a[4]=t("span",{class:"font-bold"},"Telefon:",-1)),d(" "+s(e.order.data.phone_no),1)])]),t("div",K,[a[8]||(a[8]=t("h3",{class:"text-lg leading-6 font-medium text-gray-900"},"Utlämningsställe",-1)),t("p",N,s(e.order.data.delivery_location_name),1),t("p",O,s(e.order.data.delivery_location_address),1),t("p",P,s(e.order.data.delivery_location_postal_code)+" "+s(e.order.data.delivery_location_city),1),t("p",X,[a[6]||(a[6]=t("span",{class:"font-bold"},"Datum:",-1)),d(" "+s(e.order.data.delivery_location_delivery_date),1)]),t("p",V,[a[7]||(a[7]=t("span",{class:"font-bold"},"Tid:",-1)),d(" "+s(e.order.data.delivery_location_delivery_time),1)])])]),t("div",j,[t("div",q,[e.order.data.customer_notes!=null?(r(),l("h3",z,"Kundens anteckningar")):n("",!0),t("p",U,s(e.order.data.customer_notes),1)]),t("div",W,[e.order.data.internal_notes!=null?(r(),l("h3",Z,"Egna anteckningar")):n("",!0),t("p",G,s(e.order.data.internal_notes),1)])]),t("div",H,[t("div",I,[t("div",J,[t("div",M,[t("table",Q,[a[12]||(a[12]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Antal "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Produkt "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right"}," Styckrpris "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right"}," Total ")])],-1)),t("tbody",null,[(r(!0),l(h,null,_(e.order.data.rows,(o,m)=>(r(),l("tr",{key:o.id,class:f(m%2===0?"bg-white":"bg-gray-50")},[t("td",R,s(o.quantity)+" st ",1),t("td",Y,[t("p",null,s(o.product_name),1),t("p",null,s(o.product_meta_1),1),t("p",null,s(o.product_meta_2),1),t("p",null,s(o.product_meta_3),1)]),t("td",tt,s(e.formatAmount(o.unit_price))+" Kr ",1),t("td",et,s(e.formatAmount(o.row_total))+" Kr ",1)],2))),128)),t("tr",null,[a[9]||(a[9]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900"}," Total ",-1)),a[10]||(a[10]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},null,-1)),a[11]||(a[11]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},null,-1)),t("td",st,s(e.formatAmount(e.order.data.order_total))+" kr ",1)])])])])])])])]),_:1})}const mt=p(v,[["render",at]]);export{mt as default};
