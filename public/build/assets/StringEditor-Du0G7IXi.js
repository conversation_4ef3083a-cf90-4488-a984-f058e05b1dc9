import{_ as c,S as p,c as f,w as r,r as n,b as g,e as t,t as i,l as _,m as v,d as b,g as h,k as B}from"./app-Cm2beRkj.js";import{S}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const x={components:{SBXDefaultPageLayout:S,SBXButton:p},props:{setting:Object},remember:"form",data(){return{form:this.$inertia.form({string_value:this.setting.data.string_value})}},computed:{},methods:{update(){this.form.processing||this.form.put(this.route("settings.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.setting.data.id]))}}},y={class:"mt-4"},k={for:"string_value",class:"block text-sm font-medium text-gray-700"},w={class:"mt-1"};function X(l,e,u,D,o,a){const d=n("SBXButton"),m=n("SBXDefaultPageLayout");return g(),f(m,null,{default:r(()=>[t("form",{onSubmit:e[1]||(e[1]=B((...s)=>a.update&&a.update(...s),["prevent"]))},[t("div",y,[t("label",k,i(u.setting.data.name),1),t("div",w,[_(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>o.form.string_value=s),type:"text",name:"string_value",id:"string_value",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[v,o.form.string_value]])])]),b(d,{class:"mt-4"},{default:r(()=>[h(i(l.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const N=c(x,[["render",X]]);export{N as default};
