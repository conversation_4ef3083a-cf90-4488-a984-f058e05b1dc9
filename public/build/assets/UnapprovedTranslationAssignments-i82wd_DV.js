import{_ as v,h as m,d as i,e as t,w as h,F as x,r as o,b as c,t as s,j as p,g as d}from"./app-Cm2beRkj.js";import{r as B}from"./ArrowRightIcon-CEuU5Xpj.js";import{S as y}from"./SBXDataTable-C66uxM7K.js";import{S as A}from"./SBXFilterBar-w4dDtxld.js";import{S}from"./SBXGenericNotification-BrDLezmQ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const $={components:{ArrowRightIcon:B,SBXDataTable:y,SBXFilterBar:A,SBXGenericNotification:S},props:{translationAssignments:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignments.unapproved_title"),this.$page.props.flash.authorization!=null&&(this.showAuthorizationNotification=!0)},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignments.unapproved_title")},data(){return{columns:[{key:"assignmentslot",label:this.$t("translations.translation_assignments.title")},{key:"customerslot",label:this.$t("translations.translation_assignments.customer_label")},{key:"created_at",label:this.$t("translations.translation_assignments.date_label")}],translationAssignmentFilters:{search:this.filters.search},showAuthorizationNotification:!1}},computed:{authorizationMessage(){return this.$page.props.flash.authorization==null?"":this.$page.props.flash.authorization}},methods:{changeFilters(){this.$inertia.get(this.route("translation_assignment_approvals",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translationAssignmentFilters,{preserveState:!1,replace:!0})},searchValueChanged(a){this.translationAssignmentFilters.search=a},authorizationNotificationCancelled(){this.showAuthorizationNotification=!1}}},N={class:"flex flex-col"},z={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},w={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},C={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},F={class:"text-sm font-semibold"},k={class:"text-sm"},V={class:"flex items-center"},X={class:"text-sm"},R={class:"text-sm"},D={class:"text-sm"},T={key:0,class:"text-sm font-semibold"},j={key:1,class:"text-sm font-semibold"},q={class:"text-sm font-semibold"},G={class:"text-sm font-semibold"},I={class:"text-sm"},E=["href"],M={class:"text-sm"},O=["href"];function U(a,n,_,H,l,r){const g=o("SBXFilterBar"),u=o("ArrowRightIcon"),f=o("SBXDataTable"),b=o("SBXGenericNotification");return c(),m(x,null,[i(g,{onSearchValueChanged:r.searchValueChanged,filters:l.translationAssignmentFilters,searchRoute:"translation_assignment_approvals",placeholder:a.$t("translations.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),t("div",N,[t("div",z,[t("div",w,[t("div",C,[i(f,{columns:l.columns,items:_.translationAssignments.data,showAddButton:!1,showEditButton:!0,editRoute:"translation_assignments.edit",showDeleteButton:!1,showViewButton:!0,viewRoute:"translation_assignment_approvals.show",paginator:_.translationAssignments.meta},{assignmentslot:h(e=>[t("div",null,[t("p",F,s(a.$t("translations.global.assignment_id"))+": "+s(e.item.assignment_id),1),t("p",k,s(e.item.translation_category),1),t("div",V,[t("p",X,s(e.item.from_translation_language),1),i(u,{class:"h-3 w-3 mx-2"}),t("p",R,s(e.item.to_translation_language),1)]),t("p",D,s(a.$t("translations.translation_assignments.is_authorization_required_label"))+": "+s(e.item.authorization_text),1)])]),customerslot:h(e=>[t("div",null,[e.item.assignment_type=="company"?(c(),m("p",T,s(a.$t("translations.my_assignments.assignment_detail.company_label")),1)):p("",!0),e.item.assignment_type=="personal"?(c(),m("p",j,s(a.$t("translations.my_assignments.assignment_detail.private_person_label")),1)):p("",!0),t("p",q,s(e.item.company),1),t("p",G,s(e.item.first_name)+" "+s(e.item.last_name),1),t("p",I,[t("strong",null,s(a.$t("translations.translation_assignments.email_not_required_label"))+":",1),n[0]||(n[0]=d()),t("a",{href:"mailto:"+e.item.email},s(e.item.email),9,E)]),t("p",M,[t("strong",null,s(a.$t("translations.translation_assignments.phone_no_not_required_label"))+":",1),n[1]||(n[1]=d()),t("a",{href:"tel:"+e.item.phone_no},s(e.item.phone_no),9,O)])])]),_:1},8,["columns","items","paginator"])])])])]),i(b,{show:l.showAuthorizationNotification,variant:"warning",message:r.authorizationMessage,onNotificationCancelled:r.authorizationNotificationCancelled},null,8,["show","message","onNotificationCancelled"])],64)}const nt=v($,[["render",U]]);export{nt as default};
