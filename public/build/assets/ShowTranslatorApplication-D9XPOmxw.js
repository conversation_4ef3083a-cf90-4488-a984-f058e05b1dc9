import{_ as f,p as b,c as A,w as p,S as $,r as m,b as n,e as o,t as a,h as s,g as i,j as e,F as c,i as d,d as u}from"./app-Cm2beRkj.js";import{S as g}from"./sweetalert2.all-i0W-sCgv.js";import{S as k}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const v=b({components:{SBXDefaultPageLayout:k,SBXButton:$},props:{translatorApplication:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translator_applications.admin.show_title")},data(){return{deliveryLocation:this.translatorApplication.data.deliveryLocation}},computed:{},methods:{approveTranslatorApplication(){var t=this;g.fire({title:t.$t("translations.translator_applications.admin.approve_dialog_title"),text:t.$t("translations.translator_applications.admin.approve_dialog_message"),icon:"warning",showCancelButton:!0,cancelButtonText:t.$t("translations.global.cancel"),confirmButtonColor:"#15803d",confirmButtonText:t.$t("translations.translator_applications.admin.approve_button")}).then(r=>{r.value&&t.$inertia.post(t.route("translator_applications.approve",[t.$page.props.locale.selected_market_code,t.$page.props.locale.selected_language_code,t.translatorApplication.data.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})},rejectTranslatorApplication(){var t=this;g.fire({title:t.$t("translations.translator_applications.admin.reject_dialog_title"),text:t.$t("translations.translator_applications.admin.reject_dialog_message"),icon:"warning",showCancelButton:!0,cancelButtonText:t.$t("translations.global.cancel"),confirmButtonColor:"#d33",confirmButtonText:t.$t("translations.translator_applications.admin.reject_button")}).then(r=>{r.value&&t.$inertia.post(t.route("translator_applications.reject",[t.$page.props.locale.selected_market_code,t.$page.props.locale.selected_language_code,t.translatorApplication.data.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})}}}),B={class:"text-lg block font-bold"},y={class:"text-base block font-regular"},S={class:"text-base block font-regular"},C={class:"text-base block font-regular"},T={class:"text-base block font-regular"},j={class:"text-base block font-regular"},w={key:0,class:"text-sm mt-4"},L={class:"text-sm mt-4"},X=["href"],D={class:"text-sm"},N=["href"],V={key:1,class:"mt-4"},P={class:"text-sm block font-bold"},z={class:"text-sm block"},F={key:2,class:"mt-4"},E={class:"text-sm block font-bold"},O={class:"text-sm block"},q={key:3,class:"mt-4"},G={class:"text-sm block font-bold"},H={class:"text-sm block"},I={key:4,class:"text-sm mt-4"},J={class:"mt-4 flex"};function K(t,r,M,Q,R,U){const _=m("SBXButton"),h=m("SBXDefaultPageLayout");return n(),A(h,null,{default:p(()=>[o("p",B,a(t.$t("translations.translator_applications.admin.submitted_info_title")),1),o("p",y,a(t.translatorApplication.data.company),1),o("p",S,a(t.translatorApplication.data.first_name)+" "+a(t.translatorApplication.data.last_name),1),o("p",C,a(t.translatorApplication.data.address_1),1),o("p",T,a(t.translatorApplication.data.address_2),1),o("p",j,a(t.translatorApplication.data.postal_code)+" "+a(t.translatorApplication.data.city),1),t.translatorApplication.data.company_no!=null&&t.translatorApplication.data.company_no!=""?(n(),s("p",w,[o("strong",null,a(t.$t("translations.translator_applications.admin.company_no_label"))+": ",1),i(a(t.translatorApplication.data.company_no),1)])):e("",!0),o("p",L,[o("strong",null,a(t.$t("translations.translator_applications.admin.email_label"))+": ",1),o("a",{href:`mailto:${t.translatorApplication.data.email}`},a(t.translatorApplication.data.email),9,X)]),o("p",D,[o("strong",null,a(t.$t("translations.translator_applications.admin.phone_no_label"))+": ",1),o("a",{href:`tel:${t.translatorApplication.data.phone_no}`},a(t.translatorApplication.data.phone_no),9,N)]),t.translatorApplication.data.services.length>0?(n(),s("div",V,[o("p",P,a(t.$t("translations.translator_applications.admin.services_label")),1),(n(!0),s(c,null,d(t.translatorApplication.data.services,l=>(n(),s("p",z,a(l.name),1))),256))])):e("",!0),t.translatorApplication.data.from_languages.length>0?(n(),s("div",F,[o("p",E,a(t.$t("translations.translator_applications.admin.from_languages_label")),1),(n(!0),s(c,null,d(t.translatorApplication.data.from_languages,l=>(n(),s("p",O,a(l.name),1))),256))])):e("",!0),t.translatorApplication.data.to_languages.length>0?(n(),s("div",q,[o("p",G,a(t.$t("translations.translator_applications.admin.to_languages_label")),1),(n(!0),s(c,null,d(t.translatorApplication.data.to_languages,l=>(n(),s("p",H,a(l.name),1))),256))])):e("",!0),t.translatorApplication.data.company_no!=null&&t.translatorApplication.data.company_no!=""?(n(),s("p",I,[o("strong",null,a(t.$t("translations.translator_applications.admin.authorized_id_label"))+": ",1),i(a(t.translatorApplication.data.authorization_id),1)])):e("",!0),o("div",J,[u(_,{onClick:t.approveTranslatorApplication,variant:"success",class:"mb-4 mt-2 mr-2"},{default:p(()=>[i(a(t.$t("translations.translator_applications.admin.approve_button")),1)]),_:1},8,["onClick"]),u(_,{onClick:t.rejectTranslatorApplication,variant:"danger",class:"mb-4 mt-2"},{default:p(()=>[i(a(t.$t("translations.translator_applications.admin.reject_button")),1)]),_:1},8,["onClick"])])]),_:1})}const x=f(v,[["render",K]]);export{x as default};
