import{_ as h,S as p,h as s,e,t as l,j as n,d as c,w as g,k as x,r as d,b as i}from"./app-Cm2beRkj.js";import"./sweetalert2.all-i0W-sCgv.js";import{r as y}from"./TrashIcon-fhKAjDA0.js";const v={components:{TrashIcon:y,SBXButton:p},props:["rowNo","file"],mounted(){},data(){return{currentFile:null}},computed:{isCompleted(){return this.file.article_id!=null},filename(){return"/translation_assignments/"+this.file.filename}},methods:{removeFile(){this.$emit("removeFile",this.file.id)},updateFile(o){this.currentFile=o.target.files[0],this.$emit("updateFile",this.file.id,this.currentFile)}}},w={class:"grid grid-cols-12 mb-2"},F={class:"col-span-1 flex justify-center items-center"},b={class:"text-xl font-semibold"},k={key:0,class:"col-span-10 flex justify-start items-center"},B={class:"w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 bg-white mr-4"},C={class:"text-left"},N={key:0,class:"flex text-sm text-gray-600"},S={class:"relative ml-2 cursor-pointer rounded-md bg-white font-semibold text-gray-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500"},j={key:1,class:"flex text-sm text-gray-600"},I={class:"ml-2"},T={key:1,class:"col-span-9"},V=["href"],X={class:"mt-2"},z={class:"col-span-1 flex justify-end items-center"};function A(o,a,u,D,r,t){const m=d("TrashIcon"),f=d("SBXButton");return i(),s("div",w,[e("div",F,[e("p",b,l(u.rowNo+1),1)]),t.isCompleted?n("",!0):(i(),s("div",k,[e("div",B,[e("div",C,[r.currentFile==null?(i(),s("div",N,[e("label",S,[e("span",null,l(o.$t("translations.translation_assignments.file_upload_label")),1),e("input",{id:"file-upload",type:"file",onChange:a[0]||(a[0]=(..._)=>t.updateFile&&t.updateFile(..._)),class:"sr-only"},null,32)])])):n("",!0),r.currentFile!=null?(i(),s("div",j,[e("p",I,l(r.currentFile.name),1)])):n("",!0)])])])),t.isCompleted?(i(),s("div",T,[t.filename?(i(),s("a",{key:0,href:t.filename,target:"_blank"},[e("span",X,l(this.file.original_filename),1)],8,V)):n("",!0)])):n("",!0),e("div",z,[c(f,{variant:"danger",size:"s",onClick:x(t.removeFile,["prevent"])},{default:g(()=>[c(m,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:1},8,["onClick"])])])}const q=h(v,[["render",A]]);export{q as default};
