import{b as r,h as o,J as n}from"./app-Cm2beRkj.js";const s=["type"],c={__name:"PrimaryButton",props:{type:{type:String,default:"submit"}},setup(e){return(t,a)=>(r(),o("button",{type:e.type,class:"inline-flex items-center px-4 py-2 bg-oversattare-green border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-oversattare-green active:bg-oversattare-green focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 transition"},[n(t.$slots,"default")],8,s))}};export{c as _};
