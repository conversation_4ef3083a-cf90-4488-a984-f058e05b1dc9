import{_ as c,S as _,c as f,w as n,r as l,b,e as g,d as s,g as h,t as B,k as S}from"./app-Cm2beRkj.js";import{S as v}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as L}from"./SBXInput-C8dZEgZe.js";import{S as X}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const w={components:{SBXDefaultPageLayout:v,SBXInput:L,SBXToggle:X,SBXButton:_},props:{priceList:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("PriceList: ",this.priceList.data)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.priceList.data.name,is_active:this.priceList.data.is_active,is_default:this.priceList.data.is_default,prices_include_vat:this.priceList.data.prices_include_vat})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.priceList.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("price_lists.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.priceList.data.id]))}}};function T(r,e,i,x,o,p){const m=l("SBXInput"),a=l("SBXToggle"),d=l("SBXButton"),u=l("SBXDefaultPageLayout");return b(),f(u,null,{default:n(()=>[g("form",{onSubmit:e[4]||(e[4]=S((...t)=>p.update&&p.update(...t),["prevent"]))},[s(m,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:r.$t("sbxwebshop.price_lists.name_label"),error:i.errors.name},null,8,["model","label","error"]),s(a,{model:o.form.is_active,"onUpdate:model":e[1]||(e[1]=t=>o.form.is_active=t),label:r.$t("sbxwebshop.price_lists.is_active_label"),error:i.errors.is_active},null,8,["model","label","error"]),s(a,{model:o.form.is_default,"onUpdate:model":e[2]||(e[2]=t=>o.form.is_default=t),label:r.$t("sbxwebshop.price_lists.is_default_label"),error:i.errors.is_default},null,8,["model","label","error"]),s(a,{model:o.form.prices_include_vat,"onUpdate:model":e[3]||(e[3]=t=>o.form.prices_include_vat=t),label:r.$t("sbxwebshop.price_lists.prices_include_vat_label"),error:i.errors.prices_include_vat},null,8,["model","label","error"]),s(d,{class:"mt-4"},{default:n(()=>[h(B(r.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const V=c(w,[["render",T]]);export{V as default};
