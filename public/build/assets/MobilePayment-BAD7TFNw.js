import{_ as p,S as _,h as d,e as n,t as e,g as o,d as l,w as u,r as c,b as y}from"./app-Cm2beRkj.js";import{S as b}from"./sweetalert2.all-i0W-sCgv.js";import{r as f}from"./EnvelopeIcon-C7yn4PbF.js";const g={components:{EnvelopeIcon:f,SBXButton:_},props:{payment:Object},data(){return{}},methods:{formatNumber(t){return t.toLocaleString("SE-sv",{minimumFractionDigits:2,maximumFractionDigits:2})},resendReceipt(){var t=this;b.fire({title:t.$t("translations.payments.resend_receipt_dialog_title"),text:t.$t("translations.payments.resend_receipt_dialog_message"),icon:"info",showCancelButton:!0,cancelButtonText:t.$t("translations.global.cancel"),confirmButtonColor:"#02B07D",confirmButtonText:t.$t("translations.payments.resend_receipt_dialog_send_button")}).then(s=>{s.value&&t.$inertia.post(t.route("payments.resend_receipt",[t.$page.props.locale.selected_market_code,t.$page.props.locale.selected_language_code,this.payment.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})}}},h={class:"p-4 border-b"},B={class:"text-base"},v={class:"text-base"},x={class:"text-base"},S={class:"text-base"};function $(t,s,a,w,N,r){const i=c("EnvelopeIcon"),m=c("SBXButton");return y(),d("div",h,[n("p",B,[n("strong",null,e(this.$t("translations.payments.order_no_label")),1),o(" "+e(a.payment.order_no),1)]),n("p",v,[n("strong",null,e(this.$t("translations.payments.date_label")),1),o(" "+e(a.payment.date),1)]),n("p",x,[n("strong",null,e(this.$t("translations.payments.product_label")),1),o(" "+e(a.payment.rows[0].product_name),1)]),n("p",S,[n("strong",null,e(this.$t("translations.payments.price_label")),1),o(" "+e(r.formatNumber(a.payment.price))+" "+e(t.$t("translations.payments.currency_label"))+" ("+e(t.$t("translations.payments.vat_label"))+" "+e(r.formatNumber(a.payment.vat))+" "+e(t.$t("translations.payments.currency_label"))+")",1)]),l(m,{onClick:s[0]||(s[0]=C=>r.resendReceipt()),class:"mt-2"},{default:u(()=>[l(i,{class:"w-4 h-4 text-white"})]),_:1})])}const I=p(g,[["render",$]]);export{I as default};
