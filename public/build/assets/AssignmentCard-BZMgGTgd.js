import{h as o,e as t,b as i,_ as A,ai as D,S as V,a9 as $,N as j,a as H,U as F,o as T,f as P,s as z,aj as L,c as S,a4 as O,d as l,t as s,g as _,j as m,w as g,n as y,r as d,F as N,i as U,l as X,m as q}from"./app-Cm2beRkj.js";import{u as f}from"./index-BKm97uF2.js";import"./sweetalert2.all-i0W-sCgv.js";import{r as E,a as R,b as u}from"./UsersIcon-ClDae0qo.js";import{r as G}from"./PaperClipIcon-7jOC84nb.js";import"./client-BWFz6ICJ.js";function J(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M16.403 12.652a3 3 0 000-5.304 3 3 0 00-3.75-3.751 3 3 0 00-5.305 0 3 3 0 00-3.751 3.75 3 3 0 000 5.305 3 3 0 003.75 3.751 3 3 0 005.305 0 3 3 0 003.751-3.75zm-2.546-4.46a.75.75 0 00-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z","clip-rule":"evenodd"})])}function K(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})])}function Q(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"})])}function W(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"})])}function Y(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75"})])}function Z(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184"})])}function tt(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 12a7.5 7.5 0 0015 0m-15 0a7.5 7.5 0 1115 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077l1.41-.513m14.095-5.13l1.41-.513M5.106 17.785l1.15-.964m11.49-9.642l1.149-.964M7.501 19.795l.75-1.3m7.5-12.99l.75-1.3m-6.063 16.658l.26-1.477m2.605-14.772l.26-1.477m0 17.726l-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205L12 12m6.894 5.785l-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864l-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"})])}function et(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"})])}function st(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"})])}function nt(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"})])}function at(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.383a14.406 14.406 0 01-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 10-7.517 0c.85.493 1.509 1.333 1.509 2.316V18"})])}function it(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 110-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 01-1.44-4.282m3.102.069a18.03 18.03 0 01-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 018.835 2.535M10.34 6.66a23.847 23.847 0 008.835-2.535m0 0A23.74 23.74 0 0018.795 3m.38 1.125a23.91 23.91 0 011.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 001.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 010 3.46"})])}function ot(e,n){return i(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"})])}const rt={components:{FireIcon:E,UsersIcon:R,DocumentIcon:u,LanguageIcon:D,SBXButton:V,XMarkIcon:$,InformationCircleIcon:nt,ExclamationTriangleIcon:K,PlusCircleIcon:ot,PaperClipIcon:G,CheckBadgeIcon:J,Dialog:j,DialogPanel:H,DialogTitle:F,TransitionChild:T,TransitionRoot:P},props:{assignment:Object},data(){return this.sideOverLay=z(!1),{bidForm:f("put",this.route("translation_assignment_market.make_bid",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.assignment.id]),{price:""}),viewForm:f("put",this.route("translation_assignment_market.show_details",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.assignment.id]),{}),price:"",isPriceValid:!1,isPriceTouched:!1}},computed:{bidText(){return this.assignment.bid_count==0?this.$t("translations.translation_assignment_market.first_bidder_prompt"):this.assignment.bids_left+" "+this.$t("translations.translation_assignment_market.bidder_prompt")},isButtonDisabled(){return!this.bidForm.price||!/^\d+$/.test(this.bidForm.price)||parseInt(this.bidForm.price)<=0},priceInputClass(){return this.isPriceValid?"ring-gray-300":"ring-red-500"}},methods:{showDetails(){this.sideOverLay.value=!0,this.viewForm.processing||this.viewForm.submit({preserveScroll:!0,preserveState:!0,onSuccess:e=>{}})},makeBid(){var e=this;e.bidForm.processing||e.bidForm.submit({preserveScroll:!0,preserveState:!1,onSuccess:n=>{console.log("Page UPDATED",n)}})},showAssignment(){L.get(this.route("my_assignments.show",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.assignment.assignment_id]))},getAssignmentIcon(e){var n={1:{icon:tt,color:"text-cyan-700"},2:{icon:W,color:"text-cyan-700"},4:{icon:Y,color:"text-cyan-700"},5:{icon:Z,color:"text-cyan-700"},7:{icon:st,color:"text-cyan-700"},8:{icon:u,color:"text-cyan-700"},9:{icon:et,color:"text-cyan-700"},10:{icon:Q,color:"text-cyan-700"},11:{icon:it,color:"text-cyan-700"},12:{icon:at,color:"text-cyan-700"}};return n[e]?n[e].icon:u},validatePrice(){this.isPriceTouched=!0;const e=/^\d+$/;this.isPriceValid=e.test(this.bidForm.price)&&parseInt(this.bidForm.price)>0}}},lt={key:0,class:"border rounded-xl overflow-hidden border-gray-300 bg-gray-100"},dt={class:"bg-white p-4 border-b-gray-200 border-b py-5"},ct={class:"flex items-start"},mt={class:y(["text-cyan-700 inline-flex rounded-lg p-5 ring-1 ring-gray-300"])},gt={class:"ml-4 mt-2"},_t={class:"flex just"},ut={class:"text-base text-gray-700 leading-7 font-semibold"},ht={class:"inline-flex items-center content-end rounded-md bg-gray-50 ml-2 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10"},xt={class:"text-sm text-gray-600 mt-1"},pt={class:"font-semibold"},ft={class:"flex items-center justify-between px-4"},yt={class:"inline-flex items-center rounded-lg bg-moss-200 px-3 py-1 text-sm font-medium text-moss-700 border border-moss-400"},vt={key:1,class:"border rounded-xl overflow-hidden border-gray-300 bg-gray-100"},bt={class:"bg-white p-4 border-b-gray-200 border-b py-5"},wt={class:"flex items-start"},kt={class:y(["text-cyan-700 inline-flex rounded-lg p-5 ring-1 ring-gray-300"])},Ct={class:"ml-4 mt-2"},Mt={class:"flex just"},Bt={class:"text-base text-gray-700 leading-7 font-semibold"},It={class:"inline-flex items-center content-end rounded-md bg-gray-50 ml-2 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10"},At={class:"text-sm text-gray-600 mt-1"},Dt={class:"font-semibold"},Vt={class:"flex items-center px-4"},$t={class:"fixed inset-0 overflow-hidden"},jt={class:"absolute inset-0 overflow-hidden"},Ht={class:"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10"},Ft={class:"flex h-full flex-col overflow-y-auto bg-white pb-6 shadow-xl"},Tt={class:"py-6 px-4 sm:px-6 bg-gray-600"},Pt={class:"flex items-start justify-between"},zt={class:"inline-flex align-top rounded-md bg-moss-200 mx-3 px-3 py-1 text-xs font-medium text-green-800 ring-1 ring-inset ring-green-600/20"},Lt={class:"ml-3 flex h-7 items-center"},St={class:"relative mt-6 flex-2 px-4 sm:px-6"},Ot={class:"grid grid-cols-1 lg:grid-cols-1 gap-4"},Nt={class:"grid grid-cols-2"},Ut={class:"py-5 sm:col-span-1 sm:px-0"},Xt={class:"text-sm font-semibold leading-6 text-gray-900"},qt={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},Et={class:"py-5 sm:col-span-1 sm:px-0"},Rt={class:"text-sm font-semibold leading-6 text-gray-900"},Gt={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},Jt={class:"border-t border-gray-200 py-5 sm:col-span-1 sm:px-0"},Kt={class:"text-sm font-semibold leading-6 text-gray-900"},Qt={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},Wt={class:"border-t border-gray-200 py-5 sm:col-span-1 sm:px-0"},Yt={class:"text-sm font-semibold leading-6 text-gray-900"},Zt={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},te={key:0,class:"inline-flex items-center rounded-md bg-moss-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-moss-600/20"},ee={key:1,class:"inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10"},se={class:"col-span-1 border-t border-gray-200 py-5 sm:px-0"},ne={class:"text-sm font-semibold leading-6 text-gray-900"},ae={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},ie={class:"col-span-1 border-t border-gray-200 py-5 sm:px-0"},oe={class:"text-sm font-semibold leading-6 text-gray-900"},re={key:0,class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},le={key:1,class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},de={class:"col-span-1 border-t border-gray-200 py-6 sm:px-0"},ce={class:"text-sm font-semibold leading-6 text-gray-900"},me={key:0,class:"text-sm leading-6 text-gray-700"},ge={key:1,class:"text-sm leading-6 text-gray-700"},_e={class:"border-t border-gray-200 py-5 sm:col-span-1 sm:px-0"},ue={class:"text-sm font-semibold leading-6 text-gray-900"},he={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},xe={class:"col-span-2 border-t border-gray-200 py-6 sm:px-0"},pe={class:"text-sm font-semibold leading-6 text-gray-900"},fe={key:0},ye={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},ve={class:"flex w-0 flex-1 pl-1 ml-2 items-center"},be={class:"ml-3 flex min-w-0 flex-1 gap-2"},we={class:"truncate font-medium hover:text-gray-900"},ke=["href"],Ce={class:"ml-4 flex-shrink-0 pr-2"},Me=["href"],Be={class:"col-span-2 border-t border-gray-200 py-6 sm:px-0"},Ie={class:"text-sm font-semibold leading-6 text-gray-900"},Ae={key:0,class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},De={key:1,class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},Ve={class:"py-4 px-4 bg-gray-50 rounded-md"},$e={class:"text-lg font-semibold text-gray-900"},je={class:"text-sm text-gray-600 mt-2"},He={class:"py-5 mt-1"},Fe={for:"price",class:"block text-sm font-medium leading-6 text-gray-900"},Te={class:"relative mt-2 rounded-md shadow-sm"},Pe={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"},ze={class:"text-gray-500 sm:text-sm",id:"price-currency"},Le=["disabled"];function Se(e,n,a,Oe,h,c){const x=d("LanguageIcon"),v=d("DocumentIcon"),p=d("TransitionChild"),b=d("CheckBadgeIcon"),w=d("DialogTitle"),k=d("XMarkIcon"),C=d("PaperClipIcon"),M=d("DialogPanel"),B=d("Dialog"),I=d("TransitionRoot");return i(),o("div",null,[a.assignment.user_has_bid?m("",!0):(i(),o("div",lt,[t("div",dt,[t("div",ct,[t("span",mt,[(i(),S(O(c.getAssignmentIcon(a.assignment.translation_category_id)),{class:"h-8 w-8","aria-hidden":"true"}))]),t("div",gt,[t("div",_t,[t("span",null,[l(x,{class:"h-6 w-6 mr-1 text-gray-700","aria-hidden":"true"})]),t("p",ut,s(a.assignment.from_translation_language)+" "+s(e.$t("translations.translation_assignment_market.to_label"))+" "+s(a.assignment.to_translation_language),1),t("span",ht,s(a.assignment.assignment_id),1)]),t("p",xt,[_(s(e.$t("translations.translation_categories.title"))+": ",1),t("span",pt,s(a.assignment.translation_category),1)])])])]),t("div",ft,[t("span",yt,s(c.bidText),1),t("button",{type:"button",onClick:n[0]||(n[0]=(...r)=>c.showDetails&&c.showDetails(...r)),class:"rounded-md bg-gray-700 my-4 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600"},s(e.$t("translations.translation_assignment_market.more_info_button")),1)])])),a.assignment.user_has_bid?(i(),o("div",vt,[t("div",bt,[t("div",wt,[t("span",kt,[l(v,{class:"h-8 w-8","aria-hidden":"true"})]),t("div",Ct,[t("div",Mt,[t("span",null,[l(x,{class:"h-6 w-6 mr-1 text-gray-700","aria-hidden":"true"})]),t("p",Bt,s(a.assignment.from_translation_language)+" "+s(e.$t("translations.translation_assignment_market.to_label"))+" "+s(a.assignment.to_translation_language),1),t("span",It,s(a.assignment.assignment_id),1)]),t("p",At,[_(s(e.$t("translations.translation_categories.title"))+": ",1),t("span",Dt,s(a.assignment.translation_category),1)])])])]),t("div",Vt,[t("button",{type:"button",onClick:n[1]||(n[1]=(...r)=>c.showAssignment&&c.showAssignment(...r)),class:"rounded-md bg-moss-500 my-4 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-moss-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-moss-600"},s(e.$t("translations.translation_assignment_market.show_assignment_button")),1)])])):m("",!0),l(I,{show:e.sideOverLay.value},{default:g(()=>[l(B,{class:"relative z-10",onClose:n[5]||(n[5]=r=>e.sideOverLay.value=!1)},{default:g(()=>[l(p,{as:"template",enter:"ease-in-out duration-500","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in-out duration-500","leave-from":"opacity-100","leave-to":"opacity-0"},{default:g(()=>n[6]||(n[6]=[t("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity"},null,-1)])),_:1}),t("div",$t,[t("div",jt,[t("div",Ht,[l(p,{as:"template",enter:"transform transition ease-in-out duration-500 sm:duration-700","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-500 sm:duration-700","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:g(()=>[l(M,{class:"pointer-events-auto w-screen max-w-2xl"},{default:g(()=>[t("div",Ft,[t("div",Tt,[t("div",Pt,[l(w,{class:"text-lg font-semibold leading-7 text-white"},{default:g(()=>[_(s(e.$t("translations.my_assignments.assignment_detail.title"))+" "+s(a.assignment.assignment_id)+" ",1),t("span",zt,[l(b,{class:"h-4 w-4 text-green-700 mr-1","aria-hidden":"true"}),_(" "+s(e.$t("translations.my_assignments.assignment_detail.confirmed_label")),1)])]),_:1}),t("div",Lt,[t("button",{type:"button",class:"relative rounded-md bg-gray-500 text-white hover:text-gray-500 p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2",onClick:n[2]||(n[2]=r=>e.sideOverLay.value=!1)},[n[7]||(n[7]=t("span",{class:"absolute -inset-2.5"},null,-1)),n[8]||(n[8]=t("span",{class:"sr-only"},"Close panel",-1)),l(k,{class:"h-6 w-6","aria-hidden":"true"})])])])]),t("div",St,[t("div",Ot,[t("dl",Nt,[t("div",Ut,[t("dt",Xt,s(e.$t("translations.my_assignments.assignment_detail.to_language_label")),1),t("dd",qt,s(a.assignment.from_translation_language),1)]),t("div",Et,[t("dt",Rt,s(e.$t("translations.my_assignments.assignment_detail.from_language_label")),1),t("dd",Gt,s(a.assignment.to_translation_language),1)]),t("div",Jt,[t("dt",Kt,s(e.$t("translations.my_assignments.assignment_detail.category_label")),1),t("dd",Qt,s(a.assignment.translation_category),1)]),t("div",Wt,[t("dt",Yt,s(e.$t("translations.my_assignments.assignment_detail.authorization_label")),1),t("dd",Zt,[a.assignment.is_authorization_required?(i(),o("span",te,s(e.$t("translations.global.yes")),1)):m("",!0),a.assignment.is_authorization_required?m("",!0):(i(),o("span",ee,s(e.$t("translations.global.no")),1))])]),t("div",se,[t("dt",ne,s(e.$t("translations.my_assignments.assignment_detail.created_at_label")),1),t("dd",ae,s(a.assignment.created_date),1)]),t("div",ie,[t("dt",oe,s(e.$t("translations.my_assignments.assignment_detail.file_count_label")),1),a.assignment.files.length!=null&&a.assignment.files.length>0?(i(),o("dd",re,s(a.assignment.files.length),1)):(i(),o("dd",le," -"))]),t("div",de,[t("dt",ce,s(e.$t("translations.my_assignments.assignment_detail.costumer_type_label")),1),a.assignment.assignment_type=="company"?(i(),o("dd",me,s(e.$t("translations.my_assignments.assignment_detail.company_label")),1)):m("",!0),a.assignment.assignment_type=="personal"?(i(),o("dd",ge,s(e.$t("translations.my_assignments.assignment_detail.private_person_label")),1)):m("",!0)]),t("div",_e,[t("dt",ue,s(e.$t("translations.my_assignments.assignment_detail.latest_delivery_date_label")),1),t("dd",he,s(a.assignment.created_date),1)]),t("div",xe,[t("dt",pe,s(e.$t("translations.my_assignments.assignment_detail.files_label")),1),a.assignment.files.length!=null&&a.assignment.files.length>0?(i(),o("dd",fe,[t("ul",ye,[(i(!0),o(N,null,U(a.assignment.files,r=>(i(),o("li",{class:"flex items-center justify-between py-3 text-sm hover:bg-moss-50",key:r.id},[t("div",ve,[l(C,{class:"h-5 w-5 flex-shrink-0 text-gray-400","aria-hidden":"true"}),t("div",be,[t("span",we,[t("a",{href:r.url,target:"_blank"},s(r.original_filename),9,ke)])])]),t("div",Ce,[t("a",{href:r.url,class:"font-bold text-moss-500 hover:text-moss-600 pr-1",target:"_blank"},"Ladda ner",8,Me)])]))),128))])])):m("",!0)]),t("div",Be,[t("dt",Ie,s(e.$t("translations.my_assignments.assignment_detail.notes_label")),1),a.assignment.notes!=null&&a.assignment.notes.length!=""?(i(),o("dd",Ae,s(a.assignment.notes),1)):(i(),o("dd",De," -"))])])]),t("div",Ve,[t("div",null,[t("p",$e,s(e.$t("translations.translation_assignment_market.make_bid_button")),1),t("p",je,s(e.$t("translations.translation_assignment_market.make_bid_description")),1)]),t("div",He,[t("label",Fe,s(e.$t("translations.translation_assignment_market.price_label"))+" - "+s(e.$t("translations.translation_assignment_market.excl_vat")),1),t("div",Te,[X(t("input",{type:"text",name:"price",id:"price","onUpdate:modelValue":n[3]||(n[3]=r=>h.bidForm.price=r),pattern:"^[1-9]\\d*$",class:"block w-full rounded-md border-0 py-3 pl-1-5 pr-12 text-gray-900 font-semibold ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 invalid:border-red-500 invalid:ring-red-500 invalid:ring-inset",placeholder:"0","aria-describedby":"price-currency",required:""},null,512),[[q,h.bidForm.price]]),t("div",Pe,[t("span",ze,s(e.$t("translations.translation_assignment_market.excl_vat")),1)])])]),t("button",{type:"button",onClick:n[4]||(n[4]=(...r)=>c.makeBid&&c.makeBid(...r)),disabled:c.isButtonDisabled,class:"w-full text-center gap-x-1.5 rounded-md bg-moss-500 px-3 py-4 text font-semibold text-white hover:bg-moss-600 disabled:bg-gray-200 disabled:text-gray-300 disabled:cursor-not-allowed"},s(e.$t("translations.translation_assignment_market.make_bid_button")),9,Le)])])])]),_:1})]),_:1})])])])]),_:1})]),_:1},8,["show"])])}const Ge=A(rt,[["render",Se]]);export{Ge as default};
