import{_ as l,S as d,c as _,w as m,r as x,b as r,e as o,t,h as n,j as i,g as p}from"./app-Cm2beRkj.js";import"./client-BWFz6ICJ.js";import{r as u}from"./CheckIcon-DN9uS3Am.js";import{S as f}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import h from"./StoreProduct-Buxoyu6b.js";const b={components:{CheckIcon:u,SBXDefaultPageLayout:f,StoreProduct:h,SBXButton:d},props:{order:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_store.title"),this.$page.props.credits=this.order.data.user_credit_count},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_store.title"),this.$page.props.credits=this.order.data.user_credit_count},computed:{rowTotal(){return this.order.data.rows[0].row_total.toLocaleString("SE-sv",{minimumFractionDigits:2,maximumFractionDigits:2})},vatTotal(){return this.order.data.rows[0].row_total_vat.toLocaleString("SE-sv",{minimumFractionDigits:2,maximumFractionDigits:2})}}},g={class:"mt-12 bg-white"},v={class:"mx-auto max-w-7xl px-6 lg:px-8"},k={class:"mx-auto max-w-4xl text-center"},w={class:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl"},$={class:"mt-4 text-base text-oversattare-text-black text-center"},S={class:"text-base text-oversattare-text-black text-center"},y={key:0,class:"mt-4 text-base text-oversattare-text-black text-center"},B={key:1,class:"mt-4 text-base text-oversattare-text-black text-center"},D={class:"mt-8 text-xl font-semibold text-oversattare-text-black text-center"},C={class:"text-sm text-oversattare-text-black text-center"},L={class:"mt-4 text-xl font-semibold text-oversattare-text-black text-center"},T={class:"text-sm text-oversattare-text-black text-center"},F={class:"text-xs text-oversattare-text-black"};function P(e,X,a,E,N,s){const c=x("SBXDefaultPageLayout");return r(),_(c,null,{default:m(()=>[o("div",g,[o("div",v,[o("div",k,[o("p",w,t(e.$t("translations.order_confirmation.title")),1)]),o("p",$,t(e.$t("translations.order_confirmation.subtitle_1")),1),o("p",S,t(e.$t("translations.order_confirmation.subtitle_2")),1),a.order.data.rows[0].credit_count>1?(r(),n("p",y,t(e.$t("translations.order_confirmation.leads_prompt_1"))+" "+t(a.order.data.rows[0].credit_count)+" "+t(e.$t("translations.order_confirmation.leads_prompt_2")),1)):i("",!0),a.order.data.rows[0].credit_count==1?(r(),n("p",B,t(e.$t("translations.order_confirmation.leads_prompt_1"))+" "+t(a.order.data.rows[0].credit_count)+" "+t(e.$t("translations.order_confirmation.leads_prompt_3")),1)):i("",!0),o("h4",D,t(e.$t("translations.order_confirmation.product_label")),1),o("p",C,t(a.order.data.rows[0].product_name),1),o("h4",L,t(e.$t("translations.order_confirmation.price_label")),1),o("p",T,[p(t(s.rowTotal)+" "+t(e.$t("translations.order_confirmation.currency_label"))+" ",1),o("span",F,"("+t(e.$t("translations.order_confirmation.vat_prompt"))+" "+t(s.vatTotal)+" "+t(e.$t("translations.order_confirmation.currency_label"))+")",1)])])])]),_:1})}const z=l(b,[["render",P]]);export{z as default};
