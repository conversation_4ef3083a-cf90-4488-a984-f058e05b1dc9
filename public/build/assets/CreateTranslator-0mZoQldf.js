import{_ as B,S as C,c as U,w as S,r as g,b as m,e as r,d as l,t as d,h as f,i as _,l as b,ab as v,F as c,g as X,k as z}from"./app-Cm2beRkj.js";import{u as k}from"./index-BKm97uF2.js";import{S as V}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as N}from"./SBXSelect-RMXlX9En.js";import{S as x}from"./SBXInput-C8dZEgZe.js";import{S as D}from"./SBXToggle-CrVi0Yuw.js";import{S as j}from"./StepsDivider-BRzfDQoa.js";import{S as F}from"./SBXGenericNotification-BrDLezmQ.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const G={components:{SBXDefaultPageLayout:V,SBXButton:C,SBXSelect:N,SBXInput:x,SBXToggle:D,StepsDivider:j,SBXGenericNotification:F},props:{translationLanguages:Object,translationCategories:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,this.fromTranslationLanguages=[],this.toTranslationLanguages=[];for(var s=0;s<this.translationLanguages.data.length;s++){let o=this.translationLanguages.data[s];this.fromTranslationLanguages.push({id:o.id,value:o.name,isSelected:!1}),this.toTranslationLanguages.push({id:o.id,value:o.name,isSelected:!1})}this.categories=[];for(var s=0;s<this.translationCategories.data.length;s++){let i=this.translationCategories.data[s];this.categories.push({id:i.id,value:i.name,isSelected:!1})}},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{profileSaved:!1,form:k("post",this.route("translators.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{company:null,company_no:null,first_name:null,last_name:null,address_1:null,address_2:null,postal_code:null,city:null,email:null,phone_no:null,is_authorized:!1,authorization_id:null,from_translation_languages:[],to_translation_languages:[],translation_categories:[],password:null,password_confirmation:null}),fromTranslationLanguages:[],toTranslationLanguages:[],categories:[]}},computed:{pageTitle(){return`${this.$t("translations.translators.create_title")}`}},methods:{update(){var s=this;this.form.from_translation_languages=[];for(var o=0;o<this.fromTranslationLanguages.length;o++){let i=this.fromTranslationLanguages[o];i.isSelected&&this.form.from_translation_languages.push(i.id)}this.form.to_translation_languages=[];for(var o=0;o<this.toTranslationLanguages.length;o++){let u=this.toTranslationLanguages[o];u.isSelected&&this.form.to_translation_languages.push(u.id)}this.form.translation_categories=[];for(var o=0;o<this.categories.length;o++){let u=this.categories[o];u.isSelected&&this.form.translation_categories.push(u.id)}this.form.processing||this.form.submit({preserveScroll:!0,onSuccess:i=>{s.profileSaved=!0,s.form.password=null,s.form.password_confirmation=null,setTimeout(function(){s.profileSaved=!1},3e3)}})}}},O={class:"grid grid-cols-2 gap-12"},P={class:"text-xl font-semibold mt-4"},M={class:"mt-2"},E={class:"space-y-1"},q={class:"relative flex items-start"},A={class:"flex h-6 items-center"},H=["onUpdate:modelValue"],I={class:"ml-3 text-sm leading-6"},J={class:"font-medium text-gray-900"},K={class:"mt-8 text-xl font-semibold mt-4"},Q={class:"mt-2"},R={class:"space-y-1"},W={class:"relative flex items-start"},Y={class:"flex h-6 items-center"},Z=["onUpdate:modelValue"],$={class:"ml-3 text-sm leading-6"},oo={class:"font-medium text-gray-900"},eo={class:"mt-8 text-xl font-semibold mt-4"},to={class:"mt-2"},so={class:"space-y-1"},ro={class:"relative flex items-start"},lo={class:"flex h-6 items-center"},no=["onUpdate:modelValue"],ao={class:"ml-3 text-sm leading-6"},io={class:"font-medium text-gray-900"};function mo(s,o,i,u,e,h){const n=g("SBXInput"),y=g("SBXToggle"),w=g("SBXButton"),T=g("SBXGenericNotification"),L=g("SBXDefaultPageLayout");return m(),U(L,null,{default:S(()=>[r("form",{onSubmit:o[27]||(o[27]=z((...t)=>h.update&&h.update(...t),["prevent"]))},[r("div",O,[r("div",null,[l(n,{model:e.form.company,"onUpdate:model":o[0]||(o[0]=t=>e.form.company=t),label:s.$t("translations.profile.company_label"),onChange:o[1]||(o[1]=t=>e.form.validate("company")),error:e.form.errors.company},null,8,["model","label","error"]),l(n,{model:e.form.company_no,"onUpdate:model":o[2]||(o[2]=t=>e.form.company_no=t),label:s.$t("translations.profile.company_no_label"),onChange:o[3]||(o[3]=t=>e.form.validate("company_no")),error:e.form.errors.company_no},null,8,["model","label","error"]),l(n,{model:e.form.first_name,"onUpdate:model":o[4]||(o[4]=t=>e.form.first_name=t),label:s.$t("sbxadmin.profile.first_name_label"),onChange:o[5]||(o[5]=t=>e.form.validate("first_name")),error:e.form.errors.first_name,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(n,{model:e.form.last_name,"onUpdate:model":o[6]||(o[6]=t=>e.form.last_name=t),label:s.$t("sbxadmin.profile.last_name_label"),onChange:o[7]||(o[7]=t=>e.form.validate("last_name")),error:e.form.errors.last_name,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(n,{model:e.form.address_1,"onUpdate:model":o[8]||(o[8]=t=>e.form.address_1=t),label:s.$t("translations.profile.address_1_label"),onChange:o[9]||(o[9]=t=>e.form.validate("address_1")),error:e.form.errors.address_1,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(n,{model:e.form.address_2,"onUpdate:model":o[10]||(o[10]=t=>e.form.address_2=t),label:s.$t("translations.profile.address_2_label"),onChange:o[11]||(o[11]=t=>e.form.validate("address_2")),error:e.form.errors.address_2},null,8,["model","label","error"]),l(n,{model:e.form.postal_code,"onUpdate:model":o[12]||(o[12]=t=>e.form.postal_code=t),label:s.$t("translations.profile.postal_code_label"),onChange:o[13]||(o[13]=t=>e.form.validate("postal_code")),error:e.form.errors.postal_code,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(n,{model:e.form.city,"onUpdate:model":o[14]||(o[14]=t=>e.form.city=t),label:s.$t("translations.profile.city_label"),onChange:o[15]||(o[15]=t=>e.form.validate("city")),error:e.form.errors.city,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(n,{model:e.form.email,"onUpdate:model":o[16]||(o[16]=t=>e.form.email=t),label:s.$t("sbxadmin.profile.email_label"),onChange:o[17]||(o[17]=t=>e.form.validate("email")),error:e.form.errors.email,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(n,{model:e.form.phone_no,"onUpdate:model":o[18]||(o[18]=t=>e.form.phone_no=t),label:s.$t("translations.profile.phone_no_label"),onChange:o[19]||(o[19]=t=>e.form.validate("phone_no")),error:e.form.errors.phone_no,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),l(y,{model:e.form.is_authorized,"onUpdate:model":o[20]||(o[20]=t=>e.form.is_authorized=t),label:s.$t("translations.profile.is_authorized_label"),class:"mt-4"},null,8,["model","label"]),l(n,{model:e.form.authorization_id,"onUpdate:model":o[21]||(o[21]=t=>e.form.authorization_id=t),label:s.$t("translations.profile.authorization_id_label"),onChange:o[22]||(o[22]=t=>e.form.validate("authorization_id")),error:e.form.errors.authorization_id},null,8,["model","label","error"]),l(n,{type:"password",model:e.form.password,"onUpdate:model":o[23]||(o[23]=t=>e.form.password=t),label:s.$t("sbxadmin.profile.password_label"),instruction:s.$t("sbxadmin.profile.password_instruction"),onChange:o[24]||(o[24]=t=>e.form.validate("password")),error:e.form.errors.password},null,8,["model","label","instruction","error"]),l(n,{type:"password",model:e.form.password_confirmation,"onUpdate:model":o[25]||(o[25]=t=>e.form.password_confirmation=t),label:s.$t("sbxadmin.profile.password_confirmation_label"),instruction:s.$t("sbxadmin.profile.password_instruction"),onChange:o[26]||(o[26]=t=>e.form.validate("password_confirmation")),error:e.form.errors.password_confirmation},null,8,["model","label","instruction","error"])]),r("div",null,[r("h3",P,d(s.$t("translations.translator_applications.step_2_title")),1),r("div",M,[r("fieldset",null,[r("div",E,[(m(!0),f(c,null,_(e.fromTranslationLanguages,(t,a)=>(m(),f("div",q,[r("div",A,[b(r("input",{"onUpdate:modelValue":p=>e.fromTranslationLanguages[a].isSelected=p,type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"},null,8,H),[[v,e.fromTranslationLanguages[a].isSelected]])]),r("div",I,[r("label",J,d(t.value),1)])]))),256))])])]),r("h3",K,d(s.$t("translations.translator_applications.step_3_title")),1),r("div",Q,[r("fieldset",null,[r("div",R,[(m(!0),f(c,null,_(e.toTranslationLanguages,(t,a)=>(m(),f("div",W,[r("div",Y,[b(r("input",{"onUpdate:modelValue":p=>e.toTranslationLanguages[a].isSelected=p,type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"},null,8,Z),[[v,e.toTranslationLanguages[a].isSelected]])]),r("div",$,[r("label",oo,d(t.value),1)])]))),256))])])]),r("h3",eo,d(s.$t("translations.translator_applications.step_4_title")),1),r("div",to,[r("fieldset",null,[r("div",so,[(m(!0),f(c,null,_(e.categories,(t,a)=>(m(),f("div",ro,[r("div",lo,[b(r("input",{"onUpdate:modelValue":p=>e.categories[a].isSelected=p,type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"},null,8,no),[[v,e.categories[a].isSelected]])]),r("div",ao,[r("label",io,d(t.value),1)])]))),256))])])])])]),l(w,{class:"mt-4"},{default:S(()=>[X(d(s.$t("translations.translators.update_button")),1)]),_:1})],32),l(T,{show:e.profileSaved,variant:"success",message:s.$t("translations.profile.saved_message"),onNotificationCancelled:o[28]||(o[28]=t=>e.profileSaved=!1)},null,8,["show","message"])]),_:1})}const Co=B(G,[["render",mo]]);export{Co as default};
