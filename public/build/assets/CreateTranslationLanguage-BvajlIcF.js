import{_,S as d,c as f,w as i,r as a,b as c,e as n,d as r,g as B,t as S,k as b}from"./app-Cm2beRkj.js";import{u as X}from"./index-BKm97uF2.js";import{S as v}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as h}from"./SBXInput-C8dZEgZe.js";import{S as T}from"./SBXToggle-CrVi0Yuw.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const $={components:{SBXDefaultPageLayout:v,SBXInput:h,SBXToggle:T,SBXButton:d},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:X("post",this.route("translation_languages.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{name:null,is_active:!0})}},computed:{pageTitle(){return`${this.$t("translations.translation_languages.create_title")}`}},methods:{create(){this.form.processing||this.form.submit()}}},k={class:"grid grid-cols-2 gap-4"};function y(s,e,C,D,t,l){const m=a("SBXInput"),p=a("SBXToggle"),u=a("SBXButton"),g=a("SBXDefaultPageLayout");return c(),f(g,null,{default:i(()=>[n("form",{onSubmit:e[3]||(e[3]=b((...o)=>l.create&&l.create(...o),["prevent"]))},[n("div",k,[n("div",null,[r(m,{model:t.form.name,"onUpdate:model":e[0]||(e[0]=o=>t.form.name=o),label:s.$t("translations.translation_languages.name_label"),onChange:e[1]||(e[1]=o=>t.form.validate("name")),error:t.form.errors.name},null,8,["model","label","error"]),r(p,{model:t.form.is_active,"onUpdate:model":e[2]||(e[2]=o=>t.form.is_active=o),label:s.$t("translations.translation_languages.active_label"),class:"mt-4"},null,8,["model","label"]),r(u,{class:"mt-4"},{default:i(()=>[B(S(s.$t("translations.translation_languages.create_button")),1)]),_:1})])])],32)]),_:1})}const M=_($,[["render",y]]);export{M as default};
