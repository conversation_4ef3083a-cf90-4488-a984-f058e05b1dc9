import{_ as t}from"./app-Cm2beRkj.js";const e={components:{},props:{productVariationOption:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.productVariationOption.data.name,exclude_from_webshop:this.productVariationOption.data.exclude_from_webshop})}},computed:{pageTitle(){return`${this.$t("global.edit")} - ${this.productVariationOption.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("product_variation_options.update",[this.$page.props.language.selected,this.productVariationOption.data.id]))}}};function o(i,a,p,r,n,s){return null}const u=t(e,[["render",o]]);export{u as default};
