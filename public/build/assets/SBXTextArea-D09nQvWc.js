import{_ as d,b as r,h as l,e as i,t as o,l as c,m,j as u,F as f,i as x}from"./app-Cm2beRkj.js";import{r as p}from"./ExclamationCircleIcon-C4_TqLjZ.js";const y={components:{ExclamationCircleIcon:p},props:{label:{type:String,required:!0},model:{type:String,default:null},rows:{type:Number,default:5},placeholder:{type:String,required:!1,default:null},instruction:{type:String,required:!1,default:null},error:{type:String,required:!1,default:null},clientValidationErrors:{type:Array,required:!1,default:[]}},watch:{model(n,t){this.currentValue=n}},data(){return{currentValue:this.model}}},g={class:"relative rounded-md border border-gray-300 mt-4 px-3 py-2 shadow-sm focus-within:border-blue-600 focus-within:ring-1 focus-within:ring-blue-500"},_={for:"comment",class:"block text-xs font-semibold text-gray-900"},b=["rows"],h={key:0,class:"mt-1 ml-1 text-xs text-gray-500"},w={key:1,class:"mt-1 text-xs text-red-600"},V={class:"mt-1 text-xs text-red-600"};function S(n,t,e,k,a,v){return r(),l("div",null,[i("div",g,[i("label",_,o(e.label),1),c(i("textarea",{onInput:t[0]||(t[0]=s=>n.$emit("update:model",a.currentValue)),"onUpdate:modelValue":t[1]||(t[1]=s=>a.currentValue=s),rows:e.rows,class:"block w-full border-0 p-0 pr-10 text-gray-900 placeholder-gray-500 focus:ring-0 sm:text-sm"},null,40,b),[[m,a.currentValue]])]),e.instruction?(r(),l("p",h,o(e.instruction),1)):u("",!0),e.error?(r(),l("p",w,o(e.error),1)):u("",!0),(r(!0),l(f,null,x(e.clientValidationErrors,s=>(r(),l("p",V,o(s.$message),1))),256))])}const E=d(y,[["render",S]]);export{E as S};
