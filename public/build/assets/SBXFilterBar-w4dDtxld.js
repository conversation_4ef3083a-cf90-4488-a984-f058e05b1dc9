import{_ as n,r as d,b as a,h as l,e as t,t as h,d as p,l as f,m as u,j as _,J as m}from"./app-Cm2beRkj.js";import{r as g,d as y}from"./debounce-Bpn6Ai4k.js";const b={components:{MagnifyingGlassIcon:g},props:{filters:{type:Object,required:!0},placeholder:{type:String,default:"Search..."},searchRoute:{type:String},showSearch:{type:Boolean,default:!0}},data(){return{barFilters:this.copyObject(this.filters)}},watch:{"barFilters.search":function(e,s){this.$emit("searchValueChanged",e),this.performSearch(e)}},methods:{performSearch:y(function(e){this.$inertia.get(this.route(this.searchRoute,[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.barFilters,{preserveState:!0,replace:!0})},300),copyObject(e){let s={},r;for(r in e)s[r]=e[r];return s}}},x={class:"flex-1 px-4 bg-white py-2 border-b border-gray-200"},v={class:"flex items-center"},S={class:"flex-1 flex"},w={key:0,class:"w-full flex md:ml-0",action:"#",method:"GET"},k={for:"search-field",class:"sr-only"},B={class:"relative w-full text-gray-400 focus-within:text-gray-600"},F={class:"absolute inset-y-0 left-0 flex items-center pointer-events-none","aria-hidden":"true"},$=["placeholder"],V={class:"flex items-center ml-24"};function j(e,s,r,C,o,G){const c=d("MagnifyingGlassIcon");return a(),l("div",x,[t("div",v,[t("div",S,[r.showSearch?(a(),l("form",w,[t("label",k,h(e.$t("sbxadmin.global.search_label")),1),t("div",B,[t("div",F,[p(c,{class:"h-5 w-5","aria-hidden":"true"})]),f(t("input",{id:"search-field","onUpdate:modelValue":s[0]||(s[0]=i=>o.barFilters.search=i),name:"search-field",class:"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-transparent sm:text-sm",placeholder:r.placeholder,type:"search"},null,8,$),[[u,o.barFilters.search]])])])):_("",!0)]),t("div",V,[m(e.$slots,"filterArea")])])])}const N=n(b,[["render",j]]);export{N as S};
