import{_ as n,h as r,d as s,e as t,F as u,r as l,b as p}from"./app-Cm2beRkj.js";import{S as c}from"./SBXDataTable-C66uxM7K.js";import{S as _}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const m={components:{SBXDataTable:c,SBXFilterBar:_},props:{productUnits:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.product_units.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.product_units.title")},data(){return{columns:[{key:"unit",label:this.$t("sbxwebshop.product_units.unit_label")}]}},methods:{}},b={class:"flex flex-col"},h={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},g={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},f={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function x(e,w,o,B,a,D){const i=l("SBXFilterBar"),d=l("SBXDataTable");return p(),r(u,null,[s(i,{filters:o.filters,searchRoute:"product_units",placeholder:e.$t("sbxwebshop.global.search")},null,8,["filters","placeholder"]),t("div",b,[t("div",h,[t("div",g,[t("div",f,[s(d,{columns:a.columns,items:o.productUnits.data,showAddButton:!0,addButtonText:e.$t("sbxwebshop.product_units.new_product_unit_button"),addRoute:"product_units.create",showEditButton:!0,editRoute:"product_units.edit",showDeleteButton:!0,deleteRoute:"product_units.destroy",deleteDialogTitle:e.$t("sbxwebshop.product_units.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.product_units.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.product_units.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxwebshop.global.cancel"),paginator:o.productUnits.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const O=n(m,[["render",x]]);export{O as default};
