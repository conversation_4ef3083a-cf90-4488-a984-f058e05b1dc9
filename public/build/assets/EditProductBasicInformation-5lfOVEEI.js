import{_ as S,S as _,c as g,w as p,r,b,d as a,e as h,g as B,t as w,k as X}from"./app-Cm2beRkj.js";import{S as v}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import N from"./ProductEditorsDropdown-PputzcmH.js";import{S as D}from"./SBXInput-C8dZEgZe.js";import{S as C}from"./SBXSelect-RMXlX9En.js";import{S as P}from"./SBXEditor-C2jSibHW.js";import{S as E}from"./SBXNotification-Bm69sG5a.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const x={components:{SBXDefaultPageLayout:v,ProductEditorsDropdown:N,SBXInput:D,SBXSelect:C,SBXEditor:P,SBXButton:_,SBXNotification:E},props:{product:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({article_no:this.product.data.article_no,name:this.product.data.name,long_description:this.product.data.long_description}),showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("products.basic_information.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:s=>{this.showSaveSuccess=!0}})},saveNotificationCancelled(){this.showSaveSuccess=!1}}};function I(s,o,n,V,e,i){const c=r("ProductEditorsDropdown"),l=r("SBXInput"),d=r("SBXEditor"),m=r("SBXButton"),u=r("SBXNotification"),f=r("SBXDefaultPageLayout");return b(),g(f,null,{default:p(()=>[a(c,{pages:n.pageComponents,currentPage:"basic_information",productID:n.product.data.id},null,8,["pages","productID"]),h("form",{onSubmit:o[3]||(o[3]=X((...t)=>i.update&&i.update(...t),["prevent"]))},[a(l,{model:e.form.article_no,"onUpdate:model":o[0]||(o[0]=t=>e.form.article_no=t),label:s.$t("sbxwebshop.products.article_no_label"),error:n.errors.article_no},null,8,["model","label","error"]),a(l,{model:e.form.name,"onUpdate:model":o[1]||(o[1]=t=>e.form.name=t),label:s.$t("sbxwebshop.products.name_label"),error:n.errors.name},null,8,["model","label","error"]),a(d,{modelValue:e.form.long_description,"onUpdate:modelValue":o[2]||(o[2]=t=>e.form.long_description=t)},null,8,["modelValue"]),a(m,{class:"mt-4"},{default:p(()=>[B(w(s.$t("sbxwebshop.global.save")),1)]),_:1})],32),a(u,{show:e.showSaveSuccess,onNotificationCancelled:i.saveNotificationCancelled},null,8,["show","onNotificationCancelled"])]),_:1})}const q=S(x,[["render",I]]);export{q as default};
