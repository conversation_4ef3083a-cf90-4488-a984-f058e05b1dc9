import{_ as y,S as x,c as l,w as n,r as c,b as i,e as t,t as r,h as f,i as g,F as b,d as v,g as w,k as P}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import $ from"./ProductRow-DqGYo3nG.js";import"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const k={components:{SBXDefaultPageLayout:B,SBXButton:x,ProductRow:$},props:{products:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,this.setupFormProducts()},updated(){this.$page.props.page_info.title_label=this.pageTitle,this.setupFormProducts()},remember:"form",data(){return{form:this.$inertia.form({products:[]})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_inventory.product_delivery.title")}`}},methods:{setupFormProducts(){for(var e=[],o=0;o<this.products.data.length;o++){let s=this.products.data[o];e.push({product_id:s.id,quantity:0})}this.form.products=e},productQuantityChanged(e,o){for(var s=0;s<this.form.products.length;s++)if(this.form.products[s].product_id===e){this.form.products[s].quantity=o;break}},create(){this.form.processing||this.form.post(this.route("product_inventory.product_delivery.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},S={class:"px-4 sm:px-6 lg:px-8"},C={class:"flow-root"},X={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},D={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8"},F={class:"min-w-full divide-y divide-gray-300"},L={scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-xs sm:text-sm font-semibold text-gray-900 sm:pl-3"},Q={scope:"col",class:"px-3 py-3.5 text-right text-xs sm:text-sm font-semibold text-gray-900"},T={scope:"col",class:"px-3 py-3.5 text-right text-xs sm:text-sm font-semibold text-gray-900"},j={scope:"col",class:"hidden sm:table-cell px-3 py-3.5 text-right text-xs sm:text-sm font-semibold text-gray-900"},q={class:"bg-white"};function N(e,o,s,p,R,a){const u=c("ProductRow"),_=c("SBXButton"),m=c("SBXDefaultPageLayout");return i(),l(m,null,{default:n(()=>[t("form",{onSubmit:o[0]||(o[0]=P((...d)=>a.create&&a.create(...d),["prevent"]))},[t("div",S,[t("div",C,[t("div",X,[t("div",D,[t("table",F,[t("thead",null,[t("tr",null,[t("th",L,r(e.$t("sbxwebshop.product_inventory.product_delivery.product_name_label")),1),t("th",Q,r(e.$t("sbxwebshop.product_inventory.product_delivery.current_stock_label")),1),t("th",T,r(e.$t("sbxwebshop.product_inventory.product_delivery.delivery_quantity_label")),1),t("th",j,r(e.$t("sbxwebshop.product_inventory.product_delivery.adjusted_stock_label")),1)])]),t("tbody",q,[(i(!0),f(b,null,g(s.products.data,(d,h)=>(i(),l(u,{product:d,key:d.id,rowIdx:h,onProductQuantityChanged:a.productQuantityChanged},null,8,["product","rowIdx","onProductQuantityChanged"]))),128))])]),v(_,{class:"mt-4"},{default:n(()=>[w(r(e.$t("sbxwebshop.product_inventory.product_delivery.create_button")),1)]),_:1})])])])])],32)]),_:1})}const z=y(k,[["render",N]]);export{z as default};
