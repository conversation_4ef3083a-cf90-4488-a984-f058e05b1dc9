import{_ as f,S as _,c as b,w as m,r,b as S,e as g,d as l,g as B,t as X,k as h}from"./app-Cm2beRkj.js";import{S as w}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as U}from"./SBXInput-C8dZEgZe.js";import{S as x}from"./SBXSelect-RMXlX9En.js";import{S as D}from"./SBXEditor-C2jSibHW.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const P={components:{SBXDefaultPageLayout:w,SBXInput:U,SBXSelect:x,SBXEditor:D,SBXButton:_},props:{productUnits:Object,defaultProductUnitID:Number,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({article_no:null,name:null,long_description:null,product_unit_id:this.defaultProductUnitID})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.products.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("products.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}};function V(n,e,s,I,o,a){const i=r("SBXInput"),p=r("SBXEditor"),u=r("SBXSelect"),d=r("SBXButton"),c=r("SBXDefaultPageLayout");return S(),b(c,null,{default:m(()=>[g("form",{onSubmit:e[4]||(e[4]=h((...t)=>a.create&&a.create(...t),["prevent"]))},[l(i,{model:o.form.article_no,"onUpdate:model":e[0]||(e[0]=t=>o.form.article_no=t),label:n.$t("sbxwebshop.products.article_no_label"),error:s.errors.article_no},null,8,["model","label","error"]),l(i,{model:o.form.name,"onUpdate:model":e[1]||(e[1]=t=>o.form.name=t),label:n.$t("sbxwebshop.products.name_label"),error:s.errors.name},null,8,["model","label","error"]),l(p,{modelValue:o.form.long_description,"onUpdate:modelValue":e[2]||(e[2]=t=>o.form.long_description=t)},null,8,["modelValue"]),l(u,{model:o.form.product_unit_id,"onUpdate:model":e[3]||(e[3]=t=>o.form.product_unit_id=t),items:s.productUnits.data,labelFieldName:"unit",label:n.$t("sbxwebshop.products.unit_label")},null,8,["model","items","label"]),l(d,{class:"mt-4"},{default:m(()=>[B(X(n.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const E=f(P,[["render",V]]);export{E as default};
