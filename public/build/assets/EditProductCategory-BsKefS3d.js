import{_ as c,S as g,c as f,w as l,r,b as _,e as b,d as s,g as B,t as S,k as h}from"./app-Cm2beRkj.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as y}from"./SBXInput-C8dZEgZe.js";import{S as x}from"./SBXTextArea-D09nQvWc.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const w={components:{SBXDefaultPageLayout:X,SBXInput:y,SBXTextArea:x,SBXButton:g},props:{productCategory:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.productCategory.data.name,long_description:this.productCategory.data.long_description})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.productCategory.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("product_categories.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productCategory.data.id]))}}};function C(a,e,n,T,o,p){const i=r("SBXInput"),d=r("SBXTextArea"),m=r("SBXButton"),u=r("SBXDefaultPageLayout");return _(),f(u,null,{default:l(()=>[b("form",{onSubmit:e[2]||(e[2]=h((...t)=>p.update&&p.update(...t),["prevent"]))},[s(i,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:a.$t("sbxwebshop.product_categories.name_label"),error:n.errors.name},null,8,["model","label","error"]),s(d,{model:o.form.long_description,"onUpdate:model":e[1]||(e[1]=t=>o.form.long_description=t),label:a.$t("sbxwebshop.product_categories.description_label"),rows:10,error:n.errors.long_description},null,8,["model","label","error"]),s(m,{class:"mt-4"},{default:l(()=>[B(S(a.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const A=c(w,[["render",C]]);export{A as default};
