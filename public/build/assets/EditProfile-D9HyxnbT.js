import{_ as L,S as B,c as b,w as v,r as u,b as i,e as n,l as T,v as X,h as d,i as h,t as m,F as I,n as U,d as r,g as y,j as c,k as S}from"./app-Cm2beRkj.js";import{u as w}from"./index-BKm97uF2.js";import{S as V}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as R}from"./SBXSelect-RMXlX9En.js";import{S as N}from"./SBXInput-C8dZEgZe.js";import{S as x}from"./SBXComboBox-BrrwJIQ0.js";import{S as D}from"./SBXToggle-CrVi0Yuw.js";import{S as P}from"./SBXAlert-DwVKPUUm.js";import{s as j}from"./vue-multiselect.esm-1UMdX8qQ.js";import{S as A}from"./StepsDivider-BRzfDQoa.js";import{S as M}from"./SBXGenericNotification-BrDLezmQ.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckIcon-DN9uS3Am.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const O={components:{SBXDefaultPageLayout:V,SBXButton:B,SBXSelect:R,SBXInput:N,SBXComboBox:x,SBXToggle:D,StepsDivider:A,SBXGenericNotification:M,SBXAlert:P,Multiselect:j},props:{user:Object,translationLanguages:Object,translationCategories:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle;for(var s=[],t=0;t<this.user.data.from_translation_languages.length;t++){let p=this.user.data.from_translation_languages[t];for(var a=0;a<this.translationLanguages.data.length;a++)if(this.translationLanguages.data[a].id==p.id){s.push(this.translationLanguages.data[a]);break}}this.informationForm.from_translation_languages=s;for(var f=[],t=0;t<this.user.data.to_translation_languages.length;t++){let l=this.user.data.to_translation_languages[t];for(var a=0;a<this.translationLanguages.data.length;a++)if(this.translationLanguages.data[a].id==l.id){f.push(this.translationLanguages.data[a]);break}}this.informationForm.to_translation_languages=f;for(var o=[],t=0;t<this.user.data.translation_categories.length;t++){let l=this.user.data.translation_categories[t];for(var a=0;a<this.translationCategories.data.length;a++)if(this.translationCategories.data[a].id==l.id){o.push(this.translationCategories.data[a]);break}}this.informationForm.translation_categories=o},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{tabs:[{id:"contact_info",name:this.$t("translations.profile.contact_info_tab")},{id:"information",name:this.$t("translations.profile.information_tab")}],selectedTab:"contact_info",profileSaved:!1,contactInfoForm:w("put",this.route("translation_profile.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{company:this.user.data.company,company_no:this.user.data.company_no,first_name:this.user.data.first_name,last_name:this.user.data.last_name,address_1:this.user.data.address_1,address_2:this.user.data.address_2,postal_code:this.user.data.postal_code,city:this.user.data.city,phone_no:this.user.data.phone_no,allow_text_messages:this.user.data.allow_text_messages,password:null,password_confirmation:null}),informationForm:w("put",this.route("translation_profile.update.information",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{from_translation_languages:[],to_translation_languages:[],translation_categories:[]})}},computed:{pageTitle(){return`${this.$t("sbxadmin.profile.edit_title")}`}},methods:{updateContactInfo(){var s=this;this.contactInfoForm.processing||this.contactInfoForm.submit({preserveScroll:!0,onSuccess:t=>{s.profileSaved=!0,s.contactInfoForm.password=null,s.contactInfoForm.password_confirmation=null,setTimeout(function(){s.profileSaved=!1},3e3)}})},updateInformation(){var s=this;this.informationForm.processing||this.informationForm.submit({preserveScroll:!0,onSuccess:t=>{s.profileSaved=!0,setTimeout(function(){s.profileSaved=!1},3e3)}})}}},E={class:"pt-8 sm:pt-0"},z={class:"sm:hidden"},G=["value"],q={class:"hidden sm:block"},H={class:"-mb-px flex space-x-8","aria-label":"Tabs"},J=["onClick","aria-current"],K={key:0,class:"mt-8 mb-8 sm:mb-12"},Q={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},W={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Y={class:"grid grid-cols-1 gap-4"},Z={class:"grid grid-cols-1 gap-4"},$={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},oo={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},to={class:"relative rounded-md border border-gray-300 mt-4 px-3 py-2 shadow-sm h-14"},eo={class:"block text-xs font-semibold text-gray-900"},so={class:"block w-full border-0 p-0 pr-10 text-gray-900 sm:text-sm"},no={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ao={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ro={key:1,class:"mt-8 mb-8 sm:mb-12"},lo={class:"mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4"},io={class:"text-sm font-semibold"},mo={key:0,class:"text-xs text-red-500"},co={class:"mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4"},po={class:"text-sm font-semibold"},uo={key:0,class:"text-xs text-red-500"},go={class:"mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4"},fo={class:"text-sm font-semibold"},_o={key:0,class:"text-xs text-red-500"};function bo(s,t,a,f,o,p){const l=u("SBXInput"),k=u("SBXToggle"),g=u("SBXButton"),F=u("SBXAlert"),_=u("multiselect"),C=u("SBXDefaultPageLayout");return i(),b(C,null,{default:v(()=>[n("div",E,[n("div",z,[T(n("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>o.selectedTab=e),id:"tabs",name:"tabs",class:"block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"},[(i(!0),d(I,null,h(o.tabs,e=>(i(),d("option",{key:e.id,value:e.id},m(e.name),9,G))),128))],512),[[X,o.selectedTab]])]),n("div",q,[n("nav",H,[(i(!0),d(I,null,h(o.tabs,e=>(i(),d("p",{onClick:vo=>o.selectedTab=e.id,key:e.name,class:U([e.id==o.selectedTab?"border-oversattare-blue text-oversattare-blue":"border-transparent text-oversattare-text-black hover:border-gray-300 hover:text-gray-700","whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium cursor-pointer select-none"]),"aria-current":e.id==o.selectedTab?"page":void 0},m(e.name),11,J))),128))])])]),o.selectedTab=="contact_info"?(i(),d("div",K,[n("form",{onSubmit:t[24]||(t[24]=S((...e)=>p.updateContactInfo&&p.updateContactInfo(...e),["prevent"]))},[n("div",Q,[r(l,{model:o.contactInfoForm.company,"onUpdate:model":t[1]||(t[1]=e=>o.contactInfoForm.company=e),label:s.$t("translations.profile.company_label"),onChange:t[2]||(t[2]=e=>o.contactInfoForm.validate("company")),error:o.contactInfoForm.errors.company},null,8,["model","label","error"]),r(l,{model:o.contactInfoForm.company_no,"onUpdate:model":t[3]||(t[3]=e=>o.contactInfoForm.company_no=e),label:s.$t("translations.profile.company_no_label"),onChange:t[4]||(t[4]=e=>o.contactInfoForm.validate("company_no")),error:o.contactInfoForm.errors.company_no},null,8,["model","label","error"])]),n("div",W,[r(l,{model:o.contactInfoForm.first_name,"onUpdate:model":t[5]||(t[5]=e=>o.contactInfoForm.first_name=e),label:s.$t("translations.profile.first_name_label"),onChange:t[6]||(t[6]=e=>o.contactInfoForm.validate("first_name")),error:o.contactInfoForm.errors.first_name,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),r(l,{model:o.contactInfoForm.last_name,"onUpdate:model":t[7]||(t[7]=e=>o.contactInfoForm.last_name=e),label:s.$t("translations.profile.last_name_label"),onChange:t[8]||(t[8]=e=>o.contactInfoForm.validate("last_name")),error:o.contactInfoForm.errors.last_name,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),n("div",Y,[r(l,{model:o.contactInfoForm.address_1,"onUpdate:model":t[9]||(t[9]=e=>o.contactInfoForm.address_1=e),label:s.$t("translations.profile.address_1_label"),onChange:t[10]||(t[10]=e=>o.contactInfoForm.validate("address_1")),error:o.contactInfoForm.errors.address_1,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),n("div",Z,[r(l,{model:o.contactInfoForm.address_2,"onUpdate:model":t[11]||(t[11]=e=>o.contactInfoForm.address_2=e),label:s.$t("translations.profile.address_2_label"),onChange:t[12]||(t[12]=e=>o.contactInfoForm.validate("address_2")),error:o.contactInfoForm.errors.address_2},null,8,["model","label","error"])]),n("div",$,[r(l,{model:o.contactInfoForm.postal_code,"onUpdate:model":t[13]||(t[13]=e=>o.contactInfoForm.postal_code=e),label:s.$t("translations.profile.postal_code_label"),onChange:t[14]||(t[14]=e=>o.contactInfoForm.validate("postal_code")),error:o.contactInfoForm.errors.postal_code,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"]),r(l,{model:o.contactInfoForm.city,"onUpdate:model":t[15]||(t[15]=e=>o.contactInfoForm.city=e),label:s.$t("translations.profile.city_label"),onChange:t[16]||(t[16]=e=>o.contactInfoForm.validate("city")),error:o.contactInfoForm.errors.city,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),n("div",oo,[n("div",to,[n("label",eo,m(s.$t("sbxadmin.profile.email_label")),1),n("p",so,m(a.user.data.email),1)]),r(l,{model:o.contactInfoForm.phone_no,"onUpdate:model":t[17]||(t[17]=e=>o.contactInfoForm.phone_no=e),label:s.$t("translations.profile.phone_no_label"),onChange:t[18]||(t[18]=e=>o.contactInfoForm.validate("phone_no")),error:o.contactInfoForm.errors.phone_no,instruction:s.$t("translations.global.mandatory")},null,8,["model","label","error","instruction"])]),n("div",no,[r(l,{type:"password",model:o.contactInfoForm.password,"onUpdate:model":t[19]||(t[19]=e=>o.contactInfoForm.password=e),label:s.$t("sbxadmin.profile.password_label"),instruction:s.$t("sbxadmin.profile.password_instruction"),onChange:t[20]||(t[20]=e=>o.contactInfoForm.validate("password")),error:o.contactInfoForm.errors.password},null,8,["model","label","instruction","error"]),r(l,{type:"password",model:o.contactInfoForm.password_confirmation,"onUpdate:model":t[21]||(t[21]=e=>o.contactInfoForm.password_confirmation=e),label:s.$t("sbxadmin.profile.password_confirmation_label"),instruction:s.$t("sbxadmin.profile.password_instruction"),onChange:t[22]||(t[22]=e=>o.contactInfoForm.validate("password_confirmation")),error:o.contactInfoForm.errors.password_confirmation},null,8,["model","label","instruction","error"])]),n("div",ao,[r(k,{model:o.contactInfoForm.allow_text_messages,"onUpdate:model":t[23]||(t[23]=e=>o.contactInfoForm.allow_text_messages=e),label:s.$t("translations.profile.sms_label"),class:"mt-4"},null,8,["model","label"])]),r(g,{class:"mt-4",enabled:!o.contactInfoForm.processing},{default:v(()=>[y(m(s.$t("translations.profile.update_contact_info_button")),1)]),_:1},8,["enabled"]),o.profileSaved?(i(),b(F,{key:0,title:s.$t("translations.profile.saved_title"),message:s.$t("translations.profile.saved_message"),class:"mt-4"},null,8,["title","message"])):c("",!0)],32)])):c("",!0),o.selectedTab=="information"?(i(),d("div",ro,[n("form",{onSubmit:t[28]||(t[28]=S((...e)=>p.updateInformation&&p.updateInformation(...e),["prevent"]))},[n("div",lo,[n("div",null,[n("p",io,m(s.$t("translations.profile.from_languages_label")),1),r(_,{modelValue:o.informationForm.from_translation_languages,"onUpdate:modelValue":t[25]||(t[25]=e=>o.informationForm.from_translation_languages=e),noResult:s.$t("translations.translation_assignments.no_language_found_label"),placeholder:s.$t("translations.translation_assignments.from_language_search_label"),selectLabel:s.$t("translations.translator_applications.select_language_label"),deselectLabel:s.$t("translations.translation_assignments.deselect_language_label"),label:"name","track-by":"id",options:a.translationLanguages.data,multiple:!0,taggable:!1,showNoResults:!1,class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","options"]),o.informationForm.errors.from_translation_languages?(i(),d("p",mo,m(o.informationForm.errors.from_translation_languages),1)):c("",!0)])]),n("div",co,[n("div",null,[n("p",po,m(s.$t("translations.profile.to_languages_label")),1),r(_,{modelValue:o.informationForm.to_translation_languages,"onUpdate:modelValue":t[26]||(t[26]=e=>o.informationForm.to_translation_languages=e),noResult:s.$t("translations.translation_assignments.no_language_found_label"),placeholder:s.$t("translations.translation_assignments.from_language_search_label"),selectLabel:s.$t("translations.translator_applications.select_language_label"),deselectLabel:s.$t("translations.translation_assignments.deselect_language_label"),label:"name","track-by":"id",options:a.translationLanguages.data,multiple:!0,taggable:!1,showNoResults:!1,class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","options"]),o.informationForm.errors.to_translation_languages?(i(),d("p",uo,m(o.informationForm.errors.to_translation_languages),1)):c("",!0)])]),n("div",go,[n("div",null,[n("p",fo,m(s.$t("translations.profile.categories_label")),1),r(_,{modelValue:o.informationForm.translation_categories,"onUpdate:modelValue":t[27]||(t[27]=e=>o.informationForm.translation_categories=e),noResult:s.$t("translations.translation_assignments.no_category_found_label"),placeholder:s.$t("translations.translation_assignments.from_category_search_label"),selectLabel:s.$t("translations.translator_applications.select_category_label"),deselectLabel:s.$t("translations.translation_assignments.deselect_category_label"),label:"name","track-by":"id",options:a.translationCategories.data,multiple:!0,taggable:!1,showNoResults:!1,class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","options"]),o.informationForm.errors.translation_categories?(i(),d("p",_o,m(o.informationForm.errors.translation_categories),1)):c("",!0)])]),r(g,{class:"mt-8",enabled:!o.contactInfoForm.processing},{default:v(()=>[y(m(s.$t("translations.profile.update_information_button")),1)]),_:1},8,["enabled"]),o.profileSaved?(i(),b(F,{key:0,title:s.$t("translations.profile.saved_title"),message:s.$t("translations.profile.saved_message"),class:"mt-4"},null,8,["title","message"])):c("",!0)],32)])):c("",!0)]),_:1})}const jo=L(O,[["render",bo]]);export{jo as default};
