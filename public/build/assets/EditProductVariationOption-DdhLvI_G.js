import{_ as B,S as O,c,w as _,r as s,b as d,e,d as m,j as b,h as f,F as P,i as F,g as u,t as l,k as S}from"./app-Cm2beRkj.js";import{S as k}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as j}from"./SBXInput-C8dZEgZe.js";import y from"./BundledProductRow-Gc4s_sHh.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckIcon-CSfnhiPS.js";const w={components:{SBXDefaultPageLayout:k,SBXInput:j,SBXButton:O,BundledProductRow:y},props:{productVariationOption:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("productVariationOption: ",this.productVariationOption)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.productVariationOption.data.name,quantity:parseFloat(this.productVariationOption.data.quantity).toFixed(2),price_adjustment:parseFloat(this.productVariationOption.data.price_adjustment).toFixed(2)})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.productVariationOption.data.name}`},calculatedPrice(){return this.productVariationOption.data.bundled_products.length==0?parseFloat(parseFloat(parseFloat(this.form.quantity)*this.productVariationOption.data.product_price_per_unit)).toFixed(2):this.priceIncludedProducts},adjustedPrice(){return parseFloat(parseFloat(this.calculatedPrice)+parseFloat(this.form.price_adjustment)).toFixed(2)},priceIncludedProducts(){for(var r=0,t=0;t<this.productVariationOption.data.bundled_products.length;t++){let a=this.productVariationOption.data.bundled_products[t];r+=parseFloat(a.option_price)}return parseFloat(r).toFixed(2)}},methods:{update(){this.form.processing||this.form.put(this.route("product_variations.options.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productVariationOption.data.id]))}}},x={key:1},v={class:"mt-4"};function X(r,t,a,q,n,i){const p=s("SBXInput"),g=s("BundledProductRow"),h=s("SBXButton"),V=s("SBXDefaultPageLayout");return d(),c(V,null,{default:_(()=>[e("form",{onSubmit:t[3]||(t[3]=S((...o)=>i.update&&i.update(...o),["prevent"]))},[m(p,{model:n.form.name,"onUpdate:model":t[0]||(t[0]=o=>n.form.name=o),label:r.$t("sbxwebshop.product_variations.option_name_label"),error:a.errors.name},null,8,["model","label","error"]),this.productVariationOption.data.bundled_products.length==0?(d(),c(p,{key:0,model:n.form.quantity,"onUpdate:model":t[1]||(t[1]=o=>n.form.quantity=o),label:r.$t("sbxwebshop.product_variations.option_quantity_label"),error:a.errors.quantity},null,8,["model","label","error"])):b("",!0),m(p,{model:n.form.price_adjustment,"onUpdate:model":t[2]||(t[2]=o=>n.form.price_adjustment=o),label:r.$t("sbxwebshop.product_variations.option_price_adjustment_label"),error:a.errors.price_adjustment},null,8,["model","label","error"]),this.productVariationOption.data.bundled_products.length>0?(d(),f("div",x,[t[5]||(t[5]=e("p",{class:"mt-4"},[e("strong",null,"Inkluderade produkter")],-1)),e("ul",null,[(d(!0),f(P,null,F(a.productVariationOption.data.bundled_products,o=>(d(),c(g,{product:o},null,8,["product"]))),256))]),e("p",null,[t[4]||(t[4]=e("strong",null,"Summa inkluderade produkter ",-1)),u(l(i.priceIncludedProducts)+" kr",1)])])):b("",!0),e("p",v,[t[6]||(t[6]=e("strong",null,"Pris före justering: ",-1)),u(l(i.calculatedPrice)+" kr",1)]),e("p",null,[t[7]||(t[7]=e("strong",null,"Pris: ",-1)),u(l(i.adjustedPrice)+" kr",1)]),m(h,{class:"mt-4"},{default:_(()=>[u(l(r.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const R=B(w,[["render",X]]);export{R as default};
