import{_ as w,b as o,h as l,e as s,$ as D,r as d,g as y,t as r,F as b,i as v,c as x,w as p,d as h,j as f,n as k,a4 as O,N as A,a as j,U as R,o as T,f as N,l as q,m as F,J as C}from"./app-Cm2beRkj.js";import{r as V,d as M}from"./debounce-Bpn6Ai4k.js";import{r as X,b as E}from"./SBXTable-CaSPMjSb.js";const G={props:{size:{type:Number,default:20},color:{type:String,default:"#16A34A"}},computed:{pixelSize(){return`${this.size}px`},viewBox(){return`0 0 ${this.size} ${this.size}`}}},U=["width","height","viewBox"],H={stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd","stroke-linecap":"round","stroke-linejoin":"round"},Z={transform:"translate(1.000000, 1.000000)","stroke-width":"1.5"},J=["stroke","fill"];function K(e,t,i,u,c,n){return o(),l("svg",{width:n.pixelSize,height:n.pixelSize,viewBox:n.viewBox},[s("g",H,[s("g",Z,[s("path",{d:"M13.5,16.7942286 C10.7153903,18.4019237 7.28460967,18.4019237 4.5,16.7942286 C1.71539025,15.1865334 0,12.2153902 0,9 C0,4.02943714 4.02943733,0 9,0 C13.9705627,0 18,4.02943714 18,9 C18,12.2153902 16.2846097,15.1865334 13.5,16.7942286 Z",stroke:i.color,fill:i.color},null,8,J),t[0]||(t[0]=s("polyline",{stroke:"#FFFFFF",points:"5 9.74999981 8 12.7499998 13 5.74999981"},null,-1))])])],8,U)}const Q=w(G,[["render",K]]),W={props:{size:{type:Number,default:20},color:{type:String,default:"#9CA3AF"}},computed:{pixelSize(){return`${this.size}px`},viewBox(){return`0 0 ${this.size} ${this.size}`}}},Y=["width","height","viewBox"],ee={stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd","stroke-linecap":"round","stroke-linejoin":"round"},te=["stroke"];function se(e,t,i,u,c,n){return o(),l("svg",{width:n.pixelSize,height:n.pixelSize,viewBox:n.viewBox},[s("g",ee,[s("g",{transform:"translate(1.000000, 1.000000)",stroke:i.color,"stroke-width":"1.5"},t[0]||(t[0]=[s("path",{d:"M13.5,16.7942286 C10.7153903,18.4019237 7.28460967,18.4019237 4.5,16.7942286 C1.71539025,15.1865334 0,12.2153902 0,9 C0,4.02943714 4.02943733,0 9,0 C13.9705627,0 18,4.02943714 18,9 C18,12.2153902 16.2846097,15.1865334 13.5,16.7942286 Z"},null,-1)]),8,te)])],8,Y)}const ie=w(W,[["render",se]]),ne={components:{Link:D,ChevronLeftIcon:X,ChevronRightIcon:E},props:{paginator:{type:Object,default:null}},methods:{buttonClass(e){if(e.label.toLowerCase().includes("previous")){var t="relative inline-flex items-center px-2 py-1 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500";return this.componentType(e)=="Link"&&(t+=" hover:bg-gray-50"),t}if(e.label.toLowerCase().includes("next")){var t="relative inline-flex items-center px-2 py-1 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500";return this.componentType(e)=="Link"&&(t+=" hover:bg-gray-50"),t}return e.active?"z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-2 py-1 border text-sm font-medium cursor-default":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-2 py-1 border text-sm font-medium cursor-pointer"},componentType(e){return e.active||e.url==null||this.paginator.current_page==1&&e.label.includes("Previous")||this.paginator.current_page==this.paginator.last_page&&e.label.includes("Next")?"span":"Link"},handleLink(e){this.$emit("handleLink",e)}}},re={class:"bg-gray-50 px-4 py-2 flex items-center justify-between border-t border-gray-200 sm:px-6"},oe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},ae={class:"text-xs text-gray-700 select-none"},le={class:"font-medium"},ce={class:"font-medium"},de={class:"font-medium"},ue={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},me={key:0},he={class:"sr-only"},fe={key:1,class:"select-none"},pe={key:2},ge={class:"sr-only"};function ye(e,t,i,u,c,n){const _=d("ChevronLeftIcon"),I=d("ChevronRightIcon");return o(),l("div",re,[t[0]||(t[0]=s("div",{class:"flex-1 flex justify-between sm:hidden"},[s("a",{href:"#",class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Previous "),s("a",{href:"#",class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Next ")],-1)),s("div",oe,[s("div",null,[s("p",ae,[y(r(e.$t("sbxpaginator.showing"))+" "+r(" ")+" ",1),s("span",le,r(i.paginator.from),1),y(" "+r(" ")+" "+r(e.$t("sbxpaginator.to"))+" "+r(" ")+" ",1),s("span",ce,r(i.paginator.to),1),y(" "+r(" ")+" "+r(e.$t("sbxpaginator.of"))+" "+r(" ")+" ",1),s("span",de,r(i.paginator.total),1),y(" "+r(" ")+" "+r(e.$t("sbxpaginator.rows")),1)])]),s("div",null,[s("nav",ue,[(o(!0),l(b,null,v(i.paginator.links,m=>(o(),x(O("span"),{class:k(n.buttonClass(m)),onClick:$=>n.handleLink(m.url)},{default:p(()=>[m.label.toLowerCase().includes("previous")?(o(),l("div",me,[s("span",he,r(e.$t("sbxpaginator.previous")),1),h(_,{class:"h-4 w-4","aria-hidden":"true"})])):f("",!0),!m.label.toLowerCase().includes("previous")&&!m.label.toLowerCase().includes("next")?(o(),l("div",fe,r(m.label),1)):f("",!0),m.label.toLowerCase().includes("next")?(o(),l("div",pe,[s("span",ge,r(e.$t("sbxpaginator.next")),1),h(I,{class:"h-4 w-4","aria-hidden":"true"})])):f("",!0)]),_:2},1032,["class","onClick"]))),256))])])])])}const xe=w(ne,[["render",ye]]),_e=(e,t)=>e.map(i=>i[t]),be={components:{Dialog:A,DialogPanel:j,DialogTitle:R,TransitionChild:T,TransitionRoot:N,SBXItemPickerPaginator:xe,CheckOnIcon:Q,CheckOffIcon:ie,MagnifyingGlassIcon:V},props:{title:{type:String,required:!0},itemsRoute:{type:String,required:!0},itemsRouteAdditionalIDs:{type:Array,default:[]},columns:{type:Array,required:!0},multiple:{type:Boolean,default:!1},exclude:{type:Array,default:null},filters:{type:Object,default:null},isOpen:{type:Boolean,default:!1}},mounted(){this.fetchItems()},watch:{isOpen(e,t){this.fetchItems(),this.pickedItems=[]},search(e,t){this.performSearch(e)},filters:{handler(e){this.fetchItems()},deep:!0}},data(){return{items:[],paginator:null,pickedItems:[],search:null}},computed:{dataColumns(){for(var e=[],t=0;t<this.columns.length;t++){let i=this.columns[t];e.push(i)}return e.push({key:"actions",headeralignment:"right",class:"justify-end"}),e},pickedItemIDs(){return _e(this.pickedItems,"id")},confirmButtonActive(){return this.pickedItems.length>0},confirmButtonLabel(){return this.confirmButtonActive?`${this.$t("sbxui.item_picker.confirm_button")} (${this.pickedItems.length})`:this.$t("sbxui.item_picker.confirm_button")},pickerRoute(){var e=[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code];if(this.itemsRouteAdditionalIDs.length>0)for(var t=0;t<this.itemsRouteAdditionalIDs.length;t++)e.push(this.itemsRouteAdditionalIDs[t]);var i=this.route(this.itemsRoute,e),u=!1;this.exclude!=null&&(i=`${i}${u?"&":"?"}exclude=${this.exclude}`,u=!0),this.search!=null&&(i=`${i}${u?"&":"?"}search=${this.search}`,u=!0);for(var c in this.filters)Object.prototype.hasOwnProperty.call(this.filters,c)&&this.filters[c]!=null&&(i=`${i}${u?"&":"?"}${c}=${this.filters[c]}`,u=!0);return i}},methods:{fetchItems(){var e=this;axios.get(this.pickerRoute).then(function(t){e.items=t.data.data,e.paginator=t.data.meta}).catch(function(t){console.log("Error fetching picker items: ",t)})},performSearch:M(function(e){this.fetchItems()},300),pickItem(e){this.multiple?this.pickedItems.findIndex(t=>t.id==e.id)==-1&&this.pickedItems.push(e):(this.pickedItems=[],this.pickedItems.push(e))},unpickItem(e){if(this.multiple){let t=this.pickedItems.findIndex(i=>i.id==e.id);t!=-1&&this.pickedItems.splice(t,1)}else this.pickedItems=[]},cancelItemPicker(){this.$emit("cancelItemPicker")},confirmPickedItems(){this.confirmButtonActive&&this.$emit("itemPickerPickedItems",this.pickedItems)},headerClass(e){var t="px-6 py-2 text-xs font-semibold text-gray-700 uppercase tracking-wider";return e.hasOwnProperty("headeralignment")?(e.headeralignment=="left"&&(t+=" text-left"),e.headeralignment=="center"&&(t+=" text-center"),e.headeralignment=="right"&&(t+=" text-right")):t+=" text-left",t},handlePaginatorLink(e){if(e!=null){var t=this;axios.get(e).then(function(i){t.items=i.data.data,t.paginator=i.data.meta}).catch(function(i){console.log("Error fetching picker items: ",i)})}}}},ve={class:"fixed inset-0 z-50 overflow-y-auto"},ke={class:"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"},we={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Ie={class:"sm:flex sm:items-start"},Ce={class:"mt-3 text-center sm:mt-0 sm:mx-1 sm:text-left w-full"},$e={class:"mt-2"},Pe={class:"flex-1 px-4 bg-white py-1 border-b border-gray-200"},Be={class:"flex items-center justify-between"},Se={class:"flex-1 flex"},Le={class:"w-full flex md:ml-0"},ze={class:"relative w-full text-gray-400 focus-within:text-gray-600"},De={class:"absolute inset-y-0 left-0 flex items-center pointer-events-none","aria-hidden":"true"},Oe=["placeholder"],Ae={class:"flex items-center ml-2"},je={class:"min-w-full divide-y divide-gray-200"},Re={class:"bg-gray-50"},Te={key:0},Ne={class:"bg-white divide-y divide-gray-200"},qe={class:"px-6 py-2 whitespace-nowrap max-w-xs"},Fe={key:0,class:"text-xs"},Ve={class:"flex items-center justify-end mr-4"},Me={class:"bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6"};function Xe(e,t,i,u,c,n){const _=d("TransitionChild"),I=d("DialogTitle"),m=d("MagnifyingGlassIcon"),$=d("CheckOffIcon"),P=d("CheckOnIcon"),B=d("SBXItemPickerPaginator"),S=d("DialogPanel"),L=d("Dialog"),z=d("TransitionRoot");return o(),x(z,{as:"template",show:i.isOpen},{default:p(()=>[h(L,{as:"div",class:"relative z-50",onClose:n.cancelItemPicker},{default:p(()=>[h(_,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:p(()=>t[3]||(t[3]=[s("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])),_:1}),s("div",ve,[s("div",ke,[h(_,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:p(()=>[h(S,{class:"relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"},{default:p(()=>[s("div",we,[s("div",Ie,[s("div",Ce,[h(I,{as:"h3",class:"text-xl font-medium leading-6 text-gray-900"},{default:p(()=>[y(r(i.title),1)]),_:1}),s("div",$e,[s("div",Pe,[s("div",Be,[s("div",Se,[s("div",Le,[s("div",ze,[s("div",De,[h(m,{class:"h-5 w-5","aria-hidden":"true"})]),q(s("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>c.search=a),name:"search-field",class:"block w-full h-full pl-8 pr-3 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-transparent sm:text-sm",placeholder:e.$t("sbxui.global.search"),type:"search"},null,8,Oe),[[F,c.search]])])])]),s("div",Ae,[C(e.$slots,"filtersArea")])])]),s("table",je,[s("thead",Re,[s("tr",null,[(o(!0),l(b,null,v(n.dataColumns,a=>(o(),l("th",{scope:"col",class:k(n.headerClass(a))},[a.hasOwnProperty("label")?(o(),l("span",Te,r(a.label),1)):f("",!0),C(e.$slots,"header-"+a.key)],2))),256))])]),s("tbody",Ne,[(o(!0),l(b,null,v(c.items,a=>(o(),l("tr",null,[(o(!0),l(b,null,v(i.columns,g=>(o(),l("td",qe,[s("div",{class:k("flex items-center "+g.class)},[a.hasOwnProperty(g.key)?(o(),l("span",Fe,r(a[g.key]),1)):f("",!0),C(e.$slots,g.key,{item:a})],2)]))),256)),s("td",null,[s("div",Ve,[n.pickedItemIDs.indexOf(a.id)==-1?(o(),x($,{key:0,onClick:g=>n.pickItem(a)},null,8,["onClick"])):f("",!0),n.pickedItemIDs.indexOf(a.id)!=-1?(o(),x(P,{key:1,onClick:g=>n.unpickItem(a)},null,8,["onClick"])):f("",!0)])])]))),256))])]),c.paginator?(o(),x(B,{key:0,paginator:c.paginator,onHandleLink:n.handlePaginatorLink},null,8,["paginator","onHandleLink"])):f("",!0)])])])]),s("div",Me,[s("button",{type:"button",class:k([{"bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2":n.confirmButtonActive,"bg-gray-400 cursor-default":!n.confirmButtonActive},"inline-flex w-full justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm sm:ml-3 sm:w-auto sm:text-sm"]),onClick:t[1]||(t[1]=(...a)=>n.confirmPickedItems&&n.confirmPickedItems(...a))},r(n.confirmButtonLabel),3),s("button",{type:"button",class:"mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",onClick:t[2]||(t[2]=(...a)=>n.cancelItemPicker&&n.cancelItemPicker(...a)),ref:"cancelButtonRef"},r(e.$t("sbxui.global.cancel")),513)])]),_:3})]),_:3})])])]),_:3},8,["onClose"])]),_:3},8,["show"])}const He=w(be,[["render",Xe]]);export{He as S};
