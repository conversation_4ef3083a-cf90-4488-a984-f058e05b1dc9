import{_ as d,S as _,c as f,w as i,r as s,b as c,e as n,d as r,g as S,t as B,k as b}from"./app-Cm2beRkj.js";import{u as v}from"./index-BKm97uF2.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as h}from"./SBXInput-C8dZEgZe.js";import{S as L}from"./SBXToggle-CrVi0Yuw.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const T={components:{SBXDefaultPageLayout:X,SBXInput:h,SBXToggle:L,SBXButton:_},props:{translationLanguage:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:v("put",this.route("translation_languages.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.translationLanguage.data.id]),{name:this.translationLanguage.data.name,is_active:this.translationLanguage.data.is_active})}},computed:{pageTitle(){return`${this.$t("translations.translation_languages.edit_title")}`}},methods:{update(){this.form.processing||this.form.submit({preserveScroll:!0,preserveState:!1,onSuccess:o=>{}})}}},$={class:"grid grid-cols-2 gap-4"};function k(o,t,y,D,e,l){const p=s("SBXInput"),m=s("SBXToggle"),u=s("SBXButton"),g=s("SBXDefaultPageLayout");return c(),f(g,null,{default:i(()=>[n("form",{onSubmit:t[3]||(t[3]=b((...a)=>l.update&&l.update(...a),["prevent"]))},[n("div",$,[n("div",null,[r(p,{model:e.form.name,"onUpdate:model":t[0]||(t[0]=a=>e.form.name=a),label:o.$t("translations.translation_languages.name_label"),onChange:t[1]||(t[1]=a=>e.form.validate("name")),error:e.form.errors.name},null,8,["model","label","error"]),r(m,{model:e.form.is_active,"onUpdate:model":t[2]||(t[2]=a=>e.form.is_active=a),label:o.$t("translations.translation_languages.active_label"),class:"mt-4"},null,8,["model","label"]),r(u,{class:"mt-4"},{default:i(()=>[S(B(o.$t("translations.translation_languages.update_button")),1)]),_:1})])])],32)]),_:1})}const U=d(T,[["render",k]]);export{U as default};
