import{b as s,h as t,d as o,w as n,l as c,e as i,J as r,ae as l,aa as d}from"./app-Cm2beRkj.js";const _={class:"text-sm text-gray-600"},m={__name:"ActionMessage",props:{on:Boolean},setup(e){return(a,p)=>(s(),t("div",null,[o(d,{"leave-active-class":"transition ease-in duration-1000","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:n(()=>[c(i("div",_,[r(a.$slots,"default")],512),[[l,e.on]])]),_:3})]))}};export{m as _};
