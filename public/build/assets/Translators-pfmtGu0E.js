import{_ as y,h as r,d as _,w as m,e,F as c,r as p,b as i,l as g,v as h,i as f,t as a,g as u}from"./app-Cm2beRkj.js";import{S as F}from"./SBXDataTable-C66uxM7K.js";import{S as z}from"./SBXFilterBar-w4dDtxld.js";import{S as B}from"./SBXGenericNotification-BrDLezmQ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const k={components:{SBXDataTable:F,SBXFilterBar:z,SBXGenericNotification:B},props:{translationLanguages:Object,translationCategories:Object,translators:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translators.title"),this.$page.props.flash.authorization!=null&&(this.showAuthorizationNotification=!0),console.log("Translators:",this.translators)},updated(){this.$page.props.page_info.title_label=this.$t("translations.translators.title")},data(){return{columns:[{key:"customerslot",label:this.$t("translations.translators.translator_label")},{key:"credit_count",label:this.$t("translations.translators.credit_count_label")},{key:"created_at",label:this.$t("translations.translation_assignments.date_label")},{key:"contactslot",label:this.$t("translations.translator_applications.admin.contact_label")},{key:"infoslot",label:this.$t("translations.translators.other_info_label")}],translatorFilters:{search:this.filters.search,authorized:this.filters.authorized,from_language:this.filters.from_language,to_language:this.filters.to_language,category:this.filters.category},showAuthorizationNotification:!1}},computed:{authorizationMessage(){return this.$page.props.flash.authorization==null?"":this.$page.props.flash.authorization}},methods:{changeFilters(){this.$inertia.get(this.route("translators",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translatorFilters,{preserveState:!1,replace:!0})},searchValueChanged(o){this.translatorFilters.search=o},authorizationNotificationCancelled(){this.showAuthorizationNotification=!1}}},C={class:"flex justify-end"},S={class:"flex justify-end"},T=["value"],D={class:"flex justify-end"},N=["value"],A={class:"flex justify-end"},V=["value"],j={class:"flex flex-col"},X={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},O={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},$={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},M={class:"text-sm font-semibold"},E={class:"text-sm font-semibold"},L={class:"text-sm font-semibold"},R={class:"text-sm"},U={class:"text-sm"},w={class:"text-sm"},G={class:"text-sm"},K=["href"],q={class:"text-sm"},H=["href"],I={class:"text-sm"},J={class:"text-sm"},P={class:"text-sm"},Q={key:0,class:"text-sm"},W={key:1,class:"text-sm"};function Y(o,s,d,Z,l,n){const b=p("SBXFilterBar"),v=p("SBXDataTable"),x=p("SBXGenericNotification");return i(),r(c,null,[_(b,{onSearchValueChanged:n.searchValueChanged,filters:l.translatorFilters,searchRoute:"translators",placeholder:o.$t("translations.global.search")},{filterArea:m(()=>[e("div",C,[g(e("select",{onChange:s[0]||(s[0]=(...t)=>n.changeFilters&&n.changeFilters(...t)),"onUpdate:modelValue":s[1]||(s[1]=t=>l.translatorFilters.authorized=t),class:"mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},s[8]||(s[8]=[e("option",{value:"all"},"Alla",-1),e("option",{value:"authorized"},"Auktoriserade",-1),e("option",{value:"unauthorized"},"Ej auktoriserade",-1)]),544),[[h,l.translatorFilters.authorized]])]),e("div",S,[g(e("select",{onChange:s[2]||(s[2]=(...t)=>n.changeFilters&&n.changeFilters(...t)),"onUpdate:modelValue":s[3]||(s[3]=t=>l.translatorFilters.from_language=t),class:"mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[s[9]||(s[9]=e("option",{value:"all"},"Alla",-1)),(i(!0),r(c,null,f(d.translationLanguages.data,t=>(i(),r("option",{value:t.id},a(t.name),9,T))),256))],544),[[h,l.translatorFilters.from_language]])]),e("div",D,[g(e("select",{onChange:s[4]||(s[4]=(...t)=>n.changeFilters&&n.changeFilters(...t)),"onUpdate:modelValue":s[5]||(s[5]=t=>l.translatorFilters.to_language=t),class:"mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[s[10]||(s[10]=e("option",{value:"all"},"Alla",-1)),(i(!0),r(c,null,f(d.translationLanguages.data,t=>(i(),r("option",{value:t.id},a(t.name),9,N))),256))],544),[[h,l.translatorFilters.to_language]])]),e("div",A,[g(e("select",{onChange:s[6]||(s[6]=(...t)=>n.changeFilters&&n.changeFilters(...t)),"onUpdate:modelValue":s[7]||(s[7]=t=>l.translatorFilters.category=t),class:"mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[s[11]||(s[11]=e("option",{value:"all"},"Alla",-1)),(i(!0),r(c,null,f(d.translationCategories.data,t=>(i(),r("option",{value:t.id},a(t.name),9,V))),256))],544),[[h,l.translatorFilters.category]])])]),_:1},8,["onSearchValueChanged","filters","placeholder"]),e("div",j,[e("div",X,[e("div",O,[e("div",$,[_(v,{columns:l.columns,items:d.translators.data,showAddButton:!0,addRoute:"translators.create",addButtonText:o.$t("translations.translators.add_button"),showEditButton:!0,editRoute:"translators.edit",showDeleteButton:!0,deleteRoute:"translators.destroy",deleteDialogTitle:o.$t("translations.translators.delete_dialog_title"),deleteDialogMessage:o.$t("translations.translators.delete_dialog_message"),deleteDialogOKText:o.$t("translations.translators.delete_dialog_ok_button"),deleteDialogCancelText:o.$t("translations.global.cancel"),paginator:d.translators.meta},{customerslot:m(t=>[e("div",null,[e("p",M,a(o.$t("translations.global.customer_id"))+": "+a(t.item.translator_id),1),e("p",E,a(t.item.company),1),e("p",L,a(t.item.first_name)+" "+a(t.item.last_name),1),e("p",R,a(t.item.address_1),1),e("p",U,a(t.item.address_2),1),e("p",w,a(t.item.postal_code)+" "+a(t.item.city),1)])]),contactslot:m(t=>[e("div",null,[e("p",G,[s[12]||(s[12]=e("strong",null,"E-post: ",-1)),e("a",{href:`mailto:${t.item.email}`},a(t.item.email),9,K)]),e("p",q,[s[13]||(s[13]=e("strong",null,"Telefon: ",-1)),e("a",{href:`tel:${t.item.phone_no}`},a(t.item.phone_no),9,H)])])]),infoslot:m(t=>[e("div",null,[e("p",I,[e("strong",null,a(o.$t("translations.translators.from_label"))+": ",1),u(a(t.item.from_translation_languages),1)]),e("p",J,[e("strong",null,a(o.$t("translations.translators.to_label"))+": ",1),u(a(t.item.to_translation_languages),1)]),e("p",P,[e("strong",null,a(o.$t("translations.translators.categories_label"))+": ",1),u(a(t.item.translation_categories),1)]),t.item.is_authorized?(i(),r("p",Q,[e("strong",null,a(o.$t("translations.translators.authorized_id_label"))+": ",1),u(a(t.item.authorization_id),1)])):(i(),r("p",W,[e("strong",null,a(o.$t("translations.translators.authorized_id_label"))+": ",1),s[14]||(s[14]=u(" -"))]))])]),_:1},8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])]),_(x,{show:l.showAuthorizationNotification,variant:"warning",message:n.authorizationMessage,onNotificationCancelled:n.authorizationNotificationCancelled},null,8,["show","message","onNotificationCancelled"])],64)}const mt=y(k,[["render",Y]]);export{mt as default};
