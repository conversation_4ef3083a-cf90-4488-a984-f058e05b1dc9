import{_ as m,h as c,d as r,e as t,w as u,F as h,r as o,b as p,t as g}from"./app-Cm2beRkj.js";import{S as _}from"./SBXDataTable-C66uxM7K.js";import{S as b}from"./SBXFilterBar-w4dDtxld.js";import{r as f}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as x}from"./XCircleIcon-Bi2eGVmc.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const B={components:{SBXDataTable:_,SBXFilterBar:b,CheckCircleIcon:f,XCircleIcon:x},props:{users:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxadmin.users.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxadmin.users.title")},data(){return{columns:[{key:"name",label:this.$t("sbxadmin.users.name_label")},{key:"emailslot",label:this.$t("sbxadmin.users.email_label")},{key:"role_name",label:this.$t("sbxadmin.users.role_label")}],userFilters:{search:this.filters.search}}},methods:{changeFilters(){this.$inertia.get(this.route("users",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.userFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.userFilters.search=e}}},$={class:"flex flex-col"},D={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},S={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},C={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},T=["href"];function F(e,k,s,v,a,i){const n=o("SBXFilterBar"),d=o("SBXDataTable");return p(),c(h,null,[r(n,{onSearchValueChanged:i.searchValueChanged,filters:a.userFilters,searchRoute:"users",placeholder:e.$t("sbxadmin.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),t("div",$,[t("div",D,[t("div",S,[t("div",C,[r(d,{columns:a.columns,items:s.users.data,showAddButton:!0,addButtonText:e.$t("sbxadmin.users.create_button"),addRoute:"users.create",showEditButton:!0,editRoute:"users.edit",showDeleteButton:!0,deleteRoute:"users.destroy",deleteDialogTitle:e.$t("sbxadmin.users.delete_dialog_title"),deleteDialogMessage:e.$t("sbxadmin.users.delete_dialog_message"),deleteDialogOKText:e.$t("sbxadmin.users.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxadmin.global.cancel"),paginator:s.users.meta},{emailslot:u(l=>[t("a",{class:"text-sm",href:"mailto:"+l.item.email},g(l.item.email),9,T)]),_:1},8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const K=m(B,[["render",F]]);export{K as default};
