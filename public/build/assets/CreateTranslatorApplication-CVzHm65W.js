import{_ as y,b as r,h as i,e as s,F as w,i as L,l as p,ab as v,t as a,af as S,$ as V,d as c,k as C,m as u,j as m,g as x,n as z,c as A,r as f}from"./app-Cm2beRkj.js";import{u as U}from"./index-BKm97uF2.js";import{r as T}from"./CheckIcon-CSfnhiPS.js";import{S as I}from"./StepsDivider-BRzfDQoa.js";import{S as B}from"./SBXAlert-DwVKPUUm.js";import{S as R}from"./SBXComboBox-BrrwJIQ0.js";import{s as N}from"./vue-multiselect.esm-1UMdX8qQ.js";import"./client-BWFz6ICJ.js";import"./use-controllable-D9fh3JbV.js";import"./CheckIcon-DN9uS3Am.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const $={props:{model:{type:Array,default:[]}},mounted(){console.log("currentItems",this.currentItems)},watch:{model:{handler:function(t){console.log("newValue",t),this.currentItems=t},deep:!0}},data(){return{currentItems:this.model}}},j={class:"space-y-1"},D={class:"relative flex items-start"},M={class:"flex h-6 items-center"},F=["onUpdate:modelValue"],X={class:"ml-3 text-sm leading-6"},H={class:"font-medium text-gray-900"};function O(t,l,n,_,e,h){return r(),i("fieldset",null,[s("div",j,[(r(!0),i(w,null,L(e.currentItems,(b,d)=>(r(),i("div",D,[s("div",M,[p(s("input",{"onUpdate:modelValue":g=>e.currentItems[d].isSelected=g,onInput:l[0]||(l[0]=g=>t.$emit("update:model",e.currentItems)),onChange:l[1]||(l[1]=g=>t.$emit("change",e.currentItems)),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"},null,40,F),[[v,e.currentItems[d].isSelected]])]),s("div",X,[s("label",H,a(b.value),1)])]))),256))])])}const E=y($,[["render",O]]),G={components:{Head:S,Link:V,StepsDivider:I,CheckboxGroup:E,CheckIcon:T,SBXAlert:B,SBXComboBox:R,Multiselect:N},props:{translationLanguages:Object,translationCategories:Object,errors:Object},layout:null,data(){return{form:U("post",this.route("translator_applications.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{company:null,company_no:null,first_name:null,last_name:null,address_1:null,address_2:null,postal_code:null,city:null,email:null,phone_no:null,from_translation_languages:[],to_translation_languages:[],translation_categories:[],is_authorized:!1,authorization_id:null}),termsAccepted:!1,applicationSubmitted:!1,selectedFromLanguages:[],selectedToLanguages:[],selectedCategories:[]}},computed:{confirmationMessage(){return this.$t("translations.translator_applications.confirmation_message")}},methods:{createApplication(){var t=this;!this.form.processing&&this.termsAccepted&&this.form.submit({preserveScroll:!0,onSuccess:l=>{t.applicationSubmitted=!0,t.form.company=null,t.form.company_no=null,t.form.first_name=null,t.form.last_name=null,t.form.address_1=null,t.form.address_2=null,t.form.postal_code=null,t.form.city=null,t.form.email=null,t.form.phone_no=null,t.termsAccepted=!1,t.form.from_translation_languages=[],t.form.to_translation_languages=[],t.form.translation_categories=[],t.form.is_authorized=!1,t.form.authorization_id=null,t.fromTranslationLanguages=[],t.toTranslationLanguages=[];for(var n=0;n<t.translationLanguages.data.length;n++){let _=t.translationLanguages.data[n];t.fromTranslationLanguages.push({id:_.id,value:_.name,isSelected:!1}),t.toTranslationLanguages.push({id:_.id,value:_.name,isSelected:!1})}t.categories=[];for(var n=0;n<t.translationCategories.data.length;n++){let e=t.translationCategories.data[n];t.categories.push({id:e.id,value:e.name,isSelected:!1})}t.form.reset(),setTimeout(function(){t.applicationSubmitted=!1},3e3)}})}}},Z={class:"bg-oversattare-blue-light"},q={class:"relative overflow-hidden"},J={class:"relative bg-oversattare-blue-light py-4"},K={class:"relative"},P={class:"mx-auto px-4 mb-16 sm:px-6 lg:px-8"},Q={class:"text-2xl text-center font-semibold tracking-tight text-gray-900 sm:text-3xl"},W={class:"mt-8 text-md text-center tracking-tight text-gray-900 sm:text-lg"},Y={class:"mt-2 mx-2 sm:mx-6"},ss={class:"grid grid-cols-8 gap-4"},ts={class:"mt-4 sm:mt-8 mx-2 sm:mx-6"},es={class:"grid grid-cols-8 gap-4"},ls=["placeholder"],os={class:"grid grid-cols-8 gap-4"},as={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},ns={class:"mt-2 mx-2 sm:mx-6"},rs={class:"grid grid-cols-8 gap-4"},is=["placeholder"],ms={class:"grid grid-cols-8 gap-4"},cs={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},ds={class:"mt-2 mx-2 sm:mx-6"},ps={class:"grid grid-cols-8 gap-4"},gs=["placeholder"],_s={class:"grid grid-cols-8 gap-4"},us={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},fs={class:"mt-2 mx-2 sm:mx-6"},hs={class:"grid grid-cols-8 gap-4"},bs=["placeholder"],vs={class:"grid grid-cols-8 gap-4"},xs={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},ys={class:"mt-2 mx-2 sm:mx-6"},ks={class:"grid grid-cols-8 gap-4"},ws=["placeholder"],Ls={class:"grid grid-cols-8 gap-4"},Ss={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},Vs={class:"mt-2 mx-2 sm:mx-6"},Cs={class:"grid grid-cols-8 gap-4"},zs={class:"mt-4 sm:mt-8 mx-2 sm:mx-6"},As={class:"grid grid-cols-8 gap-4"},Us={key:0,class:"text-xs text-red-500"},Ts={class:"mt-2 mx-2 sm:mx-6"},Is={class:"grid grid-cols-8 gap-4"},Bs={class:"mt-4 sm:mt-8 mx-2 sm:mx-6"},Rs={class:"grid grid-cols-8 gap-4"},Ns={key:0,class:"text-xs text-red-500"},$s={class:"mt-2 mx-2 sm:mx-6"},js={class:"grid grid-cols-8 gap-4"},Ds={class:"mt-4 sm:mt-8 mx-2 sm:mx-6"},Ms={class:"grid grid-cols-8 gap-4"},Fs={key:0,class:"text-xs text-red-500"},Xs={class:"mt-2 mx-2 sm:mx-6"},Hs={class:"grid grid-cols-8 gap-4"},Os={class:"mt-4 sm:mt-8 mx-2 sm:mx-6"},Es={class:"grid grid-cols-8 gap-4"},Gs={class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full"},Zs={class:"space-y-5"},qs={class:"relative flex items-start"},Js={class:"flex h-6 items-center"},Ks={class:"ml-3 text-sm leading-6"},Ps={for:"authorization",class:"font-medium text-gray-900 cursor-pointer"},Qs={key:0,class:"mt-4 mx-2 sm:mx-6"},Ws={class:"grid grid-cols-8 gap-4"},Ys=["placeholder"],st={class:"grid grid-cols-8 gap-4"},tt={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},et={class:"mt-2 mx-2 sm:mx-6"},lt={class:"grid grid-cols-8 gap-4"},ot={class:"mt-4 sm:mt-8 mx-2 sm:mx-6"},at={class:"grid grid-cols-8 gap-4"},nt={class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full"},rt={class:"space-y-5"},it={class:"relative flex items-start"},mt={class:"flex h-6 items-center"},ct={class:"ml-3 text-sm leading-6"},dt={for:"terms",class:"font-medium text-gray-900 cursor-pointer"},pt={class:"text-blue-700 select-none"},gt={href:"https://www.oversattare.nu/anvandarvillkor/",target:"_blank"},_t={class:"text-gray-900 select-none"},ut={class:"text-blue-700 select-none"},ft={href:"https://www.oversattare.nu/integretetspolicy/",target:"_blank"},ht={class:"mx-2 sm:mx-6"},bt={class:"grid grid-cols-8 gap-4"},vt={class:"flex w-full justify-center"},xt={class:"mx-2 sm:mx-6"},yt={class:"grid grid-cols-8 gap-4"};function kt(t,l,n,_,e,h){const b=f("Head"),d=f("StepsDivider"),g=f("multiselect"),k=f("SBXAlert");return r(),i("div",Z,[c(b,{title:"Översättare.nu | Anslut"}),s("div",q,[s("main",null,[s("div",J,[s("div",K,[s("div",P,[s("p",Q,a(t.$t("translations.translator_applications.title")),1),s("p",W,a(t.$t("translations.translator_applications.subtitle")),1),s("form",{onSubmit:l[17]||(l[17]=C((...o)=>h.createApplication&&h.createApplication(...o),["prevent"]))},[s("div",Y,[s("div",ss,[c(d,{step:1,label:t.$t("translations.translator_applications.step_1_title"),class:"mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["label"])])]),s("div",ts,[s("div",es,[p(s("input",{type:"text","onUpdate:modelValue":l[0]||(l[0]=o=>e.form.company=o),onChange:l[1]||(l[1]=o=>e.form.validate("company")),placeholder:t.$t("translations.translator_applications.company_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,ls),[[u,e.form.company]])]),s("div",os,[e.form.invalid("company")?(r(),i("p",as,a(e.form.errors.company),1)):m("",!0)])]),s("div",ns,[s("div",rs,[p(s("input",{type:"text","onUpdate:modelValue":l[2]||(l[2]=o=>e.form.first_name=o),onChange:l[3]||(l[3]=o=>e.form.validate("first_name")),placeholder:t.$t("translations.translator_applications.first_name_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,is),[[u,e.form.first_name]])]),s("div",ms,[e.form.invalid("first_name")?(r(),i("p",cs,a(e.form.errors.first_name),1)):m("",!0)])]),s("div",ds,[s("div",ps,[p(s("input",{type:"text","onUpdate:modelValue":l[4]||(l[4]=o=>e.form.last_name=o),onChange:l[5]||(l[5]=o=>e.form.validate("last_name")),placeholder:t.$t("translations.translator_applications.last_name_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,gs),[[u,e.form.last_name]])]),s("div",_s,[e.form.invalid("last_name")?(r(),i("p",us,a(e.form.errors.last_name),1)):m("",!0)])]),s("div",fs,[s("div",hs,[p(s("input",{type:"email","onUpdate:modelValue":l[6]||(l[6]=o=>e.form.email=o),onChange:l[7]||(l[7]=o=>e.form.validate("email")),placeholder:t.$t("translations.translator_applications.email_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,bs),[[u,e.form.email]])]),s("div",vs,[e.form.invalid("email")?(r(),i("p",xs,a(e.form.errors.email),1)):m("",!0)])]),s("div",ys,[s("div",ks,[p(s("input",{type:"text","onUpdate:modelValue":l[8]||(l[8]=o=>e.form.phone_no=o),onChange:l[9]||(l[9]=o=>e.form.validate("phone_no")),placeholder:t.$t("translations.translator_applications.phone_no_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,ws),[[u,e.form.phone_no]])]),s("div",Ls,[e.form.invalid("phone_no")?(r(),i("p",Ss,a(e.form.errors.phone_no),1)):m("",!0)])]),s("div",Vs,[s("div",Cs,[c(d,{step:2,label:t.$t("translations.translator_applications.step_2_title"),class:"mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["label"])])]),s("div",zs,[s("div",As,[c(g,{modelValue:e.form.from_translation_languages,"onUpdate:modelValue":l[10]||(l[10]=o=>e.form.from_translation_languages=o),noResult:t.$t("translations.translation_assignments.no_language_found_label"),placeholder:t.$t("translations.translation_assignments.from_language_search_label"),selectLabel:t.$t("translations.translation_assignments.select_language_label"),deselectLabel:t.$t("translations.translation_assignments.deselect_language_label"),selectedLabel:t.$t("translations.translation_assignments.selected_language_label"),label:"name","track-by":"id",options:n.translationLanguages.data,multiple:!0,taggable:!1,showNoResults:!1,class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","selectedLabel","options"]),n.errors.from_translation_languages?(r(),i("p",Us,a(n.errors.from_translation_languages),1)):m("",!0)])]),s("div",Ts,[s("div",Is,[c(d,{step:3,label:t.$t("translations.translator_applications.step_3_title"),class:"mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["label"])])]),s("div",Bs,[s("div",Rs,[c(g,{modelValue:e.form.to_translation_languages,"onUpdate:modelValue":l[11]||(l[11]=o=>e.form.to_translation_languages=o),noResult:t.$t("translations.translation_assignments.no_language_found_label"),placeholder:t.$t("translations.translation_assignments.to_language_search_label"),selectLabel:t.$t("translations.translation_assignments.select_language_label"),deselectLabel:t.$t("translations.translation_assignments.deselect_language_label"),selectedLabel:t.$t("translations.translation_assignments.selected_language_label"),label:"name","track-by":"id",options:n.translationLanguages.data,multiple:!0,taggable:!1,showNoResults:!1,class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","selectedLabel","options"]),n.errors.to_translation_languages?(r(),i("p",Ns,a(n.errors.to_translation_languages),1)):m("",!0)])]),s("div",$s,[s("div",js,[c(d,{step:4,label:t.$t("translations.translator_applications.step_4_title"),class:"mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["label"])])]),s("div",Ds,[s("div",Ms,[c(g,{modelValue:e.form.translation_categories,"onUpdate:modelValue":l[12]||(l[12]=o=>e.form.translation_categories=o),noResult:t.$t("translations.translation_assignments.no_category_found_label"),placeholder:t.$t("translations.translation_assignments.from_category_search_label"),selectLabel:t.$t("translations.translator_applications.select_category_label"),deselectLabel:t.$t("translations.translation_assignments.deselect_category_label"),selectedLabel:t.$t("translations.translation_assignments.selected_language_label"),label:"name","track-by":"id",options:n.translationCategories.data,multiple:!0,taggable:!1,showNoResults:!1,class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","selectedLabel","options"]),n.errors.translation_categories?(r(),i("p",Fs,a(n.errors.translation_categories),1)):m("",!0)])]),s("div",Xs,[s("div",Hs,[c(d,{step:5,label:t.$t("translations.translator_applications.step_5_title"),class:"mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["label"])])]),s("div",Os,[s("div",Es,[s("fieldset",Gs,[s("div",Zs,[s("div",qs,[s("div",Js,[p(s("input",{"onUpdate:modelValue":l[13]||(l[13]=o=>e.form.is_authorized=o),id:"authorization",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600 cursor-pointer"},null,512),[[v,e.form.is_authorized]])]),s("div",Ks,[s("label",Ps,a(t.$t("translations.translator_applications.is_authorized_label")),1)])])])])])]),e.form.is_authorized?(r(),i("div",Qs,[s("div",Ws,[p(s("input",{type:"text","onUpdate:modelValue":l[14]||(l[14]=o=>e.form.authorization_id=o),onChange:l[15]||(l[15]=o=>e.form.validate("authorization_id")),placeholder:t.$t("translations.translator_applications.authorized_id_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,Ys),[[u,e.form.authorization_id]])]),s("div",st,[e.form.invalid("authorization_id")?(r(),i("p",tt,a(e.form.errors.authorization_id),1)):m("",!0)])])):m("",!0),s("div",et,[s("div",lt,[c(d,{step:6,label:t.$t("translations.translator_applications.step_6_title"),class:"mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["label"])])]),s("div",ot,[s("div",at,[s("fieldset",nt,[s("div",rt,[s("div",it,[s("div",mt,[p(s("input",{"onUpdate:modelValue":l[16]||(l[16]=o=>e.termsAccepted=o),id:"terms","aria-describedby":"terms-description",name:"terms",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600 cursor-pointer"},null,512),[[v,e.termsAccepted]])]),s("div",ct,[s("label",dt,a(t.$t("translations.translation_assignments.terms_1")),1),l[19]||(l[19]=x(" "+a(" ")+" ")),s("span",pt,[s("a",gt,a(t.$t("translations.translation_assignments.terms_2")),1)]),s("span",_t,a(t.$t("translations.translation_assignments.terms_3")),1),s("span",ut,[s("a",ft,a(t.$t("translations.translation_assignments.terms_4")),1),l[18]||(l[18]=x("."))])])])])])])]),s("div",ht,[s("div",bt,[s("button",{type:"submit",class:z([{"w-full bg-oversattare-green hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-oversattare-green col-span-8 sm:col-start-3 sm:col-span-4":e.termsAccepted,"bg-gray-400 cursor-not-allowed":!e.termsAccepted},"mt-4 inline-flex items-center gap-x-2 rounded-md py-2.5 px-3.5 text-sm font-semibold text-white shadow-sm col-span-8 sm:col-start-3 sm:col-span-4"])},[s("div",vt,a(t.$t("translations.translator_applications.send_button")),1)],2)])]),s("div",xt,[s("div",yt,[e.applicationSubmitted?(r(),A(k,{key:0,title:t.$t("translations.translator_applications.confirmation_title"),message:t.$t("translations.translator_applications.confirmation_message"),class:"mt-4 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["title","message"])):m("",!0)])])],32)])])])])])])}const Rt=y(G,[["render",kt]]);export{Rt as default};
