import{l,ae as c,b as u,h as o,e as i,t as m,s as p,Y as d}from"./app-Cm2beRkj.js";const f={class:"text-sm text-red-600"},v={__name:"InputError",props:{message:String},setup(s){return(t,e)=>l((u(),o("div",null,[i("p",f,m(s.message),1)],512)),[[c,s.message]])}},_=["value"],h={__name:"TextInput",props:{modelValue:String},emits:["update:modelValue"],setup(s,{expose:t}){const e=p(null);return d(()=>{e.value.hasAttribute("autofocus")&&e.value.focus()}),t({focus:()=>e.value.focus()}),(r,a)=>(u(),o("input",{ref_key:"input",ref:e,class:"border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 rounded-md shadow-sm",value:s.modelValue,onInput:a[0]||(a[0]=n=>r.$emit("update:modelValue",n.target.value))},null,40,_))}};export{h as _,v as a};
