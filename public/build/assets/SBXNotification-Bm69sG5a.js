import{_ as f,r as i,b as r,h as a,e as t,d as o,w as u,j as d,aa as m}from"./app-Cm2beRkj.js";import{r as g}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as p}from"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const h={components:{CheckCircleIcon:g,ArrowRightOnRectangleIcon:p},emits:["notificationCancelled"],props:{variant:{type:String,default:"primary"},size:{type:String,default:"default"},enabled:{type:Boolean,default:!0},show:{type:Boolean,default:!1}},watch:{show(e,s){if(e==!0){var n=this;setTimeout(function(){n.$emit("notificationCancelled")},2e3)}}},computed:{variantClass(){var e="inline-flex justify-center border border-transparent shadow-sm text-sm font-medium rounded-md text-white";return this.size=="default"&&(e+=" py-2 px-4"),this.size=="s"&&(e+=" py-1 px-2"),this.variant=="primary"&&(this.enabled?e+=" bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600":e+=" bg-blue-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600 cursor-not-allowed"),this.variant=="danger"&&(e+=" bg-red-700 hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600"),this.variant=="success"&&(e+=" bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600"),e}}},v={"aria-live":"assertive",class:"fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start mt-12"},x={class:"w-full flex flex-col items-center space-y-4 sm:items-end"},y={key:0,class:"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"},b={class:"p-4"},w={class:"flex items-start"},_={class:"flex-shrink-0"},C={class:"ml-4 flex-shrink-0 flex"};function k(e,s,n,I,$,B){const l=i("CheckCircleIcon"),c=i("ArrowRightOnRectangleIcon");return r(),a("div",v,[t("div",x,[o(m,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:u(()=>[n.show?(r(),a("div",y,[t("div",b,[t("div",w,[t("div",_,[o(l,{class:"h-6 w-6 text-green-400","aria-hidden":"true"})]),s[2]||(s[2]=t("div",{class:"ml-3 w-0 flex-1 pt-0.5"},[t("p",{class:"text-sm font-medium text-gray-900"},"Informationen har sparats!")],-1)),t("div",C,[t("button",{type:"button",onClick:s[0]||(s[0]=R=>this.$emit("notificationCancelled")),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},[s[1]||(s[1]=t("span",{class:"sr-only"},"Close",-1)),o(c,{class:"h-5 w-5","aria-hidden":"true"})])])])])])):d("",!0)]),_:1})])])}const A=f(h,[["render",k]]);export{A as S};
