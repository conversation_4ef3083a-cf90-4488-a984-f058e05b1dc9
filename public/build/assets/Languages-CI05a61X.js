import{_ as d,h as r,d as l,e as t,F as g,r as o,b as m}from"./app-Cm2beRkj.js";import{S as u}from"./SBXDataTable-C66uxM7K.js";import{S as c}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const p={components:{SBXDataTable:u,SBXFilterBar:c},props:{languages:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxadmin.languages.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxadmin.languages.title")},data(){return{columns:[{key:"name",label:this.$t("sbxadmin.languages.name_label"),sortable:!0,sortDirection:"desc",sortByFormatted:!0,filterByFormatted:!0}]}},methods:{}},_={class:"flex flex-col"},b={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},h={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},f={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function x(e,B,a,D,s,T){const n=o("SBXFilterBar"),i=o("SBXDataTable");return m(),r(g,null,[l(n,{filters:a.filters,searchRoute:"languages",placeholder:e.$t("sbxadmin.global.search")},null,8,["filters","placeholder"]),t("div",_,[t("div",b,[t("div",h,[t("div",f,[l(i,{columns:s.columns,items:a.languages.data,showAddButton:!0,addButtonText:e.$t("sbxadmin.languages.new_language_button"),addRoute:"languages.create",showEditButton:!0,editRoute:"languages.edit",showDeleteButton:!0,deleteRoute:"languages.destroy",deleteDialogTitle:e.$t("sbxadmin.languages.delete_dialog_title"),deleteDialogMessage:e.$t("sbxadmin.languages.delete_dialog_message"),deleteDialogOKText:e.$t("sbxadmin.languages.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxadmin.global.cancel"),paginator:a.languages.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const O=d(p,[["render",x]]);export{O as default};
