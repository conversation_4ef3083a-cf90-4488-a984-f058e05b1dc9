import{_ as c}from"./AppLayout-C4Ym-gUG.js";import l from"./DeleteUserForm-PndVUDo-.js";import f from"./LogoutOtherBrowserSessionsForm-ByVnomjD.js";import{S as s}from"./SectionBorder-BOdnOtA6.js";import u from"./TwoFactorAuthenticationForm-DxQGA__1.js";import d from"./UpdatePasswordForm-CONWu7Kx.js";import _ from"./UpdateProfileInformationForm-Cr7mn-sU.js";import{c as h,w as p,b as o,e as i,h as r,d as t,j as a,F as g}from"./app-Cm2beRkj.js";import"./DialogModal-CZeDHxad.js";import"./SectionTitle-zHGmFabz.js";import"./DangerButton-CnZ0_ogC.js";import"./TextInput-Dx25uC_E.js";import"./SecondaryButton-Cr6mqfK5.js";import"./ActionMessage-B_UncuA7.js";import"./PrimaryButton-7FvKQ4S5.js";import"./InputLabel-dwoQhTuq.js";import"./FormSection-78brDZIO.js";const $={class:"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8"},w={key:0},k={key:1},y={key:2},I={__name:"Show",props:{confirmsTwoFactorAuthentication:Boolean,sessions:Array},setup(m){return(e,n)=>(o(),h(c,{title:"Profile"},{header:p(()=>n[0]||(n[0]=[i("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," Profile ",-1)])),default:p(()=>[i("div",null,[i("div",$,[e.$page.props.jetstream.canUpdateProfileInformation?(o(),r("div",w,[t(_,{user:e.$page.props.auth.user},null,8,["user"]),t(s)])):a("",!0),e.$page.props.jetstream.canUpdatePassword?(o(),r("div",k,[t(d,{class:"mt-10 sm:mt-0"}),t(s)])):a("",!0),e.$page.props.jetstream.canManageTwoFactorAuthentication?(o(),r("div",y,[t(u,{"requires-confirmation":m.confirmsTwoFactorAuthentication,class:"mt-10 sm:mt-0"},null,8,["requires-confirmation"]),t(s)])):a("",!0),t(f,{sessions:m.sessions,class:"mt-10 sm:mt-0"},null,8,["sessions"]),e.$page.props.jetstream.hasAccountDeletionFeatures?(o(),r(g,{key:3},[t(s),t(l,{class:"mt-10 sm:mt-0"})],64)):a("",!0)])])]),_:1}))}};export{I as default};
