import{B as r,l as n,ab as d,b as p,h as i}from"./app-Cm2beRkj.js";const h=["value"],b={__name:"Checkbox",props:{checked:{type:[<PERSON><PERSON><PERSON>,<PERSON><PERSON>an],default:!1},value:{type:String,default:null}},emits:["update:checked"],setup(e,{emit:a}){const s=a,u=e,t=r({get(){return u.checked},set(o){s("update:checked",o)}});return(o,c)=>n((p(),i("input",{"onUpdate:modelValue":c[0]||(c[0]=l=>t.value=l),type:"checkbox",value:e.value,class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,8,h)),[[d,t.value]])}};export{b as _};
