import{ac as u,h as i,d as s,C as a,w as o,F as c,b as n,af as f,e as l,t as _,j as p,n as g,g as k,k as b}from"./app-Cm2beRkj.js";import{A as x}from"./AuthenticationCard-BbMgTIf9.js";import{_ as v}from"./AuthenticationCardLogo-DOAxyrlM.js";import{_ as y,a as V}from"./TextInput-Dx25uC_E.js";import{_ as w}from"./InputLabel-dwoQhTuq.js";import{_ as C}from"./PrimaryButton-7FvKQ4S5.js";const $={key:0,class:"mb-4 font-medium text-sm text-green-600"},N={class:"flex items-center justify-end mt-4"},S={layout:null},T=Object.assign(S,{__name:"ForgotPassword",props:{status:String},setup(r){const t=u({email:""}),m=()=>{t.post(route("password.email"))};return(j,e)=>(n(),i(c,null,[s(a(f),{title:"Översättare.nu | Glömt lösenord"}),s(x,null,{logo:o(()=>[s(v)]),default:o(()=>[e[2]||(e[2]=l("div",{class:"mb-4 text-sm text-gray-600"}," Glömt ditt lösenord? Ange din e-postadress nedan så skickar vi en länk som låter dig återställa ditt lösenord och ange ett nytt. ",-1)),r.status?(n(),i("div",$,_(r.status),1)):p("",!0),l("form",{onSubmit:b(m,["prevent"])},[l("div",null,[s(w,{for:"email",value:"E-post"}),s(y,{id:"email",modelValue:a(t).email,"onUpdate:modelValue":e[0]||(e[0]=d=>a(t).email=d),type:"email",class:"mt-1 block w-full",required:"",autofocus:""},null,8,["modelValue"]),s(V,{class:"mt-2",message:a(t).errors.email},null,8,["message"])]),l("div",N,[s(C,{class:g({"opacity-25":a(t).processing}),disabled:a(t).processing},{default:o(()=>e[1]||(e[1]=[k(" Skicka länk för att återställa lösenord ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}});export{T as default};
