import{_ as u,S as c,c as d,w as n,r as l,b as f,e,t as i,l as _,m as g,d as b,g as h,k as B}from"./app-Cm2beRkj.js";import{S as x}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const S={components:{SBXDefaultPageLayout:x,SBXButton:c},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:""})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_units.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("product_units.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},w={class:"mt-4"},$={for:"name",class:"block text-sm font-medium text-gray-700"},v={class:"mt-1"};function y(s,t,k,X,a,r){const m=l("SBXButton"),p=l("SBXDefaultPageLayout");return f(),d(p,null,{default:n(()=>[e("form",{onSubmit:t[1]||(t[1]=B((...o)=>r.create&&r.create(...o),["prevent"]))},[e("div",w,[e("label",$,i(s.$t("sbxwebshop.product_units.name_label")),1),e("div",v,[_(e("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>a.form.name=o),type:"text",name:"name",id:"name",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[g,a.form.name]])])]),b(m,{class:"mt-4"},{default:n(()=>[h(i(s.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const P=u(S,[["render",y]]);export{P as default};
