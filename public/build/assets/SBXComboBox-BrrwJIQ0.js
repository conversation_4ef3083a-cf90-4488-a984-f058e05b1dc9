import{r as I}from"./CheckIcon-DN9uS3Am.js";import{r as L,A as k,L as B,B as N,F,N as S,j as q}from"./SBXAlert-DwVKPUUm.js";import{r as O}from"./ExclamationCircleIcon-C4_TqLjZ.js";import{_ as D,r,b as l,h as u,d as s,w as i,e as c,c as g,F as U,i as j,n as d,t as b,j as m}from"./app-Cm2beRkj.js";const A={components:{ExclamationCircleIcon:O,CheckIcon:I,ChevronUpDownIcon:L,Combobox:k,ComboboxButton:B,ComboboxInput:N,ComboboxLabel:F,ComboboxOption:S,ComboboxOptions:q},props:{valueFieldName:{type:String,default:"id"},labelFieldName:{type:String,default:"text"},selectedLabel:{type:String,default:"Selected:"},model:{default:[]},items:{type:Array,required:!0},instruction:{type:String,required:!1,default:null},error:{type:String,required:!1,default:null}},watch:{model(e,t){this.currentValue=e}},data(){return{currentValue:this.model,query:""}},computed:{filteredItems(){return this.query===""?this.items:this.items.filter(e=>e[this.labelFieldName].toLowerCase().includes(this.query.toLowerCase()))},selectedItemsLabel(){var e='<span class="font-semibold">'+this.selectedLabel+"</span> ";if(this.currentValue.length==0)return e+=" – ",e;for(var t=0;t<this.currentValue.length;t++){let o=this.currentValue[t];e+=o[this.labelFieldName],t<=this.currentValue.length-2?e+=", ":e+="."}return e}},methods:{change(e){this.query=e.target.value,this.$emit("update:model",this.currentValue),this.$emit("change",this.currentValue)},promptLabel(){return this.query}}},M={class:"relative mt-2"},z=["innerHTML"],E={key:0,class:"mt-1 mx-2 text-xs text-gray-500"},H={key:1,class:"mt-1 mx-2 text-xs text-red-600"};function T(e,t,o,X,h,n){const _=r("ComboboxInput"),p=r("ComboboxButton"),C=r("ChevronUpDownIcon"),y=r("CheckIcon"),w=r("ComboboxOption"),V=r("ComboboxOptions"),v=r("Combobox");return l(),u("div",null,[s(v,{as:"div",modelValue:h.currentValue,"onUpdate:modelValue":t[0]||(t[0]=a=>h.currentValue=a),multiple:""},{default:i(()=>[c("div",M,[s(p,{class:"w-full"},{default:i(()=>[s(_,{class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6",onChange:n.change,displayValue:n.promptLabel,placeholder:e.$t("translations.global.search")},null,8,["onChange","displayValue","placeholder"])]),_:1}),s(p,{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:i(()=>[s(C,{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),n.filteredItems.length>0?(l(),g(V,{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(l(!0),u(U,null,j(n.filteredItems,a=>(l(),g(w,{key:a[o.valueFieldName],value:a,as:"template"},{default:i(({active:x,selected:f})=>[c("li",{class:d(["relative cursor-default select-none py-2 pl-8 pr-4",x?"bg-blue-600 text-white":"text-gray-900"]),"data-headlessui-state":"selected"},[c("span",{class:d(["block truncate",f&&"font-semibold"])},b(a[this.labelFieldName]),3),f?(l(),u("span",{key:0,class:d(["absolute inset-y-0 left-0 flex items-center pl-1.5",x?"text-white":"text-blue-600"])},[s(y,{class:"h-5 w-5","aria-hidden":"true"})],2)):m("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):m("",!0)])]),_:1},8,["modelValue"]),c("p",{innerHTML:n.selectedItemsLabel,class:"mt-2 mx-2 text-sm"},null,8,z),o.instruction?(l(),u("p",E,b(o.instruction),1)):m("",!0),o.error?(l(),u("p",H,b(o.error),1)):m("",!0)])}const Q=D(A,[["render",T]]);export{Q as S};
