import{_ as x,S as f,c as l,w as d,r as a,b as n,e as t,t as r,h as g,i as b,F as y,d as v,g as w,k as P}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import $ from"./ProductReconciliationRow-DAZJz9VJ.js";import"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const k={components:{SBXDefaultPageLayout:B,SBXButton:f,ProductReconciliationRow:$},props:{products:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,this.setupFormProducts()},updated(){this.$page.props.page_info.title_label=this.pageTitle,this.setupFormProducts()},remember:"form",data(){return{form:this.$inertia.form({products:[]})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_inventory.reconciliation.title")}`}},methods:{setupFormProducts(){for(var e=[],o=0;o<this.products.data.length;o++){let s=this.products.data[o];e.push({product_id:s.id,quantity:0})}this.form.products=e},productQuantityChanged(e,o){for(var s=0;s<this.form.products.length;s++)if(this.form.products[s].product_id===e){this.form.products[s].quantity=o;break}},create(){this.form.processing||this.form.post(this.route("product_inventory.reconciliation.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},S={class:"px-4 sm:px-6 lg:px-8"},R={class:"flow-root"},C={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},X={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8"},F={class:"min-w-full divide-y divide-gray-300"},D={scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-xs sm:text-sm font-semibold text-gray-900 sm:pl-3"},L={scope:"col",class:"px-3 py-3.5 text-right text-xs sm:text-sm font-semibold text-gray-900"},Q={scope:"col",class:"px-3 py-3.5 text-right text-xs sm:text-sm font-semibold text-gray-900"},T={scope:"col",class:"hidden sm:table-cell px-3 py-3.5 text-right text-xs sm:text-sm font-semibold text-gray-900"},j={class:"bg-white"};function q(e,o,s,p,N,c){const u=a("ProductReconciliationRow"),m=a("SBXButton"),_=a("SBXDefaultPageLayout");return n(),l(_,null,{default:d(()=>[t("form",{onSubmit:o[0]||(o[0]=P((...i)=>c.create&&c.create(...i),["prevent"]))},[t("div",S,[t("div",R,[t("div",C,[t("div",X,[t("table",F,[t("thead",null,[t("tr",null,[t("th",D,r(e.$t("sbxwebshop.product_inventory.reconciliation.product_name_label")),1),t("th",L,r(e.$t("sbxwebshop.product_inventory.reconciliation.current_stock_label")),1),t("th",Q,r(e.$t("sbxwebshop.product_inventory.reconciliation.delivery_quantity_label")),1),t("th",T,r(e.$t("sbxwebshop.product_inventory.reconciliation.adjusted_stock_label")),1)])]),t("tbody",j,[(n(!0),g(y,null,b(s.products.data,(i,h)=>(n(),l(u,{product:i,key:i.id,rowIdx:h,onProductQuantityChanged:c.productQuantityChanged},null,8,["product","rowIdx","onProductQuantityChanged"]))),128))])]),v(m,{class:"mt-4"},{default:d(()=>[w(r(e.$t("sbxwebshop.product_inventory.reconciliation.create_button")),1)]),_:1})])])])])],32)]),_:1})}const z=x(k,[["render",q]]);export{z as default};
