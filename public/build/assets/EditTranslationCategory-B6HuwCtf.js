import{_ as g,S as c,c as _,w as l,r,b as f,e as s,d as n,g as S,t as B,k as b}from"./app-Cm2beRkj.js";import{u as v}from"./index-BKm97uF2.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as h}from"./SBXInput-C8dZEgZe.js";import{S as y}from"./SBXToggle-CrVi0Yuw.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const C={components:{SBXDefaultPageLayout:X,SBXInput:h,SBXToggle:y,SBXButton:c},props:{translationCategory:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:v("put",this.route("translation_categories.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.translationCategory.data.id]),{name:this.translationCategory.data.name,is_active:this.translationCategory.data.is_active})}},computed:{pageTitle(){return`${this.$t("translations.translation_categories.edit_title")}`}},methods:{update(){this.form.processing||this.form.submit({preserveScroll:!0,preserveState:!1,onSuccess:a=>{}})}}},T={class:"grid grid-cols-2 gap-4"};function $(a,t,k,D,e,i){const p=r("SBXInput"),m=r("SBXToggle"),u=r("SBXButton"),d=r("SBXDefaultPageLayout");return f(),_(d,null,{default:l(()=>[s("form",{onSubmit:t[3]||(t[3]=b((...o)=>i.update&&i.update(...o),["prevent"]))},[s("div",T,[s("div",null,[n(p,{model:e.form.name,"onUpdate:model":t[0]||(t[0]=o=>e.form.name=o),label:a.$t("translations.translation_categories.name_label"),onChange:t[1]||(t[1]=o=>e.form.validate("name")),error:e.form.errors.name},null,8,["model","label","error"]),n(m,{model:e.form.is_active,"onUpdate:model":t[2]||(t[2]=o=>e.form.is_active=o),label:a.$t("translations.translation_categories.active_label"),class:"mt-4"},null,8,["model","label"]),n(u,{class:"mt-4"},{default:l(()=>[S(B(a.$t("translations.translation_categories.update_button")),1)]),_:1})])])],32)]),_:1})}const U=g(C,[["render",$]]);export{U as default};
