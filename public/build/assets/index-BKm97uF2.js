import{i as G,b as gr,S as I,a as L,c as R,g as Q,r as K,o as le,d as ce,e as de,f as ge,M as vr,h as ve,s as pe,j as he,k as W,l as pr,m as ye,t as N,n as tr,p as Hr,q as Te,u as X,v as Vr,w as Ee,x as ar,y as be,z as Ae,A as q,B as qr,C as Se,U as Ar,D as $e,E as Oe,F as we,G as M,H as me,I as _e,J as ir,K as or,L as sr}from"./client-BWFz6ICJ.js";import{s as Sr,al as Ce,z as Pe,ac as Ie,a6 as Le}from"./app-Cm2beRkj.js";var Fe="[object Symbol]";function z(r){return typeof r=="symbol"||G(r)&&gr(r)==Fe}function Yr(r,e){for(var n=-1,a=r==null?0:r.length,o=Array(a);++n<a;)o[n]=e(r[n],n,r);return o}var xe=1/0,$r=I?I.prototype:void 0,Or=$r?$r.toString:void 0;function Xr(r){if(typeof r=="string")return r;if(L(r))return Yr(r,Xr)+"";if(z(r))return Or?Or.call(r):"";var e=r+"";return e=="0"&&1/r==-xe?"-0":e}var De=/\s/;function Me(r){for(var e=r.length;e--&&De.test(r.charAt(e)););return e}var Re=/^\s+/;function Ne(r){return r&&r.slice(0,Me(r)+1).replace(Re,"")}var wr=NaN,je=/^[-+]0x[0-9a-f]+$/i,Be=/^0b[01]+$/i,Ge=/^0o[0-7]+$/i,Ue=parseInt;function mr(r){if(typeof r=="number")return r;if(z(r))return wr;if(R(r)){var e=typeof r.valueOf=="function"?r.valueOf():r;r=R(e)?e+"":e}if(typeof r!="string")return r===0?r:+r;r=Ne(r);var n=Be.test(r);return n||Ge.test(r)?Ue(r.slice(2),n?2:8):je.test(r)?wr:+r}var ur=Q(K,"WeakMap");function Ke(r,e){for(var n=-1,a=r==null?0:r.length;++n<a&&e(r[n],n,r)!==!1;);return r}var We=le(Object.keys,Object),He=Object.prototype,Ve=He.hasOwnProperty;function qe(r){if(!ce(r))return We(r);var e=[];for(var n in Object(r))Ve.call(r,n)&&n!="constructor"&&e.push(n);return e}function hr(r){return de(r)?ge(r):qe(r)}var Ye=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Xe=/^\w*$/;function Ze(r,e){if(L(r))return!1;var n=typeof r;return n=="number"||n=="symbol"||n=="boolean"||r==null||z(r)?!0:Xe.test(r)||!Ye.test(r)||e!=null&&r in Object(e)}var Je="Expected a function";function yr(r,e){if(typeof r!="function"||e!=null&&typeof e!="function")throw new TypeError(Je);var n=function(){var a=arguments,o=e?e.apply(this,a):a[0],i=n.cache;if(i.has(o))return i.get(o);var u=r.apply(this,a);return n.cache=i.set(o,u)||i,u};return n.cache=new(yr.Cache||vr),n}yr.Cache=vr;var Qe=500;function ze(r){var e=yr(r,function(a){return n.size===Qe&&n.clear(),a}),n=e.cache;return e}var ke=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,rn=/\\(\\)?/g,en=ze(function(r){var e=[];return r.charCodeAt(0)===46&&e.push(""),r.replace(ke,function(n,a,o,i){e.push(o?i.replace(rn,"$1"):a||n)}),e});function nn(r){return r==null?"":Xr(r)}function k(r,e){return L(r)?r:Ze(r,e)?[r]:en(nn(r))}var tn=1/0;function Tr(r){if(typeof r=="string"||z(r))return r;var e=r+"";return e=="0"&&1/r==-tn?"-0":e}function Zr(r,e){e=k(e,r);for(var n=0,a=e.length;r!=null&&n<a;)r=r[Tr(e[n++])];return n&&n==a?r:void 0}function Z(r,e,n){var a=r==null?void 0:Zr(r,e);return a===void 0?n:a}function Er(r,e){for(var n=-1,a=e.length,o=r.length;++n<a;)r[o+n]=e[n];return r}var _r=I?I.isConcatSpreadable:void 0;function an(r){return L(r)||ve(r)||!!(_r&&r&&r[_r])}function Jr(r,e,n,a,o){var i=-1,u=r.length;for(n||(n=an),o||(o=[]);++i<u;){var c=r[i];e>0&&n(c)?e>1?Jr(c,e-1,n,a,o):Er(o,c):a||(o[o.length]=c)}return o}function on(r){var e=r==null?0:r.length;return e?Jr(r,1):[]}function sn(r){return pe(he(r,void 0,on),r+"")}function un(r,e,n){var a=-1,o=r.length;e<0&&(e=-e>o?0:o+e),n=n>o?o:n,n<0&&(n+=o),o=e>n?0:n-e>>>0,e>>>=0;for(var i=Array(o);++a<o;)i[a]=r[a+e];return i}function fn(r,e){return r&&W(e,hr(e),r)}function ln(r,e){return r&&W(e,pr(e),r)}function cn(r,e){for(var n=-1,a=r==null?0:r.length,o=0,i=[];++n<a;){var u=r[n];e(u,n,r)&&(i[o++]=u)}return i}function Qr(){return[]}var dn=Object.prototype,gn=dn.propertyIsEnumerable,Cr=Object.getOwnPropertySymbols,br=Cr?function(r){return r==null?[]:(r=Object(r),cn(Cr(r),function(e){return gn.call(r,e)}))}:Qr;function vn(r,e){return W(r,br(r),e)}var pn=Object.getOwnPropertySymbols,zr=pn?function(r){for(var e=[];r;)Er(e,br(r)),r=ye(r);return e}:Qr;function hn(r,e){return W(r,zr(r),e)}function kr(r,e,n){var a=e(r);return L(r)?a:Er(a,n(r))}function fr(r){return kr(r,hr,br)}function re(r){return kr(r,pr,zr)}var lr=Q(K,"DataView"),cr=Q(K,"Promise"),dr=Q(K,"Set"),Pr="[object Map]",yn="[object Object]",Ir="[object Promise]",Lr="[object Set]",Fr="[object WeakMap]",xr="[object DataView]",Tn=N(lr),En=N(tr),bn=N(cr),An=N(dr),Sn=N(ur),D=gr;(lr&&D(new lr(new ArrayBuffer(1)))!=xr||tr&&D(new tr)!=Pr||cr&&D(cr.resolve())!=Ir||dr&&D(new dr)!=Lr||ur&&D(new ur)!=Fr)&&(D=function(r){var e=gr(r),n=e==yn?r.constructor:void 0,a=n?N(n):"";if(a)switch(a){case Tn:return xr;case En:return Pr;case bn:return Ir;case An:return Lr;case Sn:return Fr}return e});const U=D;var $n=Object.prototype,On=$n.hasOwnProperty;function wn(r){var e=r.length,n=new r.constructor(e);return e&&typeof r[0]=="string"&&On.call(r,"index")&&(n.index=r.index,n.input=r.input),n}function mn(r,e){var n=e?Hr(r.buffer):r.buffer;return new r.constructor(n,r.byteOffset,r.byteLength)}var _n=/\w*$/;function Cn(r){var e=new r.constructor(r.source,_n.exec(r));return e.lastIndex=r.lastIndex,e}var Dr=I?I.prototype:void 0,Mr=Dr?Dr.valueOf:void 0;function Pn(r){return Mr?Object(Mr.call(r)):{}}var In="[object Boolean]",Ln="[object Date]",Fn="[object Map]",xn="[object Number]",Dn="[object RegExp]",Mn="[object Set]",Rn="[object String]",Nn="[object Symbol]",jn="[object ArrayBuffer]",Bn="[object DataView]",Gn="[object Float32Array]",Un="[object Float64Array]",Kn="[object Int8Array]",Wn="[object Int16Array]",Hn="[object Int32Array]",Vn="[object Uint8Array]",qn="[object Uint8ClampedArray]",Yn="[object Uint16Array]",Xn="[object Uint32Array]";function Zn(r,e,n){var a=r.constructor;switch(e){case jn:return Hr(r);case In:case Ln:return new a(+r);case Bn:return mn(r,n);case Gn:case Un:case Kn:case Wn:case Hn:case Vn:case qn:case Yn:case Xn:return Te(r,n);case Fn:return new a;case xn:case Rn:return new a(r);case Dn:return Cn(r);case Mn:return new a;case Nn:return Pn(r)}}var Jn="[object Map]";function Qn(r){return G(r)&&U(r)==Jn}var Rr=X&&X.isMap,zn=Rr?Vr(Rr):Qn,kn="[object Set]";function rt(r){return G(r)&&U(r)==kn}var Nr=X&&X.isSet,et=Nr?Vr(Nr):rt,nt=1,tt=2,at=4,ee="[object Arguments]",it="[object Array]",ot="[object Boolean]",st="[object Date]",ut="[object Error]",ne="[object Function]",ft="[object GeneratorFunction]",lt="[object Map]",ct="[object Number]",te="[object Object]",dt="[object RegExp]",gt="[object Set]",vt="[object String]",pt="[object Symbol]",ht="[object WeakMap]",yt="[object ArrayBuffer]",Tt="[object DataView]",Et="[object Float32Array]",bt="[object Float64Array]",At="[object Int8Array]",St="[object Int16Array]",$t="[object Int32Array]",Ot="[object Uint8Array]",wt="[object Uint8ClampedArray]",mt="[object Uint16Array]",_t="[object Uint32Array]",y={};y[ee]=y[it]=y[yt]=y[Tt]=y[ot]=y[st]=y[Et]=y[bt]=y[At]=y[St]=y[$t]=y[lt]=y[ct]=y[te]=y[dt]=y[gt]=y[vt]=y[pt]=y[Ot]=y[wt]=y[mt]=y[_t]=!0;y[ut]=y[ne]=y[ht]=!1;function B(r,e,n,a,o,i){var u,c=e&nt,l=e&tt,v=e&at;if(n&&(u=o?n(r,a,o,i):n(r)),u!==void 0)return u;if(!R(r))return r;var f=L(r);if(f){if(u=wn(r),!c)return Ee(r,u)}else{var t=U(r),g=t==ne||t==ft;if(ar(r))return be(r,c);if(t==te||t==ee||g&&!o){if(u=l||g?{}:Ae(r),!c)return l?hn(r,ln(u,r)):vn(r,fn(u,r))}else{if(!y[t])return o?r:{};u=Zn(r,t,c)}}i||(i=new q);var p=i.get(r);if(p)return p;i.set(r,u),et(r)?r.forEach(function(S){u.add(B(S,e,n,S,r,i))}):zn(r)&&r.forEach(function(S,T){u.set(T,B(S,e,n,T,r,i))});var b=v?l?re:fr:l?pr:hr,A=f?void 0:b(r);return Ke(A||r,function(S,T){A&&(T=S,S=r[T]),qr(u,T,B(S,e,n,T,r,i))}),u}var Ct=1,Pt=4;function H(r){return B(r,Ct|Pt)}var It="__lodash_hash_undefined__";function Lt(r){return this.__data__.set(r,It),this}function Ft(r){return this.__data__.has(r)}function J(r){var e=-1,n=r==null?0:r.length;for(this.__data__=new vr;++e<n;)this.add(r[e])}J.prototype.add=J.prototype.push=Lt;J.prototype.has=Ft;function xt(r,e){for(var n=-1,a=r==null?0:r.length;++n<a;)if(e(r[n],n,r))return!0;return!1}function Dt(r,e){return r.has(e)}var Mt=1,Rt=2;function ae(r,e,n,a,o,i){var u=n&Mt,c=r.length,l=e.length;if(c!=l&&!(u&&l>c))return!1;var v=i.get(r),f=i.get(e);if(v&&f)return v==e&&f==r;var t=-1,g=!0,p=n&Rt?new J:void 0;for(i.set(r,e),i.set(e,r);++t<c;){var b=r[t],A=e[t];if(a)var S=u?a(A,b,t,e,r,i):a(b,A,t,r,e,i);if(S!==void 0){if(S)continue;g=!1;break}if(p){if(!xt(e,function(T,$){if(!Dt(p,$)&&(b===T||o(b,T,n,a,i)))return p.push($)})){g=!1;break}}else if(!(b===A||o(b,A,n,a,i))){g=!1;break}}return i.delete(r),i.delete(e),g}function Nt(r){var e=-1,n=Array(r.size);return r.forEach(function(a,o){n[++e]=[o,a]}),n}function jt(r){var e=-1,n=Array(r.size);return r.forEach(function(a){n[++e]=a}),n}var Bt=1,Gt=2,Ut="[object Boolean]",Kt="[object Date]",Wt="[object Error]",Ht="[object Map]",Vt="[object Number]",qt="[object RegExp]",Yt="[object Set]",Xt="[object String]",Zt="[object Symbol]",Jt="[object ArrayBuffer]",Qt="[object DataView]",jr=I?I.prototype:void 0,er=jr?jr.valueOf:void 0;function zt(r,e,n,a,o,i,u){switch(n){case Qt:if(r.byteLength!=e.byteLength||r.byteOffset!=e.byteOffset)return!1;r=r.buffer,e=e.buffer;case Jt:return!(r.byteLength!=e.byteLength||!i(new Ar(r),new Ar(e)));case Ut:case Kt:case Vt:return Se(+r,+e);case Wt:return r.name==e.name&&r.message==e.message;case qt:case Xt:return r==e+"";case Ht:var c=Nt;case Yt:var l=a&Bt;if(c||(c=jt),r.size!=e.size&&!l)return!1;var v=u.get(r);if(v)return v==e;a|=Gt,u.set(r,e);var f=ae(c(r),c(e),a,o,i,u);return u.delete(r),f;case Zt:if(er)return er.call(r)==er.call(e)}return!1}var kt=1,ra=Object.prototype,ea=ra.hasOwnProperty;function na(r,e,n,a,o,i){var u=n&kt,c=fr(r),l=c.length,v=fr(e),f=v.length;if(l!=f&&!u)return!1;for(var t=l;t--;){var g=c[t];if(!(u?g in e:ea.call(e,g)))return!1}var p=i.get(r),b=i.get(e);if(p&&b)return p==e&&b==r;var A=!0;i.set(r,e),i.set(e,r);for(var S=u;++t<l;){g=c[t];var T=r[g],$=e[g];if(a)var F=u?a($,T,g,e,r,i):a(T,$,g,r,e,i);if(!(F===void 0?T===$||o(T,$,n,a,i):F)){A=!1;break}S||(S=g=="constructor")}if(A&&!S){var _=r.constructor,m=e.constructor;_!=m&&"constructor"in r&&"constructor"in e&&!(typeof _=="function"&&_ instanceof _&&typeof m=="function"&&m instanceof m)&&(A=!1)}return i.delete(r),i.delete(e),A}var ta=1,Br="[object Arguments]",Gr="[object Array]",V="[object Object]",aa=Object.prototype,Ur=aa.hasOwnProperty;function ia(r,e,n,a,o,i){var u=L(r),c=L(e),l=u?Gr:U(r),v=c?Gr:U(e);l=l==Br?V:l,v=v==Br?V:v;var f=l==V,t=v==V,g=l==v;if(g&&ar(r)){if(!ar(e))return!1;u=!0,f=!1}if(g&&!f)return i||(i=new q),u||$e(r)?ae(r,e,n,a,o,i):zt(r,e,l,n,a,o,i);if(!(n&ta)){var p=f&&Ur.call(r,"__wrapped__"),b=t&&Ur.call(e,"__wrapped__");if(p||b){var A=p?r.value():r,S=b?e.value():e;return i||(i=new q),o(A,S,n,a,i)}}return g?(i||(i=new q),na(r,e,n,a,o,i)):!1}function ie(r,e,n,a,o){return r===e?!0:r==null||e==null||!G(r)&&!G(e)?r!==r&&e!==e:ia(r,e,n,a,ie,o)}var nr=function(){return K.Date.now()},oa="Expected a function",sa=Math.max,ua=Math.min;function fa(r,e,n){var a,o,i,u,c,l,v=0,f=!1,t=!1,g=!0;if(typeof r!="function")throw new TypeError(oa);e=mr(e)||0,R(n)&&(f=!!n.leading,t="maxWait"in n,i=t?sa(mr(n.maxWait)||0,e):i,g="trailing"in n?!!n.trailing:g);function p(E){var O=a,x=o;return a=o=void 0,v=E,u=r.apply(x,O),u}function b(E){return v=E,c=setTimeout(T,e),f?p(E):u}function A(E){var O=E-l,x=E-v,P=e-O;return t?ua(P,i-x):P}function S(E){var O=E-l,x=E-v;return l===void 0||O>=e||O<0||t&&x>=i}function T(){var E=nr();if(S(E))return $(E);c=setTimeout(T,A(E))}function $(E){return c=void 0,g&&a?p(E):(a=o=void 0,u)}function F(){c!==void 0&&clearTimeout(c),v=0,a=l=o=c=void 0}function _(){return c===void 0?u:$(nr())}function m(){var E=nr(),O=S(E);if(a=arguments,o=this,l=E,O){if(c===void 0)return b(l);if(t)return clearTimeout(c),c=setTimeout(T,e),p(l)}return c===void 0&&(c=setTimeout(T,e)),u}return m.cancel=F,m.flush=_,m}function la(r){var e=r==null?0:r.length;return e?r[e-1]:void 0}function ca(r,e){return e.length<2?r:Zr(r,un(e,0,-1))}function Kr(r,e){return ie(r,e)}function da(r,e){return e=k(e,r),r=ca(r,e),r==null||delete r[Tr(la(e))]}function ga(r){return Oe(r)?void 0:r}var va=1,pa=2,ha=4,Wr=sn(function(r,e){var n={};if(r==null)return n;var a=!1;e=Yr(e,function(i){return i=k(i,r),a||(a=i.length>1),i}),W(r,re(r),n),a&&(n=B(n,va|pa|ha,ga));for(var o=e.length;o--;)da(n,e[o]);return n});function ya(r,e,n,a){if(!R(r))return r;e=k(e,r);for(var o=-1,i=e.length,u=i-1,c=r;c!=null&&++o<i;){var l=Tr(e[o]),v=n;if(l==="__proto__"||l==="constructor"||l==="prototype")return r;if(o!=u){var f=c[l];v=a?a(f,l,c):void 0,v===void 0&&(v=R(f)?f:we(e[o+1])?[]:{})}qr(c,l,v),c=c[l]}return r}function oe(r,e,n){return r==null?r:ya(r,e,n)}const Ta=(r,e={})=>{const n={errorsChanged:[],touchedChanged:[],validatingChanged:[],validatedChanged:[]};let a=!1,o=!1;const i=s=>s!==o?(o=s,n.validatingChanged):[];let u=[];const c=s=>{const d=[...new Set(s)];return u.length!==d.length||!d.every(h=>u.includes(h))?(u=d,n.validatedChanged):[]},l=()=>u.filter(s=>typeof t[s]>"u");let v=[];const f=s=>{const d=[...new Set(s)];return v.length!==d.length||!d.every(h=>v.includes(h))?(v=d,n.touchedChanged):[]};let t={};const g=s=>{const d=Ea(s);return Kr(t,d)?[]:(t=d,n.errorsChanged)},p=s=>{const d={...t};return delete d[Y(s)],g(d)},b=()=>Object.keys(t).length>0;let A=1500;const S=s=>{A=s,E.cancel(),E=m()};let T=e,$=null,F=[],_=null;const m=()=>fa(()=>{r({get:(s,d={},h={})=>M.get(s,P(d),O(h,d)),post:(s,d={},h={})=>M.post(s,P(d),O(h,d)),patch:(s,d={},h={})=>M.patch(s,P(d),O(h,d)),put:(s,d={},h={})=>M.put(s,P(d),O(h,d)),delete:(s,d={},h={})=>M.delete(s,P(d),O(h,d))}).catch(s=>me(s)?null:Promise.reject(s))},A,{leading:!0,trailing:!0});let E=m();const O=(s,d={})=>{const h=Array.from(s.validate??v);return{...s,validate:h,timeout:s.timeout??5e3,onValidationError:(w,j)=>([...c([...u,...h]),...g(_e(Wr({...t},h),w.data.errors))].forEach(rr=>rr()),s.onValidationError?s.onValidationError(w,j):Promise.reject(j)),onSuccess:()=>{c([...u,...h]).forEach(w=>w())},onPrecognitionSuccess:w=>([...c([...u,...h]),...g(Wr({...t},h))].forEach(j=>j()),s.onPrecognitionSuccess?s.onPrecognitionSuccess(w):w),onBefore:()=>(s.onBeforeValidation??((rr,fe)=>!Kr(rr,fe)))({data:d,touched:v},{data:T,touched:F})===!1||(s.onBefore||(()=>!0))()===!1?!1:(_=v,$=d,!0),onStart:()=>{i(!0).forEach(w=>w()),(s.onStart??(()=>null))()},onFinish:()=>{i(!1).forEach(w=>w()),F=_,T=$,_=$=null,(s.onFinish??(()=>null))()}}},x=(s,d)=>{if(typeof s>"u"){E();return}if(ir(d)&&!a){console.warn('Precognition file validation is not active. Call the "validateFiles" function on your form to enable it.');return}s=Y(s),Z(T,s)!==d&&f([s,...v]).forEach(h=>h()),v.length!==0&&E()},P=s=>a===!1?ue(s):s,C={touched:()=>v,validate(s,d){return x(s,d),C},touch(s){const d=Array.isArray(s)?s:[Y(s)];return f([...v,...d]).forEach(h=>h()),C},validating:()=>o,valid:l,errors:()=>t,hasErrors:b,setErrors(s){return g(s).forEach(d=>d()),C},forgetError(s){return p(s).forEach(d=>d()),C},reset(...s){if(s.length===0)f([]).forEach(d=>d());else{const d=[...v];s.forEach(h=>{d.includes(h)&&d.splice(d.indexOf(h),1),oe(T,h,Z(e,h))}),f(d).forEach(h=>h())}return C},setTimeout(s){return S(s),C},on(s,d){return n[s].push(d),C},validateFiles(){return a=!0,C}};return C},se=r=>Object.keys(r).reduce((e,n)=>({...e,[n]:Array.isArray(r[n])?r[n][0]:r[n]}),{}),Ea=r=>Object.keys(r).reduce((e,n)=>({...e,[n]:typeof r[n]=="string"?[r[n]]:r[n]}),{}),Y=r=>typeof r!="string"?r.target.name:r,ue=r=>{const e={...r};return Object.keys(e).forEach(n=>{const a=e[n];if(a!==null){if(ir(a)){delete e[n];return}if(Array.isArray(a)){e[n]=a.filter(o=>!ir(o));return}if(typeof a=="object"){e[n]=ue(e[n]);return}}}),e},ba=(r,e,n,a={})=>{const o=H(n),i=Object.keys(o),u=Sr([]),c=Sr([]),l=Ta(t=>t[or(r)](sr(e),f.data(),a),o).on("validatingChanged",()=>{f.validating=l.validating()}).on("validatedChanged",()=>{u.value=l.valid()}).on("touchedChanged",()=>{c.value=l.touched()}).on("errorsChanged",()=>{f.hasErrors=l.hasErrors(),f.errors=se(l.errors()),u.value=l.valid()}),v=t=>({...t,precognitive:!1,onStart:()=>{f.processing=!0,(t.onStart??(()=>null))()},onFinish:()=>{f.processing=!1,(t.onFinish??(()=>null))()},onValidationError:(g,p)=>(l.setErrors(g.data.errors),t.onValidationError?t.onValidationError(g):Promise.reject(p))});let f={...H(o),data(){const t=H(Ce(f));return i.reduce((g,p)=>({...g,[p]:t[p]}),{})},setData(t){return Object.keys(t).forEach(g=>{f[g]=t[g]}),f},touched(t){return c.value.includes(t)},touch(t){return l.touch(t),f},validate(t){return typeof t>"u"?l.validate():(t=Y(t),l.validate(t,Z(f.data(),t))),f},validating:!1,valid(t){return u.value.includes(t)},invalid(t){return typeof f.errors[t]<"u"},errors:{},hasErrors:!1,setErrors(t){return l.setErrors(t),f},forgetError(t){return l.forgetError(t),f},reset(...t){const g=H(o);return t.length===0?i.forEach(p=>f[p]=g[p]):t.forEach(p=>oe(f,p,Z(g,p))),l.reset(...t),f},setValidationTimeout(t){return l.setTimeout(t),f},processing:!1,async submit(t={}){return M[or(r)](sr(e),f.data(),v(t))},validateFiles(){return l.validateFiles(),f},validator(){return l}};return f=Pe(f),f},$a=(r,e,n,a={})=>{const o=Ie(n),i=ba(r,e,n,a);i.validator().on("errorsChanged",()=>{l(),v(se(i.validator().errors()))});const u=o.submit.bind(o),c=o.reset.bind(o),l=o.clearErrors.bind(o),v=o.setError.bind(o),f=Object.assign(o,{validating:i.validating,touched:i.touched,touch(t){return i.touch(t),f},valid:i.valid,invalid:i.invalid,clearErrors(...t){return l(...t),t.length===0?i.setErrors({}):t.forEach(i.forgetError),f},reset(...t){c(...t),i.reset(...t)},setErrors(t){return i.setErrors(t),f},forgetError(t){return i.forgetError(t),f},setError(t,g){return f.setErrors({...o.errors,...typeof g>"u"?t:{[t]:g}}),f},validate(t){return i.setData(o.data()),i.validate(t),f},setValidationTimeout(t){return i.setValidationTimeout(t),f},validateFiles(){return i.validateFiles(),f},submit(t={},g,p){const b=typeof t!="string";p=b?t:p,g=b?sr(e):g,t=b?or(r):t,u(t,g,{...p,onError:A=>{if(i.validator().setErrors(A),p.onError)return p.onError(A)}})},validator:i.validator});return Le(()=>f.validating=i.validating),f};export{$a as u};
