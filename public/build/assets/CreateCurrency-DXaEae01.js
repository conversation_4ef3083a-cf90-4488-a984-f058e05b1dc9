import{_ as c,S as d,c as _,w as i,r as s,b,e as g,d as l,g as B,t as S,k as h}from"./app-Cm2beRkj.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as w}from"./SBXInput-C8dZEgZe.js";import{S as v}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const T={components:{SBXDefaultPageLayout:X,SBXInput:w,SBXButton:d,SBXToggle:v},props:{errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:null,is_active:!1,is_default:!1})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.currencies.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("currencies.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}};function x(r,e,m,$,o,a){const p=s("SBXInput"),n=s("SBXToggle"),u=s("SBXButton"),f=s("SBXDefaultPageLayout");return b(),_(f,null,{default:i(()=>[g("form",{onSubmit:e[3]||(e[3]=h((...t)=>a.create&&a.create(...t),["prevent"]))},[l(p,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:r.$t("sbxwebshop.currencies.name_label"),error:m.errors.name},null,8,["model","label","error"]),l(n,{model:o.form.is_active,"onUpdate:model":e[1]||(e[1]=t=>o.form.is_active=t),label:r.$t("sbxwebshop.currencies.is_active_label")},null,8,["model","label"]),l(n,{model:o.form.is_default,"onUpdate:model":e[2]||(e[2]=t=>o.form.is_default=t),label:r.$t("sbxwebshop.currencies.is_default_label")},null,8,["model","label"]),l(u,{class:"mt-4"},{default:i(()=>[B(S(r.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const P=c(T,[["render",x]]);export{P as default};
