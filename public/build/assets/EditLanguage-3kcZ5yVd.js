import{_ as c,S as p,c as _,w as d,r,b as f,e,t as l,l as u,m as i,d as b,g as h,k as x}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const y={components:{SBXDefaultPageLayout:B,SBXButton:p},props:{language:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.language.data.name,language_code:this.language.data.language_code})}},computed:{pageTitle(){return`${this.$t("sbxcms.global.edit")} - ${this.language.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("languages.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.language.data.id]))}}},S={class:"mt-4"},$={for:"language_code",class:"block text-sm font-medium text-gray-700"},v={class:"mt-1"},k={class:"mt-4"},w={for:"name",class:"block text-sm font-medium text-gray-700"},X={class:"mt-1"};function D(o,a,T,V,s,n){const m=r("SBXButton"),g=r("SBXDefaultPageLayout");return f(),_(g,null,{default:d(()=>[e("form",{onSubmit:a[2]||(a[2]=x((...t)=>n.update&&n.update(...t),["prevent"]))},[e("div",S,[e("label",$,l(o.$t("sbxadmin.languages.language_code_label")),1),e("div",v,[u(e("input",{"onUpdate:modelValue":a[0]||(a[0]=t=>s.form.language_code=t),type:"text",name:"language_code",id:"language_code",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[i,s.form.language_code]])])]),e("div",k,[e("label",w,l(o.$t("sbxadmin.languages.name_label"))+" ("+l(o.$page.props.locale.selected_language_code)+")",1),e("div",X,[u(e("input",{"onUpdate:modelValue":a[1]||(a[1]=t=>s.form.name=t),type:"text",name:"name",id:"name",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[i,s.form.name]])])]),b(m,{class:"mt-4"},{default:d(()=>[h(l(o.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const P=c(y,[["render",D]]);export{P as default};
