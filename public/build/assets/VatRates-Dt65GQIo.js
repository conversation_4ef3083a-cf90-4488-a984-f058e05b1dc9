import{_ as d,h as n,d as s,e as t,F as p,r as o,b as _}from"./app-Cm2beRkj.js";import{S as c}from"./SBXDataTable-C66uxM7K.js";import{S as m}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const b={components:{SBXDataTable:c,SBXFilterBar:m},props:{vatRates:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.vat_rates.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.vat_rates.title")},data(){return{columns:[{key:"name",label:this.$t("sbxwebshop.vat_rates.name_label")}]}},methods:{}},h={class:"flex flex-col"},u={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},g={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},v={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function f(e,x,a,w,l,B){const r=o("SBXFilterBar"),i=o("SBXDataTable");return _(),n(p,null,[s(r,{filters:a.filters,searchRoute:"vat_rates",placeholder:e.$t("sbxwebshop.global.search")},null,8,["filters","placeholder"]),t("div",h,[t("div",u,[t("div",g,[t("div",v,[s(i,{columns:l.columns,items:a.vatRates.data,showAddButton:!0,addButtonText:e.$t("sbxwebshop.vat_rates.new_vat_rate_button"),addRoute:"vat_rates.create",showEditButton:!0,editRoute:"vat_rates.edit",showDeleteButton:!0,deleteRoute:"vat_rates.destroy",deleteDialogTitle:e.$t("sbxwebshop.vat_rates.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.vat_rates.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.vat_rates.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxwebshop.global.cancel"),paginator:a.vatRates.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const F=d(b,[["render",f]]);export{F as default};
