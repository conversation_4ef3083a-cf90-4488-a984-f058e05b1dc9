import{q as $e,s as R,u as me,x as O,y as I,z as X,A as ge,B as p,C as m,D as ve,E as pe,G,H as M,I as q,_ as he,S as ye,c as xe,w as H,r as j,b as Re,e as _e,d as A,g as be,t as Ee,k as we}from"./app-Cm2beRkj.js";import{S as Ce}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as Oe}from"./SBXInput-C8dZEgZe.js";import{S as je}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";function W(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return Object.keys(e).reduce((r,t)=>(n.includes(t)||(r[t]=m(e[t])),r),{})}function S(e){return typeof e=="function"}function Pe(e){return ve(e)||pe(e)}function K(e,n,r){let t=e;const a=n.split(".");for(let o=0;o<a.length;o++){if(!t[a[o]])return r;t=t[a[o]]}return t}function z(e,n,r){return p(()=>e.some(t=>K(n,t,{[r]:!1})[r]))}function Z(e,n,r){return p(()=>e.reduce((t,a)=>{const o=K(n,a,{[r]:!1})[r]||[];return t.concat(o)},[]))}function ee(e,n,r,t){return e.call(t,m(n),m(r),t)}function te(e){return e.$valid!==void 0?!e.$valid:!e}function Se(e,n,r,t,a,o,v){let{$lazy:l,$rewardEarly:f}=a,c=arguments.length>7&&arguments[7]!==void 0?arguments[7]:[],i=arguments.length>8?arguments[8]:void 0,$=arguments.length>9?arguments[9]:void 0,h=arguments.length>10?arguments[10]:void 0;const g=R(!!t.value),s=R(0);r.value=!1;const d=O([n,t].concat(c,h),()=>{if(l&&!t.value||f&&!$.value&&!r.value)return;let u;try{u=ee(e,n,i,v)}catch(x){u=Promise.reject(x)}s.value++,r.value=!!s.value,g.value=!1,Promise.resolve(u).then(x=>{s.value--,r.value=!!s.value,o.value=x,g.value=te(x)}).catch(x=>{s.value--,r.value=!!s.value,o.value=x,g.value=!0})},{immediate:!0,deep:typeof n=="object"});return{$invalid:g,$unwatch:d}}function Ve(e,n,r,t,a,o,v,l){let{$lazy:f,$rewardEarly:c}=t;const i=()=>({}),$=p(()=>{if(f&&!r.value||c&&!l.value)return!1;let h=!0;try{const g=ee(e,n,v,o);a.value=g,h=te(g)}catch(g){a.value=g}return h});return{$unwatch:i,$invalid:$}}function Le(e,n,r,t,a,o,v,l,f,c,i){const $=R(!1),h=e.$params||{},g=R(null);let s,d;e.$async?{$invalid:s,$unwatch:d}=Se(e.$validator,n,$,r,t,g,a,e.$watchTargets,f,c,i):{$invalid:s,$unwatch:d}=Ve(e.$validator,n,r,t,g,a,f,c);const u=e.$message;return{$message:S(u)?p(()=>u(W({$pending:$,$invalid:s,$params:W(h),$model:n,$response:g,$validator:o,$propertyPath:l,$property:v}))):u||"",$params:h,$pending:$,$invalid:s,$response:g,$unwatch:d}}function Te(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const n=m(e),r=Object.keys(n),t={},a={},o={};let v=null;return r.forEach(l=>{const f=n[l];switch(!0){case S(f.$validator):t[l]=f;break;case S(f):t[l]={$validator:f};break;case l==="$validationGroups":v=f;break;case l.startsWith("$"):o[l]=f;break;default:a[l]=f}}),{rules:t,nestedValidators:a,config:o,validationGroups:v}}function Be(){}const Ae="__root";function ne(e,n,r){if(r)return n?n(e()):e();try{var t=Promise.resolve(e());return n?t.then(n):t}catch(a){return Promise.reject(a)}}function ze(e,n){return ne(e,Be,n)}function Ie(e,n){var r=e();return r&&r.then?r.then(n):n(r)}function Ne(e){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];try{return Promise.resolve(e.apply(this,n))}catch(t){return Promise.reject(t)}}}function Xe(e,n,r,t,a,o,v,l,f){const c=Object.keys(e),i=t.get(a,e),$=R(!1),h=R(!1),g=R(0);if(i){if(!i.$partial)return i;i.$unwatch(),$.value=i.$dirty.value}const s={$dirty:$,$path:a,$touch:()=>{$.value||($.value=!0)},$reset:()=>{$.value&&($.value=!1)},$commit:()=>{}};return c.length?(c.forEach(d=>{s[d]=Le(e[d],n,s.$dirty,o,v,d,r,a,f,h,g)}),s.$externalResults=p(()=>l.value?[].concat(l.value).map((d,u)=>({$propertyPath:a,$property:r,$validator:"$externalResults",$uid:`${a}-externalResult-${u}`,$message:d,$params:{},$response:null,$pending:!1})):[]),s.$invalid=p(()=>{const d=c.some(u=>m(s[u].$invalid));return h.value=d,!!s.$externalResults.value.length||d}),s.$pending=p(()=>c.some(d=>m(s[d].$pending))),s.$error=p(()=>s.$dirty.value?s.$pending.value||s.$invalid.value:!1),s.$silentErrors=p(()=>c.filter(d=>m(s[d].$invalid)).map(d=>{const u=s[d];return X({$propertyPath:a,$property:r,$validator:d,$uid:`${a}-${d}`,$message:u.$message,$params:u.$params,$response:u.$response,$pending:u.$pending})}).concat(s.$externalResults.value)),s.$errors=p(()=>s.$dirty.value?s.$silentErrors.value:[]),s.$unwatch=()=>c.forEach(d=>{s[d].$unwatch()}),s.$commit=()=>{h.value=!0,g.value=Date.now()},t.set(a,e,s),s):(i&&t.set(a,e,s),s)}function ke(e,n,r,t,a,o,v){const l=Object.keys(e);return l.length?l.reduce((f,c)=>(f[c]=N({validations:e[c],state:n,key:c,parentKey:r,resultsCache:t,globalConfig:a,instance:o,externalResults:v}),f),{}):{}}function De(e,n,r){const t=p(()=>[n,r].filter(s=>s).reduce((s,d)=>s.concat(Object.values(m(d))),[])),a=p({get(){return e.$dirty.value||(t.value.length?t.value.every(s=>s.$dirty):!1)},set(s){e.$dirty.value=s}}),o=p(()=>{const s=m(e.$silentErrors)||[],d=t.value.filter(u=>(m(u).$silentErrors||[]).length).reduce((u,x)=>u.concat(...x.$silentErrors),[]);return s.concat(d)}),v=p(()=>{const s=m(e.$errors)||[],d=t.value.filter(u=>(m(u).$errors||[]).length).reduce((u,x)=>u.concat(...x.$errors),[]);return s.concat(d)}),l=p(()=>t.value.some(s=>s.$invalid)||m(e.$invalid)||!1),f=p(()=>t.value.some(s=>m(s.$pending))||m(e.$pending)||!1),c=p(()=>t.value.some(s=>s.$dirty)||t.value.some(s=>s.$anyDirty)||a.value),i=p(()=>a.value?f.value||l.value:!1),$=()=>{e.$touch(),t.value.forEach(s=>{s.$touch()})},h=()=>{e.$commit(),t.value.forEach(s=>{s.$commit()})},g=()=>{e.$reset(),t.value.forEach(s=>{s.$reset()})};return t.value.length&&t.value.every(s=>s.$dirty)&&$(),{$dirty:a,$errors:v,$invalid:l,$anyDirty:c,$error:i,$pending:f,$touch:$,$reset:g,$silentErrors:o,$commit:h}}function N(e){const n=Ne(function(){return B(),Ie(function(){if(u.$rewardEarly)return F(),ze(q)},function(){return ne(q,function(){return new Promise(y=>{if(!T.value)return y(!L.value);const E=O(T,()=>{y(!L.value),E()})})})})});let{validations:r,state:t,key:a,parentKey:o,childResults:v,resultsCache:l,globalConfig:f={},instance:c,externalResults:i}=e;const $=o?`${o}.${a}`:a,{rules:h,nestedValidators:g,config:s,validationGroups:d}=Te(r),u=Object.assign({},f,s),x=a?p(()=>{const y=m(t);return y?m(y[a]):void 0}):t,C=Object.assign({},m(i)||{}),k=p(()=>{const y=m(i);return a?y?m(y[a]):void 0:y}),D=Xe(h,x,a,l,$,u,c,k,t),b=ke(g,x,$,l,u,c,k),U={};d&&Object.entries(d).forEach(y=>{let[E,_]=y;U[E]={$invalid:z(_,b,"$invalid"),$error:z(_,b,"$error"),$pending:z(_,b,"$pending"),$errors:Z(_,b,"$errors"),$silentErrors:Z(_,b,"$silentErrors")}});const{$dirty:V,$errors:se,$invalid:L,$anyDirty:oe,$error:ie,$pending:T,$touch:B,$reset:le,$silentErrors:ue,$commit:F}=De(D,b,v),ce=a?p({get:()=>m(x),set:y=>{V.value=!0;const E=m(t),_=m(i);_&&(_[a]=C[a]),I(E[a])?E[a].value=y:E[a]=y}}):null;a&&u.$autoDirty&&O(x,()=>{V.value||B();const y=m(i);y&&(y[a]=C[a])},{flush:"sync"});function de(y){return(v.value||{})[y]}function fe(){I(i)?i.value=C:Object.keys(C).length===0?Object.keys(i).forEach(y=>{delete i[y]}):Object.assign(i,C)}return X(Object.assign({},D,{$model:ce,$dirty:V,$error:ie,$errors:se,$invalid:L,$anyDirty:oe,$pending:T,$touch:B,$reset:le,$path:$||Ae,$silentErrors:ue,$validate:n,$commit:F},v&&{$getResultsForChild:de,$clearExternalResults:fe,$validationGroups:U},b))}class Ue{constructor(){this.storage=new Map}set(n,r,t){this.storage.set(n,{rules:r,result:t})}checkRulesValidity(n,r,t){const a=Object.keys(t),o=Object.keys(r);return o.length!==a.length||!o.every(l=>a.includes(l))?!1:o.every(l=>r[l].$params?Object.keys(r[l].$params).every(f=>m(t[l].$params[f])===m(r[l].$params[f])):!0)}get(n,r){const t=this.storage.get(n);if(!t)return;const{rules:a,result:o}=t,v=this.checkRulesValidity(n,r,a),l=o.$unwatch?o.$unwatch:()=>({});return v?o:{$dirty:o.$dirty,$partial:!0,$unwatch:l}}}const P={COLLECT_ALL:!0,COLLECT_NONE:!1},J=Symbol("vuelidate#injectChildResults"),Q=Symbol("vuelidate#removeChildResults");function Fe(e){let{$scope:n,instance:r}=e;const t={},a=R([]),o=p(()=>a.value.reduce((i,$)=>(i[$]=m(t[$]),i),{}));function v(i,$){let{$registerAs:h,$scope:g,$stopPropagation:s}=$;s||n===P.COLLECT_NONE||g===P.COLLECT_NONE||n!==P.COLLECT_ALL&&n!==g||(t[h]=i,a.value.push(h))}r.__vuelidateInjectInstances=[].concat(r.__vuelidateInjectInstances||[],v);function l(i){a.value=a.value.filter($=>$!==i),delete t[i]}r.__vuelidateRemoveInstances=[].concat(r.__vuelidateRemoveInstances||[],l);const f=G(J,[]);M(J,r.__vuelidateInjectInstances);const c=G(Q,[]);return M(Q,r.__vuelidateRemoveInstances),{childResults:o,sendValidationResultsToParent:f,removeValidationResultsFromParent:c}}function re(e){return new Proxy(e,{get(n,r){return typeof n[r]=="object"?re(n[r]):p(()=>n[r])}})}let Y=0;function Ge(e,n){var r;let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};arguments.length===1&&(t=e,e=void 0,n=void 0);let{$registerAs:a,$scope:o=P.COLLECT_ALL,$stopPropagation:v,$externalResults:l,currentVueInstance:f}=t;const c=f||((r=$e())===null||r===void 0?void 0:r.proxy),i=c?c.$options:{};a||(Y+=1,a=`_vuelidate_${Y}`);const $=R({}),h=new Ue,{childResults:g,sendValidationResultsToParent:s,removeValidationResultsFromParent:d}=c?Fe({$scope:o,instance:c}):{childResults:R({})};if(!e&&i.validations){const u=i.validations;n=R({}),me(()=>{n.value=c,O(()=>S(u)?u.call(n.value,new re(n.value)):u,x=>{$.value=N({validations:x,state:n,childResults:g,resultsCache:h,globalConfig:t,instance:c,externalResults:l||c.vuelidateExternalResults})},{immediate:!0})}),t=i.validationsConfig||t}else{const u=I(e)||Pe(e)?e:X(e||{});O(u,x=>{$.value=N({validations:x,state:n,childResults:g,resultsCache:h,globalConfig:t,instance:c??{},externalResults:l})},{immediate:!0})}return c&&(s.forEach(u=>u($,{$registerAs:a,$scope:o,$stopPropagation:v})),ge(()=>d.forEach(u=>u(a)))),p(()=>Object.assign({},m($.value),g.value))}const ae=e=>{if(e=m(e),Array.isArray(e))return!!e.length;if(e==null)return!1;if(e===!1)return!0;if(e instanceof Date)return!isNaN(e.getTime());if(typeof e=="object"){for(let n in e)return!0;return!1}return!!String(e).length},Me=e=>(e=m(e),Array.isArray(e)?e.length:typeof e=="object"?Object.keys(e).length:String(e).length);function w(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t=>(t=m(t),!ae(t)||n.every(a=>a.test(t)))}w(/^[a-zA-Z]*$/);w(/^[a-zA-Z0-9]*$/);w(/^\d*(\.\d+)?$/);const qe=/^(?:[A-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[A-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9]{2,}(?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i;w(qe);function He(e){return n=>!ae(n)||Me(n)<=m(e)}function We(e){return{$validator:He(e),$message:n=>{let{$params:r}=n;return`The maximum length allowed is ${r.max}`},$params:{max:e,type:"maxLength"}}}const Ze=/^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;w(Ze);w(/(^[0-9]*$)|(^-[0-9]+$)/);w(/^[-]?\d*(\.\d+)?$/);const Je={setup(){return{v$:Ge()}},components:{SBXDefaultPageLayout:Ce,SBXButton:ye,SBXInput:Oe,SBXToggle:je},props:{productUnit:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},validations(){return{form:{unit:{maxLength:We(4)}}}},remember:"form",data(){return{form:this.$inertia.form({unit:this.productUnit.data.unit,is_default:this.productUnit.data.is_default})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.productUnit.data.unit}`}},methods:{update(){console.log("this.v$.$validate():",this.v$.$validate()),this.v$.form.$touch(),this.form.processing||this.v$.$validate()&&this.form.put(this.route("product_units.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productUnit.data.id]))}}};function Qe(e,n,r,t,a,o){const v=j("SBXInput"),l=j("SBXToggle"),f=j("SBXButton"),c=j("SBXDefaultPageLayout");return Re(),xe(c,null,{default:H(()=>[_e("form",{onSubmit:n[2]||(n[2]=we((...i)=>o.update&&o.update(...i),["prevent"]))},[A(v,{model:a.form.unit,"onUpdate:model":n[0]||(n[0]=i=>a.form.unit=i),label:e.$t("sbxwebshop.product_units.unit_label"),placeholder:"Ange enhet",error:r.errors.unit,clientValidationErrors:t.v$.form.unit.$errors},null,8,["model","label","error","clientValidationErrors"]),A(l,{model:a.form.is_default,"onUpdate:model":n[1]||(n[1]=i=>a.form.is_default=i),label:e.$t("sbxwebshop.product_units.is_default_label")},null,8,["model","label"]),A(f,{class:"mt-4"},{default:H(()=>[be(Ee(e.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const st=he(Je,[["render",Qe]]);export{st as default};
