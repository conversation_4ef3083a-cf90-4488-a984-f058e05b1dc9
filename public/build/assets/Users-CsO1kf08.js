import{_ as m,h as c,d as r,e as s,w as u,F as h,r as o,b as p,t as _}from"./app-Cm2beRkj.js";import{S as g}from"./SBXDataTable-C66uxM7K.js";import{S as b}from"./SBXFilterBar-w4dDtxld.js";import{r as f}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as x}from"./XCircleIcon-Bi2eGVmc.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const B={components:{SBXDataTable:g,SBXFilterBar:b,CheckCircleIcon:f,XCircleIcon:x},props:{users:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxadmin.users.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxadmin.users.title")},data(){return{columns:[{key:"name",label:this.$t("sbxadmin.users.name_label")},{key:"emailslot",label:this.$t("sbxadmin.users.email_label")},{key:"role_name",label:this.$t("sbxadmin.users.role_label")}],userFilters:{search:this.filters.search}}},methods:{changeFilters(){this.$inertia.get(this.route("sydfisk_users",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.userFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.userFilters.search=e}}},$={class:"flex flex-col"},k={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},y={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},D={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},S=["href"];function C(e,T,t,F,a,i){const d=o("SBXFilterBar"),n=o("SBXDataTable");return p(),c(h,null,[r(d,{onSearchValueChanged:i.searchValueChanged,filters:a.userFilters,searchRoute:"sydfisk_users",placeholder:e.$t("sbxadmin.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),s("div",$,[s("div",k,[s("div",y,[s("div",D,[r(n,{columns:a.columns,items:t.users.data,showAddButton:!0,addButtonText:e.$t("sbxadmin.users.create_button"),addRoute:"sydfisk_users.create",showEditButton:!0,editRoute:"sydfisk_users.edit",showDeleteButton:!0,deleteRoute:"sydfisk_users.destroy",deleteDialogTitle:e.$t("sbxadmin.users.delete_dialog_title"),deleteDialogMessage:e.$t("sbxadmin.users.delete_dialog_message"),deleteDialogOKText:e.$t("sbxadmin.users.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxadmin.global.cancel"),paginator:t.users.meta},{emailslot:u(l=>[s("a",{class:"text-sm",href:"mailto:"+l.item.email},_(l.item.email),9,S)]),_:1},8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const K=m(B,[["render",C]]);export{K as default};
