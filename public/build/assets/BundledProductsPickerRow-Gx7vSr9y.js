import{r as _}from"./CheckIcon-CSfnhiPS.js";import{_ as k,S as f,h as s,d as t,w as a,g as u,t as l,j as m,r as p,b as c}from"./app-Cm2beRkj.js";const w={components:{SBXButton:f,CheckIcon:_},props:{product:Object,pickedProducts:Array},computed:{selected(){return this.pickedProducts.findIndex(o=>o.id==this.product.id)!=-1}},methods:{selectRow(){this.selected?this.$emit("bundledProductUnpicked",this.product):this.$emit("bundledProductPicked",this.product)}}},x={key:0},B={key:1};function C(o,r,d,P,v,e){const n=p("CheckIcon"),i=p("SBXButton");return c(),s("div",{onClick:r[0]||(r[0]=(...h)=>e.selectRow&&e.selectRow(...h)),class:"mb-2 flex items-center cursor-pointer"},[e.selected?m("",!0):(c(),s("div",x,[t(i,{class:"mr-2",size:"s",variant:"secondary"},{default:a(()=>[t(n,{class:"text-white w-4 h-4"})]),_:1}),u(l(d.product.name),1)])),e.selected?(c(),s("div",B,[t(i,{class:"mr-2",size:"s",variant:"success"},{default:a(()=>[t(n,{class:"text-white w-4 h-4"})]),_:1}),u(l(d.product.name),1)])):m("",!0)])}const S=k(w,[["render",C]]);export{S as default};
