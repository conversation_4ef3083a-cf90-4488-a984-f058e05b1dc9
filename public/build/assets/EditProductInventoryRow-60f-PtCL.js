import{_,S as c,c as y,w as p,r as s,b,e as v,d as l,g as f,t as g,k as h}from"./app-Cm2beRkj.js";import{S}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as w}from"./SBXInput-C8dZEgZe.js";import{S as B}from"./SBXSelect-RMXlX9En.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const I={components:{SBXDefaultPageLayout:S,SBXInput:w,SBXSelect:B,SBXButton:c},props:{productInventoryRow:Object,products:Object,changeTypes:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("productInventoryRow:",this.productInventoryRow)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({product_id:this.productInventoryRow.data.product_id,product_inventory_change_type_id:this.productInventoryRow.data.product_inventory_change_type_id,value:this.productInventoryRow.data.value.toString(),notes:this.productInventoryRow.data.notes})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_inventory.inventory_list.edit_row_title")}`}},methods:{update(){this.form.processing||this.form.put(this.route("product_inventory.inventory_list.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productInventoryRow.data.id]))}}};function X(n,e,r,R,o,a){const i=s("SBXSelect"),d=s("SBXInput"),u=s("SBXButton"),m=s("SBXDefaultPageLayout");return b(),y(m,null,{default:p(()=>[v("form",{onSubmit:e[4]||(e[4]=h((...t)=>a.update&&a.update(...t),["prevent"]))},[l(i,{model:o.form.product_id,"onUpdate:model":e[0]||(e[0]=t=>o.form.product_id=t),items:r.products.data,labelFieldName:"long_name",label:n.$t("sbxwebshop.product_inventory.inventory_list.product_name_label"),error:r.errors.product_id},null,8,["model","items","label","error"]),l(i,{model:o.form.product_inventory_change_type_id,"onUpdate:model":e[1]||(e[1]=t=>o.form.product_inventory_change_type_id=t),items:r.changeTypes.data,labelFieldName:"name",label:n.$t("sbxwebshop.product_inventory.inventory_list.change_type_label"),error:r.errors.product_inventory_change_type_id},null,8,["model","items","label","error"]),l(d,{model:o.form.value,"onUpdate:model":e[2]||(e[2]=t=>o.form.value=t),label:n.$t("sbxwebshop.product_inventory.inventory_list.value_label"),error:r.errors.value},null,8,["model","label","error"]),l(d,{model:o.form.notes,"onUpdate:model":e[3]||(e[3]=t=>o.form.notes=t),label:n.$t("sbxwebshop.product_inventory.inventory_list.notes_label"),error:r.errors.notes},null,8,["model","label","error"]),l(u,{class:"mt-4"},{default:p(()=>[f(g(n.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const D=_(I,[["render",X]]);export{D as default};
