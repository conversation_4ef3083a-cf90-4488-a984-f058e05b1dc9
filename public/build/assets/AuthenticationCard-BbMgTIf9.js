import{_ as o,b as n,h as r,e as s,J as t}from"./app-Cm2beRkj.js";const a={},c={class:"min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-oversattare-green-light"},l={class:"w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg"};function d(e,i){return n(),r("div",c,[s("div",null,[t(e.$slots,"logo")]),s("div",l,[t(e.$slots,"default")])])}const f=o(a,[["render",d]]);export{f as A};
