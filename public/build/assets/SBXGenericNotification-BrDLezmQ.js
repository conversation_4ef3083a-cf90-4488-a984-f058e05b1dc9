import{_ as u,r,b as n,h as a,e as s,d,w as m,c,j as i,t as g,aa as h}from"./app-Cm2beRkj.js";import{r as p}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as v}from"./XCircleIcon-Bi2eGVmc.js";import{r as _}from"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const x={components:{CheckCircleIcon:p,XCircleIcon:v,ArrowRightOnRectangleIcon:_},emits:["notificationCancelled"],props:{show:{type:Boolean,default:!1},message:{type:String,required:!0},variant:{type:String,default:"success"}},watch:{show(e,o){if(e==!0){var t=this;setTimeout(function(){t.$emit("notificationCancelled")},2e3)}}},computed:{variantClass(){var e="inline-flex justify-center border border-transparent shadow-sm text-sm font-medium rounded-md text-white";return this.size=="default"&&(e+=" py-2 px-4"),this.size=="s"&&(e+=" py-1 px-2"),this.variant=="primary"&&(this.enabled?e+=" bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600":e+=" bg-blue-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600 cursor-not-allowed"),this.variant=="danger"&&(e+=" bg-red-700 hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600"),this.variant=="success"&&(e+=" bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600"),e}}},y={class:"w-full flex flex-col items-center space-y-4 sm:items-end"},b={key:0,class:"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"},w={class:"p-4"},C={class:"flex items-start"},k={class:"flex-shrink-0"},I={class:"ml-3 w-0 flex-1 pt-0.5"},B={class:"text-sm font-medium text-gray-900 select-none"};function S(e,o,t,$,N,X){const l=r("CheckCircleIcon"),f=r("XCircleIcon");return n(),a("div",{onClick:o[0]||(o[0]=V=>this.$emit("notificationCancelled")),"aria-live":"assertive",class:"fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start mt-12 cursor-pointer"},[s("div",y,[d(h,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:m(()=>[t.show?(n(),a("div",b,[s("div",w,[s("div",C,[s("div",k,[t.variant=="success"?(n(),c(l,{key:0,class:"h-6 w-6 text-green-400","aria-hidden":"true"})):i("",!0),t.variant=="warning"?(n(),c(f,{key:1,class:"h-6 w-6 text-red-400","aria-hidden":"true"})):i("",!0)]),s("div",I,[s("p",B,g(t.message),1)])])])])):i("",!0)]),_:1})])])}const q=u(x,[["render",S]]);export{q as S};
