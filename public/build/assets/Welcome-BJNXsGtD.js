import{_ as i,p as l,h as c,d as o,e as t,w as d,F as f,af as u,$ as m,r as s,b as g,J as p,g as _}from"./app-Cm2beRkj.js";const x=l({layout:null,components:{Head:u,Link:m},mounted(){this.$inertia.post(route("logout"))}}),h={class:"flex flex-col justify-center items-center h-screen bg-oversattare-green-light"},v={class:"mt-8 py-2 px-4 inline-flex justify-center border border-oversattare-green shadow-sm text-sm font-medium rounded-md text-white bg-oversattare-green hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"};function b(n,e,$,k,w,y){const r=s("Head"),a=s("Link");return g(),c(f,null,[o(r,{title:"Översättare.nu"}),t("div",h,[e[1]||(e[1]=t("div",{class:"mx-8"},[t("img",{src:"/graphics/oversattare_logo.png"})],-1)),o(a,{href:"/login"},{default:d(()=>[t("button",v,[p(n.$slots,"default"),e[0]||(e[0]=_("Logga in"))])]),_:3})])],64)}const B=i(x,[["render",b]]);export{B as default};
