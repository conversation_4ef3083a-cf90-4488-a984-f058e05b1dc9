import{ac as j,s as f,c as w,w as l,b as c,g as n,h as S,e as r,d as t,l as p,ae as v,aC as B,k as h,j as _,C as s,$ as E,n as A}from"./app-Cm2beRkj.js";import{_ as F}from"./ActionMessage-B_UncuA7.js";import{_ as R}from"./FormSection-78brDZIO.js";import{a as g,_ as V}from"./TextInput-Dx25uC_E.js";import{_ as k}from"./InputLabel-dwoQhTuq.js";import{_ as T}from"./PrimaryButton-7FvKQ4S5.js";import{_ as C}from"./SecondaryButton-Cr6mqfK5.js";import"./SectionTitle-zHGmFabz.js";const z={key:0,class:"col-span-6 sm:col-span-4"},D={class:"mt-2"},L=["src","alt"],M={class:"mt-2"},O={class:"col-span-6 sm:col-span-4"},Y={class:"col-span-6 sm:col-span-4"},q={key:0},G={class:"text-sm mt-2"},H={class:"mt-2 font-medium text-sm text-green-600"},te={__name:"UpdateProfileInformationForm",props:{user:Object},setup(m){const y=m,o=j({_method:"PUT",name:y.user.name,email:y.user.email,photo:null}),b=f(null),u=f(null),i=f(null),$=()=>{i.value&&(o.photo=i.value.files[0]),o.post(route("user-profile-information.update"),{errorBag:"updateProfileInformation",preserveScroll:!0,onSuccess:()=>P()})},I=()=>{b.value=!0},x=()=>{i.value.click()},N=()=>{const a=i.value.files[0];if(!a)return;const e=new FileReader;e.onload=d=>{u.value=d.target.result},e.readAsDataURL(a)},U=()=>{Inertia.delete(route("current-user-photo.destroy"),{preserveScroll:!0,onSuccess:()=>{u.value=null,P()}})},P=()=>{var a;(a=i.value)!=null&&a.value&&(i.value.value=null)};return(a,e)=>(c(),w(R,{onSubmitted:$},{title:l(()=>e[2]||(e[2]=[n(" Profile Information ")])),description:l(()=>e[3]||(e[3]=[n(" Update your account's profile information and email address. ")])),form:l(()=>[a.$page.props.jetstream.managesProfilePhotos?(c(),S("div",z,[r("input",{ref_key:"photoInput",ref:i,type:"file",class:"hidden",onChange:N},null,544),t(k,{for:"photo",value:"Photo"}),p(r("div",D,[r("img",{src:m.user.profile_photo_url,alt:m.user.name,class:"rounded-full h-20 w-20 object-cover"},null,8,L)],512),[[v,!u.value]]),p(r("div",M,[r("span",{class:"block rounded-full w-20 h-20 bg-cover bg-no-repeat bg-center",style:B("background-image: url('"+u.value+"');")},null,4)],512),[[v,u.value]]),t(C,{class:"mt-2 mr-2",type:"button",onClick:h(x,["prevent"])},{default:l(()=>e[4]||(e[4]=[n(" Select A New Photo ")])),_:1}),m.user.profile_photo_path?(c(),w(C,{key:0,type:"button",class:"mt-2",onClick:h(U,["prevent"])},{default:l(()=>e[5]||(e[5]=[n(" Remove Photo ")])),_:1})):_("",!0),t(g,{message:s(o).errors.photo,class:"mt-2"},null,8,["message"])])):_("",!0),r("div",O,[t(k,{for:"name",value:"Name"}),t(V,{id:"name",modelValue:s(o).name,"onUpdate:modelValue":e[0]||(e[0]=d=>s(o).name=d),type:"text",class:"mt-1 block w-full",autocomplete:"name"},null,8,["modelValue"]),t(g,{message:s(o).errors.name,class:"mt-2"},null,8,["message"])]),r("div",Y,[t(k,{for:"email",value:"Email"}),t(V,{id:"email",modelValue:s(o).email,"onUpdate:modelValue":e[1]||(e[1]=d=>s(o).email=d),type:"email",class:"mt-1 block w-full"},null,8,["modelValue"]),t(g,{message:s(o).errors.email,class:"mt-2"},null,8,["message"]),a.$page.props.jetstream.hasEmailVerification&&m.user.email_verified_at===null?(c(),S("div",q,[r("p",G,[e[7]||(e[7]=n(" Your email address is unverified. ")),t(s(E),{href:a.route("verification.send"),method:"post",as:"button",class:"underline text-gray-600 hover:text-gray-900",onClick:h(I,["prevent"])},{default:l(()=>e[6]||(e[6]=[n(" Click here to re-send the verification email. ")])),_:1},8,["href"])]),p(r("div",H," A new verification link has been sent to your email address. ",512),[[v,b.value]])])):_("",!0)])]),actions:l(()=>[t(F,{on:s(o).recentlySuccessful,class:"mr-3"},{default:l(()=>e[8]||(e[8]=[n(" Saved. ")])),_:1},8,["on"]),t(T,{class:A({"opacity-25":s(o).processing}),disabled:s(o).processing},{default:l(()=>e[9]||(e[9]=[n(" Save ")])),_:1},8,["class","disabled"])]),_:1}))}};export{te as default};
