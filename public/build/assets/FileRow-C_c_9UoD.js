import{_ as r,h as c,e,d as m,t as n,r as p,b as d}from"./app-Cm2beRkj.js";import"./client-BWFz6ICJ.js";import{r as f}from"./PaperClipIcon-7jOC84nb.js";const _={components:{PaperClipIcon:f},props:{file:Object},methods:{openDocument(){window.open(this.file.url,"_blank").focus()}}},u={class:"flex w-0 flex-1 items-center"},h={class:"ml-4 flex min-w-0 flex-1 gap-2"},x={class:"truncate font-medium"},g={class:"ml-4 flex-shrink-0"},w={href:"#",class:"font-medium text-gray-600 hover:text-gray-800"};function y(o,t,a,k,b,s){const i=p("PaperClipIcon");return d(),c("li",{onClick:t[0]||(t[0]=(...l)=>s.openDocument&&s.openDocument(...l)),class:"flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6"},[e("div",u,[m(i,{class:"h-5 w-5 flex-shrink-0 text-gray-400","aria-hidden":"true"}),e("div",h,[e("span",x,n(a.file.original_filename),1)])]),e("div",g,[e("a",w,n(o.$t("translations.my_assignments.assignment_detail.file_download_label")),1)])])}const B=r(_,[["render",y]]);export{B as default};
