import{_ as F,b as o,h as a,e,s as j,B as k,ad as T,x as N,n as g,j as m,t as w,k as $,Y as P,Z as D,J as c,y as S,C as v,l as B,ae as L,d,w as l,aa as E,c as b,$ as C,af as I,g as f,F as _,i as z}from"./app-Cm2beRkj.js";const O={},V={viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function R(i,n){return o(),a("svg",V,n[0]||(n[0]=[e("path",{d:"M11.395 44.428C4.557 40.198 0 32.632 0 24 0 10.745 10.745 0 24 0a23.891 23.891 0 0113.997 4.502c-.2 17.907-11.097 33.245-26.602 39.926z",fill:"#6875F5"},null,-1),e("path",{d:"M14.134 45.885A23.914 23.914 0 0024 48c13.255 0 24-10.745 24-24 0-3.516-.756-6.856-2.115-9.866-4.659 15.143-16.608 27.092-31.75 31.751z",fill:"#6875F5"},null,-1)]))}const Z=F(O,[["render",R]]),J={class:"max-w-screen-xl mx-auto py-2 px-3 sm:px-6 lg:px-8"},Q={class:"flex items-center justify-between flex-wrap"},U={class:"w-0 flex-1 flex items-center min-w-0"},Y={key:0,class:"h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},q={key:1,class:"h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},G={class:"ml-3 font-medium text-sm text-white truncate"},H={class:"shrink-0 sm:ml-3"},K={__name:"Banner",setup(i){const n=j(!0),r=k(()=>{var s;return((s=T().props.value.jetstream.flash)==null?void 0:s.bannerStyle)||"success"}),p=k(()=>{var s;return((s=T().props.value.jetstream.flash)==null?void 0:s.banner)||""});return N(p,async()=>{n.value=!0}),(s,t)=>(o(),a("div",null,[n.value&&p.value?(o(),a("div",{key:0,class:g({"bg-blue-500":r.value=="success","bg-red-700":r.value=="danger"})},[e("div",J,[e("div",Q,[e("div",U,[e("span",{class:g(["flex p-2 rounded-lg",{"bg-blue-600":r.value=="success","bg-red-600":r.value=="danger"}])},[r.value=="success"?(o(),a("svg",Y,t[1]||(t[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):m("",!0),r.value=="danger"?(o(),a("svg",q,t[2]||(t[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"},null,-1)]))):m("",!0)],2),e("p",G,w(p.value),1)]),e("div",H,[e("button",{type:"button",class:g(["-mr-1 flex p-2 rounded-md focus:outline-none sm:-mr-2 transition",{"hover:bg-blue-600 focus:bg-blue-600":r.value=="success","hover:bg-red-600 focus:bg-red-600":r.value=="danger"}]),"aria-label":"Dismiss",onClick:t[0]||(t[0]=$(u=>n.value=!1,["prevent"]))},t[3]||(t[3]=[e("svg",{class:"h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),2)])])])],2)):m("",!0)]))}},W={class:"relative"},A={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:Array,default:()=>["py-1","bg-white"]}},setup(i){const n=i;let r=j(!1);const p=u=>{r.value&&u.key==="Escape"&&(r.value=!1)};P(()=>document.addEventListener("keydown",p)),D(()=>document.removeEventListener("keydown",p));const s=k(()=>({48:"w-48"})[n.width.toString()]),t=k(()=>n.align==="left"?"origin-top-left left-0":n.align==="right"?"origin-top-right right-0":"origin-top");return(u,h)=>(o(),a("div",W,[e("div",{onClick:h[0]||(h[0]=M=>S(r)?r.value=!v(r):r=!v(r))},[c(u.$slots,"trigger")]),B(e("div",{class:"fixed inset-0 z-40",onClick:h[1]||(h[1]=M=>S(r)?r.value=!1:r=!1)},null,512),[[L,v(r)]]),d(E,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:l(()=>[B(e("div",{class:g(["absolute z-50 mt-2 rounded-md shadow-lg",[s.value,t.value]]),style:{display:"none"},onClick:h[2]||(h[2]=M=>S(r)?r.value=!1:r=!1)},[e("div",{class:g(["rounded-md ring-1 ring-black ring-opacity-5",i.contentClasses])},[c(u.$slots,"content")],2)],2),[[L,v(r)]])]),_:3})]))}},X={key:0,type:"submit",class:"block w-full px-4 py-2 text-sm leading-5 text-gray-700 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition"},ee=["href"],x={__name:"DropdownLink",props:{href:String,as:String},setup(i){return(n,r)=>(o(),a("div",null,[i.as=="button"?(o(),a("button",X,[c(n.$slots,"default")])):i.as=="a"?(o(),a("a",{key:1,href:i.href,class:"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition"},[c(n.$slots,"default")],8,ee)):(o(),b(v(C),{key:2,href:i.href,class:"block px-4 py-2 text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition"},{default:l(()=>[c(n.$slots,"default")]),_:3},8,["href"]))]))}},te={__name:"NavLink",props:{href:String,active:Boolean},setup(i){const n=i,r=k(()=>n.active?"inline-flex items-center px-1 pt-1 border-b-2 border-blue-400 text-sm font-medium leading-5 text-gray-900 focus:outline-none focus:border-blue-700 transition":"inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition");return(p,s)=>(o(),b(v(C),{href:i.href,class:g(r.value)},{default:l(()=>[c(p.$slots,"default")]),_:3},8,["href","class"]))}},y={__name:"ResponsiveNavLink",props:{active:Boolean,href:String,as:String},setup(i){const n=i,r=k(()=>n.active?"block pl-3 pr-4 py-2 border-l-4 border-blue-400 text-base font-medium text-blue-700 bg-blue-50 focus:outline-none focus:text-blue-800 focus:bg-blue-100 focus:border-blue-700 transition":"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition");return(p,s)=>(o(),a("div",null,[i.as=="button"?(o(),a("button",{key:0,class:g([r.value,"w-full text-left"])},[c(p.$slots,"default")],2)):(o(),b(v(C),{key:1,href:i.href,class:g(r.value)},{default:l(()=>[c(p.$slots,"default")]),_:3},8,["href","class"]))]))}},se={class:"min-h-screen bg-gray-100"},re={class:"bg-white border-b border-gray-100"},oe={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ne={class:"flex justify-between h-16"},ae={class:"flex"},le={class:"shrink-0 flex items-center"},ie={class:"hidden space-x-8 sm:-my-px sm:ml-10 sm:flex"},ue={class:"hidden sm:flex sm:items-center sm:ml-6"},de={class:"ml-3 relative"},pe={class:"inline-flex rounded-md"},fe={type:"button",class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:bg-gray-50 hover:text-gray-700 focus:outline-none focus:bg-gray-50 active:bg-gray-50 transition"},me={class:"w-60"},ge=["onSubmit"],ce={class:"flex items-center"},ve={key:0,class:"mr-2 h-5 w-5 text-green-400",fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",stroke:"currentColor",viewBox:"0 0 24 24"},he={class:"ml-3 relative"},be={key:0,class:"flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition"},ye=["src","alt"],we={key:1,class:"inline-flex rounded-md"},xe={type:"button",class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none transition"},ke={class:"-mr-2 flex items-center sm:hidden"},$e={class:"h-6 w-6",stroke:"currentColor",fill:"none",viewBox:"0 0 24 24"},_e={class:"pt-2 pb-3 space-y-1"},Ce={class:"pt-4 pb-1 border-t border-gray-200"},Se={class:"flex items-center px-4"},je={key:0,class:"shrink-0 mr-3"},Me=["src","alt"],Te={class:"font-medium text-base text-gray-800"},Be={class:"font-medium text-sm text-gray-500"},Le={class:"mt-3 space-y-1"},ze=["onSubmit"],Ae={class:"flex items-center"},Fe={key:0,class:"mr-2 h-5 w-5 text-green-400",fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",stroke:"currentColor",viewBox:"0 0 24 24"},Ne={key:0,class:"bg-white shadow"},Pe={class:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8"},Ee={__name:"AppLayout",props:{title:String},setup(i){const n=j(!1),r=s=>{Inertia.put(route("current-team.update"),{team_id:s.id},{preserveState:!1})},p=()=>{Inertia.post(route("logout"))};return(s,t)=>(o(),a("div",null,[d(v(I),{title:i.title},null,8,["title"]),d(K),e("div",se,[e("nav",re,[e("div",oe,[e("div",ne,[e("div",ae,[e("div",le,[d(v(C),{href:s.route("dashboard")},{default:l(()=>[d(Z,{class:"block h-9 w-auto"})]),_:1},8,["href"])]),e("div",ie,[d(te,{href:s.route("dashboard"),active:s.route().current("dashboard")},{default:l(()=>t[1]||(t[1]=[f(" Dashboard ")])),_:1},8,["href","active"])])]),e("div",ue,[e("div",de,[s.$page.props.jetstream.hasTeamFeatures?(o(),b(A,{key:0,align:"right",width:"60"},{trigger:l(()=>[e("span",pe,[e("button",fe,[f(w(s.$page.props.auth.user.current_team.name)+" ",1),t[2]||(t[2]=e("svg",{class:"ml-2 -mr-0.5 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1))])])]),content:l(()=>[e("div",me,[s.$page.props.jetstream.hasTeamFeatures?(o(),a(_,{key:0},[t[6]||(t[6]=e("div",{class:"block px-4 py-2 text-xs text-gray-400"}," Manage Team ",-1)),d(x,{href:s.route("teams.show",s.$page.props.auth.user.current_team)},{default:l(()=>t[3]||(t[3]=[f(" Team Settings ")])),_:1},8,["href"]),s.$page.props.jetstream.canCreateTeams?(o(),b(x,{key:0,href:s.route("teams.create")},{default:l(()=>t[4]||(t[4]=[f(" Create New Team ")])),_:1},8,["href"])):m("",!0),t[7]||(t[7]=e("div",{class:"border-t border-gray-100"},null,-1)),t[8]||(t[8]=e("div",{class:"block px-4 py-2 text-xs text-gray-400"}," Switch Teams ",-1)),(o(!0),a(_,null,z(s.$page.props.auth.user.all_teams,u=>(o(),a("form",{key:u.id,onSubmit:$(h=>r(u),["prevent"])},[d(x,{as:"button"},{default:l(()=>[e("div",ce,[u.id==s.$page.props.auth.user.current_team_id?(o(),a("svg",ve,t[5]||(t[5]=[e("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):m("",!0),e("div",null,w(u.name),1)])]),_:2},1024)],40,ge))),128))],64)):m("",!0)])]),_:1})):m("",!0)]),e("div",he,[d(A,{align:"right",width:"48"},{trigger:l(()=>[s.$page.props.jetstream.managesProfilePhotos?(o(),a("button",be,[e("img",{class:"h-8 w-8 rounded-full object-cover",src:s.$page.props.auth.user.profile_photo_url,alt:s.$page.props.auth.user.name},null,8,ye)])):(o(),a("span",we,[e("button",xe,[f(w(s.$page.props.auth.user.name)+" ",1),t[9]||(t[9]=e("svg",{class:"ml-2 -mr-0.5 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1))])]))]),content:l(()=>[t[13]||(t[13]=e("div",{class:"block px-4 py-2 text-xs text-gray-400"}," Manage Account ",-1)),d(x,{href:s.route("profile.show")},{default:l(()=>t[10]||(t[10]=[f(" Profile ")])),_:1},8,["href"]),s.$page.props.jetstream.hasApiFeatures?(o(),b(x,{key:0,href:s.route("api-tokens.index")},{default:l(()=>t[11]||(t[11]=[f(" API Tokens ")])),_:1},8,["href"])):m("",!0),t[14]||(t[14]=e("div",{class:"border-t border-gray-100"},null,-1)),e("form",{onSubmit:$(p,["prevent"])},[d(x,{as:"button"},{default:l(()=>t[12]||(t[12]=[f(" Log Out ")])),_:1})],32)]),_:1})])]),e("div",ke,[e("button",{class:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition",onClick:t[0]||(t[0]=u=>n.value=!n.value)},[(o(),a("svg",$e,[e("path",{class:g({hidden:n.value,"inline-flex":!n.value}),"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},null,2),e("path",{class:g({hidden:!n.value,"inline-flex":n.value}),"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,2)]))])])])]),e("div",{class:g([{block:n.value,hidden:!n.value},"sm:hidden"])},[e("div",_e,[d(y,{href:s.route("dashboard"),active:s.route().current("dashboard")},{default:l(()=>t[15]||(t[15]=[f(" Dashboard ")])),_:1},8,["href","active"])]),e("div",Ce,[e("div",Se,[s.$page.props.jetstream.managesProfilePhotos?(o(),a("div",je,[e("img",{class:"h-10 w-10 rounded-full object-cover",src:s.$page.props.auth.user.profile_photo_url,alt:s.$page.props.auth.user.name},null,8,Me)])):m("",!0),e("div",null,[e("div",Te,w(s.$page.props.auth.user.name),1),e("div",Be,w(s.$page.props.auth.user.email),1)])]),e("div",Le,[d(y,{href:s.route("profile.show"),active:s.route().current("profile.show")},{default:l(()=>t[16]||(t[16]=[f(" Profile ")])),_:1},8,["href","active"]),s.$page.props.jetstream.hasApiFeatures?(o(),b(y,{key:0,href:s.route("api-tokens.index"),active:s.route().current("api-tokens.index")},{default:l(()=>t[17]||(t[17]=[f(" API Tokens ")])),_:1},8,["href","active"])):m("",!0),e("form",{method:"POST",onSubmit:$(p,["prevent"])},[d(y,{as:"button"},{default:l(()=>t[18]||(t[18]=[f(" Log Out ")])),_:1})],32),s.$page.props.jetstream.hasTeamFeatures?(o(),a(_,{key:1},[t[22]||(t[22]=e("div",{class:"border-t border-gray-200"},null,-1)),t[23]||(t[23]=e("div",{class:"block px-4 py-2 text-xs text-gray-400"}," Manage Team ",-1)),d(y,{href:s.route("teams.show",s.$page.props.auth.user.current_team),active:s.route().current("teams.show")},{default:l(()=>t[19]||(t[19]=[f(" Team Settings ")])),_:1},8,["href","active"]),s.$page.props.jetstream.canCreateTeams?(o(),b(y,{key:0,href:s.route("teams.create"),active:s.route().current("teams.create")},{default:l(()=>t[20]||(t[20]=[f(" Create New Team ")])),_:1},8,["href","active"])):m("",!0),t[24]||(t[24]=e("div",{class:"border-t border-gray-200"},null,-1)),t[25]||(t[25]=e("div",{class:"block px-4 py-2 text-xs text-gray-400"}," Switch Teams ",-1)),(o(!0),a(_,null,z(s.$page.props.auth.user.all_teams,u=>(o(),a("form",{key:u.id,onSubmit:$(h=>r(u),["prevent"])},[d(y,{as:"button"},{default:l(()=>[e("div",Ae,[u.id==s.$page.props.auth.user.current_team_id?(o(),a("svg",Fe,t[21]||(t[21]=[e("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):m("",!0),e("div",null,w(u.name),1)])]),_:2},1024)],40,ze))),128))],64)):m("",!0)])])],2)]),s.$slots.header?(o(),a("header",Ne,[e("div",Pe,[c(s.$slots,"header")])])):m("",!0),e("main",null,[c(s.$slots,"default")])])]))}};export{Ee as _};
