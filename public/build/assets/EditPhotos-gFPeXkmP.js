import{_ as w,S as M,r as a,b as p,h as _,d as o,w as n,e as i,j as v,N as S,a as B,U as C,o as X,f as D,c as b,g as x,t as f,i as $,F as T,a9 as N,k as O}from"./app-Cm2beRkj.js";import{r as R}from"./PlusIcon-Dn95PRNa.js";import{S as j}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import U from"./ProductEditorsDropdown-PputzcmH.js";import{r as E}from"./CheckIcon-CSfnhiPS.js";import{S as V}from"./SBXTable-CaSPMjSb.js";import{S as z}from"./SBXNotification-Bm69sG5a.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const L={components:{SBXButton:M,CheckIcon:E},props:{mediaItem:Object,pickedMediaItems:Array},computed:{selected(){return this.pickedMediaItems.findIndex(t=>t.id==this.mediaItem.id)!=-1}},methods:{selectRow(){this.selected?this.$emit("mediaItemUnpicked",this.mediaItem):this.$emit("mediaItemPicked",this.mediaItem)}}},A={key:0,class:"mb-2 w-full flex items-center cursor-pointer"},F=["src"],K={key:1,class:"mb-2 w-full flex items-center cursor-pointer"},q=["src"];function G(t,e,l,y,c,s){const d=a("CheckIcon"),u=a("SBXButton");return p(),_("div",{onClick:e[0]||(e[0]=(...r)=>s.selectRow&&s.selectRow(...r)),class:"w-full"},[s.selected?v("",!0):(p(),_("div",A,[o(u,{class:"mr-2",size:"s",variant:"secondary"},{default:n(()=>[o(d,{class:"text-white w-4 h-4"})]),_:1}),i("img",{class:"w-24",src:l.mediaItem.urls.media_library_thumb},null,8,F)])),s.selected?(p(),_("div",K,[o(u,{class:"mr-2",size:"s",variant:"success"},{default:n(()=>[o(d,{class:"text-white w-4 h-4"})]),_:1}),i("img",{class:"w-24",src:l.mediaItem.urls.media_library_thumb},null,8,q)])):v("",!0)])}const H=w(L,[["render",G]]),J={components:{Dialog:S,DialogPanel:B,DialogTitle:C,TransitionChild:X,TransitionRoot:D,SBXMediaItemsPickerRow:H},props:{exclude:{type:Array,default:[]},multiple:{type:Boolean,default:!1},isOpen:{type:Boolean,default:!1}},mounted(){this.fetchMediaItems()},watch:{isOpen(t,e){this.fetchMediaItems(),this.pickedMediaItems=[]}},data(){return{mediaItems:[],pickedMediaItems:[]}},methods:{fetchMediaItems(){var t=this;axios.post(this.route("media_items.picker.media_items",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{exclude:this.exclude}).then(function(e){t.mediaItems=e.data.data}).catch(function(e){console.log("Error fetching media items: ",e)})},closePicker(){this.$emit("closeMediaItemsPicker")},mediaItemPicked(t){this.multiple?this.pickedMediaItems.findIndex(e=>e.id==t.id)==-1&&this.pickedMediaItems.push(t):(this.pickedMediaItems=[],this.pickedMediaItems.push(t))},mediaItemUnpicked(t){if(this.multiple){let e=this.pickedMediaItems.findIndex(l=>l.id==t.id);e!=-1&&this.pickedMediaItems.splice(e,1)}else this.pickedMediaItems=[]},pickMediaItems(){this.$emit("pickMediaItems",this.pickedMediaItems)}}},Q={class:"fixed inset-0 z-10 overflow-y-auto"},W={class:"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"},Y={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Z={class:"sm:flex sm:items-start"},ee={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},te={class:"mt-2 w-full h-96 overflow-auto"},se={class:"bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6"};function ie(t,e,l,y,c,s){const d=a("TransitionChild"),u=a("DialogTitle"),r=a("SBXMediaItemsPickerRow"),g=a("DialogPanel"),k=a("Dialog"),I=a("TransitionRoot");return p(),b(I,{as:"template",show:l.isOpen},{default:n(()=>[o(k,{as:"div",class:"relative z-10",onClose:s.closePicker},{default:n(()=>[o(d,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:n(()=>e[2]||(e[2]=[i("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])),_:1}),i("div",Q,[i("div",W,[o(d,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:n(()=>[o(g,{class:"relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"},{default:n(()=>[i("div",Y,[i("div",Z,[i("div",ee,[o(u,{as:"h3",class:"text-lg font-medium leading-6 text-gray-900"},{default:n(()=>[x(f(t.$t("sbxmedialibrary.media_items_picker.title")),1)]),_:1}),i("div",te,[(p(!0),_(T,null,$(c.mediaItems,m=>(p(),b(r,{mediaItem:m,pickedMediaItems:c.pickedMediaItems,onMediaItemPicked:s.mediaItemPicked,onMediaItemUnpicked:s.mediaItemUnpicked},null,8,["mediaItem","pickedMediaItems","onMediaItemPicked","onMediaItemUnpicked"]))),256))])])])]),i("div",se,[i("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm",onClick:e[0]||(e[0]=(...m)=>s.pickMediaItems&&s.pickMediaItems(...m))},f(t.$t("sbxmedialibrary.media_items_picker.select_media_items_button")),1),i("button",{type:"button",class:"mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",onClick:e[1]||(e[1]=(...m)=>s.closePicker&&s.closePicker(...m)),ref:"cancelButtonRef"},f(t.$t("sbxwebshop.global.cancel")),513)])]),_:1})]),_:1})])])]),_:1},8,["onClose"])]),_:1},8,["show"])}const oe=w(J,[["render",ie]]),ae=(t,e)=>t.map(l=>l[e]),ne={components:{XMarkIcon:N,PlusIcon:R,SBXDefaultPageLayout:j,ProductEditorsDropdown:U,SBXTable:V,SBXButton:M,SBXMediaItemsPicker:oe,SBXNotification:z},props:{product:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{columns:[{key:"photo",label:this.$t("sbxwebshop.products.photo_label")},{key:"actions",headeralignment:"right",class:"justify-end"}],form:this.$inertia.form({photos:this.product.data.photos}),isPickerOpen:!1,showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`},excludedMediaItemIDs(){var t=ae(this.form.photos,"id");return t}},methods:{update(){this.form.processing||this.form.put(this.route("products.photos.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:t=>{this.showSaveSuccess=!0}})},removePhoto(t){let e=this.form.photos.findIndex(l=>l.id==t);this.form.photos.splice(e,1)},closeMediaItemsPicker(){this.isPickerOpen=!1},pickMediaItems(t){for(var e=0;e<t.length;e++)this.form.photos.push(t[e]);this.isPickerOpen=!1},saveNotificationCancelled(){this.showSaveSuccess=!1}}},le={class:"flex flex-col mt-4"},ce={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},de={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},re={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},me={class:"flex"},pe=["src"],ue={class:"flex"};function he(t,e,l,y,c,s){const d=a("ProductEditorsDropdown"),u=a("PlusIcon"),r=a("SBXButton"),g=a("XMarkIcon"),k=a("SBXTable"),I=a("SBXNotification"),m=a("SBXMediaItemsPicker"),P=a("SBXDefaultPageLayout");return p(),b(P,null,{default:n(()=>[o(d,{pages:l.pageComponents,currentPage:"photos",productID:l.product.data.id},null,8,["pages","productID"]),i("div",le,[i("div",ce,[i("div",de,[i("div",re,[o(k,{columns:c.columns,items:c.form.photos},{"header-actions":n(()=>[o(r,{onClick:e[0]||(e[0]=h=>c.isPickerOpen=!0)},{default:n(()=>[x(f(t.$t("sbxwebshop.products.add_photos_button")),1),o(u,{class:"h-5 w-5 ml-2 mr-0 text-white"})]),_:1})]),photo:n(h=>[i("div",me,[i("img",{class:"w-36",src:h.item.urls.media_library_thumb},null,8,pe)])]),actions:n(h=>[i("div",ue,[o(r,{onClick:fe=>s.removePhoto(h.item.id),size:"s",variant:"danger",class:"ml-2"},{default:n(()=>[o(g,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])])]),_:1},8,["columns","items"])])])])]),i("form",{onSubmit:e[1]||(e[1]=O((...h)=>s.update&&s.update(...h),["prevent"]))},[o(r,{class:"mt-4"},{default:n(()=>[x(f(t.$t("sbxwebshop.global.save")),1)]),_:1})],32),o(I,{show:c.showSaveSuccess,onNotificationCancelled:s.saveNotificationCancelled},null,8,["show","onNotificationCancelled"]),o(m,{exclude:s.excludedMediaItemIDs,multiple:!0,isOpen:c.isPickerOpen,onCloseMediaItemsPicker:s.closeMediaItemsPicker,onPickMediaItems:s.pickMediaItems},null,8,["exclude","isOpen","onCloseMediaItemsPicker","onPickMediaItems"])]),_:1})}const Me=w(ne,[["render",he]]);export{Me as default};
