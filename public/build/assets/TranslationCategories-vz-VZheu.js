import{_ as m,h as l,d as n,w as f,e as o,F as g,r,b as c,l as _,v as b,i as v,t as C}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDataTable-C66uxM7K.js";import{S as w}from"./SBXFilterBar-w4dDtxld.js";import{S as x}from"./SBXGenericNotification-BrDLezmQ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const S={components:{SBXDataTable:B,SBXFilterBar:w,SBXGenericNotification:x},props:{translationCategories:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_categories.title"),this.$page.props.flash.authorization!=null&&(this.showAuthorizationNotification=!0)},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_categories.title")},data(){return{columns:[{key:"name",label:this.$t("translations.translation_categories.name_label")}],translationCategoryFilters:{search:this.filters.search,active:this.filters.active},activeOptions:[{value:!0,text:this.$t("translations.global.active")},{value:!1,text:this.$t("translations.global.inactive")}],showAuthorizationNotification:!1}},computed:{authorizationMessage(){return this.$page.props.flash.authorization==null?"":this.$page.props.flash.authorization}},methods:{changeFilters(){this.$inertia.get(this.route("translation_categories",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translationCategoryFilters,{preserveState:!1,replace:!0})},searchValueChanged(t){this.translationCategoryFilters.search=t},authorizationNotificationCancelled(){this.showAuthorizationNotification=!1}}},y={class:"flex justify-end"},D=["value"],F={class:"flex flex-col"},N={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},T={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},z={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function $(t,s,d,X,e,i){const u=r("SBXFilterBar"),h=r("SBXDataTable"),p=r("SBXGenericNotification");return c(),l(g,null,[n(u,{onSearchValueChanged:i.searchValueChanged,filters:e.translationCategoryFilters,searchRoute:"translation_categories",placeholder:t.$t("translations.global.search")},{filterArea:f(()=>[o("div",y,[_(o("select",{onChange:s[0]||(s[0]=(...a)=>i.changeFilters&&i.changeFilters(...a)),"onUpdate:modelValue":s[1]||(s[1]=a=>e.translationCategoryFilters.active=a),class:"mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(c(!0),l(g,null,v(e.activeOptions,a=>(c(),l("option",{value:a.value},C(a.text),9,D))),256))],544),[[b,e.translationCategoryFilters.active]])])]),_:1},8,["onSearchValueChanged","filters","placeholder"]),o("div",F,[o("div",N,[o("div",T,[o("div",z,[n(h,{columns:e.columns,items:d.translationCategories.data,showAddButton:!0,addRoute:"translation_categories.create",addButtonText:t.$t("translations.translation_categories.add_button"),showEditButton:!0,editRoute:"translation_categories.edit",showDeleteButton:!0,deleteRoute:"translation_categories.destroy",deleteDialogTitle:t.$t("translations.translation_categories.delete_dialog_title"),deleteDialogMessage:t.$t("translations.translation_categories.delete_dialog_message"),deleteDialogOKText:t.$t("translations.translation_categories.delete_dialog_ok_button"),deleteDialogCancelText:t.$t("translations.global.cancel"),paginator:d.translationCategories.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])]),n(p,{show:e.showAuthorizationNotification,variant:"warning",message:i.authorizationMessage,onNotificationCancelled:i.authorizationNotificationCancelled},null,8,["show","message","onNotificationCancelled"])],64)}const U=m(S,[["render",$]]);export{U as default};
