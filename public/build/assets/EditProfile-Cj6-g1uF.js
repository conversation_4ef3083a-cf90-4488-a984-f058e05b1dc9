import{_ as f,S as g,c as b,w as m,r as a,b as _,e as o,d as t,g as v,t as S,k as B}from"./app-Cm2beRkj.js";import{S as c}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as w}from"./SBXSelect-RMXlX9En.js";import{S as X}from"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const x={components:{SBXDefaultPageLayout:c,SBXButton:g,SBXSelect:w,SBXInput:X},props:{user:Object,roles:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("Profile user: ",this.user)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{profileSaved:!1,form:this.$inertia.form({first_name:this.user.data.first_name,last_name:this.user.data.last_name,email:this.user.data.email,password:null,password_confirmation:null})}},computed:{pageTitle(){return`${this.$t("sbxadmin.profile.edit_title")}`}},methods:{update(){var l=this;this.form.processing||this.form.put(this.route("profile.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.user.data.id]),{preserveScroll:!0,onSuccess:e=>{this.profileSaved=!0,setTimeout(function(){l.profileSaved=!1},3e3)}})}}},h={class:"grid grid-cols-2 gap-4"},P={class:"grid grid-cols-2 gap-4"},T={class:"grid grid-cols-2 gap-4"},U={class:"grid grid-cols-2 gap-4"},k={class:"grid grid-cols-2 gap-4"};function y(l,e,i,D,s,d){const n=a("SBXInput"),p=a("SBXButton"),u=a("SBXDefaultPageLayout");return _(),b(u,null,{default:m(()=>[o("form",{onSubmit:e[5]||(e[5]=B((...r)=>d.update&&d.update(...r),["prevent"]))},[o("div",h,[o("div",null,[t(n,{model:s.form.first_name,"onUpdate:model":e[0]||(e[0]=r=>s.form.first_name=r),label:l.$t("sbxadmin.profile.first_name_label"),error:i.errors.first_name},null,8,["model","label","error"])]),e[6]||(e[6]=o("div",null,null,-1))]),o("div",P,[o("div",null,[t(n,{model:s.form.last_name,"onUpdate:model":e[1]||(e[1]=r=>s.form.last_name=r),label:l.$t("sbxadmin.profile.last_name_label"),error:i.errors.last_name},null,8,["model","label","error"])]),e[7]||(e[7]=o("div",null,null,-1))]),o("div",T,[o("div",null,[t(n,{model:s.form.email,"onUpdate:model":e[2]||(e[2]=r=>s.form.email=r),label:l.$t("sbxadmin.profile.email_label"),error:i.errors.email},null,8,["model","label","error"])]),e[8]||(e[8]=o("div",null,null,-1))]),o("div",U,[o("div",null,[t(n,{model:s.form.password,"onUpdate:model":e[3]||(e[3]=r=>s.form.password=r),label:l.$t("sbxadmin.profile.password_label"),instruction:l.$t("sbxadmin.profile.password_instruction"),error:i.errors.password},null,8,["model","label","instruction","error"])]),e[9]||(e[9]=o("div",null,null,-1))]),o("div",k,[o("div",null,[t(n,{model:s.form.password_confirmation,"onUpdate:model":e[4]||(e[4]=r=>s.form.password_confirmation=r),label:l.$t("sbxadmin.profile.password_confirmation_label"),instruction:l.$t("sbxadmin.profile.password_instruction"),error:i.errors.password_confirmation},null,8,["model","label","instruction","error"])]),e[10]||(e[10]=o("div",null,null,-1))]),t(p,{class:"mt-4"},{default:m(()=>[v(S(l.$t("sbxadmin.profile.update_button")),1)]),_:1})],32)]),_:1})}const V=f(x,[["render",y]]);export{V as default};
