import{_ as B,S as C,c as v,w as c,r as _,b as l,e as g,d as r,h as a,i as F,F as X,j as i,k as y,t as d,g as U}from"./app-Cm2beRkj.js";import{u as D}from"./index-BKm97uF2.js";import{S as M}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as T}from"./SBXSelect-RMXlX9En.js";import{S as L}from"./SBXInput-C8dZEgZe.js";import{S as A}from"./SBXToggle-CrVi0Yuw.js";import{S as N}from"./SBXTextArea-D09nQvWc.js";import O from"./AssignmentFileRow-CmyADGqu.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";import"./sweetalert2.all-i0W-sCgv.js";import"./TrashIcon-fhKAjDA0.js";const R={components:{SBXDefaultPageLayout:M,SBXSelect:T,SBXInput:L,SBXToggle:A,SBXTextArea:N,SBXButton:C,AssignmentFileRow:O},props:{translationCategories:Object,translationLanguages:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:D("post",this.route("translation_assignments.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{translation_category_id:null,from_translation_language_id:null,to_translation_language_id:null,is_authorization_required:!1,number_of_words:null,is_company:!1,assignment_type:"personal",first_name:null,last_name:null,company:null,company_no:null,email:null,phone_no:null,notes:null,is_email_contact_allowed:!1,is_phone_contact_allowed:!1,assignment_files:[]})}},computed:{pageTitle(){return`${this.$t("translations.translation_assignments.create_title")}`},categoryOptions(){var n=[];n.push({value:null,text:this.$t("translations.translation_assignments.category_prompt"),enabled:!1});for(var e=0;e<this.translationCategories.data.length;e++){let s=this.translationCategories.data[e];n.push({value:s.id,text:s.name})}return n},languageOptions(){var n=[];n.push({value:null,text:this.$t("translations.translation_assignments.language_prompt"),enabled:!1});for(var e=0;e<this.translationLanguages.data.length;e++){let s=this.translationLanguages.data[e];n.push({value:s.id,text:s.name})}return n}},methods:{create(){this.form.assignment_type="personal",this.form.is_company&&(this.form.assignment_type="company"),this.form.processing||this.form.submit()},addFile(){this.form.assignment_files.push({id:this.form.assignment_files.length+1,file:null})},removeFile(n){for(var e=-1,s=0;s<this.form.assignment_files.length;s++)this.form.assignment_files[s].id==n&&(e=s);e!=-1&&this.form.assignment_files.splice(e,1)},updateFile(n,e){for(var s=0;s<this.form.assignment_files.length;s++){var p=this.form.assignment_files[s];p.id==n&&(p.file=e)}}}},j={class:"grid grid-cols-2 gap-4"},q={class:"mt-4"},z={key:0,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},I={key:1,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},V={key:2,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},P={key:3,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},E={key:4,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},G={key:5,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},H={key:6,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},J={key:7,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},K={key:8,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Q={key:9,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},W={class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-gray-900"},Y={key:10,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"};function Z(n,e,s,p,t,m){const b=_("SBXSelect"),u=_("SBXToggle"),f=_("SBXInput"),h=_("AssignmentFileRow"),x=_("SBXTextArea"),S=_("SBXButton"),k=_("SBXDefaultPageLayout");return l(),v(k,null,{default:c(()=>[g("form",{onSubmit:e[26]||(e[26]=y((...o)=>m.create&&m.create(...o),["prevent"]))},[g("div",j,[g("div",null,[r(b,{model:t.form.translation_category_id,"onUpdate:model":e[0]||(e[0]=o=>t.form.translation_category_id=o),items:m.categoryOptions,valueFieldName:"value",label:n.$t("translations.translation_assignments.category_label"),onChange:e[1]||(e[1]=o=>t.form.validate("translation_category_id")),error:t.form.errors.translation_category_id},null,8,["model","items","label","error"]),r(b,{model:t.form.from_translation_language_id,"onUpdate:model":e[2]||(e[2]=o=>t.form.from_translation_language_id=o),items:m.languageOptions,valueFieldName:"value",label:n.$t("translations.translation_assignments.from_language_label"),onChange:e[3]||(e[3]=o=>t.form.validate("from_translation_language_id")),error:t.form.errors.from_translation_language_id},null,8,["model","items","label","error"]),r(b,{model:t.form.to_translation_language_id,"onUpdate:model":e[4]||(e[4]=o=>t.form.to_translation_language_id=o),items:m.languageOptions,valueFieldName:"value",label:n.$t("translations.translation_assignments.to_language_label"),onChange:e[5]||(e[5]=o=>t.form.validate("to_translation_language_id")),error:t.form.errors.to_translation_language_id},null,8,["model","items","label","error"]),r(u,{model:t.form.is_authorization_required,"onUpdate:model":e[6]||(e[6]=o=>t.form.is_authorization_required=o),label:n.$t("translations.translation_assignments.is_authorization_required_label"),class:"mt-4"},null,8,["model","label"]),r(f,{model:t.form.number_of_words,"onUpdate:model":e[7]||(e[7]=o=>t.form.number_of_words=o),label:n.$t("translations.translation_assignments.number_of_words_label"),onChange:e[8]||(e[8]=o=>t.form.validate("number_of_words")),error:t.form.errors.number_of_words},null,8,["model","label","error"]),g("div",q,[(l(!0),a(X,null,F(t.form.assignment_files,(o,w)=>(l(),v(h,{file:o,key:o.id,"row-no":w,onRemoveFile:m.removeFile,onUpdateFile:m.updateFile},null,8,["file","row-no","onRemoveFile","onUpdateFile"]))),128)),t.form.errors["assignment_files.0.file"]?(l(),a("p",z,"Dokument 1 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.1.file"]?(l(),a("p",I,"Dokument 2 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.2.file"]?(l(),a("p",V,"Dokument 3 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.3.file"]?(l(),a("p",P,"Dokument 4 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.4.file"]?(l(),a("p",E,"Dokument 5 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.5.file"]?(l(),a("p",G,"Dokument 6 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.6.file"]?(l(),a("p",H,"Dokument 7 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.7.file"]?(l(),a("p",J,"Dokument 8 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.8.file"]?(l(),a("p",K,"Dokument 9 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),t.form.errors["assignment_files.9.file"]?(l(),a("p",Q,"Dokument 10 måste vara i ett giltigt format och får inte vara större än 50Mb.")):i("",!0),g("button",{onClick:e[9]||(e[9]=y((...o)=>m.addFile&&m.addFile(...o),["prevent"])),class:"col-span-8 sm:col-start-3 sm:col-span-4 inline-flex justify-center border border-transparent shadow-sm text-sm font-medium rounded-md text-white py-2 px-4 bg-moss-500 hover:bg-moss-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-moss-500"},d(n.$t("translations.translation_assignments.add_file_button")),1),g("p",W,d(n.$t("translations.translation_assignments.file_instructions")),1),t.form.invalid("assignment_files")?(l(),a("p",Y,d(t.form.errors.assignment_files),1)):i("",!0)]),r(u,{model:t.form.is_company,"onUpdate:model":e[10]||(e[10]=o=>t.form.is_company=o),label:n.$t("translations.translation_assignments.company_label"),class:"mt-4"},null,8,["model","label"]),r(f,{model:t.form.first_name,"onUpdate:model":e[11]||(e[11]=o=>t.form.first_name=o),label:n.$t("translations.translation_assignments.first_name_label"),onChange:e[12]||(e[12]=o=>t.form.validate("first_name")),error:t.form.errors.first_name},null,8,["model","label","error"]),r(f,{model:t.form.last_name,"onUpdate:model":e[13]||(e[13]=o=>t.form.last_name=o),label:n.$t("translations.translation_assignments.last_name_label"),onChange:e[14]||(e[14]=o=>t.form.validate("last_name")),error:t.form.errors.last_name},null,8,["model","label","error"]),r(f,{model:t.form.company,"onUpdate:model":e[15]||(e[15]=o=>t.form.company=o),label:n.$t("translations.translation_assignments.company_label"),onChange:e[16]||(e[16]=o=>t.form.validate("company")),error:t.form.errors.company},null,8,["model","label","error"]),r(f,{model:t.form.company_no,"onUpdate:model":e[17]||(e[17]=o=>t.form.company_no=o),label:n.$t("translations.translation_assignments.company_no_label"),onChange:e[18]||(e[18]=o=>t.form.validate("company_no")),error:t.form.errors.company_no},null,8,["model","label","error"]),r(f,{model:t.form.email,"onUpdate:model":e[19]||(e[19]=o=>t.form.email=o),label:n.$t("translations.translation_assignments.email_label"),onChange:e[20]||(e[20]=o=>t.form.validate("email")),error:t.form.errors.email},null,8,["model","label","error"]),r(f,{model:t.form.phone_no,"onUpdate:model":e[21]||(e[21]=o=>t.form.phone_no=o),label:n.$t("translations.translation_assignments.phone_no_label"),onChange:e[22]||(e[22]=o=>t.form.validate("phone_no")),error:t.form.errors.phone_no},null,8,["model","label","error"]),r(x,{model:t.form.notes,"onUpdate:model":e[23]||(e[23]=o=>t.form.notes=o),label:n.$t("translations.translation_assignments.notes_label"),rows:5,error:t.form.errors.notes},null,8,["model","label","error"]),r(u,{model:t.form.is_email_contact_allowed,"onUpdate:model":e[24]||(e[24]=o=>t.form.is_email_contact_allowed=o),label:n.$t("translations.translation_assignments.contact_option_email_label"),class:"mt-4",error:t.form.errors.is_email_contact_allowed},null,8,["model","label","error"]),r(u,{model:t.form.is_phone_contact_allowed,"onUpdate:model":e[25]||(e[25]=o=>t.form.is_phone_contact_allowed=o),label:n.$t("translations.translation_assignments.contact_option_phone_label"),class:"mt-4",error:t.form.errors.is_phone_contact_allowed},null,8,["model","label","error"]),r(S,{class:"mt-4"},{default:c(()=>[U(d(n.$t("translations.translation_assignments.create_button")),1)]),_:1})])])],32)]),_:1})}const pe=B(R,[["render",Z]]);export{pe as default};
