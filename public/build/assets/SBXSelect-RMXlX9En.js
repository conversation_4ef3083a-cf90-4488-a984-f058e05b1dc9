import{_ as c,b as l,h as a,e as n,t as s,l as m,v as f,F as b,i as h,j as u}from"./app-Cm2beRkj.js";import{r as g}from"./ExclamationCircleIcon-C4_TqLjZ.js";const x={components:{ExclamationCircleIcon:g},props:{label:{type:String,required:!0},valueFieldName:{type:String,default:"id"},labelFieldName:{type:String,default:"text"},model:{default:null},items:{type:Array,default:[]},instruction:{type:String,required:!1,default:null},error:{type:String,required:!1,default:null}},watch:{model(o,r){this.currentValue=o}},data(){return{currentValue:this.model}},methods:{change(){this.$emit("update:model",this.currentValue),this.$emit("change",this.currentValue)}}},y={class:"relative rounded-md border border-gray-300 mt-4 px-3 py-2 shadow-sm"},_={for:"unit",class:"block text-xs font-semibold text-gray-900"},p=["value","disabled"],v={key:0,class:"mt-1 ml-1 text-xs text-gray-500"},S={key:1,class:"mt-1 text-xs text-red-600"};function V(o,r,e,k,d,i){return l(),a("div",null,[n("div",y,[n("label",_,s(e.label),1),m(n("select",{"onUpdate:modelValue":r[0]||(r[0]=t=>d.currentValue=t),onChange:r[1]||(r[1]=(...t)=>i.change&&i.change(...t)),class:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(l(!0),a(b,null,h(e.items,t=>(l(),a("option",{value:t[e.valueFieldName],disabled:t.hasOwnProperty("disabled")},s(t[e.labelFieldName]),9,p))),256))],544),[[f,d.currentValue]])]),e.instruction?(l(),a("p",v,s(e.instruction),1)):u("",!0),e.error?(l(),a("p",S,s(e.error),1)):u("",!0)])}const w=c(x,[["render",V]]);export{w as S};
