import{_ as l,h as c,e,d as m,t as n,r as d,b as p}from"./app-Cm2beRkj.js";import"./client-BWFz6ICJ.js";import{r as f}from"./PaperClipIcon-7jOC84nb.js";const _={components:{PaperClipIcon:f},props:{file:Object},methods:{openDocument(){window.open(this.file.url,"_blank").focus()}}},u={class:"flex flex-1 items-center"},h={class:"ml-4 flex min-w-0 flex-1 gap-2"},x={class:"truncate font-medium"},g={class:"ml-4 flex-shrink-0"},w={href:"#",class:"font-medium text-gray-600 hover:text-gray-800"};function y(o,t,a,k,v,s){const i=d("PaperClipIcon");return p(),c("div",{onClick:t[0]||(t[0]=(...r)=>s.openDocument&&s.openDocument(...r)),class:"flex items-center justify-between py-2 pr-5 text-sm leading-6"},[e("div",u,[m(i,{class:"h-5 w-5 flex-shrink-0 text-gray-400","aria-hidden":"true"}),e("div",h,[e("span",x,n(a.file.original_filename),1)])]),e("div",g,[e("a",w,n(o.$t("translations.my_assignments.assignment_detail.file_download_label")),1)])])}const B=l(_,[["render",y]]);export{B as default};
