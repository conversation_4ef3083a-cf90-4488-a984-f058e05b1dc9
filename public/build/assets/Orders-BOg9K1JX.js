import{_ as p,h as _,d as i,e as s,w as n,F as b,r as d,b as f,g as c,t as m}from"./app-Cm2beRkj.js";import{S as g}from"./SBXDataTable-C66uxM7K.js";import{S as x}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const w={components:{SBXDataTable:g,SBXFilterBar:x},props:{orders:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.orders.title"),console.log("Orders: ",this.orders.data)},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.orders.title")},data(){return{columns:[{key:"order_no",label:this.$t("sbxwebshop.orders.order_no_label")},{key:"ordered_at",label:this.$t("sbxwebshop.orders.order_date_label")},{key:"customerslot",label:this.$t("sbxwebshop.orders.customer_label")},{key:"ordertotalslot",label:this.$t("sbxwebshop.orders.order_total_label"),headeralignment:"right",class:"justify-end"}],orderFilters:{search:this.filters.search,selectedFilter:this.filters.selectedFilter}}},computed:{filterOptions(){return[{text:"Alla produkter",value:"all"},{text:"Aktiva",value:"active"},{text:"Inaktiva",value:"inactive"}]}},methods:{changeFilters(){this.$inertia.get(this.route("orders",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.orderFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.orderFilters.search=e},resolveCustomerInfo(e){var t="";return e.company_name!=null&&(t+=e.company_name+", "),e.first_name!=null&&(t+=e.first_name+" "),e.last_name!=null&&(t+=e.last_name+", "),e.city!=null&&(t+=e.city),t},shownAmount(e){return e.order_total.toLocaleString("SE-sv",{minimumFractionDigits:2,maximumFractionDigits:2})}}},v={class:"flex flex-col"},B={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},y={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},F={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function S(e,t,a,k,l,o){const h=d("SBXFilterBar"),u=d("SBXDataTable");return f(),_(b,null,[i(h,{onSearchValueChanged:o.searchValueChanged,filters:l.orderFilters,searchRoute:"orders",placeholder:e.$t("sbxwebshop.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),s("div",v,[s("div",B,[s("div",y,[s("div",F,[i(u,{columns:l.columns,items:a.orders.data,showViewButton:!0,viewRoute:"orders.show",showAddButton:!1,addButtonText:e.$t("sbxwebshop.orders.create_button"),addRoute:"orders.create",showEditButton:!1,editRoute:"orders.edit",showDeleteButton:!1,deleteRoute:"orders.destroy",paginator:a.orders.meta},{customerslot:n(r=>[c(m(o.resolveCustomerInfo(r.item)),1)]),ordertotalslot:n(r=>[c(m(o.shownAmount(r.item))+" kr ",1)]),_:1},8,["columns","items","addButtonText","paginator"])])])])])],64)}const R=p(w,[["render",S]]);export{R as default};
