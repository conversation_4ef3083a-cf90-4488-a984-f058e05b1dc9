import{s as y,z as q,b as t,h as n,e as u,J as E,d as r,w as o,g as l,t as C,aB as K,n as b,I as H,ac as N,B as Q,ad as Y,x as z,c as _,j as m,C as V,F as D,i as U}from"./app-Cm2beRkj.js";import{b as j,a as G}from"./DialogModal-CZeDHxad.js";import{_ as R,a as I}from"./TextInput-Dx25uC_E.js";import{_ as A}from"./PrimaryButton-7FvKQ4S5.js";import{_ as T}from"./SecondaryButton-Cr6mqfK5.js";import{_ as J}from"./DangerButton-CnZ0_ogC.js";import{_ as O}from"./InputLabel-dwoQhTuq.js";import"./SectionTitle-zHGmFabz.js";const W={class:"mt-4"},h={__name:"ConfirmsPassword",props:{title:{type:String,default:"Confirm Password"},content:{type:String,default:"For your security, please confirm your password to continue."},button:{type:String,default:"Confirm"}},emits:["confirmed"],setup(x,{emit:S}){const c=S,a=y(!1),s=q({password:"",error:"",processing:!1}),p=y(null),w=()=>{axios.get(route("password.confirmation")).then(i=>{i.data.confirmed?c("confirmed"):(a.value=!0,setTimeout(()=>p.value.focus(),250))})},v=()=>{s.processing=!0,axios.post(route("password.confirm"),{password:s.password}).then(()=>{s.processing=!1,d(),H().then(()=>c("confirmed"))}).catch(i=>{s.processing=!1,s.error=i.response.data.errors.password[0],p.value.focus()})},d=()=>{a.value=!1,s.password="",s.error=""};return(i,g)=>(t(),n("span",null,[u("span",{onClick:w},[E(i.$slots,"default")]),r(j,{show:a.value,onClose:d},{title:o(()=>[l(C(x.title),1)]),content:o(()=>[l(C(x.content)+" ",1),u("div",W,[r(R,{ref_key:"passwordInput",ref:p,modelValue:s.password,"onUpdate:modelValue":g[0]||(g[0]=F=>s.password=F),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:K(v,["enter"])},null,8,["modelValue"]),r(I,{message:s.error,class:"mt-2"},null,8,["message"])])]),footer:o(()=>[r(T,{onClick:d},{default:o(()=>g[1]||(g[1]=[l(" Cancel ")])),_:1}),r(A,{class:b(["ml-3",{"opacity-25":s.processing}]),disabled:s.processing,onClick:v},{default:o(()=>[l(C(x.button),1)]),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}},X={key:0,class:"text-lg font-medium text-gray-900"},Z={key:1,class:"text-lg font-medium text-gray-900"},ee={key:2,class:"text-lg font-medium text-gray-900"},te={key:3},oe={key:0},se={class:"mt-4 max-w-xl text-sm text-gray-600"},ae={key:0,class:"font-semibold"},ne={key:1},re=["innerHTML"],le={key:0,class:"mt-4 max-w-xl text-sm text-gray-600"},ie={class:"font-semibold"},ue=["innerHTML"],ce={key:1,class:"mt-4"},de={key:1},me={class:"grid gap-1 max-w-xl mt-4 px-4 py-4 font-mono text-sm bg-gray-100 rounded-lg"},fe={class:"mt-5"},pe={key:0},ve={key:1},Ce={__name:"TwoFactorAuthenticationForm",props:{requiresConfirmation:Boolean},setup(x){const S=x,c=y(!1),a=y(!1),s=y(!1),p=y(null),w=y(null),v=y([]),d=N({code:""}),i=Q(()=>{var f;return!c.value&&((f=Y().props.value.user)==null?void 0:f.two_factor_enabled)});z(i,()=>{i.value||(d.reset(),d.clearErrors())});const g=()=>{c.value=!0,Inertia.post("/user/two-factor-authentication",{},{preserveScroll:!0,onSuccess:()=>Promise.all([F(),L(),$()]),onFinish:()=>{c.value=!1,a.value=S.requiresConfirmation}})},F=()=>axios.get("/user/two-factor-qr-code").then(f=>{p.value=f.data.svg}),L=()=>axios.get("/user/two-factor-secret-key").then(f=>{w.value=f.data.secretKey}),$=()=>axios.get("/user/two-factor-recovery-codes").then(f=>{v.value=f.data}),B=()=>{d.post("/user/confirmed-two-factor-authentication",{errorBag:"confirmTwoFactorAuthentication",preserveScroll:!0,preserveState:!0,onSuccess:()=>{a.value=!1,p.value=null,w.value=null}})},M=()=>{axios.post("/user/two-factor-recovery-codes").then(()=>$())},P=()=>{s.value=!0,Inertia.delete("/user/two-factor-authentication",{preserveScroll:!0,onSuccess:()=>{s.value=!1,a.value=!1}})};return(f,e)=>(t(),_(G,null,{title:o(()=>e[1]||(e[1]=[l(" Two Factor Authentication ")])),description:o(()=>e[2]||(e[2]=[l(" Add additional security to your account using two factor authentication. ")])),content:o(()=>[i.value&&!a.value?(t(),n("h3",X," You have enabled two factor authentication. ")):i.value&&a.value?(t(),n("h3",Z," Finish enabling two factor authentication. ")):(t(),n("h3",ee," You have not enabled two factor authentication. ")),e[11]||(e[11]=u("div",{class:"mt-3 max-w-xl text-sm text-gray-600"},[u("p",null," When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application. ")],-1)),i.value?(t(),n("div",te,[p.value?(t(),n("div",oe,[u("div",se,[a.value?(t(),n("p",ae," To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code. ")):(t(),n("p",ne," Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application or enter the setup key. "))]),u("div",{class:"mt-4",innerHTML:p.value},null,8,re),w.value?(t(),n("div",le,[u("p",ie,[e[3]||(e[3]=l(" Setup Key: ")),u("span",{innerHTML:w.value},null,8,ue)])])):m("",!0),a.value?(t(),n("div",ce,[r(O,{for:"code",value:"Code"}),r(R,{id:"code",modelValue:V(d).code,"onUpdate:modelValue":e[0]||(e[0]=k=>V(d).code=k),type:"text",name:"code",class:"block mt-1 w-1/2",inputmode:"numeric",autofocus:"",autocomplete:"one-time-code",onKeyup:K(B,["enter"])},null,8,["modelValue"]),r(I,{message:V(d).errors.code,class:"mt-2"},null,8,["message"])])):m("",!0)])):m("",!0),v.value.length>0&&!a.value?(t(),n("div",de,[e[4]||(e[4]=u("div",{class:"mt-4 max-w-xl text-sm text-gray-600"},[u("p",{class:"font-semibold"}," Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost. ")],-1)),u("div",me,[(t(!0),n(D,null,U(v.value,k=>(t(),n("div",{key:k},C(k),1))),128))])])):m("",!0)])):m("",!0),u("div",fe,[i.value?(t(),n("div",ve,[r(h,{onConfirmed:B},{default:o(()=>[a.value?(t(),_(A,{key:0,type:"button",class:b(["mr-3",{"opacity-25":c.value}]),disabled:c.value},{default:o(()=>e[6]||(e[6]=[l(" Confirm ")])),_:1},8,["class","disabled"])):m("",!0)]),_:1}),r(h,{onConfirmed:M},{default:o(()=>[v.value.length>0&&!a.value?(t(),_(T,{key:0,class:"mr-3"},{default:o(()=>e[7]||(e[7]=[l(" Regenerate Recovery Codes ")])),_:1})):m("",!0)]),_:1}),r(h,{onConfirmed:$},{default:o(()=>[v.value.length===0&&!a.value?(t(),_(T,{key:0,class:"mr-3"},{default:o(()=>e[8]||(e[8]=[l(" Show Recovery Codes ")])),_:1})):m("",!0)]),_:1}),r(h,{onConfirmed:P},{default:o(()=>[a.value?(t(),_(T,{key:0,class:b({"opacity-25":s.value}),disabled:s.value},{default:o(()=>e[9]||(e[9]=[l(" Cancel ")])),_:1},8,["class","disabled"])):m("",!0)]),_:1}),r(h,{onConfirmed:P},{default:o(()=>[a.value?m("",!0):(t(),_(J,{key:0,class:b({"opacity-25":s.value}),disabled:s.value},{default:o(()=>e[10]||(e[10]=[l(" Disable ")])),_:1},8,["class","disabled"]))]),_:1})])):(t(),n("div",pe,[r(h,{onConfirmed:g},{default:o(()=>[r(A,{type:"button",class:b({"opacity-25":c.value}),disabled:c.value},{default:o(()=>e[5]||(e[5]=[l(" Enable ")])),_:1},8,["class","disabled"])]),_:1})]))])]),_:1}))}};export{Ce as default};
