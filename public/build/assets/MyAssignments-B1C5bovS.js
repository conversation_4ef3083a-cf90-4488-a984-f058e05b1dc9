import{_ as p,h as r,e as s,F as a,i as g,c as l,j as u,r as d,b as e}from"./app-Cm2beRkj.js";import{S as _}from"./SBXFilterBar-w4dDtxld.js";import A from"./AssignmentCard-BjLtbhrG.js";import f from"./AssignmentDetail-Dj2RuUEh.js";import"./debounce-Bpn6Ai4k.js";import"./UsersIcon-ClDae0qo.js";import"./FileRow-C_c_9UoD.js";import"./client-BWFz6ICJ.js";import"./PaperClipIcon-7jOC84nb.js";const b={components:{SBXFilterBar:_,AssignmentCard:A,AssignmentDetail:f},props:{assignments:Object,selectedAssignment:{type:Object,default:null},filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.my_assignments.title"),this.assignments.data.length>0&&(this.currentAssignment=this.assignments.data[0]),this.selectedAssignment!=null&&(this.currentAssignment=this.selectedAssignment.data)},updated(){this.$page.props.page_info.title_label=this.$t("translations.my_assignments.title")},data(){return{currentAssignment:null,myAssignmentsFilters:{search:this.filters.search}}},methods:{changeFilters(){this.$inertia.get(this.route("my_assignments",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.myAssignmentsFilters,{preserveState:!1,replace:!0})},searchValueChanged(t){this.myAssignmentsFilters.search=t},selectAssignment(t){this.currentAssignment=t}}},y={class:"hidden xl:block h-screen"},v={class:"grid grid-cols-12 gap-4 h-full"},F={class:"col-span-3 border-r border-gray-300 h-full"},S={class:"col-span-9"},k={class:"xl:hidden p-4"};function B(t,C,m,x,n,c){const o=d("AssignmentCard"),h=d("AssignmentDetail");return e(),r(a,null,[s("div",y,[s("div",v,[s("div",F,[(e(!0),r(a,null,g(m.assignments.data,i=>(e(),l(o,{assignment:i,selectedAssignment:n.currentAssignment,onSelectAssignment:c.selectAssignment},null,8,["assignment","selectedAssignment","onSelectAssignment"]))),256))]),s("div",S,[n.currentAssignment?(e(),l(h,{key:0,assignment:n.currentAssignment},null,8,["assignment"])):u("",!0)])])]),s("div",k,[(e(!0),r(a,null,g(m.assignments.data,i=>(e(),l(o,{assignment:i,selectedAssignment:n.currentAssignment,onSelectAssignment:c.selectAssignment,mobile:!0},null,8,["assignment","selectedAssignment","onSelectAssignment"]))),256))])],64)}const X=p(b,[["render",B]]);export{X as default};
