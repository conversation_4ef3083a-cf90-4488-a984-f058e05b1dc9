import{ac as b,h as d,d as a,C as t,w as l,F as w,b as i,af as k,t as x,j as u,e as o,c as y,g as c,$ as v,n as V,k as $}from"./app-Cm2beRkj.js";import{A as h}from"./AuthenticationCard-BbMgTIf9.js";import{_ as B}from"./AuthenticationCardLogo-DOAxyrlM.js";import{_ as C}from"./Checkbox-D_37U_LQ.js";import{_ as f,a as p}from"./TextInput-Dx25uC_E.js";import{_ as g}from"./InputLabel-dwoQhTuq.js";import{_ as L}from"./PrimaryButton-7FvKQ4S5.js";const N={key:0,class:"mb-4 font-medium text-sm text-green-600"},j={class:"mt-4"},q={class:"block mt-4"},F={class:"flex items-center"},S={class:"flex items-center justify-end mt-4"},U={layout:null},G=Object.assign(U,{__name:"Login",props:{canResetPassword:Boolean,status:String},setup(m){const e=b({email:"",password:"",remember:!1}),_=()=>{e.transform(n=>({...n,remember:e.remember?"on":""})).post(route("login"),{onFinish:()=>e.reset("password")})};return(n,s)=>(i(),d(w,null,[a(t(k),{title:"Översättare.nu | Logga in"}),a(h,null,{logo:l(()=>[a(B)]),default:l(()=>[m.status?(i(),d("div",N,x(m.status),1)):u("",!0),o("form",{onSubmit:$(_,["prevent"])},[o("div",null,[a(g,{for:"email",value:"E-post"}),a(f,{id:"email",modelValue:t(e).email,"onUpdate:modelValue":s[0]||(s[0]=r=>t(e).email=r),type:"email",class:"mt-1 block w-full",required:"",autofocus:""},null,8,["modelValue"]),a(p,{class:"mt-2",message:t(e).errors.email},null,8,["message"])]),o("div",j,[a(g,{for:"password",value:"Lösenord"}),a(f,{id:"password",modelValue:t(e).password,"onUpdate:modelValue":s[1]||(s[1]=r=>t(e).password=r),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"current-password"},null,8,["modelValue"]),a(p,{class:"mt-2",message:t(e).errors.password},null,8,["message"])]),o("div",q,[o("label",F,[a(C,{checked:t(e).remember,"onUpdate:checked":s[2]||(s[2]=r=>t(e).remember=r),name:"remember"},null,8,["checked"]),s[3]||(s[3]=o("span",{class:"ml-2 text-sm text-gray-600"},"Kom ihåg mig",-1))])]),o("div",S,[m.canResetPassword?(i(),y(t(v),{key:0,href:n.route("password.request"),class:"underline text-sm text-gray-600 hover:text-gray-900"},{default:l(()=>s[4]||(s[4]=[c(" Glömt lösenord? ")])),_:1},8,["href"])):u("",!0),a(L,{class:V(["ml-4",{"opacity-25":t(e).processing}]),disabled:t(e).processing},{default:l(()=>s[5]||(s[5]=[c(" Logga in ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}});export{G as default};
