import{_,S,c as g,w as p,r as s,b,d as r,e as h,g as B,t as w,k as X}from"./app-Cm2beRkj.js";import{S as v}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import N from"./ProductEditorsDropdown-PputzcmH.js";import{S as x}from"./SBXInput-C8dZEgZe.js";import{S as D}from"./SBXTextArea-D09nQvWc.js";import{S as C}from"./SBXNotification-Bm69sG5a.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const P={components:{SBXDefaultPageLayout:v,ProductEditorsDropdown:N,SBXInput:x,SBXTextArea:D,SBXButton:S,SBXNotification:C},props:{product:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({seo_slug:this.product.data.seo_slug,seo_title:this.product.data.seo_title,seo_meta:this.product.data.seo_meta}),showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("products.seo.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:a=>{this.showSaveSuccess=!0}})},saveNotificationCancelled(){this.showSaveSuccess=!1}}};function T(a,e,l,y,o,i){const u=s("ProductEditorsDropdown"),n=s("SBXInput"),d=s("SBXTextArea"),m=s("SBXButton"),c=s("SBXNotification"),f=s("SBXDefaultPageLayout");return b(),g(f,null,{default:p(()=>[r(u,{pages:l.pageComponents,currentPage:"seo",productID:l.product.data.id},null,8,["pages","productID"]),h("form",{onSubmit:e[3]||(e[3]=X((...t)=>i.update&&i.update(...t),["prevent"]))},[r(n,{model:o.form.seo_slug,"onUpdate:model":e[0]||(e[0]=t=>o.form.seo_slug=t),label:a.$t("sbxwebshop.products.seo_slug_label"),error:l.errors.seo_slug},null,8,["model","label","error"]),r(n,{model:o.form.seo_title,"onUpdate:model":e[1]||(e[1]=t=>o.form.seo_title=t),label:a.$t("sbxwebshop.products.seo_title_label"),error:l.errors.seo_title},null,8,["model","label","error"]),r(d,{model:o.form.seo_meta,"onUpdate:model":e[2]||(e[2]=t=>o.form.seo_meta=t),label:a.$t("sbxwebshop.products.seo_meta_label"),rows:10,error:l.errors.seo_meta},null,8,["model","label","error"]),r(m,{class:"mt-4"},{default:p(()=>[B(w(a.$t("sbxwebshop.global.save")),1)]),_:1})],32),r(c,{show:o.showSaveSuccess,onNotificationCancelled:i.saveNotificationCancelled},null,8,["show","onNotificationCancelled"])]),_:1})}const M=_(P,[["render",T]]);export{M as default};
