import{_ as h,p as v,c,w as r,S as f,r as _,b as e,e as a,t as s,h as n,j as o,F as $,i as y,g as i,d}from"./app-Cm2beRkj.js";import{S as g}from"./sweetalert2.all-i0W-sCgv.js";import{S as k}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import x from"./FileRow-Bb-_L2Ti.js";import"./client-BWFz6ICJ.js";import"./PaperClipIcon-7jOC84nb.js";const B=v({components:{FileRow:x,SBXDefaultPageLayout:k,SBXButton:f},props:{assignment:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignments.approvals_detail.title"),console.log("Assignment",this.assignment.data)},data(){return{}},methods:{approveAssignment(){var t=this;g.fire({title:t.$t("translations.translation_assignments.approvals_detail.approve_dialog_title"),text:t.$t("translations.translation_assignments.approvals_detail.approve_dialog_message"),icon:"warning",showCancelButton:!0,cancelButtonText:t.$t("translations.global.cancel"),confirmButtonColor:"#15803d",confirmButtonText:t.$t("translations.translation_assignments.approvals_detail.approve_button")}).then(l=>{l.value&&t.$inertia.post(t.route("translation_assignment_approvals.approve",[t.$page.props.locale.selected_market_code,t.$page.props.locale.selected_language_code,t.assignment.data.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})},rejectAssignment(){var t=this;g.fire({title:t.$t("translations.translation_assignments.approvals_detail.reject_dialog_title"),text:t.$t("translations.translation_assignments.approvals_detail.reject_dialog_message"),icon:"warning",showCancelButton:!0,cancelButtonText:t.$t("translations.global.cancel"),confirmButtonColor:"#d33",confirmButtonText:t.$t("translations.translation_assignments.approvals_detail.reject_button")}).then(l=>{l.value&&t.$inertia.post(t.route("translation_assignment_approvals.reject",[t.$page.props.locale.selected_market_code,t.$page.props.locale.selected_language_code,t.assignment.data.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})},deleteAssignment(){var t=this;g.fire({title:t.$t("translations.translation_assignments.approvals_detail.delete_dialog_title"),text:t.$t("translations.translation_assignments.approvals_detail.delete_dialog_message"),icon:"warning",showCancelButton:!0,cancelButtonText:t.$t("translations.global.cancel"),confirmButtonColor:"#d33",confirmButtonText:t.$t("translations.translation_assignments.approvals_detail.delete_button")}).then(l=>{l.value&&t.$inertia.post(t.route("translation_assignment_approvals.delete",[t.$page.props.locale.selected_market_code,t.$page.props.locale.selected_language_code,t.assignment.data.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})}}}),w={class:"p-3 sm:grid sm:grid-cols-2 gap-4"},S={class:"text-2xl font-semibold text-oversattare-text-black"},C={class:"mt-4 text-lg font-semibold text-oversattare-text-black"},j={class:"text-sm text-oversattare-text-black"},A={key:0},T={class:"mt-4 text-base font-semibold text-oversattare-text-black"},X={class:"text-sm text-oversattare-text-black"},F={key:1},D={class:"mt-4 text-lg font-semibold text-oversattare-text-black"},L={class:"mt-4 text-base font-semibold text-oversattare-text-black"},N={key:0,class:"text-sm text-oversattare-text-black"},V={key:1,class:"text-sm text-oversattare-text-black"},z={class:"mt-8 mb-2 text-2xl font-semibold text-oversattare-text-black"},P={key:2,class:"text-sm text-oversattare-text-black"},R={key:3,class:"text-sm text-oversattare-text-black"},q={class:"text-sm text-oversattare-text-black"},E={key:4,class:"text-sm text-oversattare-text-black"},O={key:5,class:"text-sm text-oversattare-text-black"},U={class:"text-sm text-oversattare-text-black"},G=["href"],H={class:"text-sm text-oversattare-text-black"},I=["href"],J={class:"mt-4 text-lg font-semibold text-oversattare-text-black"},K={key:6,class:"text-sm text-oversattare-text-black"},M={key:7,class:"text-sm text-oversattare-text-black"},Q={key:8},W={class:"mt-8 text-2xl font-semibold text-oversattare-text-black"},Y={class:"text-sm text-oversattare-text-black sm:w-1/2"},Z={class:"mt-4 flex"};function tt(t,l,st,at,et,nt){const p=_("FileRow"),m=_("SBXButton"),u=_("SBXDefaultPageLayout");return e(),c(u,null,{default:r(()=>[a("div",w,[a("div",null,[a("h3",S,s(t.$t("translations.my_assignments.assignment_detail.title")),1),a("h4",C,s(t.$t("translations.my_assignments.assignment_detail.description_label")),1),a("p",j,s(t.assignment.data.assignment_id)+", "+s(t.assignment.data.translation_category)+", "+s(t.assignment.data.from_translation_language)+" "+s(t.$t("translations.my_assignments.assignment_detail.to_label"))+" "+s(t.assignment.data.to_translation_language)+".",1),t.assignment.data.number_of_words!=null&&t.assignment.data.number_of_words>0?(e(),n("div",A,[a("h4",T,s(t.$t("translations.my_assignments.assignment_detail.word_count_label")),1),a("p",X,s(t.assignment.data.number_of_words),1)])):o("",!0),t.assignment.data.files.length>0?(e(),n("div",F,[a("h4",D,s(t.$t("translations.my_assignments.assignment_detail.files_label")),1),(e(!0),n($,null,y(t.assignment.data.files,b=>(e(),c(p,{file:b},null,8,["file"]))),256))])):o("",!0),a("div",null,[a("h4",L,s(t.$t("translations.my_assignments.assignment_detail.authorization_label")),1),t.assignment.data.is_authorization_required?(e(),n("p",N,s(t.$t("translations.global.yes")),1)):o("",!0),t.assignment.data.is_authorization_required?o("",!0):(e(),n("p",V,s(t.$t("translations.global.no")),1))]),a("h4",z,s(t.$t("translations.my_assignments.assignment_detail.contact_info_title")),1),t.assignment.data.assignment_type=="company"?(e(),n("p",P,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.company_label")),1)])):o("",!0),t.assignment.data.assignment_type=="personal"?(e(),n("p",R,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.private_person_label")),1)])):o("",!0),a("p",q,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.name_label"))+": ",1),i(s(t.assignment.data.first_name)+" "+s(t.assignment.data.last_name),1)]),t.assignment.data.assignment_type=="company"?(e(),n("p",E,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.company_label"))+": ",1),i(s(t.assignment.data.company),1)])):o("",!0),t.assignment.data.assignment_type=="company"?(e(),n("p",O,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.company_no_label"))+": ",1),i(s(t.assignment.data.company_no),1)])):o("",!0),a("p",U,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.email_label"))+": ",1),a("a",{href:"mailto:"+t.assignment.data.email},s(t.assignment.data.email),9,G)]),a("p",H,[a("strong",null,s(t.$t("translations.my_assignments.assignment_detail.phone_label"))+": ",1),a("a",{href:"tel:"+t.assignment.data.phone_no},s(t.assignment.data.phone_no),9,I)]),a("h4",J,s(t.$t("translations.my_assignments.assignment_detail.contact_preferences_label")),1),t.assignment.data.is_email_contact_allowed?(e(),n("p",K,s(t.$t("translations.my_assignments.assignment_detail.email_label")),1)):o("",!0),t.assignment.data.is_phone_contact_allowed?(e(),n("p",M,s(t.$t("translations.my_assignments.assignment_detail.phone_label")),1)):o("",!0),t.assignment.data.notes!=null&&t.assignment.data.notes.length!=""?(e(),n("div",Q,[a("h4",W,s(t.$t("translations.my_assignments.assignment_detail.notes_label")),1),a("p",Y,s(t.assignment.data.notes),1)])):o("",!0),a("div",Z,[d(m,{onClick:t.approveAssignment,variant:"success",class:"mb-4 mt-2 mr-2"},{default:r(()=>[i(s(t.$t("translations.translation_assignments.approvals_detail.approve_button")),1)]),_:1},8,["onClick"]),d(m,{onClick:t.rejectAssignment,variant:"danger",class:"mb-4 mt-2 mr-2"},{default:r(()=>[i(s(t.$t("translations.translation_assignments.approvals_detail.reject_button")),1)]),_:1},8,["onClick"]),d(m,{onClick:t.deleteAssignment,variant:"danger",class:"mb-4 mt-2"},{default:r(()=>[i(s(t.$t("translations.translation_assignments.approvals_detail.delete_button")),1)]),_:1},8,["onClick"])])])])]),_:1})}const dt=h(B,[["render",tt]]);export{dt as default};
