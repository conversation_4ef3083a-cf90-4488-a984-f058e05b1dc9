import{_ as c,S as d,c as e,w as m,r as o,b as n,j as i}from"./app-Cm2beRkj.js";import{S as u}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import _ from"./BooleanEditor-Duha6gbR.js";import p from"./IntegerEditor-CZRafpd3.js";import f from"./FloatEditor-Bk-uhr1y.js";import E from"./StringEditor-Du0G7IXi.js";const y={components:{SBXDefaultPageLayout:u,SBXButton:d,BooleanEditor:_,IntegerEditor:p,FloatEditor:f,StringEditor:E},props:{setting:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("Setting: ",this.setting)},updated(){this.$page.props.page_info.title_label=this.pageTitle},computed:{pageTitle(){return`${this.$t("sbxcms.global.edit")} - ${this.setting.data.name}`}}};function S(B,h,t,b,k,x){const a=o("BooleanEditor"),s=o("IntegerEditor"),r=o("FloatEditor"),g=o("StringEditor"),l=o("SBXDefaultPageLayout");return n(),e(l,null,{default:m(()=>[t.setting.data.type=="boolean"?(n(),e(a,{key:0,setting:t.setting},null,8,["setting"])):i("",!0),t.setting.data.type=="integer"?(n(),e(s,{key:1,setting:t.setting},null,8,["setting"])):i("",!0),t.setting.data.type=="float"?(n(),e(r,{key:2,setting:t.setting},null,8,["setting"])):i("",!0),t.setting.data.type=="string"?(n(),e(g,{key:3,setting:t.setting},null,8,["setting"])):i("",!0)]),_:1})}const P=c(y,[["render",S]]);export{P as default};
