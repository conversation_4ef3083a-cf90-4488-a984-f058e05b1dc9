import{_ as x,h as d,d as _,w as p,e as s,F as u,r as h,b as c,l as v,v as y,i as b,t as i}from"./app-Cm2beRkj.js";import{S as f}from"./SBXDataTable-C66uxM7K.js";import{S as w}from"./SBXFilterBar-w4dDtxld.js";import{r as D}from"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const F={components:{SBXDataTable:f,SBXFilterBar:w,CheckCircleIcon:D},props:{inventoryRows:Object,products:Object,changeTypes:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.product_inventory.inventory_list.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.product_inventory.inventory_list.title")},data(){return{columns:[{key:"date",label:this.$t("sbxwebshop.product_inventory.inventory_list.date_label")},{key:"productslot",label:this.$t("sbxwebshop.product_inventory.inventory_list.product_name_label")},{key:"change_type",label:this.$t("sbxwebshop.product_inventory.inventory_list.change_type_label")},{key:"valueslot",label:this.$t("sbxwebshop.product_inventory.inventory_list.value_label"),headeralignment:"right",class:"justify-end"},{key:"notes",label:this.$t("sbxwebshop.product_inventory.inventory_list.notes_label")}],inventoryFilters:{search:this.filters.search,productID:this.filters.productID,changeTypeID:this.filters.changeTypeID}}},computed:{productFilterOptions(){var t=[];t.push({text:this.$t("sbxwebshop.product_inventory.inventory_list.product_filter_all_label"),value:"all"});for(var o=0;o<this.products.data.length;o++){let n=this.products.data[o];var l=n.name;n.meta_2!=null&&n.meta_2!=""&&(l+=", "+n.meta_2),n.meta_1!=null&&n.meta_1!=""&&(l+=", "+n.meta_1),t.push({text:l,value:n.id})}return t},changeTypeFilterOptions(){var t=[];t.push({text:this.$t("sbxwebshop.product_inventory.inventory_list.change_type_filter_all_label"),value:"all"});for(var o=0;o<this.changeTypes.data.length;o++){let l=this.changeTypes.data[o];t.push({text:l.name,value:l.id})}return t}},methods:{changeFilters(){this.$inertia.get(this.route("product_inventory.inventory_list",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.inventoryFilters,{preserveState:!1,replace:!0})},searchValueChanged(t){this.inventoryFilters.search=t}}},T={class:"flex justify-end"},B=["value"],k=["value"],C={class:"flex flex-col"},S={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},I={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},O={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},$={class:"text-sm font-semibold"},V={class:"text-xs"},R={class:"text-xs"},j={class:"text-xs"};function X(t,o,l,n,a,r){const m=h("SBXFilterBar"),g=h("SBXDataTable");return c(),d(u,null,[_(m,{onSearchValueChanged:r.searchValueChanged,filters:a.inventoryFilters,searchRoute:"product_inventory.inventory_list",placeholder:t.$t("sbxwebshop.global.search")},{filterArea:p(()=>[s("div",T,[v(s("select",{onChange:o[0]||(o[0]=(...e)=>r.changeFilters&&r.changeFilters(...e)),"onUpdate:modelValue":o[1]||(o[1]=e=>a.inventoryFilters.productID=e),class:"mt-1 block w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(c(!0),d(u,null,b(r.productFilterOptions,e=>(c(),d("option",{value:e.value},i(e.text),9,B))),256))],544),[[y,a.inventoryFilters.productID]]),v(s("select",{onChange:o[2]||(o[2]=(...e)=>r.changeFilters&&r.changeFilters(...e)),"onUpdate:modelValue":o[3]||(o[3]=e=>a.inventoryFilters.changeTypeID=e),class:"ml-2 mt-1 block w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(c(!0),d(u,null,b(r.changeTypeFilterOptions,e=>(c(),d("option",{value:e.value},i(e.text),9,k))),256))],544),[[y,a.inventoryFilters.changeTypeID]])])]),_:1},8,["onSearchValueChanged","filters","placeholder"]),s("div",C,[s("div",S,[s("div",I,[s("div",O,[_(g,{columns:a.columns,items:l.inventoryRows.data,showAddButton:!0,addButtonText:t.$t("sbxwebshop.product_inventory.inventory_list.new_row_label"),addRoute:"product_inventory.inventory_list.create",showEditButton:!0,editRoute:"product_inventory.inventory_list.edit",showDeleteButton:!0,deleteRoute:"product_inventory.inventory_list.destroy",deleteDialogTitle:t.$t("sbxwebshop.product_inventory.inventory_list.delete_dialog_title"),deleteDialogMessage:t.$t("sbxwebshop.product_inventory.inventory_list.delete_dialog_message"),deleteDialogOKText:t.$t("sbxwebshop.product_inventory.inventory_list.delete_dialog_ok"),deleteDialogCancelText:t.$t("sbxwebshop.global.cancel"),paginator:l.inventoryRows.meta},{productslot:p(e=>[s("div",null,[s("p",$,i(e.item.product_name),1),s("p",V,i(e.item.product_meta_2),1),s("p",R,i(e.item.product_meta_1),1)])]),valueslot:p(e=>[s("p",j,i(e.item.value)+" ("+i(e.item.stock)+"}",1)]),_:1},8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const z=x(F,[["render",X]]);export{z as default};
