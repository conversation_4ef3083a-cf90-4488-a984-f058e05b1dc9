import{_ as C,N as P,a as k,U as x,o as b,f as w,c as g,w as i,r,b as l,d as c,e as o,g as v,t as d,h as D,i as T,F as B}from"./app-Cm2beRkj.js";import R from"./ProductCategoriesPickerRow-DGuDieQ7.js";import"./CheckIcon-CSfnhiPS.js";const U={components:{Dialog:P,DialogPanel:k,DialogTitle:x,TransitionChild:b,TransitionRoot:w,ProductCategoriesPickerRow:R},props:{exclude:{type:Array,default:[]},multiple:{type:Boolean,default:!1},isOpen:{type:Boolean,default:!1}},mounted(){this.fetchProductCategories()},watch:{isOpen(t,e){this.fetchProductCategories(),this.pickedProductCategories=[]}},data(){return{productCategories:[],pickedProductCategories:[]}},methods:{fetchProductCategories(){var t=this;axios.post(this.route("product_categories.picker.product_categories",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{exclude:this.exclude}).then(function(e){t.productCategories=e.data.data}).catch(function(e){console.log("Error fetching product categories: ",e)})},closePicker(){this.$emit("closeProductCategoriesPicker")},productCategoryPicked(t){this.multiple?this.pickedProductCategories.findIndex(e=>e.id==t.id)==-1&&this.pickedProductCategories.push(t):(this.pickedProductCategories=[],this.pickedProductCategories.push(t))},productCategoryUnpicked(t){if(this.multiple){let e=this.pickedProductCategories.findIndex(n=>n.id==t.id);e!=-1&&this.pickedProductCategories.splice(e,1)}else this.pickedProductCategories=[]},pickProductCategories(){this.$emit("pickProductCategories",this.pickedProductCategories)}}},N={class:"fixed inset-0 z-10 overflow-y-auto"},V={class:"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"},j={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},I={class:"sm:flex sm:items-start"},O={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},z={class:"mt-2"},E={class:"bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6"};function F(t,e,n,A,u,s){const p=r("TransitionChild"),m=r("DialogTitle"),f=r("ProductCategoriesPickerRow"),h=r("DialogPanel"),_=r("Dialog"),y=r("TransitionRoot");return l(),g(y,{as:"template",show:n.isOpen},{default:i(()=>[c(_,{as:"div",class:"relative z-10",onClose:s.closePicker},{default:i(()=>[c(p,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:i(()=>e[2]||(e[2]=[o("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])),_:1}),o("div",N,[o("div",V,[c(p,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:i(()=>[c(h,{class:"relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"},{default:i(()=>[o("div",j,[o("div",I,[o("div",O,[c(m,{as:"h3",class:"text-lg font-medium leading-6 text-gray-900"},{default:i(()=>[v(d(t.$t("sbxwebshop.product_categories_picker.title")),1)]),_:1}),o("div",z,[(l(!0),D(B,null,T(u.productCategories,a=>(l(),g(f,{productCategory:a,pickedProductCategories:u.pickedProductCategories,onProductCategoryPicked:s.productCategoryPicked,onProductCategoryUnpicked:s.productCategoryUnpicked},null,8,["productCategory","pickedProductCategories","onProductCategoryPicked","onProductCategoryUnpicked"]))),256))])])])]),o("div",E,[o("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm",onClick:e[0]||(e[0]=(...a)=>s.pickProductCategories&&s.pickProductCategories(...a))},d(t.$t("sbxwebshop.product_categories_picker.select_product_categories_button")),1),o("button",{type:"button",class:"mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",onClick:e[1]||(e[1]=(...a)=>s.closePicker&&s.closePicker(...a)),ref:"cancelButtonRef"},d(t.$t("sbxwebshop.global.cancel")),513)])]),_:1})]),_:1})])])]),_:1},8,["onClose"])]),_:1},8,["show"])}const q=C(U,[["render",F]]);export{q as default};
