import{_ as U,h as i,e,t as a,n as c,c as x,w as f,j as d,g,d as b,k as B,r as m,b as s,F as y,i as v}from"./app-Cm2beRkj.js";import{u as V}from"./index-BKm97uF2.js";import{r as A,a as I,b as G,y as R,g as T,A as z}from"./CheckIcon-De50fY7n.js";import"./client-BWFz6ICJ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const S={components:{CheckIcon:A,CheckCircleIcon:I,DocumentTextIcon:G,RadioGroup:R,RadioGroupOption:T,Disclosure:z},props:{assignment:Object},data(){return{selectedOffer:null,processingData:!1,timeUntilDeadline:0,form:V("put",this.route("translation_assignment_market.accept_bid",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.assignment.data.assignment_id,this.assignment.data.customer_email]),{accepted_bid_id:null})}},mounted(){this.timeUntilDeadline=new Date(this.assignment.data.bids_deadline)-new Date},computed:{disableAcceptButton(){return!!(this.selectedOffer==null||this.processingData)}},methods:{acceptOffer(){console.log("Accepting offer is possible for:",this.selectedOffer),console.log("Assignment",this.form),this.form.processing||(this.processingData=!0,setTimeout(()=>this.processingData=!1,900),this.form.accepted_bid_id=this.selectedOffer,this.form.submit({preserveScroll:!0,preserveState:!1,onSuccess:o=>{window.location.href=this.route("translation_assignment_market.process_bid_payment",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.assignment.data.assignment_id,this.assignment.data.customer_email,this.form.accepted_bid_id]),console.log("Page Updated",o)},onError:o=>{console.log("Page Errors",o)}}))}}},j={class:"py-3"},E={class:"py-4 border-b border-gray-200"},N={class:"flex flex-wrap items-end justify-between sm:flex-nowrap"},F={class:"text-xl font-semibold leading-6 text-gray-800"},M={class:"ml-4 mt-2 flex-shrink-0 cursor-wait"},P=["title"],H={key:1},L={key:0,class:"py1 sm:py-4"},q={"aria-label":"Translations offers"},J={class:"flex flex-1"},K={class:"flex flex-col gap-y-1"},Q={class:"block text-large font-medium text-gray-900"},W={class:"flex items-center text-sm text-gray-500"},X={key:0,class:"rounded-md bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-700 text-wrap"},Y={key:0,class:"flex rounded-md ring-1 ring-inset ring-gray-200 px-1.5 py-0.5 text-xs font-medium text-gray-700 text-wrap"},Z={class:"mt-6 text-3xl font-medium text-gray-900"},$={class:"mt-1 flex items-center justify-end gap-x-6 border-t py-4"},ee=["disabled"],te={key:0},se={key:1,class:"flex items-center"},ne={key:1,class:"py-1 sm:py-4"},ie={class:"relative block w-full rounded-lg border-2 p-12 border-dashed border-gray-300 text-center"},ae={class:"mt-2 block text-sm font-semibold text-gray-900"};function oe(o,n,l,re,r,p){const w=m("vue-countdown"),k=m("CheckCircleIcon"),D=m("RadioGroupOption"),O=m("RadioGroup"),C=m("DocumentTextIcon");return s(),i("div",j,[e("div",E,[e("div",N,[e("h3",F,a(o.$t("translations.assignments.received_offers")),1),e("div",M,[l.assignment.data.bids_deadline!=""?(s(),i("span",{key:0,class:"inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-xs font-medium text-gray-900 ring-1 ring-inset ring-gray-200",title:`Öppen för offert till ${l.assignment.data.bids_deadline}`},[(s(),i("svg",{class:c(["h-1.5 w-1.5",{"fill-green-500 animate-pulse":r.timeUntilDeadline>0,"fill-red-500":r.timeUntilDeadline<=0}]),viewBox:"0 0 6 6","aria-hidden":"true"},n[3]||(n[3]=[e("circle",{cx:"3",cy:"3",r:"3"},null,-1)]),2)),r.timeUntilDeadline>0?(s(),x(w,{key:0,class:"text-sm select-none",time:r.timeUntilDeadline,onEnd:n[0]||(n[0]=t=>r.timeUntilDeadline=0)},{default:f(({days:t,hours:_,minutes:u})=>[g(a(t)+" dagar, "+a(_)+" timmar, "+a(u)+" minuter ",1)]),_:1},8,["time"])):d("",!0),r.timeUntilDeadline==0?(s(),i("span",H)):d("",!0),n[4]||(n[4]=g("Uppdraget stängt för fler offert "))],8,P)):d("",!0)])])]),l.assignment.data.bid_count>0?(s(),i("div",L,[e("form",{onSubmit:n[2]||(n[2]=B((...t)=>p.acceptOffer&&p.acceptOffer(...t),["prevent"]))},[e("fieldset",q,[b(O,{modelValue:r.selectedOffer,"onUpdate:modelValue":n[1]||(n[1]=t=>r.selectedOffer=t),class:"my-3 grid grid-cols-1 gap-y-6 md:grid-cols-3 sm:gap-x-4"},{default:f(()=>[(s(!0),i(y,null,v(l.assignment.data.answered_by,(t,_)=>(s(),x(D,{as:"template",key:t.id,value:t.id},{default:f(({active:u,checked:h})=>[e("div",{class:c([u?"border-green-600 ring-2 ring-green-600":"border-gray-300","relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none"])},[e("span",J,[e("span",K,[e("span",Q,"Offert #"+a(_+1),1),e("span",W,[t.is_authorized?(s(),i("span",X,a(o.$t("translations.translator_applications.is_authorized_label")),1)):d("",!0)]),t.estimated_delivery?(s(),i("span",Y,a(o.$t("translations.translator_applications.estimated_delivery_label"))+" "+a(t.estimated_delivery),1)):d("",!0),e("span",Z,[g(a(t.price+t.price*this.$page.props.market_vat_rate/100)+" kr ",1),n[5]||(n[5]=e("span",{class:"text-sm text-gray-400"},"inkl.moms",-1))])])]),b(k,{class:c([h?"":"invisible","h-5 w-5 text-green-600"]),"aria-hidden":"true"},null,8,["class"]),e("span",{class:c([u?"border":"border-2",h?"border-green-600":"border-transparent","pointer-events-none absolute -inset-px rounded-lg"]),"aria-hidden":"true"},null,2)],2)]),_:2},1032,["value"]))),128)),l.assignment.data.bid_count<l.assignment.data.bids_allowed?(s(!0),i(y,{key:0},v(l.assignment.data.bids_allowed-l.assignment.data.bid_count,t=>(s(),i("div",{key:t,class:"relative block rounded-lg border-dashed border-2 border-gray-300 bg-white p-12"}))),128)):d("",!0)]),_:1},8,["modelValue"])]),e("div",$,[e("button",{type:"submit",disabled:p.disableAcceptButton,class:c([p.disableAcceptButton?"cursor-not-allowed":"cursor-pointer","transition inline-flex items-center rounded-md bg-green-700 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600 disabled:bg-gray-300"])},[r.processingData?(s(),i("span",se,[n[6]||(n[6]=e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),g(" "+a(o.$t("translations.translation_assignments.proccessing_payment")),1)])):(s(),i("span",te,a(o.$t("translations.translation_assignments.accept_pay_button")),1))],10,ee)])],32)])):(s(),i("div",ne,[e("div",ie,[b(C,{class:"h-12 w-12 text-gray-400 mx-auto"}),e("span",ae,a(o.$t("translations.assignments.no_offers")),1)])]))])}const ge=U(S,[["render",oe]]);export{ge as default};
