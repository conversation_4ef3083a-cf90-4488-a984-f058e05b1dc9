import{_ as n,b as o,h as a,e,t as r}from"./app-Cm2beRkj.js";const l={props:{step:Number,label:String}},i={class:"flex items-center"},c={class:"flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border border-gray-900 rounded-full"},d={class:"text-base sm:text-xl text-center text-gray-900 font-semibold"},x={class:"text-sm sm:text-base text-gray-900 font-semibold"};function m(p,s,t,b,f,_){return o(),a("div",i,[e("div",c,[e("span",d,r(t.step),1)]),s[0]||(s[0]=e("span",{class:"flex-1 h-px mx-4 border-none bg-gray-900"},null,-1)),e("p",x,r(t.label),1),s[1]||(s[1]=e("span",{class:"flex-1 h-px mx-4 border-none bg-gray-900"},null,-1))])}const u=n(l,[["render",m]]);export{u as S};
