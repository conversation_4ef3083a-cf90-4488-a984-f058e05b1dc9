import{_ as v,h as l,d,w as p,e as o,F as h,r as n,b as a,l as f,v as x,i as k,t as w,j as g}from"./app-Cm2beRkj.js";import{S as C}from"./SBXDataTable-C66uxM7K.js";import{S as F}from"./SBXFilterBar-w4dDtxld.js";import{r as y}from"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const B={components:{SBXDataTable:C,SBXFilterBar:F,CheckCircleIcon:y},props:{products:Object,editRoute:String,createRoute:String,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.products.title"),console.log("Products: ",this.products.data)},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.products.title")},data(){return{columns:[{key:"article_no",label:this.$t("sbxwebshop.products.article_no_label")},{key:"name",label:this.$t("sbxwebshop.products.name_label")},{key:"activeslot",label:this.$t("sbxwebshop.products.active_label")}],productFilters:{search:this.filters.search,selectedFilter:this.filters.selectedFilter}}},computed:{filterOptions(){return[{text:"Alla produkter",value:"all"},{text:"Aktiva",value:"active"},{text:"Inaktiva",value:"inactive"}]}},methods:{changeFilters(){this.$inertia.get(this.route("products",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.productFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.productFilters.search=e},activateProduct(e){this.$inertia.put(this.route("products.activate",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]),{preserveState:!1,replace:!0})},deactivateProduct(e){this.$inertia.put(this.route("products.deactivate",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]),{preserveState:!1,replace:!0})}}},S={class:"flex justify-end"},D=["value"],$={class:"flex flex-col"},T={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},R={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},V={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},j=["onClick"],O=["onClick"];function P(e,r,i,X,c,s){const m=n("SBXFilterBar"),u=n("CheckCircleIcon"),_=n("SBXDataTable");return a(),l(h,null,[d(m,{onSearchValueChanged:s.searchValueChanged,filters:c.productFilters,searchRoute:"products",placeholder:e.$t("sbxwebshop.global.search")},{filterArea:p(()=>[o("div",S,[f(o("select",{onChange:r[0]||(r[0]=(...t)=>s.changeFilters&&s.changeFilters(...t)),"onUpdate:modelValue":r[1]||(r[1]=t=>c.productFilters.selectedFilter=t),class:"mt-1 block w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(a(!0),l(h,null,k(s.filterOptions,t=>(a(),l("option",{value:t.value},w(t.text),9,D))),256))],544),[[x,c.productFilters.selectedFilter]])])]),_:1},8,["onSearchValueChanged","filters","placeholder"]),o("div",$,[o("div",T,[o("div",R,[o("div",V,[d(_,{columns:c.columns,items:i.products.data,showAddButton:!0,addButtonText:e.$t("sbxwebshop.products.create_button"),addRoute:i.createRoute,showEditButton:!0,editRoute:i.editRoute,showDeleteButton:!0,deleteRoute:"products.destroy",deleteDialogTitle:e.$t("sbxwebshop.products.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.products.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.products.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxwebshop.global.cancel"),paginator:i.products.meta},{activeslot:p(t=>[t.item.is_active?g("",!0):(a(),l("span",{key:0,onClick:b=>s.activateProduct(t.item.id),class:"inline-flex flex-shrink-0 items-center justify-center cursor-pointer"},[d(u,{class:"h-8 w-8 text-gray-400 group-hover:text-gray-500","aria-hidden":"true"})],8,j)),t.item.is_active?(a(),l("span",{key:1,onClick:b=>s.deactivateProduct(t.item.id),class:"inline-flex flex-shrink-0 items-center justify-center cursor-pointer"},[d(u,{class:"h-8 w-8 text-green-500 group-hover:text-green-700","aria-hidden":"true"})],8,O)):g("",!0)]),_:1},8,["columns","items","addButtonText","addRoute","editRoute","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const q=v(B,[["render",P]]);export{q as default};
