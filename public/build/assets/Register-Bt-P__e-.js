import{ac as _,h as p,d as e,C as a,w as l,F as g,b as f,af as w,e as t,g as d,j as y,$ as v,n as k,k as V}from"./app-Cm2beRkj.js";import{A as h}from"./AuthenticationCard-BbMgTIf9.js";import{_ as b}from"./AuthenticationCardLogo-DOAxyrlM.js";import{_ as x}from"./Checkbox-D_37U_LQ.js";import{_ as u,a as m}from"./TextInput-Dx25uC_E.js";import{_ as n}from"./InputLabel-dwoQhTuq.js";import{_ as $}from"./PrimaryButton-7FvKQ4S5.js";const C={class:"mt-4"},q={class:"mt-4"},N={class:"mt-4"},R={key:0,class:"mt-4"},U={class:"flex items-center"},j={class:"ml-2"},B=["href"],F=["href"],A={class:"flex items-center justify-end mt-4"},T={layout:null},Z=Object.assign(T,{__name:"Register",setup(E){const s=_({name:"",email:"",password:"",password_confirmation:"",terms:!1}),c=()=>{s.post(route("register"),{onFinish:()=>s.reset("password","password_confirmation")})};return(i,o)=>(f(),p(g,null,[e(a(w),{title:"Sydfisk CRM | Registrering"}),e(h,null,{logo:l(()=>[e(b)]),default:l(()=>[t("form",{onSubmit:V(c,["prevent"])},[t("div",null,[e(n,{for:"name",value:"Namn"}),e(u,{id:"name",modelValue:a(s).name,"onUpdate:modelValue":o[0]||(o[0]=r=>a(s).name=r),type:"text",class:"mt-1 block w-full",required:"",autofocus:"",autocomplete:"name"},null,8,["modelValue"]),e(m,{class:"mt-2",message:a(s).errors.name},null,8,["message"])]),t("div",C,[e(n,{for:"email",value:"E-post"}),e(u,{id:"email",modelValue:a(s).email,"onUpdate:modelValue":o[1]||(o[1]=r=>a(s).email=r),type:"email",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),e(m,{class:"mt-2",message:a(s).errors.email},null,8,["message"])]),t("div",q,[e(n,{for:"password",value:"Lösenord"}),e(u,{id:"password",modelValue:a(s).password,"onUpdate:modelValue":o[2]||(o[2]=r=>a(s).password=r),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),e(m,{class:"mt-2",message:a(s).errors.password},null,8,["message"])]),t("div",N,[e(n,{for:"password_confirmation",value:"Bekräfta lösenord"}),e(u,{id:"password_confirmation",modelValue:a(s).password_confirmation,"onUpdate:modelValue":o[3]||(o[3]=r=>a(s).password_confirmation=r),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"new-password"},null,8,["modelValue"]),e(m,{class:"mt-2",message:a(s).errors.password_confirmation},null,8,["message"])]),i.$page.props.jetstream.hasTermsAndPrivacyPolicyFeature?(f(),p("div",R,[e(n,{for:"terms"},{default:l(()=>[t("div",U,[e(x,{id:"terms",checked:a(s).terms,"onUpdate:checked":o[4]||(o[4]=r=>a(s).terms=r),name:"terms",required:""},null,8,["checked"]),t("div",j,[o[5]||(o[5]=d(" Jag godkänner ")),t("a",{target:"_blank",href:i.route("terms.show"),class:"underline text-sm text-gray-600 hover:text-gray-900"},"de allmänna villkoren",8,B),o[6]||(o[6]=d(" och ")),t("a",{target:"_blank",href:i.route("policy.show"),class:"underline text-sm text-gray-600 hover:text-gray-900"},"personuppgiftspolicyn.",8,F)])]),e(m,{class:"mt-2",message:a(s).errors.terms},null,8,["message"])]),_:1})])):y("",!0),t("div",A,[e(a(v),{href:i.route("login"),class:"underline text-sm text-gray-600 hover:text-gray-900"},{default:l(()=>o[7]||(o[7]=[d(" Redan registrerad? ")])),_:1},8,["href"]),e($,{class:k(["ml-4",{"opacity-25":a(s).processing}]),disabled:a(s).processing},{default:l(()=>o[8]||(o[8]=[d(" Registrera ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}});export{Z as default};
