import{_ as r,h as d,d as s,e as t,F as m,r as o,b as c}from"./app-Cm2beRkj.js";import{S as p}from"./SBXDataTable-C66uxM7K.js";import{S as u}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const _={components:{SBXDataTable:p,SBXFilterBar:u},props:{settings:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxadmin.settings.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxadmin.settings.title")},data(){return{columns:[{key:"name",label:this.$t("sbxadmin.settings.name_label"),sortable:!0,sortDirection:"desc",sortByFormatted:!0,filterByFormatted:!0}]}},methods:{}},f={class:"flex flex-col"},g={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},h={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},b={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function B(a,x,e,S,i,v){const l=o("SBXFilterBar"),n=o("SBXDataTable");return c(),d(m,null,[s(l,{filters:e.filters,searchRoute:"settings",placeholder:a.$t("sbxadmin.global.search")},null,8,["filters","placeholder"]),t("div",f,[t("div",g,[t("div",h,[t("div",b,[s(n,{columns:i.columns,items:e.settings.data,showAddButton:!1,showEditButton:!0,editRoute:"settings.edit",showDeleteButton:!1,paginator:e.settings.meta},null,8,["columns","items","paginator"])])])])])],64)}const j=r(_,[["render",B]]);export{j as default};
