import{_ as B,S,h as d,d as r,e,w as c,F as u,i as v,r as o,b as m,t as l,c as $}from"./app-Cm2beRkj.js";import{r as w}from"./EnvelopeIcon-C7yn4PbF.js";import{S as x}from"./sweetalert2.all-i0W-sCgv.js";import{S as k}from"./SBXDataTable-C66uxM7K.js";import{S as F}from"./SBXFilterBar-w4dDtxld.js";import{a as X}from"./SBXTable-CaSPMjSb.js";import C from"./MobilePayment-BAD7TFNw.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./debounce-Bpn6Ai4k.js";const D={components:{EnvelopeIcon:w,SBXDataTable:k,SBXFilterBar:F,MobilePayment:C,SBXPaginator:X,SBXButton:S},props:{payments:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.payments.title"),this.$page.props.flash.authorization!=null&&(this.showAuthorizationNotification=!0)},updated(){this.$page.props.page_info.title_label=this.$t("translations.payments.title")},data(){return{columns:[{key:"order_no",label:this.$t("translations.payments.order_no_label")},{key:"date",label:this.$t("translations.payments.date_label")},{key:"productslot",label:this.$t("translations.payments.product_label")},{key:"priceslot",label:this.$t("translations.payments.price_label"),headeralignment:"right",class:"justify-end"},{key:"buttonslot",label:""}],translationAssignmentFilters:{search:this.filters.search}}},methods:{changeFilters(){this.$inertia.get(this.route("payments",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translationAssignmentFilters,{preserveState:!1,replace:!0})},searchValueChanged(t){this.translationAssignmentFilters.search=t},formatNumber(t){return t.toLocaleString("SE-sv",{minimumFractionDigits:2,maximumFractionDigits:2})},resendReceipt(t){var a=this;x.fire({title:a.$t("translations.payments.resend_receipt_dialog_title"),text:a.$t("translations.payments.resend_receipt_dialog_message"),icon:"info",showCancelButton:!0,cancelButtonText:a.$t("translations.global.cancel"),confirmButtonColor:"#02B07D",confirmButtonText:a.$t("translations.payments.resend_receipt_dialog_send_button")}).then(n=>{n.value&&a.$inertia.post(a.route("payments.resend_receipt",[a.$page.props.locale.selected_market_code,a.$page.props.locale.selected_language_code,t.id]),{},{preserveScroll:!0,onSuccess:()=>{}})})}}},P={class:"hidden sm:flex sm:flex-col"},V={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},A={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},E={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},N={class:"text-sm"},T={class:"text-sm"},j={class:"sm:hidden"};function I(t,a,n,M,p,i){const _=o("SBXFilterBar"),h=o("EnvelopeIcon"),g=o("SBXButton"),f=o("SBXDataTable"),y=o("MobilePayment"),b=o("SBXPaginator");return m(),d(u,null,[r(_,{onSearchValueChanged:i.searchValueChanged,filters:p.translationAssignmentFilters,searchRoute:"payments",placeholder:t.$t("translations.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),e("div",P,[e("div",V,[e("div",A,[e("div",E,[r(f,{columns:p.columns,items:n.payments.data,showAddButton:!1,showEditButton:!1,showDeleteButton:!1,showViewButton:!1,paginator:n.payments.meta},{productslot:c(s=>[e("div",null,[e("p",N,l(s.item.rows[0].product_name),1)])]),priceslot:c(s=>[e("div",null,[e("p",T,l(i.formatNumber(s.item.price))+" "+l(t.$t("translations.payments.currency_label"))+" ("+l(t.$t("translations.payments.vat_label"))+" "+l(i.formatNumber(s.item.vat))+" "+l(t.$t("translations.payments.currency_label"))+")",1)])]),buttonslot:c(s=>[e("div",null,[r(g,{onClick:R=>i.resendReceipt(s.item)},{default:c(()=>[r(h,{class:"w-4 h-4 text-white"})]),_:2},1032,["onClick"])])]),_:1},8,["columns","items","paginator"])])])])]),e("div",j,[(m(!0),d(u,null,v(n.payments.data,s=>(m(),$(y,{payment:s},null,8,["payment"]))),256)),r(b,{paginator:n.payments.meta},null,8,["paginator"])])],64)}const W=B(D,[["render",I]]);export{W as default};
