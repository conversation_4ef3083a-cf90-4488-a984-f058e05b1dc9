import{_ as f,S as p,c as _,w as m,r as n,b,e,t as r,l as i,m as d,d as g,g as k,k as h}from"./app-Cm2beRkj.js";import{S as x}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const B={components:{SBXDefaultPageLayout:x,SBXButton:p},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:"",market_code:"",is_website_default:!1,is_website_fallback:!1})}},computed:{pageTitle(){return`${this.$t("sbxadmin.markets.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("markets.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},w={class:"mt-4"},y={for:"market_code",class:"block text-sm font-medium text-gray-700"},S={class:"mt-1"},v={class:"mt-4"},$={for:"name",class:"block text-sm font-medium text-gray-700"},X={class:"mt-1"};function D(a,t,T,V,o,l){const c=n("SBXButton"),u=n("SBXDefaultPageLayout");return b(),_(u,null,{default:m(()=>[e("form",{onSubmit:t[2]||(t[2]=h((...s)=>l.create&&l.create(...s),["prevent"]))},[e("div",w,[e("label",y,r(a.$t("sbxadmin.markets.market_code_label")),1),e("div",S,[i(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>o.form.market_code=s),type:"text",name:"market_code",id:"market_code",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[d,o.form.market_code]])])]),e("div",v,[e("label",$,r(a.$t("sbxadmin.markets.name_label")),1),e("div",X,[i(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>o.form.name=s),type:"text",name:"name",id:"name",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[d,o.form.name]])])]),g(c,{class:"mt-4"},{default:m(()=>[k(r(a.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const M=f(B,[["render",D]]);export{M as default};
