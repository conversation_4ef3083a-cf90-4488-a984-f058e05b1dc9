import{_ as g,S as h,c as f,w as d,r as s,b as B,e as a,d as n,g as x,t as u,k as v}from"./app-Cm2beRkj.js";import{S as w}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as D}from"./SBXDataTable-C66uxM7K.js";import{S}from"./SBXInput-C8dZEgZe.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const y={components:{SBXDefaultPageLayout:w,SBXDataTable:D,SBXInput:S,SBXButton:h},props:{productVariation:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("productVariation: ",this.productVariation)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.productVariation.data.name}),columns:[{key:"name",label:this.$t("sbxwebshop.product_variations.option_name_label"),sortable:!0,sortDirection:"desc",sortByFormatted:!0,filterByFormatted:!0},{key:"price_slot",label:this.$t("sbxwebshop.product_variations.option_price_label"),sortable:!0,sortDirection:"desc",sortByFormatted:!0,filterByFormatted:!0}]}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.productVariation.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("product_variations.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productVariation.data.id]))},calculatedPrice(t){if(t.bundled_products.length==0)return parseFloat(parseFloat(parseFloat(t.quantity)*t.product_price_per_unit+parseFloat(t.price_adjustment))).toFixed(2);for(var e=0,o=0;o<t.bundled_products.length;o++){let p=t.bundled_products[o];e+=parseFloat(p.option_price)}return parseFloat(e+parseFloat(t.price_adjustment)).toFixed(2)}}},T={class:"flex flex-col mt-8"},F={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},V={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},X={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},$={class:"flex"};function k(t,e,o,p,i,l){const c=s("SBXInput"),m=s("SBXButton"),_=s("SBXDataTable"),b=s("SBXDefaultPageLayout");return B(),f(b,null,{default:d(()=>[a("form",{onSubmit:e[1]||(e[1]=v((...r)=>l.update&&l.update(...r),["prevent"]))},[n(c,{model:i.form.name,"onUpdate:model":e[0]||(e[0]=r=>i.form.name=r),label:t.$t("sbxwebshop.product_variations.name_label"),error:o.errors.name},null,8,["model","label","error"]),n(m,{class:"mt-4"},{default:d(()=>[x(u(t.$t("sbxwebshop.global.save")),1)]),_:1})],32),a("div",T,[a("div",F,[a("div",V,[a("div",X,[n(_,{columns:i.columns,items:o.productVariation.data.options,showAddButton:!0,addButtonText:t.$t("sbxwebshop.product_variations.option_create_title"),addRoute:"product_variations.options.create",addRouteAdditionalIDs:[o.productVariation.data.id],showEditButton:!0,editRoute:"product_variations.options.edit",showDeleteButton:!0,deleteRoute:"product_variations.options.destroy",deleteDialogTitle:t.$t("sbxwebshop.product_variations.option_delete_dialog_title"),deleteDialogMessage:t.$t("sbxwebshop.product_variations.option_delete_dialog_message"),deleteDialogOKText:t.$t("sbxwebshop.product_variations.option_delete_dialog_ok"),deleteDialogCancelText:t.$t("sbxwebshop.global.cancel")},{price_slot:d(r=>[a("div",$,u(l.calculatedPrice(r.item))+" kr ",1)]),_:1},8,["columns","items","addButtonText","addRouteAdditionalIDs","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText"])])])])])]),_:1})}const N=g(y,[["render",k]]);export{N as default};
