import{_ as g,S as _,c as f,w as r,r as i,b as p,e,t as l,l as m,m as d,d as b,g as h,k as x}from"./app-Cm2beRkj.js";import{S as B}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const w={components:{SBXDefaultPageLayout:B,SBXButton:_},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:"",language_code:"",is_available_for_website:!0,is_website_default:!1,is_website_fallback:!1})}},computed:{pageTitle(){return`${this.$t("sbxadmin.languages.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("languages.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},v={class:"mt-4"},y={for:"language_code",class:"block text-sm font-medium text-gray-700"},S={class:"mt-1"},k={class:"mt-4"},$={for:"name",class:"block text-sm font-medium text-gray-700"},X={class:"mt-1"};function D(o,t,T,V,s,n){const u=i("SBXButton"),c=i("SBXDefaultPageLayout");return p(),f(c,null,{default:r(()=>[e("form",{onSubmit:t[2]||(t[2]=x((...a)=>n.create&&n.create(...a),["prevent"]))},[e("div",v,[e("label",y,l(o.$t("sbxadmin.languages.language_code_label")),1),e("div",S,[m(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>s.form.language_code=a),type:"text",name:"language_code",id:"language_code",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[d,s.form.language_code]])])]),e("div",k,[e("label",$,l(o.$t("sbxadmin.languages.name_label")),1),e("div",X,[m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>s.form.name=a),type:"text",name:"name",id:"name",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[d,s.form.name]])])]),b(u,{class:"mt-4"},{default:r(()=>[h(l(o.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const N=g(w,[["render",D]]);export{N as default};
