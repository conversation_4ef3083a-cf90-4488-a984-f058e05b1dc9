import{h as _,e as n,b as o,_ as d,$ as f,c as u,w as a,r as t,d as e,t as g}from"./app-Cm2beRkj.js";import{S as h}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import w from"./AssignmentDetail-Dj2RuUEh.js";import"./FileRow-C_c_9UoD.js";import"./client-BWFz6ICJ.js";import"./PaperClipIcon-7jOC84nb.js";function L(s,i){return o(),_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[n("path",{"fill-rule":"evenodd",d:"M11.03 3.97a.75.75 0 010 1.06l-6.22 6.22H21a.75.75 0 010 1.5H4.81l6.22 6.22a.75.75 0 11-1.06 1.06l-7.5-7.5a.75.75 0 010-1.06l7.5-7.5a.75.75 0 011.06 0z","clip-rule":"evenodd"})])}const $={components:{Link:f,ArrowLeftIcon:L,SBXDefaultPageLayout:h,AssignmentDetail:w},props:{assignment:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.my_assignments.title")},updated(){this.$page.props.page_info.title_label=this.$t("translations.my_assignments.title")}},b={class:"flex items-center"},v={class:"font-semibold"};function B(s,i,l,D,k,x){const r=t("ArrowLeftIcon"),c=t("Link"),m=t("AssignmentDetail"),p=t("SBXDefaultPageLayout");return o(),u(p,null,{default:a(()=>[n("div",b,[e(r,{class:"h-4 w-4 ml-2 mr-1"}),e(c,{href:"/se/sv/mina-uppdrag"},{default:a(()=>[n("span",v,g(s.$t("translations.global.back")),1)]),_:1})]),e(m,{assignment:l.assignment.data},null,8,["assignment"])]),_:1})}const X=d($,[["render",B]]);export{X as default};
