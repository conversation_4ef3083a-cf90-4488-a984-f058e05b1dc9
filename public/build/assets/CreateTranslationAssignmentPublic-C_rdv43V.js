import{_ as z,r as c,b as l,h as r,d as u,w as x,e as t,c as C,F as y,i as k,n as w,t as i,j as m,af as D,$ as N,k as M,l as g,v as O,m as b,ab as A,g as v}from"./app-Cm2beRkj.js";import{u as q}from"./index-BKm97uF2.js";import{r as B}from"./CheckIcon-CSfnhiPS.js";import{r as R}from"./CheckIcon-DN9uS3Am.js";import{r as T,A as E,L as j,B as H,F as X,N as P,j as Z,S as G}from"./SBXAlert-DwVKPUUm.js";import{r as J}from"./ExclamationCircleIcon-C4_TqLjZ.js";import K from"./AssignmentFileRow-CmyADGqu.js";import{s as Q}from"./vue-multiselect.esm-1UMdX8qQ.js";import"./client-BWFz6ICJ.js";import"./use-controllable-D9fh3JbV.js";import"./sweetalert2.all-i0W-sCgv.js";import"./TrashIcon-fhKAjDA0.js";const W={components:{ExclamationCircleIcon:J,CheckIcon:R,ChevronUpDownIcon:T,Combobox:E,ComboboxButton:j,ComboboxInput:H,ComboboxLabel:X,ComboboxOption:P,ComboboxOptions:Z},props:{valueFieldName:{type:String,default:"id"},labelFieldName:{type:String,default:"text"},selectedLabel:{type:String,default:"Selected:"},placeholder:{type:String,required:!1,default:"Search"},model:{default:null},items:{type:Array,required:!0},instruction:{type:String,required:!1,default:null},error:{type:String,required:!1,default:null}},watch:{model(e,s){this.currentValue=e}},data(){return{currentValue:this.model,query:""}},computed:{filteredItems(){return this.query===""?this.items:this.items.filter(e=>e[this.labelFieldName].toLowerCase().includes(this.query.toLowerCase()))},selectedItemsLabel(){var e='<span class="font-semibold">'+this.selectedLabel+"</span> ";if(this.currentValue==null||this.currentValue=="")return e+=" – ",e;for(var s=0;s<this.items.length;s++){let a=this.items[s];a[this.valueFieldName]==this.currentValue&&(e+=a[this.labelFieldName])}return e}},methods:{change(e){this.query=e.target.value,this.$emit("update:model",this.currentValue),this.$emit("change",this.currentValue)},promptLabel(){return this.query}}},Y={class:"relative mt-2"},$=["innerHTML"],tt={key:0,class:"mt-1 mx-2 text-xs text-gray-500"},et={key:1,class:"mt-1 text-xs text-red-600"};function st(e,s,a,_,n,d){const L=c("ComboboxInput"),p=c("ComboboxButton"),V=c("ChevronUpDownIcon"),F=c("CheckIcon"),o=c("ComboboxOption"),f=c("ComboboxOptions"),U=c("Combobox");return l(),r("div",null,[u(U,{as:"div",modelValue:n.currentValue,"onUpdate:modelValue":s[0]||(s[0]=h=>n.currentValue=h)},{default:x(()=>[t("div",Y,[u(p,{class:"w-full"},{default:x(()=>[u(L,{class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6",onChange:d.change,displayValue:d.promptLabel,placeholder:a.placeholder},null,8,["onChange","displayValue","placeholder"])]),_:1}),u(p,{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:x(()=>[u(V,{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),d.filteredItems.length>0?(l(),C(f,{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:x(()=>[(l(!0),r(y,null,k(d.filteredItems,h=>(l(),C(o,{key:h[a.valueFieldName],value:h.id,as:"template"},{default:x(({active:S,selected:I})=>[t("li",{class:w(["relative cursor-default select-none py-2 pl-8 pr-4",S?"bg-blue-600 text-white":"text-gray-900"])},[t("span",{class:w(["block truncate",I&&"font-semibold"])},i(h[this.labelFieldName]),3),I?(l(),r("span",{key:0,class:w(["absolute inset-y-0 left-0 flex items-center pl-1.5",S?"text-white":"text-blue-600"])},[u(F,{class:"h-5 w-5","aria-hidden":"true"})],2)):m("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):m("",!0)])]),_:1},8,["modelValue"]),t("p",{innerHTML:d.selectedItemsLabel,class:"mt-2 mx-2 text-sm"},null,8,$),a.instruction?(l(),r("p",tt,i(a.instruction),1)):m("",!0),a.error?(l(),r("p",et,i(a.error),1)):m("",!0)])}const nt=z(W,[["render",st]]),ot={components:{Head:D,Link:N,SBXComboBoxSingleSelection:nt,SBXAlert:G,AssignmentFileRow:K,CheckIcon:B,Multiselect:Q},props:{translationCategories:Object,translationLanguages:Object,errors:Object},layout:null,mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:q("post",this.route("assignments.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{translation_category_id:null,from_translation_language_id:null,to_translation_language_id:null,is_authorization_required:!1,number_of_words:null,assignment_type:"personal",first_name:null,email:null,phone_no:null,notes:null,is_email_contact_allowed:!0,is_phone_contact_allowed:!0,assignment_files:[]}),assignmentSubmitted:!1,authorizedTextExpanded:!1,assignmentTypes:[{id:"personal",title:this.$t("translations.translation_assignments.personal_label")},{id:"company",title:this.$t("translations.translation_assignments.company_label")}],termsAccepted:!1,translation_data:{}}},computed:{pageTitle(){return`${this.$t("translations.translation_assignments.create_title")}`},categoryOptions(){var e=[];e.push({value:null,text:this.$t("translations.translation_assignments.category_prompt"),disabled:!0});for(var s=0;s<this.translationCategories.data.length;s++){let a=this.translationCategories.data[s];e.push({value:a.id,text:a.name})}return e},languageOptions(){var e=[];e.push({value:null,text:this.$t("translations.translation_assignments.language_prompt"),disabled:!0});for(var s=0;s<this.translationLanguages.data.length;s++){let a=this.translationLanguages.data[s];e.push({value:a.id,text:a.name})}return e},fromLanguageOptions(){var e=[];e.push({value:null,text:this.$t("translations.translation_assignments.from_language_label"),disabled:!0});for(var s=0;s<this.translationLanguages.data.length;s++){let a=this.translationLanguages.data[s];e.push({value:a.id,text:a.name})}return e},toLanguageOptions(){var e=[];e.push({value:null,text:this.$t("translations.translation_assignments.to_language_label"),disabled:!0});for(var s=0;s<this.translationLanguages.data.length;s++){let a=this.translationLanguages.data[s];e.push({value:a.id,text:a.name})}return e}},methods:{addFile(){this.form.assignment_files.push({id:this.form.assignment_files.length+1,file:null})},removeFile(e){for(var s=-1,a=0;a<this.form.assignment_files.length;a++)this.form.assignment_files[a].id==e&&(s=a);s!=-1&&this.form.assignment_files.splice(s,1)},updateFile(e,s){for(var a=0;a<this.form.assignment_files.length;a++){var _=this.form.assignment_files[a];_.id==e&&(_.file=s)}},create(){this.$gtm?this.$gtm.trackEvent({event:"kontakt_pris",category:"category",action:"action"}):console.error("$gtm is not available");var e=this;console.log("from_translation_language_id",this.form.from_translation_language_id),console.log("translation_data",this.translation_data),(this.translation_data.from_translation_language_id!=null||this.translation_data.to_translation_language_id!=null)&&(this.form.from_translation_language_id=this.translation_data.from_translation_language_id.id,this.form.to_translation_language_id=this.translation_data.to_translation_language_id.id),this.form.processing||this.form.submit({preserveScroll:!0,onSuccess:s=>{e.assignmentSubmitted=!0,e.form.translation_category_id=null,e.form.from_translation_language_id=null,e.form.to_translation_language_id=null,e.form.is_authorization_required=!1,e.form.number_of_words=null,e.form.assignment_type="company",e.form.first_name=null,e.form.email=null,e.form.phone_no=null,e.form.notes=null,e.form.is_email_contact_allowed=!0,e.form.is_phone_contact_allowed=!0,e.form.assignment_files=[],e.termsAccepted=!1,e.form.reset(),e.translation_data={},setTimeout(function(){e.assignmentSubmitted=!1},3e3)}})}}},at={class:"bg-oversattare-green-light"},lt={class:"relative overflow-hidden"},rt={class:"relative bg-oversattare-green-light py-4"},it={class:"relative"},mt={class:"mx-auto px-4 mb-16 sm:px-6 lg:px-8"},dt={class:"text-2xl text-center font-bold tracking-tight text-gray-900 sm:text-4xl"},ct={class:"mt-2 text-xl text-center tracking-tight text-gray-900"},ut={class:"mt-8 mx-2 sm:mx-6"},gt={class:"grid grid-cols-8 sm:gap-4 gap-2"},_t={class:"col-span-8 md:col-start-3 md:col-span-2 sm:col-start-3 sm:col-span-4"},pt={key:0,class:"mt-1 text-xs text-red-500"},ft={class:"col-span-8 md:col-span-2 sm:col-start-3 sm:col-span-4"},ht={key:0,class:"mt-1 text-xs text-red-500"},xt={class:"mt-2 mx-2 sm:mx-6"},bt={class:"grid grid-cols-8 gap-4"},vt=["value","disabled"],yt={class:"grid grid-cols-8 gap-4"},kt={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},wt={class:"mt-2 mx-2 sm:mx-6"},Ct={class:"grid grid-cols-8 gap-4"},Lt=["placeholder"],Vt={class:"grid grid-cols-8 gap-4"},Ft={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},St={class:"mx-2 sm:mx-6"},It={class:"grid grid-cols-8 gap-4"},Mt={class:"col-span-8 sm:col-start-3 sm:col-span-4 mt-2"},At={class:"mb-2 mx-2 sm:mx-6"},zt={class:"grid grid-cols-8 gap-4"},Ut={key:0,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Dt={key:1,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Nt={key:2,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Ot={key:3,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},qt={key:4,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Bt={key:5,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Rt={key:6,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Tt={key:7,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Et={key:8,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},jt={key:9,class:"mt-1 col-start-3 col-span-4 text-xs text-red-500"},Ht={class:"mx-2 sm:mx-6"},Xt={class:"grid grid-cols-8 gap-4"},Pt={class:"grid grid-cols-8 gap-4"},Zt={class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-gray-900"},Gt={class:"grid grid-cols-8 gap-4"},Jt={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},Kt={class:"mt-4 mx-2 sm:mx-6"},Qt={class:"grid grid-cols-8 gap-4"},Wt={class:"p-2 col-span-8 sm:col-start-3 sm:col-span-4 border border-gray-300"},Yt={class:"crelative flex items-start"},$t={class:"flex h-6 items-center"},te={class:"ml-3 text-sm leading-6"},ee={for:"terms",class:"font-medium text-gray-900"},se={class:"text-xs text-gray-900"},ne={key:0,class:"mt-2 text-xs text-gray-900"},oe={key:1,class:"mt-2 text-xs text-gray-900"},ae={class:"mt-2 mx-2 sm:mx-6"},le={class:"grid grid-cols-8 gap-4"},re={class:"mt-4 col-span-8 sm:col-start-3 sm:col-span-4"},ie={class:"space-y-0 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},me=["id","checked","onClick"],de=["onClick","for"],ce={class:"grid grid-cols-8 gap-4"},ue={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},ge={class:"mt-2 mx-2 sm:mx-6"},_e={class:"grid grid-cols-8 gap-4"},pe=["placeholder"],fe={class:"grid grid-cols-8 gap-4"},he={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},xe={class:"mt-2 mx-2 sm:mx-6"},be={class:"grid grid-cols-8 gap-2 sm:gap-4"},ve={class:"col-span-8 sm:col-start-3 sm:col-span-2"},ye=["placeholder"],ke={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},we={class:"col-span-8 sm:col-span-2"},Ce=["placeholder"],Le={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},Ve={class:"mt-2 mx-2 sm:mx-6"},Fe={class:"grid grid-cols-8 gap-4"},Se=["placeholder"],Ie={class:"grid grid-cols-8 gap-4"},Me={key:0,class:"mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500"},Ae={class:"mt-4 mx-2 sm:mx-6"},ze={class:"grid grid-cols-8 gap-4"},Ue={class:"col-span-8 sm:col-start-3 sm:col-span-4"},De={class:"space-y-5"},Ne={class:"relative flex items-start"},Oe={class:"flex h-6 items-center"},qe={class:"ml-3 text-sm leading-6"},Be={for:"terms",class:"font-medium text-gray-900"},Re={class:"text-blue-700 select-none"},Te={href:"https://www.oversattare.nu/anvandarvillkor/",target:"_blank"},Ee={class:"text-gray-900 select-none"},je={class:"text-blue-700 select-none"},He={href:"https://www.oversattare.nu/integretetspolicy/",target:"_blank"},Xe={class:"mx-2 sm:mx-6"},Pe={class:"grid grid-cols-8 gap-4"},Ze=["disabled"],Ge={class:"flex w-full justify-center"},Je={class:"mx-2 sm:mx-6"},Ke={class:"grid grid-cols-8 gap-4"};function Qe(e,s,a,_,n,d){const L=c("Head"),p=c("multiselect"),V=c("AssignmentFileRow"),F=c("SBXAlert");return l(),r("div",at,[u(L,{title:"Översättare.nu | Skicka uppdrag"}),t("div",lt,[t("main",null,[t("div",rt,[t("div",it,[t("div",mt,[t("p",dt,i(e.$t("translations.assignments.title")),1),t("p",ct,i(e.$t("translations.assignments.subtitle")),1),t("form",{onSubmit:s[18]||(s[18]=M((...o)=>d.create&&d.create(...o),["prevent"]))},[t("div",ut,[t("div",gt,[t("div",_t,[u(p,{modelValue:n.translation_data.from_translation_language_id,"onUpdate:modelValue":s[0]||(s[0]=o=>n.translation_data.from_translation_language_id=o),noResult:e.$t("translations.translation_assignments.no_language_found_label"),placeholder:e.$t("translations.translation_assignments.from_language_search_label"),selectLabel:e.$t("translations.translation_assignments.select_language_label"),deselectLabel:e.$t("translations.translation_assignments.deselect_language_label"),selectedLabel:e.$t("translations.translation_assignments.selected_language_label"),label:"name","track-by":"id",options:a.translationLanguages.data,multiple:!1,taggable:!1,showNoResults:!1,class:"block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","selectedLabel","options"]),n.form.invalid("from_translation_language_id")?(l(),r("p",pt,i(n.form.errors.from_translation_language_id),1)):m("",!0)]),t("div",ft,[u(p,{modelValue:n.translation_data.to_translation_language_id,"onUpdate:modelValue":s[1]||(s[1]=o=>n.translation_data.to_translation_language_id=o),noResult:e.$t("translations.translation_assignments.no_language_found_label"),placeholder:e.$t("translations.translation_assignments.to_language_search_label"),selectLabel:e.$t("translations.translation_assignments.select_language_label"),deselectLabel:e.$t("translations.translation_assignments.deselect_language_label"),selectedLabel:e.$t("translations.translation_assignments.selected_language_label"),label:"name","track-by":"id",options:a.translationLanguages.data,multiple:!1,taggable:!1,showNoResults:!1,class:"block w-full text-gray-900"},null,8,["modelValue","noResult","placeholder","selectLabel","deselectLabel","selectedLabel","options"]),n.form.invalid("to_translation_language_id")?(l(),r("p",ht,i(n.form.errors.to_translation_language_id),1)):m("",!0)])])]),t("div",xt,[t("div",bt,[g(t("select",{"onUpdate:modelValue":s[2]||(s[2]=o=>n.form.translation_category_id=o),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(l(!0),r(y,null,k(d.categoryOptions,o=>(l(),r("option",{value:o.value,disabled:o.hasOwnProperty("disabled")},i(o.text),9,vt))),256))],512),[[O,n.form.translation_category_id]])]),t("div",yt,[n.form.invalid("translation_category_id")?(l(),r("p",kt,i(n.form.errors.translation_category_id),1)):m("",!0)])]),t("div",wt,[t("div",Ct,[g(t("input",{type:"text","onUpdate:modelValue":s[3]||(s[3]=o=>n.form.number_of_words=o),onChange:s[4]||(s[4]=o=>n.form.validate("number_of_words")),placeholder:e.$t("translations.translation_assignments.number_of_words_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,Lt),[[b,n.form.number_of_words]])]),t("div",Vt,[n.form.invalid("number_of_words")?(l(),r("p",Ft,i(n.form.errors.number_of_words),1)):m("",!0)])]),t("div",St,[t("div",It,[t("div",Mt,[(l(!0),r(y,null,k(n.form.assignment_files,(o,f)=>(l(),C(V,{file:o,key:o.id,"row-no":f,onRemoveFile:d.removeFile,onUpdateFile:d.updateFile},null,8,["file","row-no","onRemoveFile","onUpdateFile"]))),128))])])]),t("div",At,[t("div",zt,[a.errors["assignment_files.0.file"]?(l(),r("p",Ut,"Dokument 1 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.1.file"]?(l(),r("p",Dt,"Dokument 2 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.2.file"]?(l(),r("p",Nt,"Dokument 3 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.3.file"]?(l(),r("p",Ot,"Dokument 4 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.4.file"]?(l(),r("p",qt,"Dokument 5 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.5.file"]?(l(),r("p",Bt,"Dokument 6 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.6.file"]?(l(),r("p",Rt,"Dokument 7 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.7.file"]?(l(),r("p",Tt,"Dokument 8 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.8.file"]?(l(),r("p",Et,"Dokument 9 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0),a.errors["assignment_files.9.file"]?(l(),r("p",jt,"Dokument 10 måste vara i ett giltigt format och får inte vara större än 50Mb.")):m("",!0)])]),t("div",Ht,[t("div",Xt,[t("button",{onClick:s[5]||(s[5]=M((...o)=>d.addFile&&d.addFile(...o),["prevent"])),class:"col-span-8 sm:col-start-3 sm:col-span-4 inline-flex justify-center border border-transparent shadow-sm text-sm font-medium rounded-md text-white py-2 px-4 bg-oversattare-orange hover:bg-oversattare-orange focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-oversattare-orange"},i(e.$t("translations.translation_assignments.add_file_button")),1)]),t("div",Pt,[t("p",Zt,i(e.$t("translations.translation_assignments.file_instructions")),1)]),t("div",Gt,[n.form.invalid("assignment_files")?(l(),r("p",Jt,i(n.form.errors.assignment_files),1)):m("",!0)])]),t("div",Kt,[t("div",Qt,[t("fieldset",Wt,[t("div",Yt,[t("div",$t,[g(t("input",{"onUpdate:modelValue":s[6]||(s[6]=o=>n.form.is_authorization_required=o),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"},null,512),[[A,n.form.is_authorization_required]])]),t("div",te,[t("label",ee,i(e.$t("translations.translation_assignments.is_authorization_required_label")),1),t("p",se,[v(i(e.$t("translations.translation_assignments.authorization_description_short"))+" ",1),n.authorizedTextExpanded?m("",!0):(l(),r("span",{key:0,onClick:s[7]||(s[7]=o=>n.authorizedTextExpanded=!0),class:"text-xs font-medium underline select-none cursor-pointer"},i(e.$t("translations.translation_assignments.read_more_button")),1))]),n.authorizedTextExpanded?(l(),r("p",ne,i(e.$t("translations.translation_assignments.authorization_description_long_1")),1)):m("",!0),n.authorizedTextExpanded?(l(),r("p",oe,[v(i(e.$t("translations.translation_assignments.authorization_description_long_2"))+" ",1),t("span",{onClick:s[8]||(s[8]=o=>n.authorizedTextExpanded=!1),class:"text-xs font-medium underline select-none cursor-pointer"},i(e.$t("translations.translation_assignments.read_less_button")),1)])):m("",!0)])])])])]),t("div",ae,[t("div",le,[t("fieldset",re,[t("div",ie,[(l(!0),r(y,null,k(n.assignmentTypes,o=>(l(),r("div",{key:o.id,class:"flex items-center"},[t("input",{id:o.id,name:"notification-method",type:"radio",checked:n.form.assignment_type==o.id,class:"h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-600",onClick:f=>n.form.assignment_type=o.id},null,8,me),t("label",{onClick:f=>n.form.assignment_type=o.id,for:o.id,class:"ml-3 block text-sm text-gray-900 font-medium leading-6 text-gray-900"},i(o.title),9,de)]))),128))])])]),t("div",ce,[n.form.invalid("assignment_type")?(l(),r("p",ue,i(n.form.errors.assignment_type),1)):m("",!0)])]),t("div",ge,[t("div",_e,[g(t("input",{type:"text","onUpdate:modelValue":s[9]||(s[9]=o=>n.form.first_name=o),onChange:s[10]||(s[10]=o=>n.form.validate("first_name")),placeholder:e.$t("translations.translation_assignments.name_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,pe),[[b,n.form.first_name]])]),t("div",fe,[n.form.invalid("first_name")?(l(),r("p",he,i(n.form.errors.first_name),1)):m("",!0)])]),t("div",xe,[t("div",be,[t("div",ve,[g(t("input",{type:"text","onUpdate:modelValue":s[11]||(s[11]=o=>n.form.email=o),onChange:s[12]||(s[12]=o=>n.form.validate("email")),placeholder:e.$t("translations.translation_assignments.email_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,ye),[[b,n.form.email]]),n.form.invalid("email")?(l(),r("p",ke,i(n.form.errors.email),1)):m("",!0)]),t("div",we,[g(t("input",{type:"text","onUpdate:modelValue":s[13]||(s[13]=o=>n.form.phone_no=o),onChange:s[14]||(s[14]=o=>n.form.validate("phone_no")),placeholder:e.$t("translations.translation_assignments.phone_no_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,Ce),[[b,n.form.phone_no]]),n.form.invalid("phone_no")?(l(),r("p",Le,i(n.form.errors.phone_no),1)):m("",!0)])])]),t("div",Ve,[t("div",Fe,[g(t("textarea",{"onUpdate:modelValue":s[15]||(s[15]=o=>n.form.notes=o),onChange:s[16]||(s[16]=o=>n.form.validate("notes")),rows:"4",placeholder:e.$t("translations.translation_assignments.notes_label"),class:"col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"},null,40,Se),[[b,n.form.notes]])]),t("div",Ie,[n.form.invalid("notes")?(l(),r("p",Me,i(n.form.errors.notes),1)):m("",!0)])]),t("div",Ae,[t("div",ze,[t("fieldset",Ue,[t("div",De,[t("div",Ne,[t("div",Oe,[g(t("input",{"onUpdate:modelValue":s[17]||(s[17]=o=>n.termsAccepted=o),id:"terms","aria-describedby":"terms-description",name:"terms",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600"},null,512),[[A,n.termsAccepted]])]),t("div",qe,[t("label",Be,i(e.$t("translations.translation_assignments.terms_1")),1),s[20]||(s[20]=v(" "+i(" ")+" ")),t("span",Re,[t("a",Te,i(e.$t("translations.translation_assignments.terms_2")),1)]),t("span",Ee,i(e.$t("translations.translation_assignments.terms_3")),1),t("span",je,[t("a",He,i(e.$t("translations.translation_assignments.terms_4")),1),s[19]||(s[19]=v("."))])])])])])])]),t("div",Xe,[t("div",Pe,[t("button",{type:"submit",class:w([{"w-full bg-oversattare-green hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-oversattare-green col-span-8 sm:col-start-3 sm:col-span-4":n.termsAccepted,"bg-gray-400 cursor-not-allowed":!n.termsAccepted},"mt-4 inline-flex items-center gap-x-2 rounded-md py-2.5 px-3.5 text-sm font-semibold text-white shadow-sm col-span-8 sm:col-start-3 sm:col-span-4"]),disabled:!n.termsAccepted},[t("div",Ge,i(e.$t("translations.translation_assignments.send_button")),1)],10,Ze)])]),t("div",Je,[t("div",Ke,[n.assignmentSubmitted?(l(),C(F,{key:0,title:e.$t("translations.assignments.confirmation_title"),message:e.$t("translations.assignments.confirmation_message"),class:"mt-4 col-span-8 sm:col-start-3 sm:col-span-4"},null,8,["title","message"])):m("",!0)])])],32)])])])])])])}const ms=z(ot,[["render",Qe]]);export{ms as default};
