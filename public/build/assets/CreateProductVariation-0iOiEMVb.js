import{_ as c,S as d,c as f,w as n,r as o,b as _,e as g,d as p,g as b,t as h,k as B}from"./app-Cm2beRkj.js";import{S}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as X}from"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const D={components:{SBXDefaultPageLayout:S,SBXInput:X,SBXButton:d},props:{productID:Number,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("productID: ",this.productID)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:""})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.product_variations.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("product_variations.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productID]))}}};function $(r,e,i,I,a,s){const l=o("SBXInput"),m=o("SBXButton"),u=o("SBXDefaultPageLayout");return _(),f(u,null,{default:n(()=>[g("form",{onSubmit:e[1]||(e[1]=B((...t)=>s.create&&s.create(...t),["prevent"]))},[p(l,{model:a.form.name,"onUpdate:model":e[0]||(e[0]=t=>a.form.name=t),label:r.$t("sbxwebshop.product_variations.name_label"),error:i.errors.name},null,8,["model","label","error"]),p(m,{class:"mt-4"},{default:n(()=>[b(h(r.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const y=c(D,[["render",$]]);export{y as default};
