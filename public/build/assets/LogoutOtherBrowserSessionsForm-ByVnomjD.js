import{s as y,ac as k,c as h,w as t,b as a,g as n,e,h as l,F as x,i as b,t as u,j as B,d as i,C as d,aB as C,n as S}from"./app-Cm2beRkj.js";import{_ as L}from"./ActionMessage-B_UncuA7.js";import{a as M,b as O}from"./DialogModal-CZeDHxad.js";import{_ as V,a as $}from"./TextInput-Dx25uC_E.js";import{_ as g}from"./PrimaryButton-7FvKQ4S5.js";import{_ as F}from"./SecondaryButton-Cr6mqfK5.js";import"./SectionTitle-zHGmFabz.js";const I={key:0,class:"mt-5 space-y-6"},N={key:0,fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-8 h-8 text-gray-500"},T={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round",class:"w-8 h-8 text-gray-500"},j={class:"ml-3"},z={class:"text-sm text-gray-600"},U={class:"text-xs text-gray-500"},D={key:0,class:"text-green-500 font-semibold"},E={key:1},H={class:"flex items-center mt-5"},K={class:"mt-4"},X={__name:"LogoutOtherBrowserSessionsForm",props:{sessions:Array},setup(p){const m=y(!1),c=y(null),r=k({password:""}),v=()=>{m.value=!0,setTimeout(()=>c.value.focus(),250)},w=()=>{r.delete(route("other-browser-sessions.destroy"),{preserveScroll:!0,onSuccess:()=>f(),onError:()=>c.value.focus(),onFinish:()=>r.reset()})},f=()=>{m.value=!1,r.reset()};return(P,s)=>(a(),h(M,null,{title:t(()=>s[1]||(s[1]=[n(" Browser Sessions ")])),description:t(()=>s[2]||(s[2]=[n(" Manage and log out your active sessions on other browsers and devices. ")])),content:t(()=>[s[11]||(s[11]=e("div",{class:"max-w-xl text-sm text-gray-600"}," If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password. ",-1)),p.sessions.length>0?(a(),l("div",I,[(a(!0),l(x,null,b(p.sessions,(o,_)=>(a(),l("div",{key:_,class:"flex items-center"},[e("div",null,[o.agent.is_desktop?(a(),l("svg",N,s[3]||(s[3]=[e("path",{d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"},null,-1)]))):(a(),l("svg",T,s[4]||(s[4]=[e("path",{d:"M0 0h24v24H0z",stroke:"none"},null,-1),e("rect",{x:"7",y:"4",width:"10",height:"16",rx:"1"},null,-1),e("path",{d:"M11 5h2M12 17v.01"},null,-1)])))]),e("div",j,[e("div",z,u(o.agent.platform?o.agent.platform:"Unknown")+" - "+u(o.agent.browser?o.agent.browser:"Unknown"),1),e("div",null,[e("div",U,[n(u(o.ip_address)+", ",1),o.is_current_device?(a(),l("span",D,"This device")):(a(),l("span",E,"Last active "+u(o.last_active),1))])])])]))),128))])):B("",!0),e("div",H,[i(g,{onClick:v},{default:t(()=>s[5]||(s[5]=[n(" Log Out Other Browser Sessions ")])),_:1}),i(L,{on:d(r).recentlySuccessful,class:"ml-3"},{default:t(()=>s[6]||(s[6]=[n(" Done. ")])),_:1},8,["on"])]),i(O,{show:m.value,onClose:f},{title:t(()=>s[7]||(s[7]=[n(" Log Out Other Browser Sessions ")])),content:t(()=>[s[8]||(s[8]=n(" Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices. ")),e("div",K,[i(V,{ref_key:"passwordInput",ref:c,modelValue:d(r).password,"onUpdate:modelValue":s[0]||(s[0]=o=>d(r).password=o),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:C(w,["enter"])},null,8,["modelValue"]),i($,{message:d(r).errors.password,class:"mt-2"},null,8,["message"])])]),footer:t(()=>[i(F,{onClick:f},{default:t(()=>s[9]||(s[9]=[n(" Cancel ")])),_:1}),i(g,{class:S(["ml-3",{"opacity-25":d(r).processing}]),disabled:d(r).processing,onClick:w},{default:t(()=>s[10]||(s[10]=[n(" Log Out Other Browser Sessions ")])),_:1},8,["class","disabled"])]),_:1},8,["show"])]),_:1}))}};export{X as default};
