import{ak as ct}from"./app-Cm2beRkj.js";const{Axios:yn,AxiosError:bn,CanceledError:_n,isCancel:$t,CancelToken:mn,VERSION:Tn,all:On,Cancel:jn,isAxiosError:Et,spread:Pn,toFormData:wn,AxiosHeaders:Sn,HttpStatusCode:xn,formToJSON:An,getAdapter:Cn,mergeConfig:$n}=ct;var lt=typeof global=="object"&&global&&global.Object===Object&&global,Ft=typeof self=="object"&&self&&self.Object===Object&&self,g=lt||Ft||Function("return this")(),S=g.Symbol,ft=Object.prototype,It=ft.hasOwnProperty,Ut=ft.toString,_=S?S.toStringTag:void 0;function Mt(t){var e=It.call(t,_),r=t[_];try{t[_]=void 0;var n=!0}catch{}var a=Ut.call(t);return n&&(e?t[_]=r:delete t[_]),a}var Ht=Object.prototype,Rt=Ht.toString;function zt(t){return Rt.call(t)}var Nt="[object Null]",Dt="[object Undefined]",J=S?S.toStringTag:void 0;function C(t){return t==null?t===void 0?Dt:Nt:J&&J in Object(t)?Mt(t):zt(t)}function w(t){return t!=null&&typeof t=="object"}var z=Array.isArray;function h(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}function pt(t){return t}var Lt="[object AsyncFunction]",Bt="[object Function]",Gt="[object GeneratorFunction]",qt="[object Proxy]";function B(t){if(!h(t))return!1;var e=C(t);return e==Bt||e==Gt||e==Lt||e==qt}var M=g["__core-js_shared__"],X=function(){var t=/[^.]+$/.exec(M&&M.keys&&M.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Kt(t){return!!X&&X in t}var Vt=Function.prototype,Jt=Vt.toString;function Xt(t){if(t!=null){try{return Jt.call(t)}catch{}try{return t+""}catch{}}return""}var Wt=/[\\^$.*+?()[\]{}|]/g,Yt=/^\[object .+?Constructor\]$/,Zt=Function.prototype,Qt=Object.prototype,kt=Zt.toString,te=Qt.hasOwnProperty,ee=RegExp("^"+kt.call(te).replace(Wt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function re(t){if(!h(t)||Kt(t))return!1;var e=B(t)?ee:Yt;return e.test(Xt(t))}function ne(t,e){return t==null?void 0:t[e]}function G(t,e){var r=ne(t,e);return re(r)?r:void 0}var W=Object.create,ae=function(){function t(){}return function(e){if(!h(e))return{};if(W)return W(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();function oe(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}function ie(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}var se=800,ue=16,ce=Date.now;function le(t){var e=0,r=0;return function(){var n=ce(),a=ue-(n-r);if(r=n,a>0){if(++e>=se)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}function fe(t){return function(){return t}}var x=function(){try{var t=G(Object,"defineProperty");return t({},"",{}),t}catch{}}(),pe=x?function(t,e){return x(t,"toString",{configurable:!0,enumerable:!1,value:fe(e),writable:!0})}:pt;const de=pe;var he=le(de),ge=9007199254740991,ve=/^(?:0|[1-9]\d*)$/;function dt(t,e){var r=typeof t;return e=e??ge,!!e&&(r=="number"||r!="symbol"&&ve.test(t))&&t>-1&&t%1==0&&t<e}function q(t,e,r){e=="__proto__"&&x?x(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}function $(t,e){return t===e||t!==t&&e!==e}var ye=Object.prototype,be=ye.hasOwnProperty;function _e(t,e,r){var n=t[e];(!(be.call(t,e)&&$(n,r))||r===void 0&&!(e in t))&&q(t,e,r)}function me(t,e,r,n){var a=!r;r||(r={});for(var i=-1,s=e.length;++i<s;){var o=e[i],c=n?n(r[o],t[o],o,r,t):void 0;c===void 0&&(c=t[o]),a?q(r,o,c):_e(r,o,c)}return r}var Y=Math.max;function Te(t,e,r){return e=Y(e===void 0?t.length-1:e,0),function(){for(var n=arguments,a=-1,i=Y(n.length-e,0),s=Array(i);++a<i;)s[a]=n[e+a];a=-1;for(var o=Array(e+1);++a<e;)o[a]=n[a];return o[e]=r(s),oe(t,this,o)}}function Oe(t,e){return he(Te(t,e,pt),t+"")}var je=9007199254740991;function ht(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=je}function K(t){return t!=null&&ht(t.length)&&!B(t)}function Pe(t,e,r){if(!h(r))return!1;var n=typeof e;return(n=="number"?K(r)&&dt(e,r.length):n=="string"&&e in r)?$(r[e],t):!1}function we(t){return Oe(function(e,r){var n=-1,a=r.length,i=a>1?r[a-1]:void 0,s=a>2?r[2]:void 0;for(i=t.length>3&&typeof i=="function"?(a--,i):void 0,s&&Pe(r[0],r[1],s)&&(i=a<3?void 0:i,a=1),e=Object(e);++n<a;){var o=r[n];o&&t(e,o,n,i)}return e})}var Se=Object.prototype;function gt(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||Se;return t===r}function xe(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}var Ae="[object Arguments]";function Z(t){return w(t)&&C(t)==Ae}var vt=Object.prototype,Ce=vt.hasOwnProperty,$e=vt.propertyIsEnumerable,N=Z(function(){return arguments}())?Z:function(t){return w(t)&&Ce.call(t,"callee")&&!$e.call(t,"callee")};function Ee(){return!1}var yt=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Q=yt&&typeof module=="object"&&module&&!module.nodeType&&module,Fe=Q&&Q.exports===yt,k=Fe?g.Buffer:void 0,Ie=k?k.isBuffer:void 0,bt=Ie||Ee,Ue="[object Arguments]",Me="[object Array]",He="[object Boolean]",Re="[object Date]",ze="[object Error]",Ne="[object Function]",De="[object Map]",Le="[object Number]",Be="[object Object]",Ge="[object RegExp]",qe="[object Set]",Ke="[object String]",Ve="[object WeakMap]",Je="[object ArrayBuffer]",Xe="[object DataView]",We="[object Float32Array]",Ye="[object Float64Array]",Ze="[object Int8Array]",Qe="[object Int16Array]",ke="[object Int32Array]",tr="[object Uint8Array]",er="[object Uint8ClampedArray]",rr="[object Uint16Array]",nr="[object Uint32Array]",u={};u[We]=u[Ye]=u[Ze]=u[Qe]=u[ke]=u[tr]=u[er]=u[rr]=u[nr]=!0;u[Ue]=u[Me]=u[Je]=u[He]=u[Xe]=u[Re]=u[ze]=u[Ne]=u[De]=u[Le]=u[Be]=u[Ge]=u[qe]=u[Ke]=u[Ve]=!1;function ar(t){return w(t)&&ht(t.length)&&!!u[C(t)]}function or(t){return function(e){return t(e)}}var _t=typeof exports=="object"&&exports&&!exports.nodeType&&exports,O=_t&&typeof module=="object"&&module&&!module.nodeType&&module,ir=O&&O.exports===_t,H=ir&&lt.process,tt=function(){try{var t=O&&O.require&&O.require("util").types;return t||H&&H.binding&&H.binding("util")}catch{}}(),et=tt&&tt.isTypedArray,mt=et?or(et):ar,sr=Object.prototype,ur=sr.hasOwnProperty;function cr(t,e){var r=z(t),n=!r&&N(t),a=!r&&!n&&bt(t),i=!r&&!n&&!a&&mt(t),s=r||n||a||i,o=s?xe(t.length,String):[],c=o.length;for(var f in t)(e||ur.call(t,f))&&!(s&&(f=="length"||a&&(f=="offset"||f=="parent")||i&&(f=="buffer"||f=="byteLength"||f=="byteOffset")||dt(f,c)))&&o.push(f);return o}function lr(t,e){return function(r){return t(e(r))}}function fr(t){var e=[];if(t!=null)for(var r in Object(t))e.push(r);return e}var pr=Object.prototype,dr=pr.hasOwnProperty;function hr(t){if(!h(t))return fr(t);var e=gt(t),r=[];for(var n in t)n=="constructor"&&(e||!dr.call(t,n))||r.push(n);return r}function Tt(t){return K(t)?cr(t,!0):hr(t)}var j=G(Object,"create");function gr(){this.__data__=j?j(null):{},this.size=0}function vr(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var yr="__lodash_hash_undefined__",br=Object.prototype,_r=br.hasOwnProperty;function mr(t){var e=this.__data__;if(j){var r=e[t];return r===yr?void 0:r}return _r.call(e,t)?e[t]:void 0}var Tr=Object.prototype,Or=Tr.hasOwnProperty;function jr(t){var e=this.__data__;return j?e[t]!==void 0:Or.call(e,t)}var Pr="__lodash_hash_undefined__";function wr(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=j&&e===void 0?Pr:e,this}function d(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}d.prototype.clear=gr;d.prototype.delete=vr;d.prototype.get=mr;d.prototype.has=jr;d.prototype.set=wr;function Sr(){this.__data__=[],this.size=0}function E(t,e){for(var r=t.length;r--;)if($(t[r][0],e))return r;return-1}var xr=Array.prototype,Ar=xr.splice;function Cr(t){var e=this.__data__,r=E(e,t);if(r<0)return!1;var n=e.length-1;return r==n?e.pop():Ar.call(e,r,1),--this.size,!0}function $r(t){var e=this.__data__,r=E(e,t);return r<0?void 0:e[r][1]}function Er(t){return E(this.__data__,t)>-1}function Fr(t,e){var r=this.__data__,n=E(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this}function p(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}p.prototype.clear=Sr;p.prototype.delete=Cr;p.prototype.get=$r;p.prototype.has=Er;p.prototype.set=Fr;var Ot=G(g,"Map");function Ir(){this.size=0,this.__data__={hash:new d,map:new(Ot||p),string:new d}}function Ur(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}function F(t,e){var r=t.__data__;return Ur(e)?r[typeof e=="string"?"string":"hash"]:r.map}function Mr(t){var e=F(this,t).delete(t);return this.size-=e?1:0,e}function Hr(t){return F(this,t).get(t)}function Rr(t){return F(this,t).has(t)}function zr(t,e){var r=F(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this}function v(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}v.prototype.clear=Ir;v.prototype.delete=Mr;v.prototype.get=Hr;v.prototype.has=Rr;v.prototype.set=zr;var Nr=lr(Object.getPrototypeOf,Object);const jt=Nr;var Dr="[object Object]",Lr=Function.prototype,Br=Object.prototype,Pt=Lr.toString,Gr=Br.hasOwnProperty,qr=Pt.call(Object);function Kr(t){if(!w(t)||C(t)!=Dr)return!1;var e=jt(t);if(e===null)return!0;var r=Gr.call(e,"constructor")&&e.constructor;return typeof r=="function"&&r instanceof r&&Pt.call(r)==qr}function Vr(){this.__data__=new p,this.size=0}function Jr(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}function Xr(t){return this.__data__.get(t)}function Wr(t){return this.__data__.has(t)}var Yr=200;function Zr(t,e){var r=this.__data__;if(r instanceof p){var n=r.__data__;if(!Ot||n.length<Yr-1)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new v(n)}return r.set(t,e),this.size=r.size,this}function y(t){var e=this.__data__=new p(t);this.size=e.size}y.prototype.clear=Vr;y.prototype.delete=Jr;y.prototype.get=Xr;y.prototype.has=Wr;y.prototype.set=Zr;var wt=typeof exports=="object"&&exports&&!exports.nodeType&&exports,rt=wt&&typeof module=="object"&&module&&!module.nodeType&&module,Qr=rt&&rt.exports===wt,nt=Qr?g.Buffer:void 0,at=nt?nt.allocUnsafe:void 0;function kr(t,e){if(e)return t.slice();var r=t.length,n=at?at(r):new t.constructor(r);return t.copy(n),n}var ot=g.Uint8Array;function tn(t){var e=new t.constructor(t.byteLength);return new ot(e).set(new ot(t)),e}function en(t,e){var r=e?tn(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}function rn(t){return typeof t.constructor=="function"&&!gt(t)?ae(jt(t)):{}}function nn(t){return function(e,r,n){for(var a=-1,i=Object(e),s=n(e),o=s.length;o--;){var c=s[t?o:++a];if(r(i[c],c,i)===!1)break}return e}}var an=nn();function D(t,e,r){(r!==void 0&&!$(t[e],r)||r===void 0&&!(e in t))&&q(t,e,r)}function on(t){return w(t)&&K(t)}function L(t,e){if(!(e==="constructor"&&typeof t[e]=="function")&&e!="__proto__")return t[e]}function sn(t){return me(t,Tt(t))}function un(t,e,r,n,a,i,s){var o=L(t,r),c=L(e,r),f=s.get(c);if(f){D(t,r,f);return}var l=i?i(o,c,r+"",t,e,s):void 0,b=l===void 0;if(b){var I=z(c),U=!I&&bt(c),V=!I&&!U&&mt(c);l=c,I||U||V?z(o)?l=o:on(o)?l=ie(o):U?(b=!1,l=kr(c,!0)):V?(b=!1,l=en(c,!0)):l=[]:Kr(c)||N(c)?(l=o,N(o)?l=sn(o):(!h(o)||B(o))&&(l=rn(c))):b=!1}b&&(s.set(c,l),a(l,c,n,i,s),s.delete(c)),D(t,r,l)}function St(t,e,r,n,a){t!==e&&an(e,function(i,s){if(a||(a=new y),h(i))un(t,e,s,r,St,n,a);else{var o=n?n(L(t,s),i,s+"",t,e,a):void 0;o===void 0&&(o=i),D(t,s,o)}},Tt)}var it=we(function(t,e,r){St(t,e,r)});let P=ct.create(),xt=(t,e)=>`${t.method}:${t.baseURL??e.defaults.baseURL??""}${t.url}`,At=t=>t.status===204&&t.headers["precognition-success"]==="true";const A={},R={get:(t,e={},r={})=>T(m("get",t,e,r)),post:(t,e={},r={})=>T(m("post",t,e,r)),patch:(t,e={},r={})=>T(m("patch",t,e,r)),put:(t,e={},r={})=>T(m("put",t,e,r)),delete:(t,e={},r={})=>T(m("delete",t,e,r)),use(t){return P=t,R},axios(){return P},fingerprintRequestsUsing(t){return xt=t===null?()=>null:t,R},determineSuccessUsing(t){return At=t,R}},m=(t,e,r,n)=>({url:e,method:t,...n,...["get","delete"].includes(t)?{params:it({},r,n==null?void 0:n.params)}:{data:it({},r,n==null?void 0:n.data)}}),T=(t={})=>{const e=[cn,fn,pn].reduce((r,n)=>n(r),t);return(e.onBefore??(()=>!0))()===!1?Promise.resolve(null):((e.onStart??(()=>null))(),P.request(e).then(async r=>{e.precognitive&&st(r);const n=r.status;let a=r;return e.precognitive&&e.onPrecognitionSuccess&&At(a)&&(a=await Promise.resolve(e.onPrecognitionSuccess(a)??a)),e.onSuccess&&ln(n)&&(a=await Promise.resolve(e.onSuccess(a)??a)),(ut(e,n)??(s=>s))(a)??a},r=>dn(r)?Promise.reject(r):(e.precognitive&&st(r.response),(ut(e,r.response.status)??((a,i)=>Promise.reject(i)))(r.response,r))).finally(e.onFinish??(()=>null)))},cn=t=>({...t,timeout:t.timeout??P.defaults.timeout??3e4,precognitive:t.precognitive!==!1,fingerprint:typeof t.fingerprint>"u"?xt(t,P):t.fingerprint,headers:{...t.headers,"Content-Type":hn(t),...t.precognitive!==!1?{Precognition:!0}:{},...t.validate?{"Precognition-Validate-Only":Array.from(t.validate).join()}:{}}}),ln=t=>t>=200&&t<300,fn=t=>{var e;return typeof t.fingerprint!="string"||((e=A[t.fingerprint])==null||e.abort(),delete A[t.fingerprint]),t},pn=t=>typeof t.fingerprint!="string"||t.signal||t.cancelToken?t:(A[t.fingerprint]=new AbortController,{...t,signal:A[t.fingerprint].signal}),st=t=>{var e;if(((e=t.headers)==null?void 0:e.precognition)!=="true")throw Error("Did not receive a Precognition response. Ensure you have the Precognition middleware in place for the route.")},dn=t=>{var e;return!Et(t)||typeof((e=t.response)==null?void 0:e.status)!="number"||$t(t)},ut=(t,e)=>({401:t.onUnauthorized,403:t.onForbidden,404:t.onNotFound,409:t.onConflict,422:t.onValidationError,423:t.onLocked})[e],hn=t=>{var e,r,n;return((e=t.headers)==null?void 0:e["Content-Type"])??((r=t.headers)==null?void 0:r["Content-type"])??((n=t.headers)==null?void 0:n["content-type"])??(Ct(t.data)?"multipart/form-data":"application/json")},Ct=t=>gn(t)||typeof t=="object"&&t!==null&&Object.values(t).some(e=>Ct(e)),gn=t=>typeof File<"u"&&t instanceof File||t instanceof Blob||typeof FileList<"u"&&t instanceof FileList&&t.length>0,En=t=>typeof t=="string"?t:t(),Fn=t=>typeof t=="string"?t.toLowerCase():t();export{y as A,_e as B,$ as C,mt as D,Kr as E,dt as F,R as G,Et as H,it as I,gn as J,Fn as K,En as L,v as M,S,ot as U,z as a,C as b,h as c,gt as d,K as e,cr as f,G as g,N as h,w as i,Te as j,me as k,Tt as l,jt as m,Ot as n,lr as o,tn as p,en as q,g as r,he as s,Xt as t,tt as u,or as v,ie as w,bt as x,kr as y,rn as z};
