import{h as B,e as A,b as C,p as D,s as h,H as N,am as K,B as c,an as V,ao as T,L as G,ap as E,W as x,P as _,al as g,aq as W,ar as U,O as F,Q as q,V as J,R as Q,T as I,F as X,Y,Z,G as ee,X as y,as as P,at as L,au as k,av as j}from"./app-Cm2beRkj.js";import{K as M}from"./label-D4lfsZnZ.js";import{d as ae,e as le,p as te}from"./use-controllable-D9fh3JbV.js";function pe(e,u){return C(),B("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[A("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"})])}var re=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(re||{});let oe=Symbol("DisclosureContext"),ce=D({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(e,{slots:u,attrs:m}){let w=`headlessui-disclosure-button-${x()}`,p=`headlessui-disclosure-panel-${x()}`,t=h(e.defaultOpen?0:1),n=h(null),R=h(null),s={buttonId:w,panelId:p,disclosureState:t,panel:n,button:R,toggleDisclosure(){t.value=V(t.value,{0:1,1:0})},closeDisclosure(){t.value!==1&&(t.value=1)},close(i){s.closeDisclosure();let v=i?i instanceof HTMLElement?i:i.value instanceof HTMLElement?E(i):E(s.button):E(s.button);v==null||v.focus()}};return N(oe,s),K(c(()=>V(t.value,{0:T.Open,1:T.Closed}))),()=>{let{defaultOpen:i,...v}=e,f={open:t.value===0,close:s.close};return G({theirProps:v,ourProps:{},slot:f,slots:u,attrs:m,name:"Disclosure"})}}});function ne(e,u){return e===u}let $=Symbol("RadioGroupContext");function H(e){let u=ee($,null);if(u===null){let m=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(m,H),m}return u}let ve=D({name:"RadioGroup",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>ne},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},name:{type:String,optional:!0}},inheritAttrs:!1,setup(e,{emit:u,attrs:m,slots:w,expose:p}){let t=h(null),n=h([]),R=M({name:"RadioGroupLabel"}),s=_({name:"RadioGroupDescription"});p({el:t,$el:t});let[i,v]=ae(c(()=>e.modelValue),a=>u("update:modelValue",a),c(()=>e.defaultValue)),f={options:n,value:i,disabled:c(()=>e.disabled),firstOption:c(()=>n.value.find(a=>!a.propsRef.disabled)),containsCheckedOption:c(()=>n.value.some(a=>f.compare(g(a.propsRef.value),g(e.modelValue)))),compare(a,r){if(typeof e.by=="string"){let l=e.by;return(a==null?void 0:a[l])===(r==null?void 0:r[l])}return e.by(a,r)},change(a){var r;if(e.disabled||f.compare(g(i.value),g(a)))return!1;let l=(r=n.value.find(d=>f.compare(g(d.propsRef.value),g(a))))==null?void 0:r.propsRef;return l!=null&&l.disabled?!1:(v(a),!0)},registerOption(a){n.value.push(a),n.value=W(n.value,r=>r.element)},unregisterOption(a){let r=n.value.findIndex(l=>l.id===a);r!==-1&&n.value.splice(r,1)}};N($,f),U({container:c(()=>E(t)),accept(a){return a.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:a.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(a){a.setAttribute("role","none")}});function b(a){if(!t.value||!t.value.contains(a.target))return;let r=n.value.filter(l=>l.propsRef.disabled===!1).map(l=>l.element);switch(a.key){case y.Enter:te(a.currentTarget);break;case y.ArrowLeft:case y.ArrowUp:if(a.preventDefault(),a.stopPropagation(),L(r,k.Previous|k.WrapAround)===j.Success){let l=n.value.find(d=>{var o;return d.element===((o=P(t))==null?void 0:o.activeElement)});l&&f.change(l.propsRef.value)}break;case y.ArrowRight:case y.ArrowDown:if(a.preventDefault(),a.stopPropagation(),L(r,k.Next|k.WrapAround)===j.Success){let l=n.value.find(d=>{var o;return d.element===((o=P(d.element))==null?void 0:o.activeElement)});l&&f.change(l.propsRef.value)}break;case y.Space:{a.preventDefault(),a.stopPropagation();let l=n.value.find(d=>{var o;return d.element===((o=P(d.element))==null?void 0:o.activeElement)});l&&f.change(l.propsRef.value)}break}}let O=`headlessui-radiogroup-${x()}`;return()=>{let{disabled:a,name:r,...l}=e,d={ref:t,id:O,role:"radiogroup","aria-labelledby":R.value,"aria-describedby":s.value,onKeydown:b};return F(X,[...r!=null&&i.value!=null?le({[r]:i.value}).map(([o,S])=>F(q,J({features:Q.Hidden,key:o,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:o,value:S}))):[],G({ourProps:d,theirProps:{...m,...I(l,["modelValue","defaultValue"])},slot:{},attrs:m,slots:w,name:"RadioGroup"})])}}});var ue=(e=>(e[e.Empty=1]="Empty",e[e.Active=2]="Active",e))(ue||{});let fe=D({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1}},setup(e,{attrs:u,slots:m,expose:w}){let p=H("RadioGroupOption"),t=`headlessui-radiogroup-option-${x()}`,n=M({name:"RadioGroupLabel"}),R=_({name:"RadioGroupDescription"}),s=h(null),i=c(()=>({value:e.value,disabled:e.disabled})),v=h(1);w({el:s,$el:s}),Y(()=>p.registerOption({id:t,element:s,propsRef:i})),Z(()=>p.unregisterOption(t));let f=c(()=>{var o;return((o=p.firstOption.value)==null?void 0:o.id)===t}),b=c(()=>p.disabled.value||e.disabled),O=c(()=>p.compare(g(p.value.value),g(e.value))),a=c(()=>b.value?-1:O.value||!p.containsCheckedOption.value&&f.value?0:-1);function r(){var o;!p.change(e.value)||(v.value|=2,(o=s.value)==null||o.focus())}function l(){v.value|=2}function d(){v.value&=-3}return()=>{let o=I(e,["value","disabled"]),S={checked:O.value,disabled:b.value,active:!!(v.value&2)},z={id:t,ref:s,role:"radio","aria-checked":O.value?"true":"false","aria-labelledby":n.value,"aria-describedby":R.value,"aria-disabled":b.value?!0:void 0,tabIndex:a.value,onClick:b.value?void 0:r,onFocus:b.value?void 0:l,onBlur:b.value?void 0:d};return G({ourProps:z,theirProps:o,slot:S,attrs:u,slots:m,name:"RadioGroupOption"})}}});function me(e,u){return C(),B("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[A("path",{"fill-rule":"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z","clip-rule":"evenodd"})])}function be(e,u){return C(),B("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[A("path",{"fill-rule":"evenodd",d:"M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z","clip-rule":"evenodd"})])}export{ce as A,me as a,pe as b,fe as g,be as r,ve as y};
