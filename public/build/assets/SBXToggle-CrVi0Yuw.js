import{p as V,s as _,P as O,H as R,L as C,G as N,B as h,M as T,O as S,Q as E,V as F,R as H,T as X,F as $,W as A,X as g,_ as I,r as y,b,h as w,d as v,w as x,n as d,e as r,t as p,j as k}from"./app-Cm2beRkj.js";import{K as Q,P as U}from"./label-D4lfsZnZ.js";import{d as W,p as J}from"./use-controllable-D9fh3JbV.js";let B=Symbol("GroupContext"),Y=V({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(a,{slots:l,attrs:e}){let s=_(null),n=Q({name:"SwitchLabel",props:{onClick(){!s.value||(s.value.click(),s.value.focus({preventScroll:!0}))}}}),o=O({name:"SwitchDescription"});return R(B,{switchRef:s,labelledby:n,describedby:o}),()=>C({theirProps:a,ourProps:{},slot:{},slots:l,attrs:e,name:"SwitchGroup"})}}),Z=V({name:"Switch",emits:{"update:modelValue":a=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,default:!1},name:{type:String,optional:!0},value:{type:String,optional:!0}},inheritAttrs:!1,setup(a,{emit:l,attrs:e,slots:s,expose:n}){let o=N(B,null),f=`headlessui-switch-${A()}`,[i,m]=W(h(()=>a.modelValue),t=>l("update:modelValue",t),h(()=>a.defaultChecked));function u(){m(!i.value)}let M=_(null),c=o===null?M:o.switchRef,P=T(h(()=>({as:a.as,type:e.type})),c);n({el:c,$el:c});function j(t){t.preventDefault(),u()}function G(t){t.key===g.Space?(t.preventDefault(),u()):t.key===g.Enter&&J(t.currentTarget)}function L(t){t.preventDefault()}return()=>{let{name:t,value:z,...D}=a,q={checked:i.value},K={id:f,ref:c,role:"switch",type:P.value,tabIndex:0,"aria-checked":i.value,"aria-labelledby":o==null?void 0:o.labelledby.value,"aria-describedby":o==null?void 0:o.describedby.value,onClick:j,onKeyup:G,onKeypress:L};return S($,[t!=null&&i.value!=null?S(E,F({features:H.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:i.value,name:t,value:z})):null,C({ourProps:K,theirProps:{...e,...X(D,["modelValue","defaultChecked"])},slot:q,attrs:e,slots:s,name:"Switch"})])}}}),ee=U;const te={components:{Switch:Z,SwitchGroup:Y,SwitchLabel:ee},props:{label:{type:String,required:!0},model:{type:Boolean,required:!0,default:!1},instruction:{type:String,required:!1,default:null},error:{type:String,required:!1,default:null}},watch:{currentValue(a,l){this.$emit("update:model",a)},model(a,l){this.currentValue=this.model}},data(){return{currentValue:this.model}}},le={class:"sr-only"},ae={class:"text-xs font-semibold text-gray-900 select-none cursor-pointer"},ne={key:0,class:"mt-2 text-xs text-gray-500"},oe={key:1,class:"mt-2 text-xs text-red-600"};function re(a,l,e,s,n,o){const f=y("Switch"),i=y("SwitchLabel"),m=y("SwitchGroup");return b(),w("div",null,[v(m,{as:"div",class:"flex items-center"},{default:x(()=>[v(f,{modelValue:n.currentValue,"onUpdate:modelValue":l[0]||(l[0]=u=>n.currentValue=u),class:d([e.model?"bg-green-500":"bg-gray-200","relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"])},{default:x(()=>[r("span",le,p(e.label),1),r("span",{class:d([n.currentValue?"translate-x-5":"translate-x-0","pointer-events-none relative inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"])},[r("span",{class:d([n.currentValue?"opacity-0 ease-out duration-100":"opacity-100 ease-in duration-200","absolute inset-0 flex h-full w-full items-center justify-center transition-opacity"]),"aria-hidden":"true"},l[1]||(l[1]=[r("svg",{class:"h-3 w-3 text-gray-400",fill:"none",viewBox:"0 0 12 12"},[r("path",{d:"M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]),2),r("span",{class:d([n.currentValue?"opacity-100 ease-in duration-200":"opacity-0 ease-out duration-100","absolute inset-0 flex h-full w-full items-center justify-center transition-opacity"]),"aria-hidden":"true"},l[2]||(l[2]=[r("svg",{class:"h-3 w-3 text-green-500",fill:"currentColor",viewBox:"0 0 12 12"},[r("path",{d:"M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z"})],-1)]),2)],2)]),_:1},8,["modelValue","class"]),v(i,{as:"span",class:"ml-3"},{default:x(()=>[r("span",ae,p(e.label),1)]),_:1})]),_:1}),e.instruction?(b(),w("p",ne,p(e.instruction),1)):k("",!0),e.error?(b(),w("p",oe,p(e.error),1)):k("",!0)])}const ce=I(te,[["render",re]]);export{ce as S};
