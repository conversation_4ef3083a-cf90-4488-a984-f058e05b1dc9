import{_ as C,S as y,h as r,d as i,e as s,w as c,F as S,r as o,b as m,t as n,j as d,g as p}from"./app-Cm2beRkj.js";import{S as A}from"./sweetalert2.all-i0W-sCgv.js";import{r as w}from"./ArrowRightIcon-CEuU5Xpj.js";import{r as N}from"./EnvelopeIcon-C7yn4PbF.js";import{r as T}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as X}from"./XCircleIcon-Bi2eGVmc.js";import{S as z}from"./SBXDataTable-C66uxM7K.js";import{S as D}from"./SBXFilterBar-w4dDtxld.js";import{S as I}from"./SBXGenericNotification-BrDLezmQ.js";import"./TrashIcon-fhKAjDA0.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const F={components:{ArrowRightIcon:w,CheckCircleIcon:T,XCircleIcon:X,EnvelopeIcon:N,SBXButton:y,SBXDataTable:z,SBXFilterBar:D,SBXGenericNotification:I},props:{translationAssignments:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignments.title"),this.$page.props.flash.authorization!=null&&(this.showAuthorizationNotification=!0)},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_assignments.title")},data(){return{columns:[{key:"assignmentslot",label:this.$t("translations.translation_assignments.title")},{key:"customerslot",label:this.$t("translations.translation_assignments.customer_label")},{key:"number_of_bids",label:this.$t("translations.translation_assignments.bid_count")},{key:"number_of_views",label:this.$t("translations.translation_assignments.view_count")},{key:"created_at",label:this.$t("translations.translation_assignments.date_label")},{key:"activeslot",label:this.$t("translations.translation_assignments.active_label")},{key:"emailslot",label:this.$t("translations.translation_assignments.mail_label")}],translationAssignmentFilters:{search:this.filters.search},showAuthorizationNotification:!1}},computed:{authorizationMessage(){return this.$page.props.flash.authorization==null?"":this.$page.props.flash.authorization}},methods:{changeFilters(){this.$inertia.get(this.route("translation_assignments",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translationAssignmentFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.translationAssignmentFilters.search=e},authorizationNotificationCancelled(){this.showAuthorizationNotification=!1},activateAssignment(e){this.$inertia.put(this.route("translation_assignments.activate",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]),{preserveState:!1,replace:!0})},deactivateAssignment(e){this.$inertia.put(this.route("translation_assignments.deactivate",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]),{preserveState:!1,replace:!0})},resendMatchedAssignmentEmails(e){var a=this;A.fire({title:a.$t("translations.translation_assignments.resend_dialog_title"),text:a.$t("translations.translation_assignments.resend_dialog_message"),icon:"warning",showCancelButton:!0,cancelButtonText:a.$t("translations.global.cancel"),confirmButtonColor:"#d33",confirmButtonText:a.$t("translations.translation_assignments.resend_dialog_button")}).then(_=>{_.value&&a.$inertia.post(a.route("translation_assignment_resend_match_email",[a.$page.props.locale.selected_market_code,a.$page.props.locale.selected_language_code,e]),{},{preserveScroll:!0,onSuccess:()=>{}})})}}},V={class:"flex flex-col"},R={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},E={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},M={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},j={class:"text-sm font-semibold"},O={class:"text-sm"},q={class:"flex items-center"},G={class:"text-sm"},K={class:"text-sm"},H={class:"text-sm"},J={key:0,class:"text-sm font-semibold"},L={key:1,class:"text-sm font-semibold"},P={class:"text-sm font-semibold"},Q={class:"text-sm font-semibold"},U={class:"text-sm"},W=["href"],Y={class:"text-sm"},Z=["href"],tt=["onClick"],et=["onClick"];function st(e,a,_,nt,g,l){const u=o("SBXFilterBar"),f=o("ArrowRightIcon"),b=o("XCircleIcon"),v=o("CheckCircleIcon"),$=o("EnvelopeIcon"),x=o("SBXButton"),B=o("SBXDataTable"),k=o("SBXGenericNotification");return m(),r(S,null,[i(u,{onSearchValueChanged:l.searchValueChanged,filters:g.translationAssignmentFilters,searchRoute:"translation_assignments",placeholder:e.$t("translations.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),s("div",V,[s("div",R,[s("div",E,[s("div",M,[i(B,{columns:g.columns,items:_.translationAssignments.data,showAddButton:!0,addRoute:"translation_assignments.create",addButtonText:e.$t("translations.translation_assignments.add_button"),showEditButton:!0,editRoute:"translation_assignments.edit",showViewButton:!0,viewRoute:"translation_assignments.view",showDeleteButton:!0,deleteRoute:"translation_assignments.destroy",deleteDialogTitle:e.$t("translations.translation_assignments.delete_dialog_title"),deleteDialogMessage:e.$t("translations.translation_assignments.delete_dialog_message"),deleteDialogOKText:e.$t("translations.translation_assignments.delete_dialog_ok_button"),deleteDialogCancelText:e.$t("translations.global.cancel"),paginator:_.translationAssignments.meta},{assignmentslot:c(t=>[s("div",null,[s("p",j,n(e.$t("translations.global.assignment_id"))+": "+n(t.item.assignment_id),1),s("p",O,n(t.item.translation_category),1),s("div",q,[s("p",G,n(t.item.from_translation_language),1),i(f,{class:"h-3 w-3 mx-2"}),s("p",K,n(t.item.to_translation_language),1)]),s("p",H,n(e.$t("translations.translation_assignments.is_authorization_required_label"))+": "+n(t.item.authorization_text),1)])]),customerslot:c(t=>[s("div",null,[t.item.assignment_type=="company"?(m(),r("p",J,n(e.$t("translations.my_assignments.assignment_detail.company_label")),1)):d("",!0),t.item.assignment_type=="personal"?(m(),r("p",L,n(e.$t("translations.my_assignments.assignment_detail.private_person_label")),1)):d("",!0),s("p",P,n(t.item.company),1),s("p",Q,n(t.item.first_name)+" "+n(t.item.last_name),1),s("p",U,[s("strong",null,n(e.$t("translations.translation_assignments.email_not_required_label"))+":",1),a[0]||(a[0]=p()),s("a",{href:"mailto:"+t.item.email},n(t.item.email),9,W)]),s("p",Y,[s("strong",null,n(e.$t("translations.translation_assignments.phone_no_not_required_label"))+":",1),a[1]||(a[1]=p()),s("a",{href:"tel:"+t.item.phone_no},n(t.item.phone_no),9,Z)])])]),activeslot:c(t=>[t.item.is_active?d("",!0):(m(),r("span",{key:0,onClick:h=>l.activateAssignment(t.item.id),class:"inline-flex flex-shrink-0 items-center justify-center cursor-pointer"},[i(b,{class:"h-8 w-8 text-red-600 group-hover:text-red-800","aria-hidden":"true"})],8,tt)),t.item.is_active?(m(),r("span",{key:1,onClick:h=>l.deactivateAssignment(t.item.id),class:"inline-flex flex-shrink-0 items-center justify-center cursor-pointer"},[i(v,{class:"h-8 w-8 text-green-600 group-hover:text-green-800","aria-hidden":"true"})],8,et)):d("",!0)]),emailslot:c(t=>[i(x,{onClick:h=>l.resendMatchedAssignmentEmails(t.item.id),size:"s"},{default:c(()=>[i($,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])]),_:1},8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])]),i(k,{show:g.showAuthorizationNotification,variant:"warning",message:l.authorizationMessage,onNotificationCancelled:l.authorizationNotificationCancelled},null,8,["show","message","onNotificationCancelled"])],64)}const ft=C(F,[["render",st]]);export{ft as default};
