import{b as o,h as k,e as m,_ as x,S as C,$ as R,a0 as S,r,c as h,a1 as D,w as i,d as l,g as T,t as A,i as M,J as P,j as p}from"./app-Cm2beRkj.js";import{r as $}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as b}from"./TrashIcon-fhKAjDA0.js";import{S as O}from"./sweetalert2.all-i0W-sCgv.js";import{S as z,a as E}from"./SBXTable-CaSPMjSb.js";function X(e,a){return o(),k("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})])}function j(e,a){return o(),k("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})])}function V(e,a){return o(),k("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[m("path",{"fill-rule":"evenodd",d:"M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z","clip-rule":"evenodd"})])}const L={components:{SBXTable:z,SBXPaginator:E,SBXButton:C,Link:R,EyeIcon:X,PlusIcon:V,PencilIcon:j,TrashIcon:b,CheckCircleIcon:$,ArrowRightOnRectangleIcon:S},props:{columns:{type:Array,required:!0,default:[]},items:{type:Array,required:!0,default:[]},showAddButton:{type:Boolean,default:!0},addButtonText:{type:String},addRoute:{type:String},addRouteParameters:{type:Object},addRouteAdditionalIDs:{type:Array,default:[]},showViewButton:{type:Boolean,default:!1},viewRoute:{type:String},showEditButton:{type:Boolean,default:!0},editRoute:{type:String},showDeleteButton:{type:Boolean,default:!0},deleteRoute:{type:String},deleteRouteAdditionalIDs:{type:Array,default:[]},deleteDialogTitle:{type:String},deleteDialogMessage:{type:String},deleteDialogOKText:{type:String},deleteDialogCancelText:{type:String},paginator:{type:Object,default:null},pickMode:{type:Boolean,default:!1},pickModeReturnRoute:{type:String,default:null},pickModeReturnRouteParameters:{type:Array,default:[]},pickedItems:{type:Array,default:[]}},emits:["pickItem","unpickItem"],computed:{dataColumns(){for(var e=[],a=0;a<this.columns.length;a++){let t=this.columns[a];e.push(t)}return e.push({key:"actions",headeralignment:"right",class:"justify-end"}),e},addButtonEnabled(){return!(this.pickMode&&this.pickedItems.length==0)}},methods:{addItem(){var e=[];e.push(this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code);for(var a=0;a<this.addRouteAdditionalIDs.length;a++){let t=this.addRouteAdditionalIDs[a];e.push(t)}this.pickMode?this.pickedItems.length>0&&this.$inertia.post(this.route(this.addRoute,e),{picked_ids:this.pickedItems,return_route:this.pickModeReturnRoute,return_route_parameters:this.pickModeReturnRouteParameters}):this.addRouteParameters!=null?this.$inertia.get(this.route(this.addRoute,e),this.addRouteParameters):this.$inertia.get(this.route(this.addRoute,e))},viewItem(e){this.$inertia.get(this.route(this.viewRoute,[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]))},editItem(e){this.$inertia.get(this.route(this.editRoute,[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]))},pickItem(e){this.pickedItems.indexOf(e.id)==-1?this.$emit("pickItem",e.id):this.$emit("unpickItem",e.id)},deleteItem(e){var a=this,t=[];t.push(this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code),t.push(e.id);for(var g=0;g<this.deleteRouteAdditionalIDs.length;g++){let d=this.deleteRouteAdditionalIDs[g];d.type=="value"?t.push(d.value):t.push(e[d.value])}O.fire({title:a.deleteDialogTitle,text:a.deleteDialogMessage,icon:"warning",showCancelButton:!0,cancelButtonText:a.deleteDialogCancelText,confirmButtonColor:"#d33",confirmButtonText:a.deleteDialogOKText}).then(d=>{d.value&&a.$inertia.delete(a.route(a.deleteRoute,t))})}}},N={class:"flex"},q={class:"flex"};function K(e,a,t,g,d,s){const f=r("PlusIcon"),c=r("SBXButton"),_=r("EyeIcon"),I=r("PencilIcon"),w=r("TrashIcon"),y=r("ArrowRightOnRectangleIcon"),v=r("CheckCircleIcon"),B=r("SBXTable");return o(),h(B,{columns:s.dataColumns,items:t.items,paginator:t.paginator},D({_:2},[t.showAddButton?{name:"header-actions",fn:i(()=>[l(c,{onClick:s.addItem,enabled:s.addButtonEnabled},{default:i(()=>[T(A(t.addButtonText)+" ",1),l(f,{class:"h-5 w-5 ml-2 mr-0 text-white"})]),_:1},8,["onClick","enabled"])]),key:"0"}:void 0,M(t.columns,n=>({name:n.key,fn:i(u=>[P(e.$slots,n.key,{item:u.item})])})),t.pickMode?void 0:{name:"actions",fn:i(n=>[m("div",N,[t.showViewButton?(o(),h(c,{key:0,onClick:u=>s.viewItem(n.item.id),size:"s"},{default:i(()=>[l(_,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])):p("",!0),t.showEditButton?(o(),h(c,{key:1,onClick:u=>s.editItem(n.item.id),size:"s",class:"ml-2"},{default:i(()=>[l(I,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])):p("",!0),t.showDeleteButton?(o(),h(c,{key:2,onClick:u=>s.deleteItem(n.item),variant:"danger",size:"s",class:"ml-2"},{default:i(()=>[l(w,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])):p("",!0)])]),key:"1"},t.pickMode?{name:"actions",fn:i(n=>[m("div",q,[t.pickedItems.indexOf(n.item.id)==-1?(o(),h(c,{key:0,onClick:u=>s.pickItem(n.item),variant:"danger",size:"s",class:"ml-2"},{default:i(()=>[l(y,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])):p("",!0),t.pickedItems.indexOf(n.item.id)!=-1?(o(),h(c,{key:1,onClick:u=>s.pickItem(n.item),variant:"success",size:"s",class:"ml-2"},{default:i(()=>[l(v,{class:"h-5 w-5 text-white","aria-hidden":"true"})]),_:2},1032,["onClick"])):p("",!0)])]),key:"2"}:void 0]),1032,["columns","items","paginator"])}const U=x(L,[["render",K]]);export{U as S};
