import{_ as f,S as b,c as _,w as p,r,b as g,e as v,d as s,g as h,t as B,k as S}from"./app-Cm2beRkj.js";import{S as c}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as X}from"./SBXInput-C8dZEgZe.js";import{S as R}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const w={components:{SBXDefaultPageLayout:c,SBXInput:X,SBXButton:b,SBXToggle:R},props:{vatRate:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("VatRate: ",this.vatRate)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.vatRate.data.name,rate:this.vatRate.data.rate,is_active:this.vatRate.data.is_active,is_default:this.vatRate.data.is_default})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.vatRate.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("vat_rates.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.vatRate.data.id]))}}};function T(o,e,l,x,a,i){const m=r("SBXInput"),n=r("SBXToggle"),d=r("SBXButton"),u=r("SBXDefaultPageLayout");return g(),_(u,null,{default:p(()=>[v("form",{onSubmit:e[4]||(e[4]=S((...t)=>i.update&&i.update(...t),["prevent"]))},[s(m,{model:a.form.name,"onUpdate:model":e[0]||(e[0]=t=>a.form.name=t),label:o.$t("sbxwebshop.vat_rates.name_label"),error:l.errors.name},null,8,["model","label","error"]),s(m,{model:a.form.rate,"onUpdate:model":e[1]||(e[1]=t=>a.form.rate=t),label:o.$t("sbxwebshop.vat_rates.rate_label"),error:l.errors.rate},null,8,["model","label","error"]),s(n,{model:a.form.is_active,"onUpdate:model":e[2]||(e[2]=t=>a.form.is_active=t),label:o.$t("sbxwebshop.vat_rates.is_active_label")},null,8,["model","label"]),s(n,{model:a.form.is_default,"onUpdate:model":e[3]||(e[3]=t=>a.form.is_default=t),label:o.$t("sbxwebshop.vat_rates.is_default_label")},null,8,["model","label"]),s(d,{class:"mt-4"},{default:p(()=>[h(B(o.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const L=f(w,[["render",T]]);export{L as default};
