import{h as a,d as s,C as o,e,F as n,b as i,af as c}from"./app-Cm2beRkj.js";import{_ as l}from"./AuthenticationCardLogo-DOAxyrlM.js";const r={class:"font-sans text-gray-900 antialiased"},d={class:"pt-4 bg-gray-100"},m={class:"min-h-screen flex flex-col items-center pt-6 sm:pt-0"},_=["innerHTML"],v={__name:"PrivacyPolicy",props:{policy:String},setup(t){return(p,f)=>(i(),a(n,null,[s(o(c),{title:"Privacy Policy"}),e("div",r,[e("div",d,[e("div",m,[e("div",null,[s(l)]),e("div",{class:"w-full sm:max-w-2xl mt-6 p-6 bg-white shadow-md overflow-hidden sm:rounded-lg prose",innerHTML:t.policy},null,8,_)])])])],64))}};export{v as default};
