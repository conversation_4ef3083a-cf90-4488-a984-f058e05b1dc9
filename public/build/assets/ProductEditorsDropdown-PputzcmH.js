import{_ as n,h as a,l as d,v as u,e as i,F as g,i as p,b as o,t as h}from"./app-Cm2beRkj.js";const P={props:["pages","currentPage","productID"],data(){return{selectedPage:this.currentPage}},computed:{selectedPageObject(){for(var t=null,e=0;e<this.pages.length;e++){let r=this.pages[e];if(r.value==this.selectedPage){t=r;break}}return t}},methods:{switchPage(){let t=this.route(this.selectedPageObject.route,[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productID]);this.$inertia.get(t)}}},f={class:"flex justify-end"},m=["value"];function _(t,e,r,b,l,c){return o(),a("div",f,[d(i("select",{onChange:e[0]||(e[0]=(...s)=>c.switchPage&&c.switchPage(...s)),"onUpdate:modelValue":e[1]||(e[1]=s=>l.selectedPage=s),class:"mt-1 block w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(o(!0),a(g,null,p(r.pages,s=>(o(),a("option",{value:s.value},h(t.$t(s.label)),9,m))),256))],544),[[u,l.selectedPage]])])}const w=n(P,[["render",_]]);export{w as default};
