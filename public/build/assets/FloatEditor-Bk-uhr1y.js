import{_ as c,S as f,c as p,w as l,r,b as _,e as t,t as n,l as g,m as v,d as b,g as h,k as B}from"./app-Cm2beRkj.js";import{S as x}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const S={components:{SBXDefaultPageLayout:x,SBXButton:f},props:{setting:Object},remember:"form",data(){return{form:this.$inertia.form({float_value:this.setting.data.float_value})}},computed:{},methods:{update(){this.form.processing||this.form.put(this.route("settings.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.setting.data.id]))}}},y={class:"mt-4"},k={for:"float_value",class:"block text-sm font-medium text-gray-700"},w={class:"mt-1"};function X(u,e,d,D,a,s){const i=r("SBXButton"),m=r("SBXDefaultPageLayout");return _(),p(m,null,{default:l(()=>[t("form",{onSubmit:e[1]||(e[1]=B((...o)=>s.update&&s.update(...o),["prevent"]))},[t("div",y,[t("label",k,n(d.setting.data.name),1),t("div",w,[g(t("input",{"onUpdate:modelValue":e[0]||(e[0]=o=>a.form.float_value=o),type:"text",name:"float_value",id:"float_value",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[v,a.form.float_value]])])]),b(i,{class:"mt-4"},{default:l(()=>[h(n(u.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const N=c(S,[["render",X]]);export{N as default};
