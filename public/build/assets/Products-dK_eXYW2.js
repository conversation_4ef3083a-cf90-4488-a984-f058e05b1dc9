import{_ as v,h as i,d as o,e as s,w as p,F as x,r as l,b as c,t as n,j as u}from"./app-Cm2beRkj.js";import{S as b}from"./SBXDataTable-C66uxM7K.js";import{S as k}from"./SBXFilterBar-w4dDtxld.js";import{r as C}from"./CheckCircleIcon-SzRA1Ei3.js";import{r as $}from"./XCircleIcon-Bi2eGVmc.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const B={components:{SBXDataTable:b,SBXFilterBar:k,CheckCircleIcon:C,XCircleIcon:$},props:{products:Object,editRoute:String,createRoute:String,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_products.title"),console.log("Products: ",this.products.data)},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_products.title")},data(){return{columns:[{key:"productslot",label:this.$t("translations.translation_products.name_label")}],productFilters:{search:this.filters.search,selectedFilter:this.filters.selectedFilter}}},computed:{filterOptions(){return[{text:"Alla produkter",value:"all"},{text:"Aktiva",value:"active"},{text:"Inaktiva",value:"inactive"}]}},methods:{changeFilters(){this.$inertia.get(this.route("translation_products",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.productFilters,{preserveState:!1,replace:!0})},searchValueChanged(e){this.productFilters.search=e},activateProduct(e){this.$inertia.put(this.route("products.activate",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]),{preserveState:!1,replace:!0})},deactivateProduct(e){this.$inertia.put(this.route("products.deactivate",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,e]),{preserveState:!1,replace:!0})}}},S={class:"flex flex-col"},D={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},F={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},T={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"},w={class:"text-sm font-semibold"},y={class:"text-xs"},R={class:"text-xs"},X=["onClick"],I=["onClick"];function V(e,P,a,j,d,r){const h=l("SBXFilterBar"),_=l("XCircleIcon"),m=l("CheckCircleIcon"),g=l("SBXDataTable");return c(),i(x,null,[o(h,{onSearchValueChanged:r.searchValueChanged,filters:d.productFilters,searchRoute:"translation_products",placeholder:e.$t("translations.global.search")},null,8,["onSearchValueChanged","filters","placeholder"]),s("div",S,[s("div",D,[s("div",F,[s("div",T,[o(g,{columns:d.columns,items:a.products.data,showAddButton:!1,addButtonText:e.$t("sbxwebshop.products.create_button"),addRoute:a.createRoute,showEditButton:!0,editRoute:a.editRoute,showDeleteButton:!1,deleteRoute:"products.destroy",deleteDialogTitle:e.$t("sbxwebshop.products.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.products.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.products.delete_dialog_ok"),deleteDialogCancelText:e.$t("translations.global.cancel"),paginator:a.products.meta},{productslot:p(t=>[s("div",null,[s("p",w,n(t.item.name),1),s("p",y,n(t.item.manufacturer),1),s("p",R,n(t.item.packet_size),1)])]),activeslot:p(t=>[t.item.is_active?u("",!0):(c(),i("span",{key:0,onClick:f=>r.activateProduct(t.item.id),class:"inline-flex flex-shrink-0 items-center justify-center cursor-pointer"},[o(_,{class:"h-8 w-8 text-red-600 group-hover:text-red-800","aria-hidden":"true"})],8,X)),t.item.is_active?(c(),i("span",{key:1,onClick:f=>r.deactivateProduct(t.item.id),class:"inline-flex flex-shrink-0 items-center justify-center cursor-pointer"},[o(m,{class:"h-8 w-8 text-green-600 group-hover:text-green-800","aria-hidden":"true"})],8,I)):u("",!0)]),_:1},8,["columns","items","addButtonText","addRoute","editRoute","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const H=v(B,[["render",V]]);export{H as default};
