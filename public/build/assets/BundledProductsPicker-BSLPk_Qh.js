import{_ as k,N as x,a as b,U as y,o as w,f as v,c as m,w as i,r as c,b as n,d as r,e as s,g as B,t as a,h as D,i as T,F as C}from"./app-Cm2beRkj.js";import R from"./BundledProductsPickerRow-Gx7vSr9y.js";import"./CheckIcon-CSfnhiPS.js";const U={components:{Dialog:x,DialogPanel:b,DialogTitle:y,TransitionChild:w,TransitionRoot:v,BundledProductsPickerRow:R},props:{exclude:{type:Array,default:[]},multiple:{type:Boolean,default:!1},isOpen:{type:Boolean,default:!1},product:{type:Object,default:!1}},mounted(){this.fetchProducts()},watch:{isOpen(t,e){this.fetchProducts(),this.pickedProducts=[]}},data(){return{products:[],pickedProducts:[]}},methods:{fetchProducts(){var t=this;axios.post(this.route("bundled_products.picker.products",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{exclude:this.exclude,product_id:this.product.data.id}).then(function(e){t.products=e.data.data,console.log("Picker products: ",t.products)}).catch(function(e){console.log("Error fetching products: ",e)})},closePicker(){this.$emit("closeBundledProductsPicker")},productPicked(t){this.multiple?this.pickedProducts.findIndex(e=>e.id==t.id)==-1&&this.pickedProducts.push(t):(this.pickedProducts=[],this.pickedProducts.push(t))},productUnpicked(t){if(this.multiple){let e=this.pickedProducts.findIndex(l=>l.id==t.id);e!=-1&&this.pickedProducts.splice(e,1)}else this.pickedProducts=[]},pickProducts(){this.$emit("pickProducts",this.pickedProducts)}}},N={class:"fixed inset-0 z-10 overflow-y-auto"},V={class:"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"},j={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},O={class:"sm:flex sm:items-start"},I={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},z={class:"mt-2"},E={class:"bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6"};function F(t,e,l,A,u,o){const p=c("TransitionChild"),f=c("DialogTitle"),h=c("BundledProductsPickerRow"),g=c("DialogPanel"),P=c("Dialog"),_=c("TransitionRoot");return n(),m(_,{as:"template",show:l.isOpen},{default:i(()=>[r(P,{as:"div",class:"relative z-10",onClose:o.closePicker},{default:i(()=>[r(p,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:i(()=>e[2]||(e[2]=[s("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])),_:1}),s("div",N,[s("div",V,[r(p,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:i(()=>[r(g,{class:"relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"},{default:i(()=>[s("div",j,[s("div",O,[s("div",I,[r(f,{as:"h3",class:"text-lg font-medium leading-6 text-gray-900"},{default:i(()=>[B(a(t.$t("sbxwebshop.bundled_products_picker.title")),1)]),_:1}),s("div",z,[(n(!0),D(C,null,T(u.products,d=>(n(),m(h,{product:d,pickedProducts:u.pickedProducts,onBundledProductPicked:o.productPicked,onBundledProductUnpicked:o.productUnpicked},null,8,["product","pickedProducts","onBundledProductPicked","onBundledProductUnpicked"]))),256))])])])]),s("div",E,[s("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm",onClick:e[0]||(e[0]=(...d)=>o.pickProducts&&o.pickProducts(...d))},a(t.$t("sbxwebshop.product_categories_picker.select_product_categories_button")),1),s("button",{type:"button",class:"mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",onClick:e[1]||(e[1]=(...d)=>o.closePicker&&o.closePicker(...d)),ref:"cancelButtonRef"},a(t.$t("sbxwebshop.global.cancel")),513)])]),_:1})]),_:1})])])]),_:1},8,["onClose"])]),_:1},8,["show"])}const q=k(U,[["render",F]]);export{q as default};
