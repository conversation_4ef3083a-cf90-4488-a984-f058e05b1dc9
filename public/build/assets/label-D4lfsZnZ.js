import{s as v,H as L,B as g,p as h,Y as j,Z as C,C as k,L as O,G as P,W as S}from"./app-Cm2beRkj.js";let i=Symbol("LabelContext");function p(){let t=P(i,null);if(t===null){let r=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(r,p),r}return t}function y({slot:t={},name:r="Label",props:n={}}={}){let e=v([]);function a(s){return e.value.push(s),()=>{let l=e.value.indexOf(s);l!==-1&&e.value.splice(l,1)}}return L(i,{register:a,slot:t,name:r,props:n}),g(()=>e.value.length>0?e.value.join(" "):void 0)}let E=h({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1}},setup(t,{slots:r,attrs:n}){let e=p(),a=`headlessui-label-${S()}`;return j(()=>C(e.register(a))),()=>{let{name:s="Label",slot:l={},props:c={}}=e,{passive:d,...o}=t,u={...Object.entries(c).reduce((b,[m,f])=>Object.assign(b,{[m]:k(f)}),{}),id:a};return d&&(delete u.onClick,delete o.onClick),O({ourProps:u,theirProps:o,slot:l,attrs:n,slots:r,name:s})}}});export{y as K,E as P};
