import{_ as b,S as _,c as g,w as d,r as i,b as v,e as a,d as s,g as S,t as B,k as w}from"./app-Cm2beRkj.js";import{u as y}from"./index-BKm97uF2.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as k}from"./SBXSelect-RMXlX9En.js";import{S as C}from"./SBXInput-C8dZEgZe.js";import"./client-BWFz6ICJ.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const P={components:{SBXDefaultPageLayout:X,SBXButton:_,SBXSelect:k,SBXInput:C},props:{roles:Object,salesPeople:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},data(){return{form:y("post",this.route("sydfisk_users.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),{role_id:null,first_name:null,last_name:null,email:null,phone_no:null,sales_person_id:null,password:null,password_confirmation:null})}},computed:{pageTitle(){return`${this.$t("sbxadmin.users.create_title")}`},roleOptions(){var l=[];l.push({id:null,name:"Välj roll",disabled:!0});for(var e=0;e<this.roles.data.length;e++){let n=this.roles.data[e];l.push({id:n.id,name:n.name})}return l},salesPeopleOptions(){var l=[];l.push({id:null,name:"Välj säljare",disabled:!0});for(var e=0;e<this.salesPeople.data.length;e++){let n=this.salesPeople.data[e];l.push({id:n.id,name:n.name})}return l}},methods:{create(){this.form.processing||this.form.submit()}}},U={class:"grid grid-cols-2 gap-4"};function O(l,e,n,j,o,m){const p=i("SBXSelect"),t=i("SBXInput"),f=i("SBXButton"),u=i("SBXDefaultPageLayout");return v(),g(u,null,{default:d(()=>[a("form",{onSubmit:e[16]||(e[16]=w((...r)=>m.create&&m.create(...r),["prevent"]))},[a("div",U,[a("div",null,[s(p,{model:o.form.role_id,"onUpdate:model":e[0]||(e[0]=r=>o.form.role_id=r),items:m.roleOptions,labelFieldName:"name",label:l.$t("sbxadmin.users.role_label"),onChange:e[1]||(e[1]=r=>o.form.validate("role_id")),error:o.form.errors.role_id},null,8,["model","items","label","error"]),s(p,{model:o.form.sales_person_id,"onUpdate:model":e[2]||(e[2]=r=>o.form.sales_person_id=r),items:m.salesPeopleOptions,labelFieldName:"name",label:l.$t("sydfisk.sydfisk_users.sales_person_label"),onChange:e[3]||(e[3]=r=>o.form.validate("sales_person_id")),error:o.form.errors.sales_person_id},null,8,["model","items","label","error"]),s(t,{model:o.form.first_name,"onUpdate:model":e[4]||(e[4]=r=>o.form.first_name=r),label:l.$t("sbxadmin.users.first_name_label"),onChange:e[5]||(e[5]=r=>o.form.validate("first_name")),error:o.form.errors.first_name},null,8,["model","label","error"]),s(t,{model:o.form.last_name,"onUpdate:model":e[6]||(e[6]=r=>o.form.last_name=r),label:l.$t("sbxadmin.users.last_name_label"),onChange:e[7]||(e[7]=r=>o.form.validate("last_name")),error:o.form.errors.last_name},null,8,["model","label","error"]),s(t,{model:o.form.email,"onUpdate:model":e[8]||(e[8]=r=>o.form.email=r),label:l.$t("sbxadmin.users.email_label"),onChange:e[9]||(e[9]=r=>o.form.validate("email")),error:o.form.errors.email},null,8,["model","label","error"]),s(t,{model:o.form.phone_no,"onUpdate:model":e[10]||(e[10]=r=>o.form.phone_no=r),label:l.$t("sydfisk.sydfisk_users.phone_no_label"),onChange:e[11]||(e[11]=r=>o.form.validate("phone_no")),error:o.form.errors.phone_no},null,8,["model","label","error"]),s(t,{type:"password",model:o.form.password,"onUpdate:model":e[12]||(e[12]=r=>o.form.password=r),label:l.$t("sbxadmin.users.password_label"),onChange:e[13]||(e[13]=r=>o.form.validate("password")),error:o.form.errors.password},null,8,["model","label","error"]),s(t,{type:"password",model:o.form.password_confirmation,"onUpdate:model":e[14]||(e[14]=r=>o.form.password_confirmation=r),label:l.$t("sbxadmin.users.password_confirmation_label"),onChange:e[15]||(e[15]=r=>o.form.validate("password_confirmation")),error:o.form.errors.password_confirmation},null,8,["model","label","error"]),s(f,{class:"mt-4"},{default:d(()=>[S(B(l.$t("sydfisk.sydfisk_users.create_button")),1)]),_:1})])])],32)]),_:1})}const I=b(P,[["render",O]]);export{I as default};
