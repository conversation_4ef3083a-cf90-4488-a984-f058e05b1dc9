import{z as Xs,a5 as Zs,p as Qs,s as eo,q as to,a6 as no,I as ro,C as io,A as so,O as ln,a7 as oo,a8 as lo,_ as ao,r as co,b as gr,h as yr,e as oe,n as he,j as uo,d as fo,F as ho}from"./app-Cm2beRkj.js";function B(r){this.content=r}B.prototype={constructor:B,find:function(r){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===r)return e;return-1},get:function(r){var e=this.find(r);return e==-1?void 0:this.content[e+1]},update:function(r,e,t){var n=t&&t!=r?this.remove(t):this,i=n.find(r),s=n.content.slice();return i==-1?s.push(t||r,e):(s[i+1]=e,t&&(s[i]=t)),new B(s)},remove:function(r){var e=this.find(r);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new B(t)},addToStart:function(r,e){return new B([r,e].concat(this.remove(r).content))},addToEnd:function(r,e){var t=this.remove(r).content.slice();return t.push(r,e),new B(t)},addBefore:function(r,e,t){var n=this.remove(e),i=n.content.slice(),s=n.find(r);return i.splice(s==-1?i.length:s,0,e,t),new B(i)},forEach:function(r){for(var e=0;e<this.content.length;e+=2)r(this.content[e],this.content[e+1])},prepend:function(r){return r=B.from(r),r.size?new B(r.content.concat(this.subtract(r).content)):this},append:function(r){return r=B.from(r),r.size?new B(this.subtract(r).content.concat(r.content)):this},subtract:function(r){var e=this;r=B.from(r);for(var t=0;t<r.content.length;t+=2)e=e.remove(r.content[t]);return e},toObject:function(){var r={};return this.forEach(function(e,t){r[e]=t}),r},get size(){return this.content.length>>1}};B.from=function(r){if(r instanceof B)return r;var e=[];if(r)for(var t in r)e.push(t,r[t]);return new B(e)};function Ci(r,e,t){for(let n=0;;n++){if(n==r.childCount||n==e.childCount)return r.childCount==e.childCount?null:t;let i=r.child(n),s=e.child(n);if(i==s){t+=i.nodeSize;continue}if(!i.sameMarkup(s))return t;if(i.isText&&i.text!=s.text){for(let o=0;i.text[o]==s.text[o];o++)t++;return t}if(i.content.size||s.content.size){let o=Ci(i.content,s.content,t+1);if(o!=null)return o}t+=i.nodeSize}}function Oi(r,e,t,n){for(let i=r.childCount,s=e.childCount;;){if(i==0||s==0)return i==s?null:{a:t,b:n};let o=r.child(--i),l=e.child(--s),a=o.nodeSize;if(o==l){t-=a,n-=a;continue}if(!o.sameMarkup(l))return{a:t,b:n};if(o.isText&&o.text!=l.text){let c=0,d=Math.min(o.text.length,l.text.length);for(;c<d&&o.text[o.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,n--;return{a:t,b:n}}if(o.content.size||l.content.size){let c=Oi(o.content,l.content,t-1,n-1);if(c)return c}t-=a,n-=a}}class y{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,i=0,s){for(let o=0,l=0;l<t;o++){let a=this.content[o],c=l+a.nodeSize;if(c>e&&n(a,i+l,s||null,o)!==!1&&a.content.size){let d=l+1;a.nodesBetween(Math.max(0,e-d),Math.min(a.content.size,t-d),n,i+d)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,i){let s="",o=!0;return this.nodesBetween(e,t,(l,a)=>{l.isText?(s+=l.text.slice(Math.max(e,a)-a,t-a),o=!n):l.isLeaf?(i?s+=typeof i=="function"?i(l):i:l.type.spec.leafText&&(s+=l.type.spec.leafText(l)),o=!n):!o&&l.isBlock&&(s+=n,o=!0)},0),s}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,i=this.content.slice(),s=0;for(t.isText&&t.sameMarkup(n)&&(i[i.length-1]=t.withText(t.text+n.text),s=1);s<e.content.length;s++)i.push(e.content[s]);return new y(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let n=[],i=0;if(t>e)for(let s=0,o=0;o<t;s++){let l=this.content[s],a=o+l.nodeSize;a>e&&((o<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-o),Math.min(l.text.length,t-o)):l=l.cut(Math.max(0,e-o-1),Math.min(l.content.size,t-o-1))),n.push(l),i+=l.nodeSize),o=a}return new y(n,i)}cutByIndex(e,t){return e==t?y.empty:e==0&&t==this.content.length?this:new y(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let i=this.content.slice(),s=this.size+t.nodeSize-n.nodeSize;return i[e]=t,new y(i,s)}addToStart(e){return new y([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new y(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let i=this.content[t];e(i,n,t),n+=i.nodeSize}}findDiffStart(e,t=0){return Ci(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return Oi(this,e,t,n)}findIndex(e,t=-1){if(e==0)return Tt(0,e);if(e==this.size)return Tt(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,i=0;;n++){let s=this.child(n),o=i+s.nodeSize;if(o>=e)return o==e||t>0?Tt(n+1,o):Tt(n,i);i=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return y.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new y(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return y.empty;let t,n=0;for(let i=0;i<e.length;i++){let s=e[i];n+=s.nodeSize,i&&s.isText&&e[i-1].sameMarkup(s)?(t||(t=e.slice(0,i)),t[t.length-1]=s.withText(t[t.length-1].text+s.text)):t&&t.push(s)}return new y(t||e,n)}static from(e){if(!e)return y.empty;if(e instanceof y)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new y([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}y.empty=new y([],0);const an={index:0,offset:0};function Tt(r,e){return an.index=r,an.offset=e,an}function Rt(r,e){if(r===e)return!0;if(!(r&&typeof r=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(r);if(Array.isArray(e)!=t)return!1;if(t){if(r.length!=e.length)return!1;for(let n=0;n<r.length;n++)if(!Rt(r[n],e[n]))return!1}else{for(let n in r)if(!(n in e)||!Rt(r[n],e[n]))return!1;for(let n in e)if(!(n in r))return!1}return!0}let N=class Dn{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let i=0;i<e.length;i++){let s=e[i];if(this.eq(s))return e;if(this.type.excludes(s.type))t||(t=e.slice(0,i));else{if(s.type.excludes(this.type))return e;!n&&s.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),n=!0),t&&t.push(s)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Rt(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);return n.create(t.attrs)}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return Dn.none;if(e instanceof Dn)return[e];let t=e.slice();return t.sort((n,i)=>n.type.rank-i.type.rank),t}};N.none=[];class Pt extends Error{}class k{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=Ni(this.content,e+this.openStart,t);return n&&new k(n,this.openStart,this.openEnd)}removeBetween(e,t){return new k(Ti(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return k.empty;let n=t.openStart||0,i=t.openEnd||0;if(typeof n!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new k(y.fromJSON(e,t.content),n,i)}static maxOpen(e,t=!0){let n=0,i=0;for(let s=e.firstChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.firstChild)n++;for(let s=e.lastChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.lastChild)i++;return new k(e,n,i)}}k.empty=new k(y.empty,0,0);function Ti(r,e,t){let{index:n,offset:i}=r.findIndex(e),s=r.maybeChild(n),{index:o,offset:l}=r.findIndex(t);if(i==e||s.isText){if(l!=t&&!r.child(o).isText)throw new RangeError("Removing non-flat range");return r.cut(0,e).append(r.cut(t))}if(n!=o)throw new RangeError("Removing non-flat range");return r.replaceChild(n,s.copy(Ti(s.content,e-i-1,t-i-1)))}function Ni(r,e,t,n){let{index:i,offset:s}=r.findIndex(e),o=r.maybeChild(i);if(s==e||o.isText)return n&&!n.canReplace(i,i,t)?null:r.cut(0,e).append(t).append(r.cut(e));let l=Ni(o.content,e-s-1,t);return l&&r.replaceChild(i,o.copy(l))}function po(r,e,t){if(t.openStart>r.depth)throw new Pt("Inserted content deeper than insertion position");if(r.depth-t.openStart!=e.depth-t.openEnd)throw new Pt("Inconsistent open depths");return Ei(r,e,t,0)}function Ei(r,e,t,n){let i=r.index(n),s=r.node(n);if(i==e.index(n)&&n<r.depth-t.openStart){let o=Ei(r,e,t,n+1);return s.copy(s.content.replaceChild(i,o))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&r.depth==n&&e.depth==n){let o=r.parent,l=o.content;return Fe(o,l.cut(0,r.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:o,end:l}=mo(t,r);return Fe(s,vi(r,o,l,e,n))}else return Fe(s,Bt(r,e,n))}function Ai(r,e){if(!e.type.compatibleContent(r.type))throw new Pt("Cannot join "+e.type.name+" onto "+r.type.name)}function In(r,e,t){let n=r.node(t);return Ai(n,e.node(t)),n}function ze(r,e){let t=e.length-1;t>=0&&r.isText&&r.sameMarkup(e[t])?e[t]=r.withText(e[t].text+r.text):e.push(r)}function yt(r,e,t,n){let i=(e||r).node(t),s=0,o=e?e.index(t):i.childCount;r&&(s=r.index(t),r.depth>t?s++:r.textOffset&&(ze(r.nodeAfter,n),s++));for(let l=s;l<o;l++)ze(i.child(l),n);e&&e.depth==t&&e.textOffset&&ze(e.nodeBefore,n)}function Fe(r,e){return r.type.checkContent(e),r.copy(e)}function vi(r,e,t,n,i){let s=r.depth>i&&In(r,e,i+1),o=n.depth>i&&In(t,n,i+1),l=[];return yt(null,r,i,l),s&&o&&e.index(i)==t.index(i)?(Ai(s,o),ze(Fe(s,vi(r,e,t,n,i+1)),l)):(s&&ze(Fe(s,Bt(r,e,i+1)),l),yt(e,t,i,l),o&&ze(Fe(o,Bt(t,n,i+1)),l)),yt(n,null,i,l),new y(l)}function Bt(r,e,t){let n=[];if(yt(null,r,t,n),r.depth>t){let i=In(r,e,t+1);ze(Fe(i,Bt(r,e,t+1)),n)}return yt(e,null,t,n),new y(n)}function mo(r,e){let t=e.depth-r.openStart,i=e.node(t).copy(r.content);for(let s=t-1;s>=0;s--)i=e.node(s).copy(y.from(i));return{start:i.resolveNoCache(r.openStart+t),end:i.resolveNoCache(i.content.size-r.openEnd-t)}}class St{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],i=e.child(t);return n?e.child(t).cut(n):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let s=0;s<e;s++)i+=n.child(s).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return N.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),i=e.maybeChild(t);if(!n){let l=n;n=i,i=l}let s=n.marks;for(var o=0;o<s.length;o++)s[o].type.spec.inclusive===!1&&(!i||!s[o].isInSet(i.marks))&&(s=s[o--].removeFromSet(s));return s}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,i=e.parent.maybeChild(e.index());for(var s=0;s<n.length;s++)n[s].type.spec.inclusive===!1&&(!i||!n[s].isInSet(i.marks))&&(n=n[s--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new zt(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],i=0,s=t;for(let o=e;;){let{index:l,offset:a}=o.content.findIndex(s),c=s-a;if(n.push(o,l,i+a),!c||(o=o.child(l),o.isText))break;s=c-1,i+=a+1}return new St(t,n,s)}static resolveCached(e,t){for(let i=0;i<cn.length;i++){let s=cn[i];if(s.pos==t&&s.doc==e)return s}let n=cn[dn]=St.resolve(e,t);return dn=(dn+1)%go,n}}let cn=[],dn=0,go=12;class zt{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const yo=Object.create(null);let Le=class Rn{constructor(e,t,n,i=N.none){this.type=e,this.attrs=t,this.marks=i,this.content=n||y.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,i=0){this.content.nodesBetween(e,t,n,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,i){return this.content.textBetween(e,t,n,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&Rt(this.attrs,t||e.defaultAttrs||yo)&&N.sameSet(this.marks,n||N.none)}copy(e=null){return e==this.content?this:new Rn(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new Rn(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return k.empty;let i=this.resolve(e),s=this.resolve(t),o=n?0:i.sharedDepth(t),l=i.start(o),c=i.node(o).content.cut(i.pos-l,s.pos-l);return new k(c,i.depth-o,s.depth-o)}replace(e,t,n){return po(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let i=this.content.child(t-1);return{node:i,index:t-1,offset:n-i.nodeSize}}resolve(e){return St.resolveCached(this,e)}resolveNoCache(e){return St.resolve(this,e)}rangeHasMark(e,t,n){let i=!1;return t>e&&this.nodesBetween(e,t,s=>(n.isInSet(s.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),Di(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=y.empty,i=0,s=n.childCount){let o=this.contentMatchAt(e).matchFragment(n,i,s),l=o&&o.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<s;a++)if(!this.type.allowsMarks(n.child(a).marks))return!1;return!0}canReplaceWith(e,t,n,i){if(i&&!this.type.allowsMarks(i))return!1;let s=this.contentMatchAt(e).matchType(n),o=s&&s.matchFragment(this.content,t);return o?o.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content);let e=N.none;for(let t=0;t<this.marks.length;t++)e=this.marks[t].addToSet(e);if(!N.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n=null;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let i=y.fromJSON(e,t.content);return e.nodeType(t.type).create(t.attrs,i,n)}};Le.prototype.text=void 0;class Ft extends Le{constructor(e,t,n,i){if(super(e,t,null,i),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):Di(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new Ft(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new Ft(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function Di(r,e){for(let t=r.length-1;t>=0;t--)e=r[t].type.name+"("+e+")";return e}class $e{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new bo(e,t);if(n.next==null)return $e.empty;let i=Ii(n);n.next&&n.err("Unexpected trailing text");let s=Oo(Co(i));return To(s,n),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let i=this;for(let s=t;i&&s<n;s++)i=i.matchType(e.child(s).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let i=[this];function s(o,l){let a=o.matchFragment(e,n);if(a&&(!t||a.validEnd))return y.from(l.map(c=>c.createAndFill()));for(let c=0;c<o.next.length;c++){let{type:d,next:u}=o.next[c];if(!(d.isText||d.hasRequiredAttrs())&&i.indexOf(u)==-1){i.push(u);let f=s(u,l.concat(d));if(f)return f}}return null}return s(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let i=n.shift(),s=i.match;if(s.matchType(e)){let o=[];for(let l=i;l.type;l=l.via)o.push(l.type);return o.reverse()}for(let o=0;o<s.next.length;o++){let{type:l,next:a}=s.next[o];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(n.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(n){e.push(n);for(let i=0;i<n.next.length;i++)e.indexOf(n.next[i].next)==-1&&t(n.next[i].next)}return t(this),e.map((n,i)=>{let s=i+(n.validEnd?"*":" ")+" ";for(let o=0;o<n.next.length;o++)s+=(o?", ":"")+n.next[o].type.name+"->"+e.indexOf(n.next[o].next);return s}).join(`
`)}}$e.empty=new $e(!0);class bo{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function Ii(r){let e=[];do e.push(ko(r));while(r.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function ko(r){let e=[];do e.push(xo(r));while(r.next&&r.next!=")"&&r.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function xo(r){let e=wo(r);for(;;)if(r.eat("+"))e={type:"plus",expr:e};else if(r.eat("*"))e={type:"star",expr:e};else if(r.eat("?"))e={type:"opt",expr:e};else if(r.eat("{"))e=So(r,e);else break;return e}function br(r){/\D/.test(r.next)&&r.err("Expected number, got '"+r.next+"'");let e=Number(r.next);return r.pos++,e}function So(r,e){let t=br(r),n=t;return r.eat(",")&&(r.next!="}"?n=br(r):n=-1),r.eat("}")||r.err("Unclosed braced range"),{type:"range",min:t,max:n,expr:e}}function Mo(r,e){let t=r.nodeTypes,n=t[e];if(n)return[n];let i=[];for(let s in t){let o=t[s];o.groups.indexOf(e)>-1&&i.push(o)}return i.length==0&&r.err("No node type or group '"+e+"' found"),i}function wo(r){if(r.eat("(")){let e=Ii(r);return r.eat(")")||r.err("Missing closing paren"),e}else if(/\W/.test(r.next))r.err("Unexpected token '"+r.next+"'");else{let e=Mo(r,r.next).map(t=>(r.inline==null?r.inline=t.isInline:r.inline!=t.isInline&&r.err("Mixing inline and block content"),{type:"name",value:t}));return r.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function Co(r){let e=[[]];return i(s(r,0),t()),e;function t(){return e.push([])-1}function n(o,l,a){let c={term:a,to:l};return e[o].push(c),c}function i(o,l){o.forEach(a=>a.to=l)}function s(o,l){if(o.type=="choice")return o.exprs.reduce((a,c)=>a.concat(s(c,l)),[]);if(o.type=="seq")for(let a=0;;a++){let c=s(o.exprs[a],l);if(a==o.exprs.length-1)return c;i(c,l=t())}else if(o.type=="star"){let a=t();return n(l,a),i(s(o.expr,a),a),[n(a)]}else if(o.type=="plus"){let a=t();return i(s(o.expr,l),a),i(s(o.expr,a),a),[n(a)]}else{if(o.type=="opt")return[n(l)].concat(s(o.expr,l));if(o.type=="range"){let a=l;for(let c=0;c<o.min;c++){let d=t();i(s(o.expr,a),d),a=d}if(o.max==-1)i(s(o.expr,a),a);else for(let c=o.min;c<o.max;c++){let d=t();n(a,d),i(s(o.expr,a),d),a=d}return[n(a)]}else{if(o.type=="name")return[n(l,void 0,o.value)];throw new Error("Unknown expr type")}}}}function Ri(r,e){return e-r}function kr(r,e){let t=[];return n(e),t.sort(Ri);function n(i){let s=r[i];if(s.length==1&&!s[0].term)return n(s[0].to);t.push(i);for(let o=0;o<s.length;o++){let{term:l,to:a}=s[o];!l&&t.indexOf(a)==-1&&n(a)}}}function Oo(r){let e=Object.create(null);return t(kr(r,0));function t(n){let i=[];n.forEach(o=>{r[o].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let d=0;d<i.length;d++)i[d][0]==l&&(c=i[d][1]);kr(r,a).forEach(d=>{c||i.push([l,c=[]]),c.indexOf(d)==-1&&c.push(d)})})});let s=e[n.join(",")]=new $e(n.indexOf(r.length-1)>-1);for(let o=0;o<i.length;o++){let l=i[o][1].sort(Ri);s.next.push({type:i[o][0],next:e[l.join(",")]||t(l)})}return s}}function To(r,e){for(let t=0,n=[r];t<n.length;t++){let i=n[t],s=!i.validEnd,o=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];o.push(a.name),s&&!(a.isText||a.hasRequiredAttrs())&&(s=!1),n.indexOf(c)==-1&&n.push(c)}s&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function Pi(r){let e=Object.create(null);for(let t in r){let n=r[t];if(!n.hasDefault)return null;e[t]=n.default}return e}function Bi(r,e){let t=Object.create(null);for(let n in r){let i=e&&e[n];if(i===void 0){let s=r[n];if(s.hasDefault)i=s.default;else throw new RangeError("No value supplied for attribute "+n)}t[n]=i}return t}function zi(r){let e=Object.create(null);if(r)for(let t in r)e[t]=new No(r[t]);return e}let xr=class Fi{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=zi(n.attrs),this.defaultAttrs=Pi(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==$e.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:Bi(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Le(this,this.computeAttrs(e),y.from(t),N.setFrom(n))}createChecked(e=null,t,n){return t=y.from(t),this.checkContent(t),new Le(this,this.computeAttrs(e),t,N.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),t=y.from(t),t.size){let o=this.contentMatch.fillBefore(t);if(!o)return null;t=o.append(t)}let i=this.contentMatch.matchFragment(t),s=i&&i.fillBefore(y.empty,!0);return s?new Le(this,e,t.append(s),N.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:N.none:e}static compile(e,t){let n=Object.create(null);e.forEach((s,o)=>n[s]=new Fi(s,t,o));let i=t.spec.topNode||"doc";if(!n[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let s in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}};class No{constructor(e){this.hasDefault=Object.prototype.hasOwnProperty.call(e,"default"),this.default=e.default}get isRequired(){return!this.hasDefault}}class Ut{constructor(e,t,n,i){this.name=e,this.rank=t,this.schema=n,this.spec=i,this.attrs=zi(i.attrs),this.excluded=null;let s=Pi(this.attrs);this.instance=s?new N(this,s):null}create(e=null){return!e&&this.instance?this.instance:new N(this,Bi(this.attrs,e))}static compile(e,t){let n=Object.create(null),i=0;return e.forEach((s,o)=>n[s]=new Ut(s,i++,t,o)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}excludes(e){return this.excluded.indexOf(e)>-1}}class Eo{constructor(e){this.cached=Object.create(null),this.spec={nodes:B.from(e.nodes),marks:B.from(e.marks||{}),topNode:e.topNode},this.nodes=xr.compile(this.spec.nodes,this),this.marks=Ut.compile(this.spec.marks,this);let t=Object.create(null);for(let n in this.nodes){if(n in this.marks)throw new RangeError(n+" can not be both a node and a mark");let i=this.nodes[n],s=i.spec.content||"",o=i.spec.marks;i.contentMatch=t[s]||(t[s]=$e.parse(s,this.nodes)),i.inlineContent=i.contentMatch.inlineContent,i.markSet=o=="_"?null:o?Sr(this,o.split(" ")):o==""||!i.inlineContent?[]:null}for(let n in this.marks){let i=this.marks[n],s=i.spec.excludes;i.excluded=s==null?[i]:s==""?[]:Sr(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof xr){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,n,i)}text(e,t){let n=this.nodes.text;return new Ft(n,n.defaultAttrs,e,N.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Le.fromJSON(this,e)}markFromJSON(e){return N.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function Sr(r,e){let t=[];for(let n=0;n<e.length;n++){let i=e[n],s=r.marks[i],o=s;if(s)t.push(s);else for(let l in r.marks){let a=r.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(o=a)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[n]+"'")}return t}class nt{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[],t.forEach(n=>{n.tag?this.tags.push(n):n.style&&this.styles.push(n)}),this.normalizeLists=!this.tags.some(n=>{if(!/^(ul|ol)\b/.test(n.tag)||!n.node)return!1;let i=e.nodes[n.node];return i.contentMatch.matchType(i)})}parse(e,t={}){let n=new wr(this,t,!1);return n.addAll(e,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new wr(this,t,!0);return n.addAll(e,t.from,t.to),k.maxOpen(n.finish())}matchTag(e,t,n){for(let i=n?this.tags.indexOf(n)+1:0;i<this.tags.length;i++){let s=this.tags[i];if(Do(e,s.tag)&&(s.namespace===void 0||e.namespaceURI==s.namespace)&&(!s.context||t.matchesContext(s.context))){if(s.getAttrs){let o=s.getAttrs(e);if(o===!1)continue;s.attrs=o||void 0}return s}}}matchStyle(e,t,n,i){for(let s=i?this.styles.indexOf(i)+1:0;s<this.styles.length;s++){let o=this.styles[s],l=o.style;if(!(l.indexOf(e)!=0||o.context&&!n.matchesContext(o.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(o.getAttrs){let a=o.getAttrs(t);if(a===!1)continue;o.attrs=a||void 0}return o}}}static schemaRules(e){let t=[];function n(i){let s=i.priority==null?50:i.priority,o=0;for(;o<t.length;o++){let l=t[o];if((l.priority==null?50:l.priority)<s)break}t.splice(o,0,i)}for(let i in e.marks){let s=e.marks[i].spec.parseDOM;s&&s.forEach(o=>{n(o=Cr(o)),o.mark=i})}for(let i in e.nodes){let s=e.nodes[i].spec.parseDOM;s&&s.forEach(o=>{n(o=Cr(o)),o.node=i})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new nt(e,nt.schemaRules(e)))}}const Li={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Ao={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Vi={ol:!0,ul:!0},Lt=1,Vt=2,bt=4;function Mr(r,e,t){return e!=null?(e?Lt:0)|(e==="full"?Vt:0):r&&r.whitespace=="pre"?Lt|Vt:t&~bt}class Nt{constructor(e,t,n,i,s,o,l){this.type=e,this.attrs=t,this.marks=n,this.pendingMarks=i,this.solid=s,this.options=l,this.content=[],this.activeMarks=N.none,this.stashMarks=[],this.match=o||(l&bt?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(y.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let n=this.type.contentMatch,i;return(i=n.findWrapping(e.type))?(this.match=n,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&Lt)){let n=this.content[this.content.length-1],i;if(n&&n.isText&&(i=/[ \t\r\n\u000c]+$/.exec(n.text))){let s=n;n.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=s.withText(s.text.slice(0,s.text.length-i[0].length))}}let t=y.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(y.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}popFromStashMark(e){for(let t=this.stashMarks.length-1;t>=0;t--)if(e.eq(this.stashMarks[t]))return this.stashMarks.splice(t,1)[0]}applyPending(e){for(let t=0,n=this.pendingMarks;t<n.length;t++){let i=n[t];(this.type?this.type.allowsMarkType(i.type):Ro(i.type,e))&&!i.isInSet(this.activeMarks)&&(this.activeMarks=i.addToSet(this.activeMarks),this.pendingMarks=i.removeFromSet(this.pendingMarks))}}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Li.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class wr{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0;let i=t.topNode,s,o=Mr(null,t.preserveWhitespace,0)|(n?bt:0);i?s=new Nt(i.type,i.attrs,N.none,N.none,!0,t.topMatch||i.type.contentMatch,o):n?s=new Nt(null,null,N.none,N.none,!0,null,o):s=new Nt(e.schema.topNodeType,null,N.none,N.none,!0,null,o),this.nodes=[s],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e){if(e.nodeType==3)this.addTextNode(e);else if(e.nodeType==1){let t=e.getAttribute("style"),n=t?this.readStyles(Io(t)):null,i=this.top;if(n!=null)for(let s=0;s<n.length;s++)this.addPendingMark(n[s]);if(this.addElement(e),n!=null)for(let s=0;s<n.length;s++)this.removePendingMark(n[s],i)}}addTextNode(e){let t=e.nodeValue,n=this.top;if(n.options&Vt||n.inlineContext(e)||/[^ \t\r\n\u000c]/.test(t)){if(n.options&Lt)n.options&Vt?t=t.replace(/\r\n?/g,`
`):t=t.replace(/\r?\n|\r/g," ");else if(t=t.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(t)&&this.open==this.nodes.length-1){let i=n.content[n.content.length-1],s=e.previousSibling;(!i||s&&s.nodeName=="BR"||i.isText&&/[ \t\r\n\u000c]$/.test(i.text))&&(t=t.slice(1))}t&&this.insertNode(this.parser.schema.text(t)),this.findInText(e)}else this.findInside(e)}addElement(e,t){let n=e.nodeName.toLowerCase(),i;Vi.hasOwnProperty(n)&&this.parser.normalizeLists&&vo(e);let s=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(i=this.parser.matchTag(e,this,t));if(s?s.ignore:Ao.hasOwnProperty(n))this.findInside(e),this.ignoreFallback(e);else if(!s||s.skip||s.closeParent){s&&s.closeParent?this.open=Math.max(0,this.open-1):s&&s.skip.nodeType&&(e=s.skip);let o,l=this.top,a=this.needsBlock;if(Li.hasOwnProperty(n))l.content.length&&l.content[0].isInline&&this.open&&(this.open--,l=this.top),o=!0,l.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e);return}this.addAll(e),o&&this.sync(l),this.needsBlock=a}else this.addElementByRule(e,s,s.consuming===!1?i:void 0)}leafFallback(e){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`))}ignoreFallback(e){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"))}readStyles(e){let t=N.none;e:for(let n=0;n<e.length;n+=2)for(let i=void 0;;){let s=this.parser.matchStyle(e[n],e[n+1],this,i);if(!s)continue e;if(s.ignore)return null;if(t=this.parser.schema.marks[s.mark].create(s.attrs).addToSet(t),s.consuming===!1)i=s;else break}return t}addElementByRule(e,t,n){let i,s,o;t.node?(s=this.parser.schema.nodes[t.node],s.isLeaf?this.insertNode(s.create(t.attrs))||this.leafFallback(e):i=this.enter(s,t.attrs||null,t.preserveWhitespace)):(o=this.parser.schema.marks[t.mark].create(t.attrs),this.addPendingMark(o));let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(n)this.addElement(e,n);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a)}i&&this.sync(l)&&this.open--,o&&this.removePendingMark(o,l)}addAll(e,t,n){let i=t||0;for(let s=t?e.childNodes[t]:e.firstChild,o=n==null?null:e.childNodes[n];s!=o;s=s.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(s);this.findAtPoint(e,i)}findPlace(e){let t,n;for(let i=this.open;i>=0;i--){let s=this.nodes[i],o=s.findWrapping(e);if(o&&(!t||t.length>o.length)&&(t=o,n=s,!o.length)||s.solid)break}if(!t)return!1;this.sync(n);for(let i=0;i<t.length;i++)this.enterInner(t[i],null,!1);return!0}insertNode(e){if(e.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&this.enterInner(t)}if(this.findPlace(e)){this.closeExtra();let t=this.top;t.applyPending(e.type),t.match&&(t.match=t.match.matchType(e.type));let n=t.activeMarks;for(let i=0;i<e.marks.length;i++)(!t.type||t.type.allowsMarkType(e.marks[i].type))&&(n=e.marks[i].addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n){let i=this.findPlace(e.create(t));return i&&this.enterInner(e,t,!0,n),i}enterInner(e,t=null,n=!1,i){this.closeExtra();let s=this.top;s.applyPending(e),s.match=s.match&&s.match.matchType(e);let o=Mr(e,i,s.options);s.options&bt&&s.content.length==0&&(o|=bt),this.nodes.push(new Nt(e,t,s.activeMarks,s.pendingMarks,n,null,o)),this.open++}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let i=n.length-1;i>=0;i--)e+=n[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(n?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,i=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),s=-(n?n.depth+1:0)+(i?0:1),o=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=s;a--)if(o(l-1,a))return!0;return!1}else{let d=a>0||a==0&&i?this.nodes[a].type:n&&a>=s?n.node(a-s).type:null;if(!d||d.name!=c&&d.groups.indexOf(c)==-1)return!1;a--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let n=this.parser.schema.nodes[t];if(n.isTextblock&&n.defaultAttrs)return n}}addPendingMark(e){let t=Po(e,this.top.pendingMarks);t&&this.top.stashMarks.push(t),this.top.pendingMarks=e.addToSet(this.top.pendingMarks)}removePendingMark(e,t){for(let n=this.open;n>=0;n--){let i=this.nodes[n];if(i.pendingMarks.lastIndexOf(e)>-1)i.pendingMarks=e.removeFromSet(i.pendingMarks);else{i.activeMarks=e.removeFromSet(i.activeMarks);let o=i.popFromStashMark(e);o&&i.type&&i.type.allowsMarkType(o.type)&&(i.activeMarks=o.addToSet(i.activeMarks))}if(i==t)break}}}function vo(r){for(let e=r.firstChild,t=null;e;e=e.nextSibling){let n=e.nodeType==1?e.nodeName.toLowerCase():null;n&&Vi.hasOwnProperty(n)&&t?(t.appendChild(e),e=t):n=="li"?t=e:n&&(t=null)}}function Do(r,e){return(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,e)}function Io(r){let e=/\s*([\w-]+)\s*:\s*([^;]+)/g,t,n=[];for(;t=e.exec(r);)n.push(t[1],t[2].trim());return n}function Cr(r){let e={};for(let t in r)e[t]=r[t];return e}function Ro(r,e){let t=e.schema.nodes;for(let n in t){let i=t[n];if(!i.allowsMarkType(r))continue;let s=[],o=l=>{s.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:d}=l.edge(a);if(c==e||s.indexOf(d)<0&&o(d))return!0}};if(o(i.contentMatch))return!0}}function Po(r,e){for(let t=0;t<e.length;t++)if(r.eq(e[t]))return e[t]}class de{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=un(t).createDocumentFragment());let i=n,s=[];return e.forEach(o=>{if(s.length||o.marks.length){let l=0,a=0;for(;l<s.length&&a<o.marks.length;){let c=o.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(s[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<s.length;)i=s.pop()[1];for(;a<o.marks.length;){let c=o.marks[a++],d=this.serializeMark(c,o.isInline,t);d&&(s.push([c,i]),i.appendChild(d.dom),i=d.contentDOM||d.dom)}}i.appendChild(this.serializeNodeInner(o,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:i}=de.renderSpec(un(t),this.nodes[e.type.name](e));if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let s=this.serializeMark(e.marks[i],e.isInline,t);s&&((s.contentDOM||s.dom).appendChild(n),n=s.dom)}return n}serializeMark(e,t,n={}){let i=this.marks[e.type.name];return i&&de.renderSpec(un(n),i(e,t))}static renderSpec(e,t,n=null){if(typeof t=="string")return{dom:e.createTextNode(t)};if(t.nodeType!=null)return{dom:t};if(t.dom&&t.dom.nodeType!=null)return t;let i=t[0],s=i.indexOf(" ");s>0&&(n=i.slice(0,s),i=i.slice(s+1));let o,l=n?e.createElementNS(n,i):e.createElement(i),a=t[1],c=1;if(a&&typeof a=="object"&&a.nodeType==null&&!Array.isArray(a)){c=2;for(let d in a)if(a[d]!=null){let u=d.indexOf(" ");u>0?l.setAttributeNS(d.slice(0,u),d.slice(u+1),a[d]):l.setAttribute(d,a[d])}}for(let d=c;d<t.length;d++){let u=t[d];if(u===0){if(d<t.length-1||d>c)throw new RangeError("Content hole must be the only child of its parent node");return{dom:l,contentDOM:l}}else{let{dom:f,contentDOM:h}=de.renderSpec(e,u,n);if(l.appendChild(f),h){if(o)throw new RangeError("Multiple content holes");o=h}}}return{dom:l,contentDOM:o}}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new de(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Or(e.nodes);return t.text||(t.text=n=>n.text),t}static marksFromSchema(e){return Or(e.marks)}}function Or(r){let e={};for(let t in r){let n=r[t].spec.toDOM;n&&(e[t]=n)}return e}function un(r){return r.document||window.document}const Hi=65535,$i=Math.pow(2,16);function Bo(r,e){return r+e*$i}function Tr(r){return r&Hi}function zo(r){return(r-(r&Hi))/$i}const Ji=1,Wi=2,vt=4,qi=8;class Pn{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(this.delInfo&qi)>0}get deletedBefore(){return(this.delInfo&(Ji|vt))>0}get deletedAfter(){return(this.delInfo&(Wi|vt))>0}get deletedAcross(){return(this.delInfo&vt)>0}}class Z{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&Z.empty)return Z.empty}recover(e){let t=0,n=Tr(e);if(!this.inverted)for(let i=0;i<n;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[n*3]+t+zo(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let i=0,s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+s],d=this.ranges[l+o],u=a+c;if(e<=u){let f=c?e==a?-1:e==u?1:t:t,h=a+i+(f<0?0:d);if(n)return h;let p=e==(t<0?a:u)?null:Bo(l/3,e-a),m=e==a?Wi:e==u?Ji:vt;return(t<0?e!=a:e!=u)&&(m|=qi),new Pn(h,m,p)}i+=d-c}return n?e+i:new Pn(e+i,0,null)}touches(e,t){let n=0,i=Tr(t),s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?n:0);if(a>e)break;let c=this.ranges[l+s],d=a+c;if(e<=d&&l==i*3)return!0;n+=this.ranges[l+o]-c}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let i=0,s=0;i<this.ranges.length;i+=3){let o=this.ranges[i],l=o-(this.inverted?s:0),a=o+(this.inverted?0:s),c=this.ranges[i+t],d=this.ranges[i+n];e(l,l+c,a,a+d),s+=d-c}}invert(){return new Z(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?Z.empty:new Z(e<0?[0,-e,0]:[0,0,e])}}Z.empty=new Z([]);class Ze{constructor(e=[],t,n=0,i=e.length){this.maps=e,this.mirror=t,this.from=n,this.to=i}slice(e=0,t=this.maps.length){return new Ze(this.maps,this.mirror,e,t)}copy(){return new Ze(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(e,t){this.to=this.maps.push(e),t!=null&&this.setMirror(this.maps.length-1,t)}appendMapping(e){for(let t=0,n=this.maps.length;t<e.maps.length;t++){let i=e.getMirror(t);this.appendMap(e.maps[t],i!=null&&i<t?n+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this.maps.length+e.maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e.maps[t].invert(),i!=null&&i>t?n-i-1:void 0)}}invert(){let e=new Ze;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this.maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let i=0;for(let s=this.from;s<this.to;s++){let o=this.maps[s],l=o.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(s);if(a!=null&&a>s&&a<this.to){s=a,e=this.maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return n?e:new Pn(e,i,null)}}const fn=Object.create(null);class G{getMap(){return Z.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=fn[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in fn)throw new RangeError("Duplicate use of step JSON ID "+e);return fn[e]=t,t.prototype.jsonID=e,t}}class D{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new D(e,null)}static fail(e){return new D(null,e)}static fromReplace(e,t,n,i){try{return D.ok(e.replace(t,n,i))}catch(s){if(s instanceof Pt)return D.fail(s.message);throw s}}}function Xn(r,e,t){let n=[];for(let i=0;i<r.childCount;i++){let s=r.child(i);s.content.size&&(s=s.copy(Xn(s.content,e,s))),s.isInline&&(s=e(s,t,i)),n.push(s)}return y.fromArray(n)}class Se extends G{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),i=n.node(n.sharedDepth(this.to)),s=new k(Xn(t.content,(o,l)=>!o.isAtom||!l.type.allowsMarkType(this.mark.type)?o:o.mark(this.mark.addToSet(o.marks)),i),t.openStart,t.openEnd);return D.fromReplace(e,this.from,this.to,s)}invert(){return new ue(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Se(t.pos,n.pos,this.mark)}merge(e){return e instanceof Se&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Se(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new Se(t.from,t.to,e.markFromJSON(t.mark))}}G.jsonID("addMark",Se);class ue extends G{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new k(Xn(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return D.fromReplace(e,this.from,this.to,n)}invert(){return new Se(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new ue(t.pos,n.pos,this.mark)}merge(e){return e instanceof ue&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new ue(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new ue(t.from,t.to,e.markFromJSON(t.mark))}}G.jsonID("removeMark",ue);class Me extends G{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return D.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return D.fromReplace(e,this.pos,this.pos+1,new k(y.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let n=this.mark.addToSet(t.marks);if(n.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(n))return new Me(this.pos,t.marks[i]);return new Me(this.pos,this.mark)}}return new rt(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Me(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new Me(t.pos,e.markFromJSON(t.mark))}}G.jsonID("addNodeMark",Me);class rt extends G{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return D.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return D.fromReplace(e,this.pos,this.pos+1,new k(y.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new Me(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new rt(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new rt(t.pos,e.markFromJSON(t.mark))}}G.jsonID("removeNodeMark",rt);class V extends G{constructor(e,t,n,i=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=i}apply(e){return this.structure&&Bn(e,this.from,this.to)?D.fail("Structure replace would overwrite content"):D.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new Z([this.from,this.to-this.from,this.slice.size])}invert(e){return new V(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new V(t.pos,Math.max(t.pos,n.pos),this.slice)}merge(e){if(!(e instanceof V)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?k.empty:new k(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new V(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?k.empty:new k(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new V(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new V(t.from,t.to,k.fromJSON(e,t.slice),!!t.structure)}}G.jsonID("replace",V);class I extends G{constructor(e,t,n,i,s,o,l=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=i,this.slice=s,this.insert=o,this.structure=l}apply(e){if(this.structure&&(Bn(e,this.from,this.gapFrom)||Bn(e,this.gapTo,this.to)))return D.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return D.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?D.fromReplace(e,this.from,this.to,n):D.fail("Content does not fit in gap")}getMap(){return new Z([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new I(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),i=e.map(this.gapFrom,-1),s=e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||i<t.pos||s>n.pos?null:new I(t.pos,n.pos,i,s,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new I(t.from,t.to,t.gapFrom,t.gapTo,k.fromJSON(e,t.slice),t.insert,!!t.structure)}}G.jsonID("replaceAround",I);function Bn(r,e,t){let n=r.resolve(e),i=t-e,s=n.depth;for(;i>0&&s>0&&n.indexAfter(s)==n.node(s).childCount;)s--,i--;if(i>0){let o=n.node(s).maybeChild(n.indexAfter(s));for(;i>0;){if(!o||o.isLeaf)return!0;o=o.firstChild,i--}}return!1}function Fo(r,e,t,n){let i=[],s=[],o,l;r.doc.nodesBetween(e,t,(a,c,d)=>{if(!a.isInline)return;let u=a.marks;if(!n.isInSet(u)&&d.type.allowsMarkType(n.type)){let f=Math.max(c,e),h=Math.min(c+a.nodeSize,t),p=n.addToSet(u);for(let m=0;m<u.length;m++)u[m].isInSet(p)||(o&&o.to==f&&o.mark.eq(u[m])?o.to=h:i.push(o=new ue(f,h,u[m])));l&&l.to==f?l.to=h:s.push(l=new Se(f,h,n))}}),i.forEach(a=>r.step(a)),s.forEach(a=>r.step(a))}function Lo(r,e,t,n){let i=[],s=0;r.doc.nodesBetween(e,t,(o,l)=>{if(!o.isInline)return;s++;let a=null;if(n instanceof Ut){let c=o.marks,d;for(;d=n.isInSet(c);)(a||(a=[])).push(d),c=d.removeFromSet(c)}else n?n.isInSet(o.marks)&&(a=[n]):a=o.marks;if(a&&a.length){let c=Math.min(l+o.nodeSize,t);for(let d=0;d<a.length;d++){let u=a[d],f;for(let h=0;h<i.length;h++){let p=i[h];p.step==s-1&&u.eq(i[h].style)&&(f=p)}f?(f.to=c,f.step=s):i.push({style:u,from:Math.max(l,e),to:c,step:s})}}}),i.forEach(o=>r.step(new ue(o.from,o.to,o.style)))}function Vo(r,e,t,n=t.contentMatch){let i=r.doc.nodeAt(e),s=[],o=e+1;for(let l=0;l<i.childCount;l++){let a=i.child(l),c=o+a.nodeSize,d=n.matchType(a.type);if(!d)s.push(new V(o,c,k.empty));else{n=d;for(let u=0;u<a.marks.length;u++)t.allowsMarkType(a.marks[u].type)||r.step(new ue(o,c,a.marks[u]))}o=c}if(!n.validEnd){let l=n.fillBefore(y.empty,!0);r.replace(o,o,new k(l,0,0))}for(let l=s.length-1;l>=0;l--)r.step(s[l])}function Ho(r,e,t){return(e==0||r.canReplace(e,r.childCount))&&(t==r.childCount||r.canReplace(0,t))}function dt(r){let t=r.parent.content.cutByIndex(r.startIndex,r.endIndex);for(let n=r.depth;;--n){let i=r.$from.node(n),s=r.$from.index(n),o=r.$to.indexAfter(n);if(n<r.depth&&i.canReplace(s,o,t))return n;if(n==0||i.type.spec.isolating||!Ho(i,s,o))break}return null}function $o(r,e,t){let{$from:n,$to:i,depth:s}=e,o=n.before(s+1),l=i.after(s+1),a=o,c=l,d=y.empty,u=0;for(let p=s,m=!1;p>t;p--)m||n.index(p)>0?(m=!0,d=y.from(n.node(p).copy(d)),u++):a--;let f=y.empty,h=0;for(let p=s,m=!1;p>t;p--)m||i.after(p+1)<i.end(p)?(m=!0,f=y.from(i.node(p).copy(f)),h++):c++;r.step(new I(a,c,o,l,new k(d.append(f),u,h),d.size-u,!0))}function Zn(r,e,t=null,n=r){let i=Jo(r,e),s=i&&Wo(n,e);return s?i.map(Nr).concat({type:e,attrs:t}).concat(s.map(Nr)):null}function Nr(r){return{type:r,attrs:null}}function Jo(r,e){let{parent:t,startIndex:n,endIndex:i}=r,s=t.contentMatchAt(n).findWrapping(e);if(!s)return null;let o=s.length?s[0]:e;return t.canReplaceWith(n,i,o)?s:null}function Wo(r,e){let{parent:t,startIndex:n,endIndex:i}=r,s=t.child(n),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let a=(o.length?o[o.length-1]:e).contentMatch;for(let c=n;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:o}function qo(r,e,t){let n=y.empty;for(let o=t.length-1;o>=0;o--){if(n.size){let l=t[o].type.contentMatch.matchFragment(n);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}n=y.from(t[o].type.create(t[o].attrs,n))}let i=e.start,s=e.end;r.step(new I(i,s,i,s,new k(n,0,0),t.length,!0))}function Ko(r,e,t,n,i){if(!n.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=r.steps.length;r.doc.nodesBetween(e,t,(o,l)=>{if(o.isTextblock&&!o.hasMarkup(n,i)&&jo(r.doc,r.mapping.slice(s).map(l),n)){r.clearIncompatible(r.mapping.slice(s).map(l,1),n);let a=r.mapping.slice(s),c=a.map(l,1),d=a.map(l+o.nodeSize,1);return r.step(new I(c,d,c+1,d-1,new k(y.from(n.create(i,null,o.marks)),0,0),1,!0)),!1}})}function jo(r,e,t){let n=r.resolve(e),i=n.index();return n.parent.canReplaceWith(i,i+1,t)}function Uo(r,e,t,n,i){let s=r.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");t||(t=s.type);let o=t.create(n,null,i||s.marks);if(s.isLeaf)return r.replaceWith(e,e+s.nodeSize,o);if(!t.validContent(s.content))throw new RangeError("Invalid content for node type "+t.name);r.step(new I(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new k(y.from(o),0,0),1,!0))}function Qe(r,e,t=1,n){let i=r.resolve(e),s=i.depth-t,o=n&&n[n.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,d=t-2;c>s;c--,d--){let u=i.node(c),f=i.index(c);if(u.type.spec.isolating)return!1;let h=u.content.cutByIndex(f,u.childCount),p=n&&n[d]||u;if(p!=u&&(h=h.replaceChild(0,p.type.create(p.attrs))),!u.canReplace(f+1,u.childCount)||!p.type.validContent(h))return!1}let l=i.indexAfter(s),a=n&&n[0];return i.node(s).canReplaceWith(l,l,a?a.type:i.node(s+1).type)}function Go(r,e,t=1,n){let i=r.doc.resolve(e),s=y.empty,o=y.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){s=y.from(i.node(l).copy(s));let d=n&&n[c];o=y.from(d?d.type.create(d.attrs,o):i.node(l).copy(o))}r.step(new V(e,e,new k(s.append(o),t,t),!0))}function ut(r,e){let t=r.resolve(e),n=t.index();return _o(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(n,n+1)}function _o(r,e){return!!(r&&e&&!r.isLeaf&&r.canAppend(e))}function Yo(r,e,t){let n=new V(e-t,e+t,k.empty,!0);r.step(n)}function Xo(r,e,t){let n=r.resolve(e);if(n.parent.canReplaceWith(n.index(),n.index(),t))return e;if(n.parentOffset==0)for(let i=n.depth-1;i>=0;i--){let s=n.index(i);if(n.node(i).canReplaceWith(s,s,t))return n.before(i+1);if(s>0)return null}if(n.parentOffset==n.parent.content.size)for(let i=n.depth-1;i>=0;i--){let s=n.indexAfter(i);if(n.node(i).canReplaceWith(s,s,t))return n.after(i+1);if(s<n.node(i).childCount)return null}return null}function Ki(r,e,t){let n=r.resolve(e);if(!t.content.size)return e;let i=t.content;for(let s=0;s<t.openStart;s++)i=i.firstChild.content;for(let s=1;s<=(t.openStart==0&&t.size?2:1);s++)for(let o=n.depth;o>=0;o--){let l=o==n.depth?0:n.pos<=(n.start(o+1)+n.end(o+1))/2?-1:1,a=n.index(o)+(l>0?1:0),c=n.node(o),d=!1;if(s==1)d=c.canReplace(a,a,i);else{let u=c.contentMatchAt(a).findWrapping(i.firstChild.type);d=u&&c.canReplaceWith(a,a,u[0])}if(d)return l==0?n.pos:l<0?n.before(o+1):n.after(o+1)}return null}function Qn(r,e,t=e,n=k.empty){if(e==t&&!n.size)return null;let i=r.resolve(e),s=r.resolve(t);return ji(i,s,n)?new V(e,t,n):new Zo(i,s,n).fit()}function ji(r,e,t){return!t.openStart&&!t.openEnd&&r.start()==e.start()&&r.parent.canReplace(r.index(),e.index(),t.content)}class Zo{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=y.empty;for(let i=0;i<=e.depth;i++){let s=e.node(i);this.frontier.push({type:s.type,match:s.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=y.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(e<0?this.$to:n.doc.resolve(e));if(!i)return null;let s=this.placed,o=n.depth,l=i.depth;for(;o&&l&&s.childCount==1;)s=s.firstChild.content,o--,l--;let a=new k(s,o,l);return e>-1?new I(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new V(n.pos,i.pos,a):null}findFittable(){for(let e=1;e<=2;e++)for(let t=this.unplaced.openStart;t>=0;t--){let n,i=null;t?(i=hn(this.unplaced.content,t-1).firstChild,n=i.content):n=this.unplaced.content;let s=n.firstChild;for(let o=this.depth;o>=0;o--){let{type:l,match:a}=this.frontier[o],c,d=null;if(e==1&&(s?a.matchType(s.type)||(d=a.fillBefore(y.from(s),!1)):i&&l.compatibleContent(i.type)))return{sliceDepth:t,frontierDepth:o,parent:i,inject:d};if(e==2&&s&&(c=a.findWrapping(s.type)))return{sliceDepth:t,frontierDepth:o,parent:i,wrap:c};if(i&&a.matchType(i.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=hn(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new k(e,t+1,Math.max(n,i.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=hn(e,t);if(i.childCount<=1&&t>0){let s=e.size-t<=t+i.size;this.unplaced=new k(pt(e,t-1,1),t-1,s?t-1:n)}else this.unplaced=new k(pt(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:i,wrap:s}){for(;this.depth>t;)this.closeFrontierNode();if(s)for(let m=0;m<s.length;m++)this.openFrontierNode(s[m]);let o=this.unplaced,l=n?n.content:o.content,a=o.openStart-e,c=0,d=[],{match:u,type:f}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)d.push(i.child(m));u=u.matchFragment(i)}let h=l.size+e-(o.content.size-o.openEnd);for(;c<l.childCount;){let m=l.child(c),g=u.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(u=g,d.push(Ui(m.mark(f.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=mt(this.placed,t,y.from(d)),this.frontier[t].match=u,p&&h<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let b=g.lastChild;this.frontier.push({type:b.type,match:b.contentMatchAt(b.childCount)}),g=b.content}this.unplaced=p?e==0?k.empty:new k(pt(o.content,e-1,1),e-1,h<0?o.openEnd:e-1):new k(pt(o.content,e,c),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!pn(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,i=this.$to.after(n);for(;n>1&&i==this.$to.end(--n);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:i}=this.frontier[t],s=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=pn(e,t,i,n,s);if(o){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],d=pn(e,l,c,a,!0);if(!d||d.childCount)continue e}return{depth:t,fit:o,move:s?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=mt(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let i=e.node(n),s=i.type.contentMatch.fillBefore(i.content,!0,e.index(n));this.openFrontierNode(i.type,i.attrs,s)}return e}openFrontierNode(e,t=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=mt(this.placed,this.depth,y.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(y.empty,!0);t.childCount&&(this.placed=mt(this.placed,this.frontier.length,t))}}function pt(r,e,t){return e==0?r.cutByIndex(t,r.childCount):r.replaceChild(0,r.firstChild.copy(pt(r.firstChild.content,e-1,t)))}function mt(r,e,t){return e==0?r.append(t):r.replaceChild(r.childCount-1,r.lastChild.copy(mt(r.lastChild.content,e-1,t)))}function hn(r,e){for(let t=0;t<e;t++)r=r.firstChild.content;return r}function Ui(r,e,t){if(e<=0)return r;let n=r.content;return e>1&&(n=n.replaceChild(0,Ui(n.firstChild,e-1,n.childCount==1?t-1:0))),e>0&&(n=r.type.contentMatch.fillBefore(n).append(n),t<=0&&(n=n.append(r.type.contentMatch.matchFragment(n).fillBefore(y.empty,!0)))),r.copy(n)}function pn(r,e,t,n,i){let s=r.node(e),o=i?r.indexAfter(e):r.index(e);if(o==s.childCount&&!t.compatibleContent(s.type))return null;let l=n.fillBefore(s.content,!0,o);return l&&!Qo(t,s.content,o)?l:null}function Qo(r,e,t){for(let n=t;n<e.childCount;n++)if(!r.allowsMarks(e.child(n).marks))return!0;return!1}function el(r){return r.spec.defining||r.spec.definingForContent}function tl(r,e,t,n){if(!n.size)return r.deleteRange(e,t);let i=r.doc.resolve(e),s=r.doc.resolve(t);if(ji(i,s,n))return r.step(new V(e,t,n));let o=_i(i,r.doc.resolve(t));o[o.length-1]==0&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let f=i.depth,h=i.pos-1;f>0;f--,h--){let p=i.node(f).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;o.indexOf(f)>-1?l=f:i.before(f)==h&&o.splice(1,0,-f)}let a=o.indexOf(l),c=[],d=n.openStart;for(let f=n.content,h=0;;h++){let p=f.firstChild;if(c.push(p),h==n.openStart)break;f=p.content}for(let f=d-1;f>=0;f--){let h=c[f].type,p=el(h);if(p&&i.node(a).type!=h)d=f;else if(p||!h.isTextblock)break}for(let f=n.openStart;f>=0;f--){let h=(f+d+1)%(n.openStart+1),p=c[h];if(p)for(let m=0;m<o.length;m++){let g=o[(m+a)%o.length],b=!0;g<0&&(b=!1,g=-g);let S=i.node(g-1),E=i.index(g-1);if(S.canReplaceWith(E,E,p.type,p.marks))return r.replace(i.before(g),b?s.after(g):t,new k(Gi(n.content,0,n.openStart,h),h,n.openEnd))}}let u=r.steps.length;for(let f=o.length-1;f>=0&&(r.replace(e,t,n),!(r.steps.length>u));f--){let h=o[f];h<0||(e=i.before(h),t=s.after(h))}}function Gi(r,e,t,n,i){if(e<t){let s=r.firstChild;r=r.replaceChild(0,s.copy(Gi(s.content,e+1,t,n,s)))}if(e>n){let s=i.contentMatchAt(0),o=s.fillBefore(r).append(r);r=o.append(s.matchFragment(o).fillBefore(y.empty,!0))}return r}function nl(r,e,t,n){if(!n.isInline&&e==t&&r.doc.resolve(e).parent.content.size){let i=Xo(r.doc,e,n.type);i!=null&&(e=t=i)}r.replaceRange(e,t,new k(y.from(n),0,0))}function rl(r,e,t){let n=r.doc.resolve(e),i=r.doc.resolve(t),s=_i(n,i);for(let o=0;o<s.length;o++){let l=s[o],a=o==s.length-1;if(a&&l==0||n.node(l).type.contentMatch.validEnd)return r.delete(n.start(l),i.end(l));if(l>0&&(a||n.node(l-1).canReplace(n.index(l-1),i.indexAfter(l-1))))return r.delete(n.before(l),i.after(l))}for(let o=1;o<=n.depth&&o<=i.depth;o++)if(e-n.start(o)==n.depth-o&&t>n.end(o)&&i.end(o)-t!=i.depth-o)return r.delete(n.before(o),t);r.delete(e,t)}function _i(r,e){let t=[],n=Math.min(r.depth,e.depth);for(let i=n;i>=0;i--){let s=r.start(i);if(s<r.pos-(r.depth-i)||e.end(i)>e.pos+(e.depth-i)||r.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(s==e.start(i)||i==r.depth&&i==e.depth&&r.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==s-1)&&t.push(i)}return t}class et extends G{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return D.fail("No node at attribute step's position");let n=Object.create(null);for(let s in t.attrs)n[s]=t.attrs[s];n[this.attr]=this.value;let i=t.type.create(n,null,t.marks);return D.fromReplace(e,this.pos,this.pos+1,new k(y.from(i),0,t.isLeaf?0:1))}getMap(){return Z.empty}invert(e){return new et(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new et(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new et(t.pos,t.attr,t.value)}}G.jsonID("attr",et);let it=class extends Error{};it=function r(e){let t=Error.call(this,e);return t.__proto__=r.prototype,t};it.prototype=Object.create(Error.prototype);it.prototype.constructor=it;it.prototype.name="TransformError";class il{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Ze}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new it(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=k.empty){let i=Qn(this.doc,e,t,n);return i&&this.step(i),this}replaceWith(e,t,n){return this.replace(e,t,new k(y.from(n),0,0))}delete(e,t){return this.replace(e,t,k.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return tl(this,e,t,n),this}replaceRangeWith(e,t,n){return nl(this,e,t,n),this}deleteRange(e,t){return rl(this,e,t),this}lift(e,t){return $o(this,e,t),this}join(e,t=1){return Yo(this,e,t),this}wrap(e,t){return qo(this,e,t),this}setBlockType(e,t=e,n,i=null){return Ko(this,e,t,n,i),this}setNodeMarkup(e,t,n=null,i=[]){return Uo(this,e,t,n,i),this}setNodeAttribute(e,t,n){return this.step(new et(e,t,n)),this}addNodeMark(e,t){return this.step(new Me(e,t)),this}removeNodeMark(e,t){if(!(t instanceof N)){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(t=t.isInSet(n.marks),!t)return this}return this.step(new rt(e,t)),this}split(e,t=1,n){return Go(this,e,t,n),this}addMark(e,t,n){return Fo(this,e,t,n),this}removeMark(e,t,n){return Lo(this,e,t,n),this}clearIncompatible(e,t,n){return Vo(this,e,t,n),this}}const mn=Object.create(null);class C{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new sl(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=k.empty){let n=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=n,n=n.lastChild;let s=e.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:c}=o[l],d=e.mapping.slice(s);e.replaceRange(d.map(a.pos),d.map(c.pos),l?k.empty:t),l==0&&vr(e,s,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:o,$to:l}=i[s],a=e.mapping.slice(n),c=a.map(o.pos),d=a.map(l.pos);s?e.deleteRange(c,d):(e.replaceRangeWith(c,d,t),vr(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let i=e.parent.inlineContent?new O(e):je(e.node(0),e.parent,e.pos,e.index(),t,n);if(i)return i;for(let s=e.depth-1;s>=0;s--){let o=t<0?je(e.node(0),e.node(s),e.before(s+1),e.index(s),t,n):je(e.node(0),e.node(s),e.after(s+1),e.index(s)+1,t,n);if(o)return o}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new re(e.node(0))}static atStart(e){return je(e,e,0,0,1)||new re(e)}static atEnd(e){return je(e,e,e.content.size,e.childCount,-1)||new re(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=mn[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in mn)throw new RangeError("Duplicate use of selection JSON ID "+e);return mn[e]=t,t.prototype.jsonID=e,t}getBookmark(){return O.between(this.$anchor,this.$head).getBookmark()}}C.prototype.visible=!0;class sl{constructor(e,t){this.$from=e,this.$to=t}}let Er=!1;function Ar(r){!Er&&!r.parent.inlineContent&&(Er=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+r.parent.type.name+")"))}class O extends C{constructor(e,t=e){Ar(e),Ar(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return C.near(n);let i=e.resolve(t.map(this.anchor));return new O(i.parent.inlineContent?i:n,n)}replace(e,t=k.empty){if(super.replace(e,t),t==k.empty){let n=this.$from.marksAcross(this.$to);n&&e.ensureMarks(n)}}eq(e){return e instanceof O&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Gt(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new O(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let i=e.resolve(t);return new this(i,n==t?i:e.resolve(n))}static between(e,t,n){let i=e.pos-t.pos;if((!n||i)&&(n=i>=0?1:-1),!t.parent.inlineContent){let s=C.findFrom(t,n,!0)||C.findFrom(t,-n,!0);if(s)t=s.$head;else return C.near(t,n)}return e.parent.inlineContent||(i==0?e=t:(e=(C.findFrom(e,-n,!0)||C.findFrom(e,n,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new O(e,t)}}C.jsonID("text",O);class Gt{constructor(e,t){this.anchor=e,this.head=t}map(e){return new Gt(e.map(this.anchor),e.map(this.head))}resolve(e){return O.between(e.resolve(this.anchor),e.resolve(this.head))}}class M extends C{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:i}=t.mapResult(this.anchor),s=e.resolve(i);return n?C.near(s):new M(s)}content(){return new k(y.from(this.node),0,0)}eq(e){return e instanceof M&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new er(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new M(e.resolve(t.anchor))}static create(e,t){return new M(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}M.prototype.visible=!1;C.jsonID("node",M);class er{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new Gt(n,n):new er(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&M.isSelectable(n)?new M(t):C.near(t)}}class re extends C{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=k.empty){if(t==k.empty){e.delete(0,e.doc.content.size);let n=C.atStart(e.doc);n.eq(e.selection)||e.setSelection(n)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new re(e)}map(e){return new re(e)}eq(e){return e instanceof re}getBookmark(){return ol}}C.jsonID("all",re);const ol={map(){return this},resolve(r){return new re(r)}};function je(r,e,t,n,i,s=!1){if(e.inlineContent)return O.create(r,t);for(let o=n-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let l=e.child(o);if(l.isAtom){if(!s&&M.isSelectable(l))return M.create(r,t-(i<0?l.nodeSize:0))}else{let a=je(r,l,t+i,i<0?l.childCount:0,i,s);if(a)return a}t+=l.nodeSize*i}return null}function vr(r,e,t){let n=r.steps.length-1;if(n<e)return;let i=r.steps[n];if(!(i instanceof V||i instanceof I))return;let s=r.mapping.maps[n],o;s.forEach((l,a,c,d)=>{o==null&&(o=d)}),r.setSelection(C.near(r.doc.resolve(o),t))}const Dr=1,Et=2,Ir=4;class ll extends il{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|Dr)&~Et,this.storedMarks=null,this}get selectionSet(){return(this.updated&Dr)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=Et,this}ensureMarks(e){return N.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&Et)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&~Et,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||N.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(n==null&&(n=t),n=n??t,!e)return this.deleteRange(t,n);let s=this.storedMarks;if(!s){let o=this.doc.resolve(t);s=n==t?o.marks():o.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,i.text(e,s)),this.selection.empty||this.setSelection(C.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=Ir,this}get scrolledIntoView(){return(this.updated&Ir)>0}}function Rr(r,e){return!e||!r?r:r.bind(e)}class gt{constructor(e,t,n){this.name=e,this.init=Rr(t.init,n),this.apply=Rr(t.apply,n)}}const al=[new gt("doc",{init(r){return r.doc||r.schema.topNodeType.createAndFill()},apply(r){return r.doc}}),new gt("selection",{init(r,e){return r.selection||C.atStart(e.doc)},apply(r){return r.selection}}),new gt("storedMarks",{init(r){return r.storedMarks||null},apply(r,e,t,n){return n.selection.$cursor?r.storedMarks:null}}),new gt("scrollToSelection",{init(){return 0},apply(r,e){return r.scrolledIntoView?e+1:e}})];class gn{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=al.slice(),t&&t.forEach(n=>{if(this.pluginsByKey[n.key])throw new RangeError("Adding different instances of a keyed plugin ("+n.key+")");this.plugins.push(n),this.pluginsByKey[n.key]=n,n.spec.state&&this.fields.push(new gt(n.key,n.spec.state,n))})}}class Ge{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let i=this.config.plugins[n];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),i=null;for(;;){let s=!1;for(let o=0;o<this.config.plugins.length;o++){let l=this.config.plugins[o];if(l.spec.appendTransaction){let a=i?i[o].n:0,c=i?i[o].state:this,d=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,n);if(d&&n.filterTransaction(d,o)){if(d.setMeta("appendedTransaction",e),!i){i=[];for(let u=0;u<this.config.plugins.length;u++)i.push(u<o?{state:n,n:t.length}:{state:this,n:0})}t.push(d),n=n.applyInner(d),s=!0}i&&(i[o]={state:n,n:t.length})}}if(!s)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Ge(this.config),n=this.config.fields;for(let i=0;i<n.length;i++){let s=n[i];t[s.name]=s.apply(e,this[s.name],this,t)}return t}get tr(){return new ll(this)}static create(e){let t=new gn(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new Ge(t);for(let i=0;i<t.fields.length;i++)n[t.fields[i].name]=t.fields[i].init(e,n);return n}reconfigure(e){let t=new gn(this.schema,e.plugins),n=t.fields,i=new Ge(t);for(let s=0;s<n.length;s++){let o=n[s].name;i[o]=this.hasOwnProperty(o)?this[o]:n[s].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(n=>n.toJSON())),e&&typeof e=="object")for(let n in e){if(n=="doc"||n=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[n],s=i.spec.state;s&&s.toJSON&&(t[n]=s.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new gn(e.schema,e.plugins),s=new Ge(i);return i.fields.forEach(o=>{if(o.name=="doc")s.doc=Le.fromJSON(e.schema,t.doc);else if(o.name=="selection")s.selection=C.fromJSON(s.doc,t.selection);else if(o.name=="storedMarks")t.storedMarks&&(s.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let l in n){let a=n[l],c=a.spec.state;if(a.key==o.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){s[o.name]=c.fromJSON.call(a,e,t[l],s);return}}s[o.name]=o.init(e,s)}}),s}}function Yi(r,e,t){for(let n in r){let i=r[n];i instanceof Function?i=i.bind(e):n=="handleDOMEvents"&&(i=Yi(i,e,{})),t[n]=i}return t}class ee{constructor(e){this.spec=e,this.props={},e.props&&Yi(e.props,this,this.props),this.key=e.key?e.key.key:Xi("plugin")}getState(e){return e[this.key]}}const yn=Object.create(null);function Xi(r){return r in yn?r+"$"+ ++yn[r]:(yn[r]=0,r+"$")}class ve{constructor(e="key"){this.key=Xi(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const _=function(r){for(var e=0;;e++)if(r=r.previousSibling,!r)return e},Mt=function(r){let e=r.assignedSlot||r.parentNode;return e&&e.nodeType==11?e.host:e};let Pr=null;const pe=function(r,e,t){let n=Pr||(Pr=document.createRange());return n.setEnd(r,t??r.nodeValue.length),n.setStart(r,e||0),n},Je=function(r,e,t,n){return t&&(Br(r,e,t,n,-1)||Br(r,e,t,n,1))},cl=/^(img|br|input|textarea|hr)$/i;function Br(r,e,t,n,i){for(;;){if(r==t&&e==n)return!0;if(e==(i<0?0:ae(r))){let s=r.parentNode;if(!s||s.nodeType!=1||ul(r)||cl.test(r.nodeName)||r.contentEditable=="false")return!1;e=_(r)+(i<0?0:1),r=s}else if(r.nodeType==1){if(r=r.childNodes[e+(i<0?-1:0)],r.contentEditable=="false")return!1;e=i<0?ae(r):0}else return!1}}function ae(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function dl(r,e,t){for(let n=e==0,i=e==ae(r);n||i;){if(r==t)return!0;let s=_(r);if(r=r.parentNode,!r)return!1;n=n&&s==0,i=i&&s==ae(r)}}function ul(r){let e;for(let t=r;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==r||e.contentDOM==r)}const _t=function(r){return r.focusNode&&Je(r.focusNode,r.focusOffset,r.anchorNode,r.anchorOffset)};function _e(r,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=r,t.key=t.code=e,t}function fl(r){let e=r.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}const Ne=typeof navigator<"u"?navigator:null,zr=typeof document<"u"?document:null,De=Ne&&Ne.userAgent||"",zn=/Edge\/(\d+)/.exec(De),Zi=/MSIE \d/.exec(De),Fn=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(De),j=!!(Zi||Fn||zn),Ce=Zi?document.documentMode:Fn?+Fn[1]:zn?+zn[1]:0,se=!j&&/gecko\/(\d+)/i.test(De);se&&+(/Firefox\/(\d+)/.exec(De)||[0,0])[1];const Ln=!j&&/Chrome\/(\d+)/.exec(De),K=!!Ln,hl=Ln?+Ln[1]:0,H=!j&&!!Ne&&/Apple Computer/.test(Ne.vendor),st=H&&(/Mobile\/\w+/.test(De)||!!Ne&&Ne.maxTouchPoints>2),X=st||(Ne?/Mac/.test(Ne.platform):!1),ce=/Android \d/.test(De),Yt=!!zr&&"webkitFontSmoothing"in zr.documentElement.style,pl=Yt?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function ml(r){return{left:0,right:r.documentElement.clientWidth,top:0,bottom:r.documentElement.clientHeight}}function ye(r,e){return typeof r=="number"?r:r[e]}function gl(r){let e=r.getBoundingClientRect(),t=e.width/r.offsetWidth||1,n=e.height/r.offsetHeight||1;return{left:e.left,right:e.left+r.clientWidth*t,top:e.top,bottom:e.top+r.clientHeight*n}}function Fr(r,e,t){let n=r.someProp("scrollThreshold")||0,i=r.someProp("scrollMargin")||5,s=r.dom.ownerDocument;for(let o=t||r.dom;o;o=Mt(o)){if(o.nodeType!=1)continue;let l=o,a=l==s.body,c=a?ml(s):gl(l),d=0,u=0;if(e.top<c.top+ye(n,"top")?u=-(c.top-e.top+ye(i,"top")):e.bottom>c.bottom-ye(n,"bottom")&&(u=e.bottom-c.bottom+ye(i,"bottom")),e.left<c.left+ye(n,"left")?d=-(c.left-e.left+ye(i,"left")):e.right>c.right-ye(n,"right")&&(d=e.right-c.right+ye(i,"right")),d||u)if(a)s.defaultView.scrollBy(d,u);else{let f=l.scrollLeft,h=l.scrollTop;u&&(l.scrollTop+=u),d&&(l.scrollLeft+=d);let p=l.scrollLeft-f,m=l.scrollTop-h;e={left:e.left-p,top:e.top-m,right:e.right-p,bottom:e.bottom-m}}if(a)break}}function yl(r){let e=r.dom.getBoundingClientRect(),t=Math.max(0,e.top),n,i;for(let s=(e.left+e.right)/2,o=t+1;o<Math.min(innerHeight,e.bottom);o+=5){let l=r.root.elementFromPoint(s,o);if(!l||l==r.dom||!r.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){n=l,i=a.top;break}}return{refDOM:n,refTop:i,stack:Qi(r.dom)}}function Qi(r){let e=[],t=r.ownerDocument;for(let n=r;n&&(e.push({dom:n,top:n.scrollTop,left:n.scrollLeft}),r!=t);n=Mt(n));return e}function bl({refDOM:r,refTop:e,stack:t}){let n=r?r.getBoundingClientRect().top:0;es(t,n==0?0:n-e)}function es(r,e){for(let t=0;t<r.length;t++){let{dom:n,top:i,left:s}=r[t];n.scrollTop!=i+e&&(n.scrollTop=i+e),n.scrollLeft!=s&&(n.scrollLeft=s)}}let Ke=null;function kl(r){if(r.setActive)return r.setActive();if(Ke)return r.focus(Ke);let e=Qi(r);r.focus(Ke==null?{get preventScroll(){return Ke={preventScroll:!0},!0}}:void 0),Ke||(Ke=!1,es(e,0))}function ts(r,e){let t,n=2e8,i,s=0,o=e.top,l=e.top;for(let a=r.firstChild,c=0;a;a=a.nextSibling,c++){let d;if(a.nodeType==1)d=a.getClientRects();else if(a.nodeType==3)d=pe(a).getClientRects();else continue;for(let u=0;u<d.length;u++){let f=d[u];if(f.top<=o&&f.bottom>=l){o=Math.max(f.bottom,o),l=Math.min(f.top,l);let h=f.left>e.left?f.left-e.left:f.right<e.left?e.left-f.right:0;if(h<n){t=a,n=h,i=h&&t.nodeType==3?{left:f.right<e.left?f.right:f.left,top:e.top}:e,a.nodeType==1&&h&&(s=c+(e.left>=(f.left+f.right)/2?1:0));continue}}!t&&(e.left>=f.right&&e.top>=f.top||e.left>=f.left&&e.top>=f.bottom)&&(s=c+1)}}return t&&t.nodeType==3?xl(t,i):!t||n&&t.nodeType==1?{node:r,offset:s}:ts(t,i)}function xl(r,e){let t=r.nodeValue.length,n=document.createRange();for(let i=0;i<t;i++){n.setEnd(r,i+1),n.setStart(r,i);let s=be(n,1);if(s.top!=s.bottom&&tr(e,s))return{node:r,offset:i+(e.left>=(s.left+s.right)/2?1:0)}}return{node:r,offset:0}}function tr(r,e){return r.left>=e.left-1&&r.left<=e.right+1&&r.top>=e.top-1&&r.top<=e.bottom+1}function Sl(r,e){let t=r.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<r.getBoundingClientRect().left?t:r}function Ml(r,e,t){let{node:n,offset:i}=ts(e,t),s=-1;if(n.nodeType==1&&!n.firstChild){let o=n.getBoundingClientRect();s=o.left!=o.right&&t.left>(o.left+o.right)/2?1:-1}return r.docView.posFromDOM(n,i,s)}function wl(r,e,t,n){let i=-1;for(let s=e;s!=r.dom;){let o=r.docView.nearestDesc(s,!0);if(!o)return null;if(o.node.isBlock&&o.parent){let l=o.dom.getBoundingClientRect();if(l.left>n.left||l.top>n.top)i=o.posBefore;else if(l.right<n.left||l.bottom<n.top)i=o.posAfter;else break}s=o.dom.parentNode}return i>-1?i:r.docView.posFromDOM(e,t,1)}function ns(r,e,t){let n=r.childNodes.length;if(n&&t.top<t.bottom)for(let i=Math.max(0,Math.min(n-1,Math.floor(n*(e.top-t.top)/(t.bottom-t.top))-2)),s=i;;){let o=r.childNodes[s];if(o.nodeType==1){let l=o.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(tr(e,c))return ns(o,e,c)}}if((s=(s+1)%n)==i)break}return r}function Cl(r,e){let t=r.dom.ownerDocument,n,i=0;if(t.caretPositionFromPoint)try{let a=t.caretPositionFromPoint(e.left,e.top);a&&({offsetNode:n,offset:i}=a)}catch{}if(!n&&t.caretRangeFromPoint){let a=t.caretRangeFromPoint(e.left,e.top);a&&({startContainer:n,startOffset:i}=a)}let s=(r.root.elementFromPoint?r.root:t).elementFromPoint(e.left,e.top),o;if(!s||!r.dom.contains(s.nodeType!=1?s.parentNode:s)){let a=r.dom.getBoundingClientRect();if(!tr(e,a)||(s=ns(r.dom,e,a),!s))return null}if(H)for(let a=s;n&&a;a=Mt(a))a.draggable&&(n=void 0);if(s=Sl(s,e),n){if(se&&n.nodeType==1&&(i=Math.min(i,n.childNodes.length),i<n.childNodes.length)){let a=n.childNodes[i],c;a.nodeName=="IMG"&&(c=a.getBoundingClientRect()).right<=e.left&&c.bottom>e.top&&i++}n==r.dom&&i==n.childNodes.length-1&&n.lastChild.nodeType==1&&e.top>n.lastChild.getBoundingClientRect().bottom?o=r.state.doc.content.size:(i==0||n.nodeType!=1||n.childNodes[i-1].nodeName!="BR")&&(o=wl(r,n,i,e))}o==null&&(o=Ml(r,s,e));let l=r.docView.nearestDesc(s,!0);return{pos:o,inside:l?l.posAtStart-l.border:-1}}function be(r,e){let t=r.getClientRects();return t.length?t[e<0?0:t.length-1]:r.getBoundingClientRect()}const Ol=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function rs(r,e,t){let{node:n,offset:i,atom:s}=r.docView.domFromPos(e,t<0?-1:1),o=Yt||se;if(n.nodeType==3)if(o&&(Ol.test(n.nodeValue)||(t<0?!i:i==n.nodeValue.length))){let a=be(pe(n,i,i),t);if(se&&i&&/\s/.test(n.nodeValue[i-1])&&i<n.nodeValue.length){let c=be(pe(n,i-1,i-1),-1);if(c.top==a.top){let d=be(pe(n,i,i+1),-1);if(d.top!=a.top)return ht(d,d.left<c.left)}}return a}else{let a=i,c=i,d=t<0?1:-1;return t<0&&!i?(c++,d=-1):t>=0&&i==n.nodeValue.length?(a--,d=1):t<0?a--:c++,ht(be(pe(n,a,c),1),d<0)}if(!r.state.doc.resolve(e-(s||0)).parent.inlineContent){if(s==null&&i&&(t<0||i==ae(n))){let a=n.childNodes[i-1];if(a.nodeType==1)return bn(a.getBoundingClientRect(),!1)}if(s==null&&i<ae(n)){let a=n.childNodes[i];if(a.nodeType==1)return bn(a.getBoundingClientRect(),!0)}return bn(n.getBoundingClientRect(),t>=0)}if(s==null&&i&&(t<0||i==ae(n))){let a=n.childNodes[i-1],c=a.nodeType==3?pe(a,ae(a)-(o?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return ht(be(c,1),!1)}if(s==null&&i<ae(n)){let a=n.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?pe(a,0,o?0:1):a.nodeType==1?a:null:null;if(c)return ht(be(c,-1),!0)}return ht(be(n.nodeType==3?pe(n):n,-t),t>=0)}function ht(r,e){if(r.width==0)return r;let t=e?r.left:r.right;return{top:r.top,bottom:r.bottom,left:t,right:t}}function bn(r,e){if(r.height==0)return r;let t=e?r.top:r.bottom;return{top:t,bottom:t,left:r.left,right:r.right}}function is(r,e,t){let n=r.state,i=r.root.activeElement;n!=e&&r.updateState(e),i!=r.dom&&r.focus();try{return t()}finally{n!=e&&r.updateState(n),i!=r.dom&&i&&i.focus()}}function Tl(r,e,t){let n=e.selection,i=t=="up"?n.$from:n.$to;return is(r,e,()=>{let{node:s}=r.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=r.docView.nearestDesc(s,!0);if(!l)break;if(l.node.isBlock){s=l.dom;break}s=l.dom.parentNode}let o=rs(r,i.pos,1);for(let l=s.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=pe(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let d=a[c];if(d.bottom>d.top+1&&(t=="up"?o.top-d.top>(d.bottom-o.top)*2:d.bottom-o.bottom>(o.bottom-d.top)*2))return!1}}return!0})}const Nl=/[\u0590-\u08ac]/;function El(r,e,t){let{$head:n}=e.selection;if(!n.parent.isTextblock)return!1;let i=n.parentOffset,s=!i,o=i==n.parent.content.size,l=r.domSelection();return!Nl.test(n.parent.textContent)||!l.modify?t=="left"||t=="backward"?s:o:is(r,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:d,anchorOffset:u}=r.domSelectionRange(),f=l.caretBidiLevel;l.modify("move",t,"character");let h=n.depth?r.docView.domAfterPos(n.before()):r.dom,{focusNode:p,focusOffset:m}=r.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(d,u),a&&(a!=d||c!=u)&&l.extend&&l.extend(a,c)}catch{}return f!=null&&(l.caretBidiLevel=f),g})}let Lr=null,Vr=null,Hr=!1;function Al(r,e,t){return Lr==e&&Vr==t?Hr:(Lr=e,Vr=t,Hr=t=="up"||t=="down"?Tl(r,e,t):El(r,e,t))}const ie=0,$r=1,Ye=2,fe=3;class Ot{constructor(e,t,n,i){this.parent=e,this.children=t,this.dom=n,this.contentDOM=i,this.dirty=ie,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let i=this.children[t];if(i==e)return n;n+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(n<0){let s,o;if(e==this.contentDOM)s=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.previousSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.previousSibling;return s?this.posBeforeChild(o)+o.size:this.posAtStart}else{let s,o;if(e==this.contentDOM)s=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.nextSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.nextSibling;return s?this.posBeforeChild(o):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>_(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!1;break}if(s.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!0;break}if(s.nextSibling)break}}return i??n>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,i=e;i;i=i.parentNode){let s=this.getDesc(i),o;if(s&&(!t||s.node))if(n&&(o=s.nodeDOM)&&!(o.nodeType==1?o.contains(e.nodeType==1?e:e.parentNode):o==e))n=!1;else return s}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let i=e;i;i=i.parentNode){let s=this.getDesc(i);if(s)return s.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let i=this.children[t],s=n+i.size;if(n==e&&s!=n){for(;!i.border&&i.children.length;)i=i.children[0];return i}if(e<s)return i.descAt(e-n-i.border);n=s}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,i=0;for(let s=0;n<this.children.length;n++){let o=this.children[n],l=s+o.size;if(l>e||o instanceof ls){i=e-s;break}s=l}if(i)return this.children[n].domFromPos(i-this.children[n].border,t);for(let s;n&&!(s=this.children[n-1]).size&&s instanceof ss&&s.side>=0;n--);if(t<=0){let s,o=!0;for(;s=n?this.children[n-1]:null,!(!s||s.dom.parentNode==this.contentDOM);n--,o=!1);return s&&t&&o&&!s.border&&!s.domAtom?s.domFromPos(s.size,t):{node:this.contentDOM,offset:s?_(s.dom)+1:0}}else{let s,o=!0;for(;s=n<this.children.length?this.children[n]:null,!(!s||s.dom.parentNode==this.contentDOM);n++,o=!1);return s&&o&&!s.border&&!s.domAtom?s.domFromPos(0,t):{node:this.contentDOM,offset:s?_(s.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,s=-1;for(let o=n,l=0;;l++){let a=this.children[l],c=o+a.size;if(i==-1&&e<=c){let d=o+a.border;if(e>=d&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,d);e=o;for(let u=l;u>0;u--){let f=this.children[u-1];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(1)){i=_(f.dom)+1;break}e-=f.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let d=l+1;d<this.children.length;d++){let u=this.children[d];if(u.size&&u.dom.parentNode==this.contentDOM&&!u.emptyChildAt(-1)){s=_(u.dom);break}t+=u.size}s==-1&&(s=this.contentDOM.childNodes.length);break}o=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:s}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(t.nodeType!=1||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,i=!1){let s=Math.min(e,t),o=Math.max(e,t);for(let f=0,h=0;f<this.children.length;f++){let p=this.children[f],m=h+p.size;if(s>h&&o<m)return p.setSelection(e-h-p.border,t-h-p.border,n,i);h=m}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=n.getSelection(),d=!1;if((se||H)&&e==t){let{node:f,offset:h}=l;if(f.nodeType==3){if(d=!!(h&&f.nodeValue[h-1]==`
`),d&&h==f.nodeValue.length)for(let p=f,m;p;p=p.parentNode){if(m=p.nextSibling){m.nodeName=="BR"&&(l=a={node:m.parentNode,offset:_(m)+1});break}let g=p.pmViewDesc;if(g&&g.node&&g.node.isBlock)break}}else{let p=f.childNodes[h-1];d=p&&(p.nodeName=="BR"||p.contentEditable=="false")}}if(se&&c.focusNode&&c.focusNode!=a.node&&c.focusNode.nodeType==1){let f=c.focusNode.childNodes[c.focusOffset];f&&f.contentEditable=="false"&&(i=!0)}if(!(i||d&&H)&&Je(l.node,l.offset,c.anchorNode,c.anchorOffset)&&Je(a.node,a.offset,c.focusNode,c.focusOffset))return;let u=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),u=!0}catch{}}if(!u){if(e>t){let h=l;l=a,a=h}let f=document.createRange();f.setEnd(a.node,a.offset),f.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(f)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,i=0;i<this.children.length;i++){let s=this.children[i],o=n+s.size;if(n==o?e<=o&&t>=n:e<o&&t>n){let l=n+s.border,a=o-s.border;if(e>=l&&t<=a){this.dirty=e==n||t==o?Ye:$r,e==l&&t==a&&(s.contentLost||s.dom.parentNode!=this.contentDOM)?s.dirty=fe:s.markDirty(e-l,t-l);return}else s.dirty=s.dom==s.contentDOM&&s.dom.parentNode==this.contentDOM&&!s.children.length?Ye:fe}n=o}this.dirty=Ye}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=e==1?Ye:$r;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}}class ss extends Ot{constructor(e,t,n,i){let s,o=t.type.toDOM;if(typeof o=="function"&&(o=o(n,()=>{if(!s)return i;if(s.parent)return s.parent.posBeforeChild(s)})),!t.type.spec.raw){if(o.nodeType!=1){let l=document.createElement("span");l.appendChild(o),o=l}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,s=this}matchesWidget(e){return this.dirty==ie&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class vl extends Ot{constructor(e,t,n,i){super(e,[],t,null),this.textDOM=n,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class We extends Ot{constructor(e,t,n,i){super(e,[],n,i),this.mark=t}static create(e,t,n,i){let s=i.nodeViews[t.type.name],o=s&&s(t,i,n);return(!o||!o.dom)&&(o=de.renderSpec(document,t.type.spec.toDOM(t,n))),new We(e,t,o.dom,o.contentDOM||o.dom)}parseRule(){return this.dirty&fe||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM||void 0}}matchesMark(e){return this.dirty!=fe&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=ie){let n=this.parent;for(;!n.node;)n=n.parent;n.dirty<this.dirty&&(n.dirty=this.dirty),this.dirty=ie}}slice(e,t,n){let i=We.create(this.parent,this.mark,!0,n),s=this.children,o=this.size;t<o&&(s=$n(s,t,o,n)),e>0&&(s=$n(s,0,e,n));for(let l=0;l<s.length;l++)s[l].parent=i;return i.children=s,i}}class qe extends Ot{constructor(e,t,n,i,s,o,l,a,c){super(e,[],s,o),this.node=t,this.outerDeco=n,this.innerDeco=i,this.nodeDOM=l,o&&this.updateChildren(a,c)}static create(e,t,n,i,s,o){let l=s.nodeViews[t.type.name],a,c=l&&l(t,s,()=>{if(!a)return o;if(a.parent)return a.parent.posBeforeChild(a)},n,i),d=c&&c.dom,u=c&&c.contentDOM;if(t.isText){if(!d)d=document.createTextNode(t.text);else if(d.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else d||({dom:d,contentDOM:u}=de.renderSpec(document,t.type.spec.toDOM(t)));!u&&!t.isText&&d.nodeName!="BR"&&(d.hasAttribute("contenteditable")||(d.contentEditable="false"),t.type.spec.draggable&&(d.draggable=!0));let f=d;return d=ds(d,n,t),c?a=new Dl(e,t,n,i,d,u||null,f,c,s,o+1):t.isText?new Xt(e,t,n,i,d,f,s):new qe(e,t,n,i,d,u||null,f,s,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>y.empty)}return e}matchesNode(e,t,n){return this.dirty==ie&&e.eq(this.node)&&Hn(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,i=t,s=e.composing?this.localCompositionInfo(e,t):null,o=s&&s.pos>-1?s:null,l=s&&s.pos<0,a=new Rl(this,o&&o.node,e);zl(this.node,this.innerDeco,(c,d,u)=>{c.spec.marks?a.syncToMarks(c.spec.marks,n,e):c.type.side>=0&&!u&&a.syncToMarks(d==this.node.childCount?N.none:this.node.child(d).marks,n,e),a.placeWidget(c,e,i)},(c,d,u,f)=>{a.syncToMarks(c.marks,n,e);let h;a.findNodeMatch(c,d,u,f)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(h=a.findIndexWithChild(s.node))>-1&&a.updateNodeAt(c,d,u,h,e)||a.updateNextNode(c,d,u,e,f)||a.addNode(c,d,u,e,i),i+=c.nodeSize}),a.syncToMarks([],n,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==Ye)&&(o&&this.protectLocalComposition(e,o),as(this.contentDOM,this.children,e),st&&Fl(this.dom))}localCompositionInfo(e,t){let{from:n,to:i}=e.state.selection;if(!(e.state.selection instanceof O)||n<t||i>t+this.node.content.size)return null;let s=e.domSelectionRange(),o=Ll(s.focusNode,s.focusOffset);if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let l=o.nodeValue,a=Vl(this.node.content,l,n-t,i-t);return a<0?null:{node:o,pos:a,text:l}}else return{node:o,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:i}){if(this.getDesc(t))return;let s=t;for(;s.parentNode!=this.contentDOM;s=s.parentNode){for(;s.previousSibling;)s.parentNode.removeChild(s.previousSibling);for(;s.nextSibling;)s.parentNode.removeChild(s.nextSibling);s.pmViewDesc&&(s.pmViewDesc=void 0)}let o=new vl(this,s,t,i);e.input.compositionNodes.push(o),this.children=$n(this.children,n,n+i.length,e,o)}update(e,t,n,i){return this.dirty==fe||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,n,i),!0)}updateInner(e,t,n,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=ie}updateOuterDeco(e){if(Hn(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,n=this.dom;this.dom=cs(this.dom,this.nodeDOM,Vn(this.outerDeco,this.node,t),Vn(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable")}get domAtom(){return this.node.isAtom}}function Jr(r,e,t,n,i){return ds(n,e,r),new qe(void 0,r,e,t,n,n,n,i,0)}class Xt extends qe{constructor(e,t,n,i,s,o,l){super(e,t,n,i,s,null,o,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,i){return this.dirty==fe||this.dirty!=ie&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=ie||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=ie,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,n){let i=this.node.cut(e,t),s=document.createTextNode(i.text);return new Xt(this.parent,i,this.outerDeco,this.innerDeco,s,s,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=fe)}get domAtom(){return!1}}class ls extends Ot{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==ie&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class Dl extends qe{constructor(e,t,n,i,s,o,l,a,c,d){super(e,t,n,i,s,o,l,c,d),this.spec=a}update(e,t,n,i){if(this.dirty==fe)return!1;if(this.spec.update){let s=this.spec.update(e,t,n);return s&&this.updateInner(e,t,n,i),s}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,n,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,i){this.spec.setSelection?this.spec.setSelection(e,t,n):super.setSelection(e,t,n,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function as(r,e,t){let n=r.firstChild,i=!1;for(let s=0;s<e.length;s++){let o=e[s],l=o.dom;if(l.parentNode==r){for(;l!=n;)n=Wr(n),i=!0;n=n.nextSibling}else i=!0,r.insertBefore(l,n);if(o instanceof We){let a=n?n.previousSibling:r.lastChild;as(o.contentDOM,o.children,t),n=a?a.nextSibling:r.firstChild}}for(;n;)n=Wr(n),i=!0;i&&t.trackWrites==r&&(t.trackWrites=null)}const kt=function(r){r&&(this.nodeName=r)};kt.prototype=Object.create(null);const Pe=[new kt];function Vn(r,e,t){if(r.length==0)return Pe;let n=t?Pe[0]:new kt,i=[n];for(let s=0;s<r.length;s++){let o=r[s].type.attrs;if(o){o.nodeName&&i.push(n=new kt(o.nodeName));for(let l in o){let a=o[l];a!=null&&(t&&i.length==1&&i.push(n=new kt(e.isInline?"span":"div")),l=="class"?n.class=(n.class?n.class+" ":"")+a:l=="style"?n.style=(n.style?n.style+";":"")+a:l!="nodeName"&&(n[l]=a))}}}return i}function cs(r,e,t,n){if(t==Pe&&n==Pe)return e;let i=e;for(let s=0;s<n.length;s++){let o=n[s],l=t[s];if(s){let a;l&&l.nodeName==o.nodeName&&i!=r&&(a=i.parentNode)&&a.nodeName.toLowerCase()==o.nodeName||(a=document.createElement(o.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=Pe[0]),i=a}Il(i,l||Pe[0],o)}return i}function Il(r,e,t){for(let n in e)n!="class"&&n!="style"&&n!="nodeName"&&!(n in t)&&r.removeAttribute(n);for(let n in t)n!="class"&&n!="style"&&n!="nodeName"&&t[n]!=e[n]&&r.setAttribute(n,t[n]);if(e.class!=t.class){let n=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let s=0;s<n.length;s++)i.indexOf(n[s])==-1&&r.classList.remove(n[s]);for(let s=0;s<i.length;s++)n.indexOf(i[s])==-1&&r.classList.add(i[s]);r.classList.length==0&&r.removeAttribute("class")}if(e.style!=t.style){if(e.style){let n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=n.exec(e.style);)r.style.removeProperty(i[1])}t.style&&(r.style.cssText+=t.style)}}function ds(r,e,t){return cs(r,r,Pe,Vn(e,t,r.nodeType!=1))}function Hn(r,e){if(r.length!=e.length)return!1;for(let t=0;t<r.length;t++)if(!r[t].type.eq(e[t].type))return!1;return!0}function Wr(r){let e=r.nextSibling;return r.parentNode.removeChild(r),e}class Rl{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=Pl(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let i=0,s=this.stack.length>>1,o=Math.min(s,e.length);for(;i<o&&(i==s-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<s;)this.destroyRest(),this.top.dirty=ie,this.index=this.stack.pop(),this.top=this.stack.pop(),s--;for(;s<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++)if(this.top.children[a].matchesMark(e[s])){l=a;break}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=We.create(this.top,e[s],t,n);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,s++}}findNodeMatch(e,t,n,i){let s=-1,o;if(i>=this.preMatch.index&&(o=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))s=this.top.children.indexOf(o,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,n)&&!this.preMatch.matched.has(c)){s=l;break}}return s<0?!1:(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,n,i,s){let o=this.top.children[i];return o.dirty==fe&&o.dom==o.contentDOM&&(o.dirty=Ye),o.update(e,t,n,s)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let n=e.pmViewDesc;if(n){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==n)return i}return-1}e=t}}updateNextNode(e,t,n,i,s){for(let o=this.index;o<this.top.children.length;o++){let l=this.top.children[o];if(l instanceof qe){let a=this.preMatch.matched.get(l);if(a!=null&&a!=s)return!1;let c=l.dom;if(!(this.lock&&(c==this.lock||c.nodeType==1&&c.contains(this.lock.parentNode))&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&l.dirty!=fe&&Hn(t,l.outerDeco)))&&l.update(e,t,n,i))return this.destroyBetween(this.index,o),l.dom!=c&&(this.changed=!0),this.index++,!0;break}}return!1}addNode(e,t,n,i,s){this.top.children.splice(this.index++,0,qe.create(this.top,e,t,n,i,s)),this.changed=!0}placeWidget(e,t,n){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let s=new ss(this.top,e,t,n);this.top.children.splice(this.index++,0,s),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof We;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Xt)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((H||K)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);e=="IMG"&&(n.className="ProseMirror-separator",n.alt=""),e=="BR"&&(n.className="ProseMirror-trailingBreak");let i=new ls(this.top,[],n,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}}function Pl(r,e){let t=e,n=t.children.length,i=r.childCount,s=new Map,o=[];e:for(;i>0;){let l;for(;;)if(n){let c=t.children[n-1];if(c instanceof We)t=c,n=c.children.length;else{l=c,n--;break}}else{if(t==e)break e;n=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=r.child(i-1))break;--i,s.set(l,i),o.push(l)}}return{index:i,matched:s,matches:o.reverse()}}function Bl(r,e){return r.type.side-e.type.side}function zl(r,e,t,n){let i=e.locals(r),s=0;if(i.length==0){for(let c=0;c<r.childCount;c++){let d=r.child(c);n(d,i,e.forChild(s,d),c),s+=d.nodeSize}return}let o=0,l=[],a=null;for(let c=0;;){if(o<i.length&&i[o].to==s){let p=i[o++],m;for(;o<i.length&&i[o].to==s;)(m||(m=[p])).push(i[o++]);if(m){m.sort(Bl);for(let g=0;g<m.length;g++)t(m[g],c,!!a)}else t(p,c,!!a)}let d,u;if(a)u=-1,d=a,a=null;else if(c<r.childCount)u=c,d=r.child(c++);else break;for(let p=0;p<l.length;p++)l[p].to<=s&&l.splice(p--,1);for(;o<i.length&&i[o].from<=s&&i[o].to>s;)l.push(i[o++]);let f=s+d.nodeSize;if(d.isText){let p=f;o<i.length&&i[o].from<p&&(p=i[o].from);for(let m=0;m<l.length;m++)l[m].to<p&&(p=l[m].to);p<f&&(a=d.cut(p-s),d=d.cut(0,p-s),f=p,u=-1)}let h=d.isInline&&!d.isLeaf?l.filter(p=>!p.inline):l.slice();n(d,h,e.forChild(s,d),u),s=f}}function Fl(r){if(r.nodeName=="UL"||r.nodeName=="OL"){let e=r.style.cssText;r.style.cssText=e+"; list-style: square !important",window.getComputedStyle(r).listStyle,r.style.cssText=e}}function Ll(r,e){for(;;){if(r.nodeType==3)return r;if(r.nodeType==1&&e>0){if(r.childNodes.length>e&&r.childNodes[e].nodeType==3)return r.childNodes[e];r=r.childNodes[e-1],e=ae(r)}else if(r.nodeType==1&&e<r.childNodes.length)r=r.childNodes[e],e=0;else return null}}function Vl(r,e,t,n){for(let i=0,s=0;i<r.childCount&&s<=n;){let o=r.child(i++),l=s;if(s+=o.nodeSize,!o.isText)continue;let a=o.text;for(;i<r.childCount;){let c=r.child(i++);if(s+=c.nodeSize,!c.isText)break;a+=c.text}if(s>=t){let c=l<n?a.lastIndexOf(e,n-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==n&&a.length>=n+e.length-l&&a.slice(n-l,n-l+e.length)==e)return n}}return-1}function $n(r,e,t,n,i){let s=[];for(let o=0,l=0;o<r.length;o++){let a=r[o],c=l,d=l+=a.size;c>=t||d<=e?s.push(a):(c<e&&s.push(a.slice(0,e-c,n)),i&&(s.push(i),i=void 0),d>t&&s.push(a.slice(t-c,a.size,n)))}return s}function nr(r,e=null){let t=r.domSelectionRange(),n=r.state.doc;if(!t.focusNode)return null;let i=r.docView.nearestDesc(t.focusNode),s=i&&i.size==0,o=r.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(o<0)return null;let l=n.resolve(o),a,c;if(_t(t)){for(a=l;i&&!i.node;)i=i.parent;let d=i.node;if(i&&d.isAtom&&M.isSelectable(d)&&i.parent&&!(d.isInline&&dl(t.focusNode,t.focusOffset,i.dom))){let u=i.posBefore;c=new M(o==u?l:n.resolve(u))}}else{let d=r.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(d<0)return null;a=n.resolve(d)}if(!c){let d=e=="pointer"||r.state.selection.head<l.pos&&!s?1:-1;c=rr(r,a,l,d)}return c}function us(r){return r.editable?r.hasFocus():hs(r)&&document.activeElement&&document.activeElement.contains(r.dom)}function me(r,e=!1){let t=r.state.selection;if(fs(r,t),!!us(r)){if(!e&&r.input.mouseDown&&r.input.mouseDown.allowDefault&&K){let n=r.domSelectionRange(),i=r.domObserver.currentSelection;if(n.anchorNode&&i.anchorNode&&Je(n.anchorNode,n.anchorOffset,i.anchorNode,i.anchorOffset)){r.input.mouseDown.delayedSelectionSync=!0,r.domObserver.setCurSelection();return}}if(r.domObserver.disconnectSelection(),r.cursorWrapper)$l(r);else{let{anchor:n,head:i}=t,s,o;qr&&!(t instanceof O)&&(t.$from.parent.inlineContent||(s=Kr(r,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(o=Kr(r,t.to))),r.docView.setSelection(n,i,r.root,e),qr&&(s&&jr(s),o&&jr(o)),t.visible?r.dom.classList.remove("ProseMirror-hideselection"):(r.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&Hl(r))}r.domObserver.setCurSelection(),r.domObserver.connectSelection()}}const qr=H||K&&hl<63;function Kr(r,e){let{node:t,offset:n}=r.docView.domFromPos(e,0),i=n<t.childNodes.length?t.childNodes[n]:null,s=n?t.childNodes[n-1]:null;if(H&&i&&i.contentEditable=="false")return kn(i);if((!i||i.contentEditable=="false")&&(!s||s.contentEditable=="false")){if(i)return kn(i);if(s)return kn(s)}}function kn(r){return r.contentEditable="true",H&&r.draggable&&(r.draggable=!1,r.wasDraggable=!0),r}function jr(r){r.contentEditable="false",r.wasDraggable&&(r.draggable=!0,r.wasDraggable=null)}function Hl(r){let e=r.dom.ownerDocument;e.removeEventListener("selectionchange",r.input.hideSelectionGuard);let t=r.domSelectionRange(),n=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",r.input.hideSelectionGuard=()=>{(t.anchorNode!=n||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",r.input.hideSelectionGuard),setTimeout(()=>{(!us(r)||r.state.selection.visible)&&r.dom.classList.remove("ProseMirror-hideselection")},20))})}function $l(r){let e=r.domSelection(),t=document.createRange(),n=r.cursorWrapper.dom,i=n.nodeName=="IMG";i?t.setEnd(n.parentNode,_(n)+1):t.setEnd(n,0),t.collapse(!1),e.removeAllRanges(),e.addRange(t),!i&&!r.state.selection.visible&&j&&Ce<=11&&(n.disabled=!0,n.disabled=!1)}function fs(r,e){if(e instanceof M){let t=r.docView.descAt(e.from);t!=r.lastSelectedViewDesc&&(Ur(r),t&&t.selectNode(),r.lastSelectedViewDesc=t)}else Ur(r)}function Ur(r){r.lastSelectedViewDesc&&(r.lastSelectedViewDesc.parent&&r.lastSelectedViewDesc.deselectNode(),r.lastSelectedViewDesc=void 0)}function rr(r,e,t,n){return r.someProp("createSelectionBetween",i=>i(r,e,t))||O.between(e,t,n)}function Gr(r){return r.editable&&!r.hasFocus()?!1:hs(r)}function hs(r){let e=r.domSelectionRange();if(!e.anchorNode)return!1;try{return r.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(r.editable||r.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Jl(r){let e=r.docView.domFromPos(r.state.selection.anchor,0),t=r.domSelectionRange();return Je(e.node,e.offset,t.anchorNode,t.anchorOffset)}function Jn(r,e){let{$anchor:t,$head:n}=r.selection,i=e>0?t.max(n):t.min(n),s=i.parent.inlineContent?i.depth?r.doc.resolve(e>0?i.after():i.before()):null:i;return s&&C.findFrom(s,e)}function Re(r,e){return r.dispatch(r.state.tr.setSelection(e).scrollIntoView()),!0}function _r(r,e,t){let n=r.state.selection;if(n instanceof O){if(!n.empty||t.indexOf("s")>-1)return!1;if(r.endOfTextblock(e>0?"right":"left")){let i=Jn(r.state,e);return i&&i instanceof M?Re(r,i):!1}else if(!(X&&t.indexOf("m")>-1)){let i=n.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,o;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return s.isAtom||(o=r.docView.descAt(l))&&!o.contentDOM?M.isSelectable(s)?Re(r,new M(e<0?r.state.doc.resolve(i.pos-s.nodeSize):i)):Yt?Re(r,new O(r.state.doc.resolve(e<0?l:l+s.nodeSize))):!1:!1}}else{if(n instanceof M&&n.node.isInline)return Re(r,new O(e>0?n.$to:n.$from));{let i=Jn(r.state,e);return i?Re(r,i):!1}}}function Ht(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function xt(r){let e=r.pmViewDesc;return e&&e.size==0&&(r.nextSibling||r.nodeName!="BR")}function xn(r){let e=r.domSelectionRange(),t=e.focusNode,n=e.focusOffset;if(!t)return;let i,s,o=!1;for(se&&t.nodeType==1&&n<Ht(t)&&xt(t.childNodes[n])&&(o=!0);;)if(n>0){if(t.nodeType!=1)break;{let l=t.childNodes[n-1];if(xt(l))i=t,s=--n;else if(l.nodeType==3)t=l,n=t.nodeValue.length;else break}}else{if(ps(t))break;{let l=t.previousSibling;for(;l&&xt(l);)i=t.parentNode,s=_(l),l=l.previousSibling;if(l)t=l,n=Ht(t);else{if(t=t.parentNode,t==r.dom)break;n=0}}}o?Wn(r,t,n):i&&Wn(r,i,s)}function Sn(r){let e=r.domSelectionRange(),t=e.focusNode,n=e.focusOffset;if(!t)return;let i=Ht(t),s,o;for(;;)if(n<i){if(t.nodeType!=1)break;let l=t.childNodes[n];if(xt(l))s=t,o=++n;else break}else{if(ps(t))break;{let l=t.nextSibling;for(;l&&xt(l);)s=l.parentNode,o=_(l)+1,l=l.nextSibling;if(l)t=l,n=0,i=Ht(t);else{if(t=t.parentNode,t==r.dom)break;n=i=0}}}s&&Wn(r,s,o)}function ps(r){let e=r.pmViewDesc;return e&&e.node&&e.node.isBlock}function Wn(r,e,t){let n=r.domSelection();if(_t(n)){let s=document.createRange();s.setEnd(e,t),s.setStart(e,t),n.removeAllRanges(),n.addRange(s)}else n.extend&&n.extend(e,t);r.domObserver.setCurSelection();let{state:i}=r;setTimeout(()=>{r.state==i&&me(r)},50)}function Yr(r,e,t){let n=r.state.selection;if(n instanceof O&&!n.empty||t.indexOf("s")>-1||X&&t.indexOf("m")>-1)return!1;let{$from:i,$to:s}=n;if(!i.parent.inlineContent||r.endOfTextblock(e<0?"up":"down")){let o=Jn(r.state,e);if(o&&o instanceof M)return Re(r,o)}if(!i.parent.inlineContent){let o=e<0?i:s,l=n instanceof re?C.near(o,e):C.findFrom(o,e);return l?Re(r,l):!1}return!1}function Xr(r,e){if(!(r.state.selection instanceof O))return!0;let{$head:t,$anchor:n,empty:i}=r.state.selection;if(!t.sameParent(n))return!0;if(!i)return!1;if(r.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(s&&!s.isText){let o=r.state.tr;return e<0?o.delete(t.pos-s.nodeSize,t.pos):o.delete(t.pos,t.pos+s.nodeSize),r.dispatch(o),!0}return!1}function Zr(r,e,t){r.domObserver.stop(),e.contentEditable=t,r.domObserver.start()}function Wl(r){if(!H||r.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=r.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let n=e.firstChild;Zr(r,n,"true"),setTimeout(()=>Zr(r,n,"false"),20)}return!1}function ql(r){let e="";return r.ctrlKey&&(e+="c"),r.metaKey&&(e+="m"),r.altKey&&(e+="a"),r.shiftKey&&(e+="s"),e}function Kl(r,e){let t=e.keyCode,n=ql(e);return t==8||X&&t==72&&n=="c"?Xr(r,-1)||xn(r):t==46||X&&t==68&&n=="c"?Xr(r,1)||Sn(r):t==13||t==27?!0:t==37||X&&t==66&&n=="c"?_r(r,-1,n)||xn(r):t==39||X&&t==70&&n=="c"?_r(r,1,n)||Sn(r):t==38||X&&t==80&&n=="c"?Yr(r,-1,n)||xn(r):t==40||X&&t==78&&n=="c"?Wl(r)||Yr(r,1,n)||Sn(r):n==(X?"m":"c")&&(t==66||t==73||t==89||t==90)}function ms(r,e){r.someProp("transformCopied",h=>{e=h(e,r)});let t=[],{content:n,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&n.childCount==1&&n.firstChild.childCount==1;){i--,s--;let h=n.firstChild;t.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),n=h.content}let o=r.someProp("clipboardSerializer")||de.fromSchema(r.state.schema),l=Ss(),a=l.createElement("div");a.appendChild(o.serializeFragment(n,{document:l}));let c=a.firstChild,d,u=0;for(;c&&c.nodeType==1&&(d=xs[c.nodeName.toLowerCase()]);){for(let h=d.length-1;h>=0;h--){let p=l.createElement(d[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),u++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${s}${u?` -${u}`:""} ${JSON.stringify(t)}`);let f=r.someProp("clipboardTextSerializer",h=>h(e,r))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:f}}function gs(r,e,t,n,i){let s=i.parent.type.spec.code,o,l;if(!t&&!e)return null;let a=e&&(n||s||!t);if(a){if(r.someProp("transformPastedText",f=>{e=f(e,s||n,r)}),s)return e?new k(y.from(r.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):k.empty;let u=r.someProp("clipboardTextParser",f=>f(e,i,n,r));if(u)l=u;else{let f=i.marks(),{schema:h}=r.state,p=de.fromSchema(h);o=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=o.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,f)))})}}else r.someProp("transformPastedHTML",u=>{t=u(t,r)}),o=Gl(t),Yt&&_l(o);let c=o&&o.querySelector("[data-pm-slice]"),d=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(d&&d[3])for(let u=+d[3];u>0;u--){let f=o.firstChild;for(;f&&f.nodeType!=1;)f=f.nextSibling;if(!f)break;o=f}if(l||(l=(r.someProp("clipboardParser")||r.someProp("domParser")||nt.fromSchema(r.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||d),context:i,ruleFromNode(f){return f.nodeName=="BR"&&!f.nextSibling&&f.parentNode&&!jl.test(f.parentNode.nodeName)?{ignore:!0}:null}})),d)l=Yl(Qr(l,+d[1],+d[2]),d[4]);else if(l=k.maxOpen(Ul(l.content,i),!0),l.openStart||l.openEnd){let u=0,f=0;for(let h=l.content.firstChild;u<l.openStart&&!h.type.spec.isolating;u++,h=h.firstChild);for(let h=l.content.lastChild;f<l.openEnd&&!h.type.spec.isolating;f++,h=h.lastChild);l=Qr(l,u,f)}return r.someProp("transformPasted",u=>{l=u(l,r)}),l}const jl=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Ul(r,e){if(r.childCount<2)return r;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),s,o=[];if(r.forEach(l=>{if(!o)return;let a=i.findWrapping(l.type),c;if(!a)return o=null;if(c=o.length&&s.length&&bs(a,s,l,o[o.length-1],0))o[o.length-1]=c;else{o.length&&(o[o.length-1]=ks(o[o.length-1],s.length));let d=ys(l,a);o.push(d),i=i.matchType(d.type),s=a}}),o)return y.from(o)}return r}function ys(r,e,t=0){for(let n=e.length-1;n>=t;n--)r=e[n].create(null,y.from(r));return r}function bs(r,e,t,n,i){if(i<r.length&&i<e.length&&r[i]==e[i]){let s=bs(r,e,t,n.lastChild,i+1);if(s)return n.copy(n.content.replaceChild(n.childCount-1,s));if(n.contentMatchAt(n.childCount).matchType(i==r.length-1?t.type:r[i+1]))return n.copy(n.content.append(y.from(ys(t,r,i+1))))}}function ks(r,e){if(e==0)return r;let t=r.content.replaceChild(r.childCount-1,ks(r.lastChild,e-1)),n=r.contentMatchAt(r.childCount).fillBefore(y.empty,!0);return r.copy(t.append(n))}function qn(r,e,t,n,i,s){let o=e<0?r.firstChild:r.lastChild,l=o.content;return i<n-1&&(l=qn(l,e,t,n,i+1,s)),i>=t&&(l=e<0?o.contentMatchAt(0).fillBefore(l,r.childCount>1||s<=i).append(l):l.append(o.contentMatchAt(o.childCount).fillBefore(y.empty,!0))),r.replaceChild(e<0?0:r.childCount-1,o.copy(l))}function Qr(r,e,t){return e<r.openStart&&(r=new k(qn(r.content,-1,e,r.openStart,0,r.openEnd),e,r.openEnd)),t<r.openEnd&&(r=new k(qn(r.content,1,t,r.openEnd,0,0),r.openStart,t)),r}const xs={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let ei=null;function Ss(){return ei||(ei=document.implementation.createHTMLDocument("title"))}function Gl(r){let e=/^(\s*<meta [^>]*>)*/.exec(r);e&&(r=r.slice(e[0].length));let t=Ss().createElement("div"),n=/<([a-z][^>\s]+)/i.exec(r),i;if((i=n&&xs[n[1].toLowerCase()])&&(r=i.map(s=>"<"+s+">").join("")+r+i.map(s=>"</"+s+">").reverse().join("")),t.innerHTML=r,i)for(let s=0;s<i.length;s++)t=t.querySelector(i[s])||t;return t}function _l(r){let e=r.querySelectorAll(K?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let n=e[t];n.childNodes.length==1&&n.textContent==" "&&n.parentNode&&n.parentNode.replaceChild(r.ownerDocument.createTextNode(" "),n)}}function Yl(r,e){if(!r.size)return r;let t=r.content.firstChild.type.schema,n;try{n=JSON.parse(e)}catch{return r}let{content:i,openStart:s,openEnd:o}=r;for(let l=n.length-2;l>=0;l-=2){let a=t.nodes[n[l]];if(!a||a.hasRequiredAttrs())break;i=y.from(a.create(n[l+1],i)),s++,o++}return new k(i,s,o)}const $={},J={},Xl={touchstart:!0,touchmove:!0};class Zl{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function Ql(r){for(let e in $){let t=$[e];r.dom.addEventListener(e,r.input.eventHandlers[e]=n=>{ta(r,n)&&!ir(r,n)&&(r.editable||!(n.type in J))&&t(r,n)},Xl[e]?{passive:!0}:void 0)}H&&r.dom.addEventListener("input",()=>null),Kn(r)}function we(r,e){r.input.lastSelectionOrigin=e,r.input.lastSelectionTime=Date.now()}function ea(r){r.domObserver.stop();for(let e in r.input.eventHandlers)r.dom.removeEventListener(e,r.input.eventHandlers[e]);clearTimeout(r.input.composingTimeout),clearTimeout(r.input.lastIOSEnterFallbackTimeout)}function Kn(r){r.someProp("handleDOMEvents",e=>{for(let t in e)r.input.eventHandlers[t]||r.dom.addEventListener(t,r.input.eventHandlers[t]=n=>ir(r,n))})}function ir(r,e){return r.someProp("handleDOMEvents",t=>{let n=t[e.type];return n?n(r,e)||e.defaultPrevented:!1})}function ta(r,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=r.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function na(r,e){!ir(r,e)&&$[e.type]&&(r.editable||!(e.type in J))&&$[e.type](r,e)}J.keydown=(r,e)=>{let t=e;if(r.input.shiftKey=t.keyCode==16||t.shiftKey,!ws(r,t)&&(r.input.lastKeyCode=t.keyCode,r.input.lastKeyCodeTime=Date.now(),!(ce&&K&&t.keyCode==13)))if(t.keyCode!=229&&r.domObserver.forceFlush(),st&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let n=Date.now();r.input.lastIOSEnter=n,r.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{r.input.lastIOSEnter==n&&(r.someProp("handleKeyDown",i=>i(r,_e(13,"Enter"))),r.input.lastIOSEnter=0)},200)}else r.someProp("handleKeyDown",n=>n(r,t))||Kl(r,t)?t.preventDefault():we(r,"key")};J.keyup=(r,e)=>{e.keyCode==16&&(r.input.shiftKey=!1)};J.keypress=(r,e)=>{let t=e;if(ws(r,t)||!t.charCode||t.ctrlKey&&!t.altKey||X&&t.metaKey)return;if(r.someProp("handleKeyPress",i=>i(r,t))){t.preventDefault();return}let n=r.state.selection;if(!(n instanceof O)||!n.$from.sameParent(n.$to)){let i=String.fromCharCode(t.charCode);r.someProp("handleTextInput",s=>s(r,n.$from.pos,n.$to.pos,i))||r.dispatch(r.state.tr.insertText(i).scrollIntoView()),t.preventDefault()}};function Zt(r){return{left:r.clientX,top:r.clientY}}function ra(r,e){let t=e.x-r.clientX,n=e.y-r.clientY;return t*t+n*n<100}function sr(r,e,t,n,i){if(n==-1)return!1;let s=r.state.doc.resolve(n);for(let o=s.depth+1;o>0;o--)if(r.someProp(e,l=>o>s.depth?l(r,t,s.nodeAfter,s.before(o),i,!0):l(r,t,s.node(o),s.before(o),i,!1)))return!0;return!1}function tt(r,e,t){r.focused||r.focus();let n=r.state.tr.setSelection(e);t=="pointer"&&n.setMeta("pointer",!0),r.dispatch(n)}function ia(r,e){if(e==-1)return!1;let t=r.state.doc.resolve(e),n=t.nodeAfter;return n&&n.isAtom&&M.isSelectable(n)?(tt(r,new M(t),"pointer"),!0):!1}function sa(r,e){if(e==-1)return!1;let t=r.state.selection,n,i;t instanceof M&&(n=t.node);let s=r.state.doc.resolve(e);for(let o=s.depth+1;o>0;o--){let l=o>s.depth?s.nodeAfter:s.node(o);if(M.isSelectable(l)){n&&t.$from.depth>0&&o>=t.$from.depth&&s.before(t.$from.depth+1)==t.$from.pos?i=s.before(t.$from.depth):i=s.before(o);break}}return i!=null?(tt(r,M.create(r.state.doc,i),"pointer"),!0):!1}function oa(r,e,t,n,i){return sr(r,"handleClickOn",e,t,n)||r.someProp("handleClick",s=>s(r,e,n))||(i?sa(r,t):ia(r,t))}function la(r,e,t,n){return sr(r,"handleDoubleClickOn",e,t,n)||r.someProp("handleDoubleClick",i=>i(r,e,n))}function aa(r,e,t,n){return sr(r,"handleTripleClickOn",e,t,n)||r.someProp("handleTripleClick",i=>i(r,e,n))||ca(r,t,n)}function ca(r,e,t){if(t.button!=0)return!1;let n=r.state.doc;if(e==-1)return n.inlineContent?(tt(r,O.create(n,0,n.content.size),"pointer"),!0):!1;let i=n.resolve(e);for(let s=i.depth+1;s>0;s--){let o=s>i.depth?i.nodeAfter:i.node(s),l=i.before(s);if(o.inlineContent)tt(r,O.create(n,l+1,l+1+o.content.size),"pointer");else if(M.isSelectable(o))tt(r,M.create(n,l),"pointer");else continue;return!0}}function or(r){return $t(r)}const Ms=X?"metaKey":"ctrlKey";$.mousedown=(r,e)=>{let t=e;r.input.shiftKey=t.shiftKey;let n=or(r),i=Date.now(),s="singleClick";i-r.input.lastClick.time<500&&ra(t,r.input.lastClick)&&!t[Ms]&&(r.input.lastClick.type=="singleClick"?s="doubleClick":r.input.lastClick.type=="doubleClick"&&(s="tripleClick")),r.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:s};let o=r.posAtCoords(Zt(t));o&&(s=="singleClick"?(r.input.mouseDown&&r.input.mouseDown.done(),r.input.mouseDown=new da(r,o,t,!!n)):(s=="doubleClick"?la:aa)(r,o.pos,o.inside,t)?t.preventDefault():we(r,"pointer"))};class da{constructor(e,t,n,i){this.view=e,this.pos=t,this.event=n,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[Ms],this.allowDefault=n.shiftKey;let s,o;if(t.inside>-1)s=e.state.doc.nodeAt(t.inside),o=t.inside;else{let d=e.state.doc.resolve(t.pos);s=d.parent,o=d.depth?d.before():0}const l=i?null:n.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a?a.dom:null;let{selection:c}=e.state;(n.button==0&&s.type.spec.draggable&&s.type.spec.selectable!==!1||c instanceof M&&c.from<=o&&c.to>o)&&(this.mightDrag={node:s,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&se&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),we(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>me(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(Zt(e))),this.updateAllowDefault(e),this.allowDefault||!t?we(this.view,"pointer"):oa(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||H&&this.mightDrag&&!this.mightDrag.node.isAtom||K&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(tt(this.view,C.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):we(this.view,"pointer")}move(e){this.updateAllowDefault(e),we(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}$.touchstart=r=>{r.input.lastTouch=Date.now(),or(r),we(r,"pointer")};$.touchmove=r=>{r.input.lastTouch=Date.now(),we(r,"pointer")};$.contextmenu=r=>or(r);function ws(r,e){return r.composing?!0:H&&Math.abs(e.timeStamp-r.input.compositionEndedAt)<500?(r.input.compositionEndedAt=-2e8,!0):!1}const ua=ce?5e3:-1;J.compositionstart=J.compositionupdate=r=>{if(!r.composing){r.domObserver.flush();let{state:e}=r,t=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(n=>n.type.spec.inclusive===!1)))r.markCursor=r.state.storedMarks||t.marks(),$t(r,!0),r.markCursor=null;else if($t(r),se&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let n=r.domSelectionRange();for(let i=n.focusNode,s=n.focusOffset;i&&i.nodeType==1&&s!=0;){let o=s<0?i.lastChild:i.childNodes[s-1];if(!o)break;if(o.nodeType==3){r.domSelection().collapse(o,o.nodeValue.length);break}else i=o,s=-1}}r.input.composing=!0}Cs(r,ua)};J.compositionend=(r,e)=>{r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=e.timeStamp,Cs(r,20))};function Cs(r,e){clearTimeout(r.input.composingTimeout),e>-1&&(r.input.composingTimeout=setTimeout(()=>$t(r),e))}function Os(r){for(r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=fa());r.input.compositionNodes.length>0;)r.input.compositionNodes.pop().markParentsDirty()}function fa(){let r=document.createEvent("Event");return r.initEvent("event",!0,!0),r.timeStamp}function $t(r,e=!1){if(!(ce&&r.domObserver.flushingSoon>=0)){if(r.domObserver.forceFlush(),Os(r),e||r.docView&&r.docView.dirty){let t=nr(r);return t&&!t.eq(r.state.selection)?r.dispatch(r.state.tr.setSelection(t)):r.updateState(r.state),!0}return!1}}function ha(r,e){if(!r.dom.parentNode)return;let t=r.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let n=getSelection(),i=document.createRange();i.selectNodeContents(e),r.dom.blur(),n.removeAllRanges(),n.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),r.focus()},50)}const ot=j&&Ce<15||st&&pl<604;$.copy=J.cut=(r,e)=>{let t=e,n=r.state.selection,i=t.type=="cut";if(n.empty)return;let s=ot?null:t.clipboardData,o=n.content(),{dom:l,text:a}=ms(r,o);s?(t.preventDefault(),s.clearData(),s.setData("text/html",l.innerHTML),s.setData("text/plain",a)):ha(r,l),i&&r.dispatch(r.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function pa(r){return r.openStart==0&&r.openEnd==0&&r.content.childCount==1?r.content.firstChild:null}function ma(r,e){if(!r.dom.parentNode)return;let t=r.input.shiftKey||r.state.selection.$from.parent.type.spec.code,n=r.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(n.contentEditable="true"),n.style.cssText="position: fixed; left: -10000px; top: 10px",n.focus(),setTimeout(()=>{r.focus(),n.parentNode&&n.parentNode.removeChild(n),t?jn(r,n.value,null,e):jn(r,n.textContent,n.innerHTML,e)},50)}function jn(r,e,t,n){let i=gs(r,e,t,r.input.shiftKey,r.state.selection.$from);if(r.someProp("handlePaste",l=>l(r,n,i||k.empty)))return!0;if(!i)return!1;let s=pa(i),o=s?r.state.tr.replaceSelectionWith(s,r.input.shiftKey):r.state.tr.replaceSelection(i);return r.dispatch(o.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}J.paste=(r,e)=>{let t=e;if(r.composing&&!ce)return;let n=ot?null:t.clipboardData;n&&jn(r,n.getData("text/plain"),n.getData("text/html"),t)?t.preventDefault():ma(r,t)};class ga{constructor(e,t){this.slice=e,this.move=t}}const Ts=X?"altKey":"ctrlKey";$.dragstart=(r,e)=>{let t=e,n=r.input.mouseDown;if(n&&n.done(),!t.dataTransfer)return;let i=r.state.selection,s=i.empty?null:r.posAtCoords(Zt(t));if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof M?i.to-1:i.to))){if(n&&n.mightDrag)r.dispatch(r.state.tr.setSelection(M.create(r.state.doc,n.mightDrag.pos)));else if(t.target&&t.target.nodeType==1){let c=r.docView.nearestDesc(t.target,!0);c&&c.node.type.spec.draggable&&c!=r.docView&&r.dispatch(r.state.tr.setSelection(M.create(r.state.doc,c.posBefore)))}}let o=r.state.selection.content(),{dom:l,text:a}=ms(r,o);t.dataTransfer.clearData(),t.dataTransfer.setData(ot?"Text":"text/html",l.innerHTML),t.dataTransfer.effectAllowed="copyMove",ot||t.dataTransfer.setData("text/plain",a),r.dragging=new ga(o,!t[Ts])};$.dragend=r=>{let e=r.dragging;window.setTimeout(()=>{r.dragging==e&&(r.dragging=null)},50)};J.dragover=J.dragenter=(r,e)=>e.preventDefault();J.drop=(r,e)=>{let t=e,n=r.dragging;if(r.dragging=null,!t.dataTransfer)return;let i=r.posAtCoords(Zt(t));if(!i)return;let s=r.state.doc.resolve(i.pos),o=n&&n.slice;o?r.someProp("transformPasted",p=>{o=p(o,r)}):o=gs(r,t.dataTransfer.getData(ot?"Text":"text/plain"),ot?null:t.dataTransfer.getData("text/html"),!1,s);let l=!!(n&&!t[Ts]);if(r.someProp("handleDrop",p=>p(r,t,o||k.empty,l))){t.preventDefault();return}if(!o)return;t.preventDefault();let a=o?Ki(r.state.doc,s.pos,o):s.pos;a==null&&(a=s.pos);let c=r.state.tr;l&&c.deleteSelection();let d=c.mapping.map(a),u=o.openStart==0&&o.openEnd==0&&o.content.childCount==1,f=c.doc;if(u?c.replaceRangeWith(d,d,o.content.firstChild):c.replaceRange(d,d,o),c.doc.eq(f))return;let h=c.doc.resolve(d);if(u&&M.isSelectable(o.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(o.content.firstChild))c.setSelection(new M(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,b,S)=>p=S),c.setSelection(rr(r,h,c.doc.resolve(p)))}r.focus(),r.dispatch(c.setMeta("uiEvent","drop"))};$.focus=r=>{r.input.lastFocus=Date.now(),r.focused||(r.domObserver.stop(),r.dom.classList.add("ProseMirror-focused"),r.domObserver.start(),r.focused=!0,setTimeout(()=>{r.docView&&r.hasFocus()&&!r.domObserver.currentSelection.eq(r.domSelectionRange())&&me(r)},20))};$.blur=(r,e)=>{let t=e;r.focused&&(r.domObserver.stop(),r.dom.classList.remove("ProseMirror-focused"),r.domObserver.start(),t.relatedTarget&&r.dom.contains(t.relatedTarget)&&r.domObserver.currentSelection.clear(),r.focused=!1)};$.beforeinput=(r,e)=>{if(K&&ce&&e.inputType=="deleteContentBackward"){r.domObserver.flushSoon();let{domChangeCount:n}=r.input;setTimeout(()=>{if(r.input.domChangeCount!=n||(r.dom.blur(),r.focus(),r.someProp("handleKeyDown",s=>s(r,_e(8,"Backspace")))))return;let{$cursor:i}=r.state.selection;i&&i.pos>0&&r.dispatch(r.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let r in J)$[r]=J[r];function wt(r,e){if(r==e)return!0;for(let t in r)if(r[t]!==e[t])return!1;for(let t in e)if(!(t in r))return!1;return!0}class lr{constructor(e,t){this.toDOM=e,this.spec=t||Ve,this.side=this.spec.side||0}map(e,t,n,i){let{pos:s,deleted:o}=e.mapResult(t.from+i,this.side<0?-1:1);return o?null:new Q(s-n,s-n,this)}valid(){return!0}eq(e){return this==e||e instanceof lr&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&wt(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Oe{constructor(e,t){this.attrs=e,this.spec=t||Ve}map(e,t,n,i){let s=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-n,o=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-n;return s>=o?null:new Q(s,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Oe&&wt(this.attrs,e.attrs)&&wt(this.spec,e.spec)}static is(e){return e.type instanceof Oe}destroy(){}}class ar{constructor(e,t){this.attrs=e,this.spec=t||Ve}map(e,t,n,i){let s=e.mapResult(t.from+i,1);if(s.deleted)return null;let o=e.mapResult(t.to+i,-1);return o.deleted||o.pos<=s.pos?null:new Q(s.pos-n,o.pos-n,this)}valid(e,t){let{index:n,offset:i}=e.content.findIndex(t.from),s;return i==t.from&&!(s=e.child(n)).isText&&i+s.nodeSize==t.to}eq(e){return this==e||e instanceof ar&&wt(this.attrs,e.attrs)&&wt(this.spec,e.spec)}destroy(){}}class Q{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new Q(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new Q(e,e,new lr(t,n))}static inline(e,t,n,i){return new Q(e,t,new Oe(n,i))}static node(e,t,n,i){return new Q(e,t,new ar(n,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof Oe}}const Ue=[],Ve={};class v{constructor(e,t){this.local=e.length?e:Ue,this.children=t.length?t:Ue}static create(e,t){return t.length?Jt(t,e,0,Ve):L}find(e,t,n){let i=[];return this.findInner(e??0,t??1e9,i,0,n),i}findInner(e,t,n,i,s){for(let o=0;o<this.local.length;o++){let l=this.local[o];l.from<=t&&l.to>=e&&(!s||s(l.spec))&&n.push(l.copy(l.from+i,l.to+i))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let l=this.children[o]+1;this.children[o+2].findInner(e-l,t-l,n,i+l,s)}}map(e,t,n){return this==L||e.maps.length==0?this:this.mapInner(e,t,0,0,n||Ve)}mapInner(e,t,n,i,s){let o;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,n,i);a&&a.type.valid(t,a)?(o||(o=[])).push(a):s.onRemove&&s.onRemove(this.local[l].spec)}return this.children.length?ya(this.children,o||[],e,t,n,i,s):o?new v(o.sort(He),Ue):L}add(e,t){return t.length?this==L?v.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let i,s=0;e.forEach((l,a)=>{let c=a+n,d;if(d=Es(t,l,c)){for(i||(i=this.children.slice());s<i.length&&i[s]<a;)s+=3;i[s]==a?i[s+2]=i[s+2].addInner(l,d,c+1):i.splice(s,0,a,a+l.nodeSize,Jt(d,l,c+1,Ve)),s+=3}});let o=Ns(s?As(t):t,-n);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||o.splice(l--,1);return new v(o.length?this.local.concat(o).sort(He):this.local,i||this.children)}remove(e){return e.length==0||this==L?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,i=this.local;for(let s=0;s<n.length;s+=3){let o,l=n[s]+t,a=n[s+1]+t;for(let d=0,u;d<e.length;d++)(u=e[d])&&u.from>l&&u.to<a&&(e[d]=null,(o||(o=[])).push(u));if(!o)continue;n==this.children&&(n=this.children.slice());let c=n[s+2].removeInner(o,l+1);c!=L?n[s+2]=c:(n.splice(s,3),s-=3)}if(i.length){for(let s=0,o;s<e.length;s++)if(o=e[s])for(let l=0;l<i.length;l++)i[l].eq(o,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return n==this.children&&i==this.local?this:i.length||n.length?new v(i,n):L}forChild(e,t){if(this==L)return this;if(t.isLeaf)return v.empty;let n,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(n=this.children[l+2]);break}let s=e+1,o=s+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<o&&a.to>s&&a.type instanceof Oe){let c=Math.max(s,a.from)-s,d=Math.min(o,a.to)-s;c<d&&(i||(i=[])).push(a.copy(c,d))}}if(i){let l=new v(i.sort(He),Ue);return n?new xe([l,n]):l}return n||L}eq(e){if(this==e)return!0;if(!(e instanceof v)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return cr(this.localsInner(e))}localsInner(e){if(this==L)return Ue;if(e.inlineContent||!this.local.some(Oe.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof Oe||t.push(this.local[n]);return t}}v.empty=new v([],[]);v.removeOverlap=cr;const L=v.empty;class xe{constructor(e){this.members=e}map(e,t){const n=this.members.map(i=>i.map(e,t,Ve));return xe.from(n)}forChild(e,t){if(t.isLeaf)return v.empty;let n=[];for(let i=0;i<this.members.length;i++){let s=this.members[i].forChild(e,t);s!=L&&(s instanceof xe?n=n.concat(s.members):n.push(s))}return xe.from(n)}eq(e){if(!(e instanceof xe)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let i=0;i<this.members.length;i++){let s=this.members[i].localsInner(e);if(s.length)if(!t)t=s;else{n&&(t=t.slice(),n=!1);for(let o=0;o<s.length;o++)t.push(s[o])}}return t?cr(n?t:t.sort(He)):Ue}static from(e){switch(e.length){case 0:return L;case 1:return e[0];default:return new xe(e.every(t=>t instanceof v)?e:e.reduce((t,n)=>t.concat(n instanceof v?n:n.members),[]))}}}function ya(r,e,t,n,i,s,o){let l=r.slice();for(let c=0,d=s;c<t.maps.length;c++){let u=0;t.maps[c].forEach((f,h,p,m)=>{let g=m-p-(h-f);for(let b=0;b<l.length;b+=3){let S=l[b+1];if(S<0||f>S+d-u)continue;let E=l[b]+d-u;h>=E?l[b+1]=f<=E?-2:-1:p>=i&&g&&(l[b]+=g,l[b+1]+=g)}u+=g}),d=t.maps[c].map(d,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let d=t.map(r[c]+s),u=d-i;if(u<0||u>=n.content.size){a=!0;continue}let f=t.map(r[c+1]+s,-1),h=f-i,{index:p,offset:m}=n.content.findIndex(u),g=n.maybeChild(p);if(g&&m==u&&m+g.nodeSize==h){let b=l[c+2].mapInner(t,g,d+1,r[c]+s+1,o);b!=L?(l[c]=u,l[c+1]=h,l[c+2]=b):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=ba(l,r,e,t,i,s,o),d=Jt(c,n,0,o);e=d.local;for(let u=0;u<l.length;u+=3)l[u+1]<0&&(l.splice(u,3),u-=3);for(let u=0,f=0;u<d.children.length;u+=3){let h=d.children[u];for(;f<l.length&&l[f]<h;)f+=3;l.splice(f,0,d.children[u],d.children[u+1],d.children[u+2])}}return new v(e.sort(He),l)}function Ns(r,e){if(!e||!r.length)return r;let t=[];for(let n=0;n<r.length;n++){let i=r[n];t.push(new Q(i.from+e,i.to+e,i.type))}return t}function ba(r,e,t,n,i,s,o){function l(a,c){for(let d=0;d<a.local.length;d++){let u=a.local[d].map(n,i,c);u?t.push(u):o.onRemove&&o.onRemove(a.local[d].spec)}for(let d=0;d<a.children.length;d+=3)l(a.children[d+2],a.children[d]+c+1)}for(let a=0;a<r.length;a+=3)r[a+1]==-1&&l(r[a+2],e[a]+s+1);return t}function Es(r,e,t){if(e.isLeaf)return null;let n=t+e.nodeSize,i=null;for(let s=0,o;s<r.length;s++)(o=r[s])&&o.from>t&&o.to<n&&((i||(i=[])).push(o),r[s]=null);return i}function As(r){let e=[];for(let t=0;t<r.length;t++)r[t]!=null&&e.push(r[t]);return e}function Jt(r,e,t,n){let i=[],s=!1;e.forEach((l,a)=>{let c=Es(r,l,a+t);if(c){s=!0;let d=Jt(c,l,t+a+1,n);d!=L&&i.push(a,a+l.nodeSize,d)}});let o=Ns(s?As(r):r,-t).sort(He);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||(n.onRemove&&n.onRemove(o[l].spec),o.splice(l--,1));return o.length||i.length?new v(o,i):L}function He(r,e){return r.from-e.from||r.to-e.to}function cr(r){let e=r;for(let t=0;t<e.length-1;t++){let n=e[t];if(n.from!=n.to)for(let i=t+1;i<e.length;i++){let s=e[i];if(s.from==n.from){s.to!=n.to&&(e==r&&(e=r.slice()),e[i]=s.copy(s.from,n.to),ti(e,i+1,s.copy(n.to,s.to)));continue}else{s.from<n.to&&(e==r&&(e=r.slice()),e[t]=n.copy(n.from,s.from),ti(e,i,n.copy(s.from,n.to)));break}}}return e}function ti(r,e,t){for(;e<r.length&&He(t,r[e])>0;)e++;r.splice(e,0,t)}function Mn(r){let e=[];return r.someProp("decorations",t=>{let n=t(r.state);n&&n!=L&&e.push(n)}),r.cursorWrapper&&e.push(v.create(r.state.doc,[r.cursorWrapper.deco])),xe.from(e)}const ka={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},xa=j&&Ce<=11;class Sa{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class Ma{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Sa,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.observer=window.MutationObserver&&new window.MutationObserver(n=>{for(let i=0;i<n.length;i++)this.queue.push(n[i]);j&&Ce<=11&&n.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),xa&&(this.onCharData=n=>{this.queue.push({target:n.target,type:"characterData",oldValue:n.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,ka)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(Gr(this.view)){if(this.suppressingSelectionUpdates)return me(this.view);if(j&&Ce<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Je(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,n;for(let s=e.focusNode;s;s=Mt(s))t.add(s);for(let s=e.anchorNode;s;s=Mt(s))if(t.has(s)){n=s;break}let i=n&&this.view.docView.nearestDesc(n);if(i&&i.ignoreMutation({type:"selection",target:n.nodeType==3?n.parentNode:n}))return this.setCurSelection(),!0}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.observer?this.observer.takeRecords():[];this.queue.length&&(t=this.queue.concat(t),this.queue.length=0);let n=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Gr(e)&&!this.ignoreSelectionChange(n),s=-1,o=-1,l=!1,a=[];if(e.editable)for(let d=0;d<t.length;d++){let u=this.registerMutation(t[d],a);u&&(s=s<0?u.from:Math.min(u.from,s),o=o<0?u.to:Math.max(u.to,o),u.typeOver&&(l=!0))}if(se&&a.length>1){let d=a.filter(u=>u.nodeName=="BR");if(d.length==2){let u=d[0],f=d[1];u.parentNode&&u.parentNode.parentNode==f.parentNode?f.remove():u.remove()}}let c=null;s<0&&i&&e.input.lastFocus>Date.now()-200&&e.input.lastTouch<Date.now()-300&&_t(n)&&(c=nr(e))&&c.eq(C.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,me(e),this.currentSelection.set(n),e.scrollToSelection()):(s>-1||i)&&(s>-1&&(e.docView.markDirty(s,o),wa(e)),this.handleDOMChange(s,o,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||me(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(n==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if(e.type=="childList"){for(let d=0;d<e.addedNodes.length;d++)t.push(e.addedNodes[d]);if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let i=e.previousSibling,s=e.nextSibling;if(j&&Ce<=11&&e.addedNodes.length)for(let d=0;d<e.addedNodes.length;d++){let{previousSibling:u,nextSibling:f}=e.addedNodes[d];(!u||Array.prototype.indexOf.call(e.addedNodes,u)<0)&&(i=u),(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(s=f)}let o=i&&i.parentNode==e.target?_(i)+1:0,l=n.localPosFromDOM(e.target,o,-1),a=s&&s.parentNode==e.target?_(s):e.target.childNodes.length,c=n.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}}}let ni=new WeakMap,ri=!1;function wa(r){if(!ni.has(r)&&(ni.set(r,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(r.dom).whiteSpace)!==-1)){if(r.requiresGeckoHackNode=se,ri)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),ri=!0}}function Ca(r){let e;function t(a){a.preventDefault(),a.stopImmediatePropagation(),e=a.getTargetRanges()[0]}r.dom.addEventListener("beforeinput",t,!0),document.execCommand("indent"),r.dom.removeEventListener("beforeinput",t,!0);let n=e.startContainer,i=e.startOffset,s=e.endContainer,o=e.endOffset,l=r.domAtPos(r.state.selection.anchor);return Je(l.node,l.offset,s,o)&&([n,i,s,o]=[s,o,n,i]),{anchorNode:n,anchorOffset:i,focusNode:s,focusOffset:o}}function Oa(r,e,t){let{node:n,fromOffset:i,toOffset:s,from:o,to:l}=r.docView.parseRange(e,t),a=r.domSelectionRange(),c,d=a.anchorNode;if(d&&r.dom.contains(d.nodeType==1?d:d.parentNode)&&(c=[{node:d,offset:a.anchorOffset}],_t(a)||c.push({node:a.focusNode,offset:a.focusOffset})),K&&r.input.lastKeyCode===8)for(let g=s;g>i;g--){let b=n.childNodes[g-1],S=b.pmViewDesc;if(b.nodeName=="BR"&&!S){s=g;break}if(!S||S.size)break}let u=r.state.doc,f=r.someProp("domParser")||nt.fromSchema(r.state.schema),h=u.resolve(o),p=null,m=f.parse(n,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:s,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:Ta,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,b=c[1]&&c[1].pos;b==null&&(b=g),p={anchor:g+o,head:b+o}}return{doc:m,sel:p,from:o,to:l}}function Ta(r){let e=r.pmViewDesc;if(e)return e.parseRule();if(r.nodeName=="BR"&&r.parentNode){if(H&&/^(ul|ol)$/i.test(r.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(r.parentNode.lastChild==r||H&&/^(tr|table)$/i.test(r.parentNode.nodeName))return{ignore:!0}}else if(r.nodeName=="IMG"&&r.getAttribute("mark-placeholder"))return{ignore:!0};return null}function Na(r,e,t,n,i){if(e<0){let T=r.input.lastSelectionTime>Date.now()-50?r.input.lastSelectionOrigin:null,ft=nr(r,T);if(ft&&!r.state.selection.eq(ft)){let on=r.state.tr.setSelection(ft);T=="pointer"?on.setMeta("pointer",!0):T=="key"&&on.scrollIntoView(),r.dispatch(on)}return}let s=r.state.doc.resolve(e),o=s.sharedDepth(t);e=s.before(o+1),t=r.state.doc.resolve(t).after(o+1);let l=r.state.selection,a=Oa(r,e,t),c=r.state.doc,d=c.slice(a.from,a.to),u,f;r.input.lastKeyCode===8&&Date.now()-100<r.input.lastKeyCodeTime?(u=r.state.selection.to,f="end"):(u=r.state.selection.from,f="start"),r.input.lastKeyCode=null;let h=va(d.content,a.doc.content,a.from,u,f);if((st&&r.input.lastIOSEnter>Date.now()-225||ce)&&i.some(T=>T.nodeName=="DIV"||T.nodeName=="P"||T.nodeName=="LI")&&(!h||h.endA>=h.endB)&&r.someProp("handleKeyDown",T=>T(r,_e(13,"Enter")))){r.input.lastIOSEnter=0;return}if(!h)if(n&&l instanceof O&&!l.empty&&l.$head.sameParent(l.$anchor)&&!r.composing&&!(a.sel&&a.sel.anchor!=a.sel.head))h={start:l.from,endA:l.to,endB:l.to};else{if(a.sel){let T=ii(r,r.state.doc,a.sel);T&&!T.eq(r.state.selection)&&r.dispatch(r.state.tr.setSelection(T))}return}if(K&&r.cursorWrapper&&a.sel&&a.sel.anchor==r.cursorWrapper.deco.from&&a.sel.head==a.sel.anchor){let T=h.endB-h.start;a.sel={anchor:a.sel.anchor+T,head:a.sel.anchor+T}}r.input.domChangeCount++,r.state.selection.from<r.state.selection.to&&h.start==h.endB&&r.state.selection instanceof O&&(h.start>r.state.selection.from&&h.start<=r.state.selection.from+2&&r.state.selection.from>=a.from?h.start=r.state.selection.from:h.endA<r.state.selection.to&&h.endA>=r.state.selection.to-2&&r.state.selection.to<=a.to&&(h.endB+=r.state.selection.to-h.endA,h.endA=r.state.selection.to)),j&&Ce<=11&&h.endB==h.start+1&&h.endA==h.start&&h.start>a.from&&a.doc.textBetween(h.start-a.from-1,h.start-a.from+1)=="  "&&(h.start--,h.endA--,h.endB--);let p=a.doc.resolveNoCache(h.start-a.from),m=a.doc.resolveNoCache(h.endB-a.from),g=c.resolve(h.start),b=p.sameParent(m)&&p.parent.inlineContent&&g.end()>=h.endA,S;if((st&&r.input.lastIOSEnter>Date.now()-225&&(!b||i.some(T=>T.nodeName=="DIV"||T.nodeName=="P"))||!b&&p.pos<a.doc.content.size&&(S=C.findFrom(a.doc.resolve(p.pos+1),1,!0))&&S.head==m.pos)&&r.someProp("handleKeyDown",T=>T(r,_e(13,"Enter")))){r.input.lastIOSEnter=0;return}if(r.state.selection.anchor>h.start&&Aa(c,h.start,h.endA,p,m)&&r.someProp("handleKeyDown",T=>T(r,_e(8,"Backspace")))){ce&&K&&r.domObserver.suppressSelectionUpdates();return}K&&ce&&h.endB==h.start&&(r.input.lastAndroidDelete=Date.now()),ce&&!b&&p.start()!=m.start()&&m.parentOffset==0&&p.depth==m.depth&&a.sel&&a.sel.anchor==a.sel.head&&a.sel.head==h.endA&&(h.endB-=2,m=a.doc.resolveNoCache(h.endB-a.from),setTimeout(()=>{r.someProp("handleKeyDown",function(T){return T(r,_e(13,"Enter"))})},20));let E=h.start,q=h.endA,P,ge,te;if(b){if(p.pos==m.pos)j&&Ce<=11&&p.parentOffset==0&&(r.domObserver.suppressSelectionUpdates(),setTimeout(()=>me(r),20)),P=r.state.tr.delete(E,q),ge=c.resolve(h.start).marksAcross(c.resolve(h.endA));else if(h.endA==h.endB&&(te=Ea(p.parent.content.cut(p.parentOffset,m.parentOffset),g.parent.content.cut(g.parentOffset,h.endA-g.start()))))P=r.state.tr,te.type=="add"?P.addMark(E,q,te.mark):P.removeMark(E,q,te.mark);else if(p.parent.child(p.index()).isText&&p.index()==m.index()-(m.textOffset?0:1)){let T=p.parent.textBetween(p.parentOffset,m.parentOffset);if(r.someProp("handleTextInput",ft=>ft(r,E,q,T)))return;P=r.state.tr.insertText(T,E,q)}}if(P||(P=r.state.tr.replace(E,q,a.doc.slice(h.start-a.from,h.endB-a.from))),a.sel){let T=ii(r,P.doc,a.sel);T&&!(K&&ce&&r.composing&&T.empty&&(h.start!=h.endB||r.input.lastAndroidDelete<Date.now()-100)&&(T.head==E||T.head==P.mapping.map(q)-1)||j&&T.empty&&T.head==E)&&P.setSelection(T)}ge&&P.ensureMarks(ge),r.dispatch(P.scrollIntoView())}function ii(r,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:rr(r,e.resolve(t.anchor),e.resolve(t.head))}function Ea(r,e){let t=r.firstChild.marks,n=e.firstChild.marks,i=t,s=n,o,l,a;for(let d=0;d<n.length;d++)i=n[d].removeFromSet(i);for(let d=0;d<t.length;d++)s=t[d].removeFromSet(s);if(i.length==1&&s.length==0)l=i[0],o="add",a=d=>d.mark(l.addToSet(d.marks));else if(i.length==0&&s.length==1)l=s[0],o="remove",a=d=>d.mark(l.removeFromSet(d.marks));else return null;let c=[];for(let d=0;d<e.childCount;d++)c.push(a(e.child(d)));if(y.from(c).eq(r))return{mark:l,type:o}}function Aa(r,e,t,n,i){if(!n.parent.isTextblock||t-e<=i.pos-n.pos||wn(n,!0,!1)<i.pos)return!1;let s=r.resolve(e);if(s.parentOffset<s.parent.content.size||!s.parent.isTextblock)return!1;let o=r.resolve(wn(s,!0,!0));return!o.parent.isTextblock||o.pos>t||wn(o,!0,!1)<t?!1:n.parent.content.cut(n.parentOffset).eq(o.parent.content)}function wn(r,e,t){let n=r.depth,i=e?r.end():r.pos;for(;n>0&&(e||r.indexAfter(n)==r.node(n).childCount);)n--,i++,e=!1;if(t){let s=r.node(n).maybeChild(r.indexAfter(n));for(;s&&!s.isLeaf;)s=s.firstChild,i++}return i}function va(r,e,t,n,i){let s=r.findDiffStart(e,t);if(s==null)return null;let{a:o,b:l}=r.findDiffEnd(e,t+r.size,t+e.size);if(i=="end"){let a=Math.max(0,s-Math.min(o,l));n-=o+a-s}if(o<s&&r.size<e.size){let a=n<=s&&n>=o?s-n:0;s-=a,l=s+(l-o),o=s}else if(l<s){let a=n<=s&&n>=l?s-n:0;s-=a,o=s+(o-l),l=s}return{start:s,endA:o,endB:l}}class Da{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Zl,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(ci),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=li(this),oi(this),this.nodeViews=ai(this),this.docView=Jr(this.state.doc,si(this),Mn(this),this.dom,this),this.domObserver=new Ma(this,(n,i,s,o)=>Na(this,n,i,s,o)),this.domObserver.start(),Ql(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&Kn(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(ci),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){let n=this.state,i=!1,s=!1;e.storedMarks&&this.composing&&(Os(this),s=!0),this.state=e;let o=n.plugins!=e.plugins||this._props.plugins!=t.plugins;if(o||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let f=ai(this);Ra(f,this.nodeViews)&&(this.nodeViews=f,i=!0)}(o||t.handleDOMEvents!=this._props.handleDOMEvents)&&Kn(this),this.editable=li(this),oi(this);let l=Mn(this),a=si(this),c=n.plugins!=e.plugins&&!n.doc.eq(e.doc)?"reset":e.scrollToSelection>n.scrollToSelection?"to selection":"preserve",d=i||!this.docView.matchesNode(e.doc,a,l);(d||!e.selection.eq(n.selection))&&(s=!0);let u=c=="preserve"&&s&&this.dom.style.overflowAnchor==null&&yl(this);if(s){this.domObserver.stop();let f=d&&(j||K)&&!this.composing&&!n.selection.empty&&!e.selection.empty&&Ia(n.selection,e.selection);if(d){let h=K?this.trackWrites=this.domSelectionRange().focusNode:null;(i||!this.docView.update(e.doc,a,l,this))&&(this.docView.updateOuterDeco([]),this.docView.destroy(),this.docView=Jr(e.doc,a,l,this.dom,this)),h&&!this.trackWrites&&(f=!0)}f||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&Jl(this))?me(this,f):(fs(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(n),c=="reset"?this.dom.scrollTop=0:c=="to selection"?this.scrollToSelection():u&&bl(u)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof M){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Fr(this,t.getBoundingClientRect(),e)}else Fr(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let n=this.directPlugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let n=this.state.plugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}}someProp(e,t){let n=this._props&&this._props[e],i;if(n!=null&&(i=t?t(n):n))return i;for(let o=0;o<this.directPlugins.length;o++){let l=this.directPlugins[o].props[e];if(l!=null&&(i=t?t(l):l))return i}let s=this.state.plugins;if(s)for(let o=0;o<s.length;o++){let l=s[o].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(j){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&kl(this.dom),me(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}posAtCoords(e){return Cl(this,e)}coordsAtPos(e,t=1){return rs(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let i=this.docView.posFromDOM(e,t,n);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return Al(this,t||this.state,e)}destroy(){this.docView&&(ea(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Mn(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null)}get isDestroyed(){return this.docView==null}dispatchEvent(e){return na(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){return H&&this.root.nodeType===11&&fl(this.dom.ownerDocument)==this.dom?Ca(this):this.domSelection()}domSelection(){return this.root.getSelection()}}function si(r){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(r.editable),e.translate="no",r.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(r.state)),t)for(let n in t)n=="class"&&(e.class+=" "+t[n]),n=="style"?e.style=(e.style?e.style+";":"")+t[n]:!e[n]&&n!="contenteditable"&&n!="nodeName"&&(e[n]=String(t[n]))}),[Q.node(0,r.state.doc.content.size,e)]}function oi(r){if(r.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),r.cursorWrapper={dom:e,deco:Q.widget(r.state.selection.head,e,{raw:!0,marks:r.markCursor})}}else r.cursorWrapper=null}function li(r){return!r.someProp("editable",e=>e(r.state)===!1)}function Ia(r,e){let t=Math.min(r.$anchor.sharedDepth(r.head),e.$anchor.sharedDepth(e.head));return r.$anchor.start(t)!=e.$anchor.start(t)}function ai(r){let e=Object.create(null);function t(n){for(let i in n)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=n[i])}return r.someProp("nodeViews",t),r.someProp("markViews",t),e}function Ra(r,e){let t=0,n=0;for(let i in r){if(r[i]!=e[i])return!0;t++}for(let i in e)n++;return t!=n}function ci(r){if(r.spec.state||r.spec.filterTransaction||r.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var Ee={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Wt={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},di=typeof navigator<"u"&&/Chrome\/(\d+)/.exec(navigator.userAgent),Pa=typeof navigator<"u"&&/Mac/.test(navigator.platform),Ba=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),za=Pa||di&&+di[1]<57;for(var z=0;z<10;z++)Ee[48+z]=Ee[96+z]=String(z);for(var z=1;z<=24;z++)Ee[z+111]="F"+z;for(var z=65;z<=90;z++)Ee[z]=String.fromCharCode(z+32),Wt[z]=String.fromCharCode(z);for(var Cn in Ee)Wt.hasOwnProperty(Cn)||(Wt[Cn]=Ee[Cn]);function Fa(r){var e=za&&(r.ctrlKey||r.altKey||r.metaKey)||Ba&&r.shiftKey&&r.key&&r.key.length==1||r.key=="Unidentified",t=!e&&r.key||(r.shiftKey?Wt:Ee)[r.keyCode]||r.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}const La=typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):!1;function Va(r){let e=r.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let n,i,s,o;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))La?o=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return n&&(t="Alt-"+t),i&&(t="Ctrl-"+t),o&&(t="Meta-"+t),s&&(t="Shift-"+t),t}function Ha(r){let e=Object.create(null);for(let t in r)e[Va(t)]=r[t];return e}function On(r,e,t){return e.altKey&&(r="Alt-"+r),e.ctrlKey&&(r="Ctrl-"+r),e.metaKey&&(r="Meta-"+r),t!==!1&&e.shiftKey&&(r="Shift-"+r),r}function $a(r){return new ee({props:{handleKeyDown:vs(r)}})}function vs(r){let e=Ha(r);return function(t,n){let i=Fa(n),s=i.length==1&&i!=" ",o,l=e[On(i,n,!s)];if(l&&l(t.state,t.dispatch,t))return!0;if(s&&(n.shiftKey||n.altKey||n.metaKey||i.charCodeAt(0)>127)&&(o=Ee[n.keyCode])&&o!=i){let a=e[On(o,n,!0)];if(a&&a(t.state,t.dispatch,t))return!0}else if(s&&n.shiftKey){let a=e[On(i,n,!0)];if(a&&a(t.state,t.dispatch,t))return!0}return!1}}const Ja=(r,e)=>r.selection.empty?!1:(e&&e(r.tr.deleteSelection().scrollIntoView()),!0),Wa=(r,e,t)=>{let{$cursor:n}=r.selection;if(!n||(t?!t.endOfTextblock("backward",r):n.parentOffset>0))return!1;let i=Ds(n);if(!i){let o=n.blockRange(),l=o&&dt(o);return l==null?!1:(e&&e(r.tr.lift(o,l).scrollIntoView()),!0)}let s=i.nodeBefore;if(!s.type.spec.isolating&&Ps(r,i,e))return!0;if(n.parent.content.size==0&&(lt(s,"end")||M.isSelectable(s))){let o=Qn(r.doc,n.before(),n.after(),k.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=r.tr.step(o);l.setSelection(lt(s,"end")?C.findFrom(l.doc.resolve(l.mapping.map(i.pos,-1)),-1):M.create(l.doc,i.pos-s.nodeSize)),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==n.depth-1?(e&&e(r.tr.delete(i.pos-s.nodeSize,i.pos).scrollIntoView()),!0):!1};function lt(r,e,t=!1){for(let n=r;n;n=e=="start"?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(t&&n.childCount!=1)return!1}return!1}const qa=(r,e,t)=>{let{$head:n,empty:i}=r.selection,s=n;if(!i)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("backward",r):n.parentOffset>0)return!1;s=Ds(n)}let o=s&&s.nodeBefore;return!o||!M.isSelectable(o)?!1:(e&&e(r.tr.setSelection(M.create(r.doc,s.pos-o.nodeSize)).scrollIntoView()),!0)};function Ds(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){if(r.index(e)>0)return r.doc.resolve(r.before(e+1));if(r.node(e).type.spec.isolating)break}return null}const Ka=(r,e,t)=>{let{$cursor:n}=r.selection;if(!n||(t?!t.endOfTextblock("forward",r):n.parentOffset<n.parent.content.size))return!1;let i=Is(n);if(!i)return!1;let s=i.nodeAfter;if(Ps(r,i,e))return!0;if(n.parent.content.size==0&&(lt(s,"start")||M.isSelectable(s))){let o=Qn(r.doc,n.before(),n.after(),k.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=r.tr.step(o);l.setSelection(lt(s,"start")?C.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):M.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==n.depth-1?(e&&e(r.tr.delete(i.pos,i.pos+s.nodeSize).scrollIntoView()),!0):!1},ja=(r,e,t)=>{let{$head:n,empty:i}=r.selection,s=n;if(!i)return!1;if(n.parent.isTextblock){if(t?!t.endOfTextblock("forward",r):n.parentOffset<n.parent.content.size)return!1;s=Is(n)}let o=s&&s.nodeAfter;return!o||!M.isSelectable(o)?!1:(e&&e(r.tr.setSelection(M.create(r.doc,s.pos)).scrollIntoView()),!0)};function Is(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){let t=r.node(e);if(r.index(e)+1<t.childCount)return r.doc.resolve(r.after(e+1));if(t.type.spec.isolating)break}return null}const Ua=(r,e)=>{let{$from:t,$to:n}=r.selection,i=t.blockRange(n),s=i&&dt(i);return s==null?!1:(e&&e(r.tr.lift(i,s).scrollIntoView()),!0)},Ga=(r,e)=>{let{$head:t,$anchor:n}=r.selection;return!t.parent.type.spec.code||!t.sameParent(n)?!1:(e&&e(r.tr.insertText(`
`).scrollIntoView()),!0)};function Rs(r){for(let e=0;e<r.edgeCount;e++){let{type:t}=r.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const _a=(r,e)=>{let{$head:t,$anchor:n}=r.selection;if(!t.parent.type.spec.code||!t.sameParent(n))return!1;let i=t.node(-1),s=t.indexAfter(-1),o=Rs(i.contentMatchAt(s));if(!o||!i.canReplaceWith(s,s,o))return!1;if(e){let l=t.after(),a=r.tr.replaceWith(l,l,o.createAndFill());a.setSelection(C.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},Ya=(r,e)=>{let t=r.selection,{$from:n,$to:i}=t;if(t instanceof re||n.parent.inlineContent||i.parent.inlineContent)return!1;let s=Rs(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let o=(!n.parentOffset&&i.index()<i.parent.childCount?n:i).pos,l=r.tr.insert(o,s.createAndFill());l.setSelection(O.create(l.doc,o+1)),e(l.scrollIntoView())}return!0},Xa=(r,e)=>{let{$cursor:t}=r.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let s=t.before();if(Qe(r.doc,s))return e&&e(r.tr.split(s).scrollIntoView()),!0}let n=t.blockRange(),i=n&&dt(n);return i==null?!1:(e&&e(r.tr.lift(n,i).scrollIntoView()),!0)},Za=(r,e)=>{let{$from:t,to:n}=r.selection,i,s=t.sharedDepth(n);return s==0?!1:(i=t.before(s),e&&e(r.tr.setSelection(M.create(r.doc,i))),!0)};function Qa(r,e,t){let n=e.nodeBefore,i=e.nodeAfter,s=e.index();return!n||!i||!n.type.compatibleContent(i.type)?!1:!n.content.size&&e.parent.canReplace(s-1,s)?(t&&t(r.tr.delete(e.pos-n.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(s,s+1)||!(i.isTextblock||ut(r.doc,e.pos))?!1:(t&&t(r.tr.clearIncompatible(e.pos,n.type,n.contentMatchAt(n.childCount)).join(e.pos).scrollIntoView()),!0)}function Ps(r,e,t){let n=e.nodeBefore,i=e.nodeAfter,s,o;if(n.type.spec.isolating||i.type.spec.isolating)return!1;if(Qa(r,e,t))return!0;let l=e.parent.canReplace(e.index(),e.index()+1);if(l&&(s=(o=n.contentMatchAt(n.childCount)).findWrapping(i.type))&&o.matchType(s[0]||i.type).validEnd){if(t){let u=e.pos+i.nodeSize,f=y.empty;for(let m=s.length-1;m>=0;m--)f=y.from(s[m].create(null,f));f=y.from(n.copy(f));let h=r.tr.step(new I(e.pos-1,u,e.pos,u,new k(f,1,0),s.length,!0)),p=u+2*s.length;ut(h.doc,p)&&h.join(p),t(h.scrollIntoView())}return!0}let a=C.findFrom(e,1),c=a&&a.$from.blockRange(a.$to),d=c&&dt(c);if(d!=null&&d>=e.depth)return t&&t(r.tr.lift(c,d).scrollIntoView()),!0;if(l&&lt(i,"start",!0)&&lt(n,"end")){let u=n,f=[];for(;f.push(u),!u.isTextblock;)u=u.lastChild;let h=i,p=1;for(;!h.isTextblock;h=h.firstChild)p++;if(u.canReplace(u.childCount,u.childCount,h.content)){if(t){let m=y.empty;for(let b=f.length-1;b>=0;b--)m=y.from(f[b].copy(m));let g=r.tr.step(new I(e.pos-f.length,e.pos+i.nodeSize,e.pos+p,e.pos+i.nodeSize-p,new k(m,f.length,0),0,!0));t(g.scrollIntoView())}return!0}}return!1}function Bs(r){return function(e,t){let n=e.selection,i=r<0?n.$from:n.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return i.node(s).isTextblock?(t&&t(e.tr.setSelection(O.create(e.doc,r<0?i.start(s):i.end(s)))),!0):!1}}const ec=Bs(-1),tc=Bs(1);function nc(r,e=null){return function(t,n){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=o&&Zn(o,r,e);return l?(n&&n(t.tr.wrap(o,l).scrollIntoView()),!0):!1}}function ui(r,e=null){return function(t,n){let{from:i,to:s}=t.selection,o=!1;return t.doc.nodesBetween(i,s,(l,a)=>{if(o)return!1;if(!(!l.isTextblock||l.hasMarkup(r,e)))if(l.type==r)o=!0;else{let c=t.doc.resolve(a),d=c.index();o=c.parent.canReplaceWith(d,d+1,r)}}),o?(n&&n(t.tr.setBlockType(i,s,r,e).scrollIntoView()),!0):!1}}typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform()=="darwin";function rc(r,e=null){return function(t,n){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=!1,a=o;if(!o)return!1;if(o.depth>=2&&i.node(o.depth-1).type.compatibleContent(r)&&o.startIndex==0){if(i.index(o.depth-1)==0)return!1;let d=t.doc.resolve(o.start-2);a=new zt(d,d,o.depth),o.endIndex<o.parent.childCount&&(o=new zt(i,t.doc.resolve(s.end(o.depth)),o.depth)),l=!0}let c=Zn(a,r,e,o);return c?(n&&n(ic(t.tr,o,c,l,r).scrollIntoView()),!0):!1}}function ic(r,e,t,n,i){let s=y.empty;for(let d=t.length-1;d>=0;d--)s=y.from(t[d].type.create(t[d].attrs,s));r.step(new I(e.start-(n?2:0),e.end,e.start,e.end,new k(s,0,0),t.length,!0));let o=0;for(let d=0;d<t.length;d++)t[d].type==i&&(o=d+1);let l=t.length-o,a=e.start+t.length-(n?2:0),c=e.parent;for(let d=e.startIndex,u=e.endIndex,f=!0;d<u;d++,f=!1)!f&&Qe(r.doc,a,l)&&(r.split(a,l),a+=2*l),a+=c.child(d).nodeSize;return r}function sc(r){return function(e,t){let{$from:n,$to:i}=e.selection,s=n.blockRange(i,o=>o.childCount>0&&o.firstChild.type==r);return s?t?n.node(s.depth-1).type==r?oc(e,t,r,s):lc(e,t,s):!0:!1}}function oc(r,e,t,n){let i=r.tr,s=n.end,o=n.$to.end(n.depth);s<o&&(i.step(new I(s-1,o,s,o,new k(y.from(t.create(null,n.parent.copy())),1,0),1,!0)),n=new zt(i.doc.resolve(n.$from.pos),i.doc.resolve(o),n.depth));const l=dt(n);if(l==null)return!1;i.lift(n,l);let a=i.mapping.map(s,-1)-1;return ut(i.doc,a)&&i.join(a),e(i.scrollIntoView()),!0}function lc(r,e,t){let n=r.tr,i=t.parent;for(let h=t.end,p=t.endIndex-1,m=t.startIndex;p>m;p--)h-=i.child(p).nodeSize,n.delete(h-1,h+1);let s=n.doc.resolve(t.start),o=s.nodeAfter;if(n.mapping.map(t.end)!=t.start+s.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=s.node(-1),d=s.index(-1);if(!c.canReplace(d+(l?0:1),d+1,o.content.append(a?y.empty:y.from(i))))return!1;let u=s.pos,f=u+o.nodeSize;return n.step(new I(u-(l?1:0),f+(a?1:0),u+1,f-1,new k((l?y.empty:y.from(i.copy(y.empty))).append(a?y.empty:y.from(i.copy(y.empty))),l?0:1,a?0:1),l?0:1)),e(n.scrollIntoView()),!0}function ac(r){return function(e,t){let{$from:n,$to:i}=e.selection,s=n.blockRange(i,c=>c.childCount>0&&c.firstChild.type==r);if(!s)return!1;let o=s.startIndex;if(o==0)return!1;let l=s.parent,a=l.child(o-1);if(a.type!=r)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,d=y.from(c?r.create():null),u=new k(y.from(r.create(null,y.from(l.type.create(null,d)))),c?3:1,0),f=s.start,h=s.end;t(e.tr.step(new I(f-(c?3:1),h,f,h,u,1,!0)).scrollIntoView())}return!0}}function Qt(r){const{state:e,transaction:t}=r;let{selection:n}=t,{doc:i}=t,{storedMarks:s}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),filterTransaction:e.filterTransaction,plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return s},get selection(){return n},get doc(){return i},get tr(){return n=t.selection,i=t.doc,s=t.storedMarks,t}}}class en{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:n}=this,{view:i}=t,{tr:s}=n,o=this.buildProps(s);return Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...d)=>{const u=a(...d)(o);return!s.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(s),u}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:n,editor:i,state:s}=this,{view:o}=i,l=[],a=!!e,c=e||s.tr,d=()=>(!a&&t&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(c),l.every(f=>f===!0)),u={...Object.fromEntries(Object.entries(n).map(([f,h])=>[f,(...m)=>{const g=this.buildProps(c,t),b=h(...m)(g);return l.push(b),u}])),run:d};return u}createCan(e){const{rawCommands:t,state:n}=this,i=!1,s=e||n.tr,o=this.buildProps(s,i);return{...Object.fromEntries(Object.entries(t).map(([a,c])=>[a,(...d)=>c(...d)({...o,dispatch:void 0})])),chain:()=>this.createChain(s,i)}}buildProps(e,t=!0){const{rawCommands:n,editor:i,state:s}=this,{view:o}=i;s.storedMarks&&e.setStoredMarks(s.storedMarks);const l={tr:e,editor:i,view:o,state:Qt({state:s,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([a,c])=>[a,(...d)=>c(...d)(l)]))}};return l}}class cc{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach(i=>i.apply(this,t)),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(i=>i!==t):delete this.callbacks[e]),this}removeAllListeners(){this.callbacks={}}}function x(r,e,t){return r.config[e]===void 0&&r.parent?x(r.parent,e,t):typeof r.config[e]=="function"?r.config[e].bind({...t,parent:r.parent?x(r.parent,e,t):null}):r.config[e]}function tn(r){const e=r.filter(i=>i.type==="extension"),t=r.filter(i=>i.type==="node"),n=r.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:n}}function zs(r){const e=[],{nodeExtensions:t,markExtensions:n}=tn(r),i=[...t,...n],s={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return r.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=x(o,"addGlobalAttributes",l);if(!a)return;a().forEach(d=>{d.types.forEach(u=>{Object.entries(d.attributes).forEach(([f,h])=>{e.push({type:u,name:f,attribute:{...s,...h}})})})})}),i.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=x(o,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([d,u])=>{const f={...s,...u};u!=null&&u.isRequired&&(u==null?void 0:u.default)===void 0&&delete f.default,e.push({type:o.name,name:d,attribute:f})})}),e}function R(r,e){if(typeof r=="string"){if(!e.nodes[r])throw Error(`There is no node type named '${r}'. Maybe you forgot to add the extension?`);return e.nodes[r]}return r}function W(...r){return r.filter(e=>!!e).reduce((e,t)=>{const n={...e};return Object.entries(t).forEach(([i,s])=>{if(!n[i]){n[i]=s;return}i==="class"?n[i]=[n[i],s].join(" "):i==="style"?n[i]=[n[i],s].join("; "):n[i]=s}),n},{})}function Un(r,e){return e.filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(r.attrs)||{}:{[t.name]:r.attrs[t.name]}).reduce((t,n)=>W(t,n),{})}function Fs(r){return typeof r=="function"}function w(r,e=void 0,...t){return Fs(r)?e?r.bind(e)(...t):r(...t):r}function dc(r={}){return Object.keys(r).length===0&&r.constructor===Object}function uc(r){return typeof r!="string"?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):r==="true"?!0:r==="false"?!1:r}function fi(r,e){return r.style?r:{...r,getAttrs:t=>{const n=r.getAttrs?r.getAttrs(t):r.attrs;if(n===!1)return!1;const i=e.reduce((s,o)=>{const l=o.attribute.parseHTML?o.attribute.parseHTML(t):uc(t.getAttribute(o.name));return l==null?s:{...s,[o.name]:l}},{});return{...n,...i}}}}function hi(r){return Object.fromEntries(Object.entries(r).filter(([e,t])=>e==="attrs"&&dc(t)?!1:t!=null))}function fc(r){var e;const t=zs(r),{nodeExtensions:n,markExtensions:i}=tn(r),s=(e=n.find(a=>x(a,"topNode")))===null||e===void 0?void 0:e.name,o=Object.fromEntries(n.map(a=>{const c=t.filter(g=>g.type===a.name),d={name:a.name,options:a.options,storage:a.storage},u=r.reduce((g,b)=>{const S=x(b,"extendNodeSchema",d);return{...g,...S?S(a):{}}},{}),f=hi({...u,content:w(x(a,"content",d)),marks:w(x(a,"marks",d)),group:w(x(a,"group",d)),inline:w(x(a,"inline",d)),atom:w(x(a,"atom",d)),selectable:w(x(a,"selectable",d)),draggable:w(x(a,"draggable",d)),code:w(x(a,"code",d)),defining:w(x(a,"defining",d)),isolating:w(x(a,"isolating",d)),attrs:Object.fromEntries(c.map(g=>{var b;return[g.name,{default:(b=g==null?void 0:g.attribute)===null||b===void 0?void 0:b.default}]}))}),h=w(x(a,"parseHTML",d));h&&(f.parseDOM=h.map(g=>fi(g,c)));const p=x(a,"renderHTML",d);p&&(f.toDOM=g=>p({node:g,HTMLAttributes:Un(g,c)}));const m=x(a,"renderText",d);return m&&(f.toText=m),[a.name,f]})),l=Object.fromEntries(i.map(a=>{const c=t.filter(m=>m.type===a.name),d={name:a.name,options:a.options,storage:a.storage},u=r.reduce((m,g)=>{const b=x(g,"extendMarkSchema",d);return{...m,...b?b(a):{}}},{}),f=hi({...u,inclusive:w(x(a,"inclusive",d)),excludes:w(x(a,"excludes",d)),group:w(x(a,"group",d)),spanning:w(x(a,"spanning",d)),code:w(x(a,"code",d)),attrs:Object.fromEntries(c.map(m=>{var g;return[m.name,{default:(g=m==null?void 0:m.attribute)===null||g===void 0?void 0:g.default}]}))}),h=w(x(a,"parseHTML",d));h&&(f.parseDOM=h.map(m=>fi(m,c)));const p=x(a,"renderHTML",d);return p&&(f.toDOM=m=>p({mark:m,HTMLAttributes:Un(m,c)})),[a.name,f]}));return new Eo({topNode:s,nodes:o,marks:l})}function Tn(r,e){return e.nodes[r]||e.marks[r]||null}function pi(r,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===r.name):e}const hc=(r,e=500)=>{let t="";const n=r.parentOffset;return r.parent.nodesBetween(Math.max(0,n-e),n,(i,s,o,l)=>{var a,c;const d=((c=(a=i.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:i,pos:s,parent:o,index:l}))||i.textContent||"%leaf%";t+=d.slice(0,Math.max(0,n-s))}),t};function dr(r){return Object.prototype.toString.call(r)==="[object RegExp]"}class nn{constructor(e){this.find=e.find,this.handler=e.handler}}const pc=(r,e)=>{if(dr(e))return e.exec(r);const t=e(r);if(!t)return null;const n=[];return n.push(t.text),n.index=t.index,n.input=r,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),n.push(t.replaceWith)),n};function Nn(r){var e;const{editor:t,from:n,to:i,text:s,rules:o,plugin:l}=r,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(n);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(f=>f.type.spec.code))return!1;let d=!1;const u=hc(c)+s;return o.forEach(f=>{if(d)return;const h=pc(u,f.find);if(!h)return;const p=a.state.tr,m=Qt({state:a.state,transaction:p}),g={from:n-(h[0].length-s.length),to:i},{commands:b,chain:S,can:E}=new en({editor:t,state:m});f.handler({state:m,range:g,match:h,commands:b,chain:S,can:E})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:n,to:i,text:s}),a.dispatch(p),d=!0)}),d}function mc(r){const{editor:e,rules:t}=r,n=new ee({state:{init(){return null},apply(i,s){const o=i.getMeta(n);return o||(i.selectionSet||i.docChanged?null:s)}},props:{handleTextInput(i,s,o,l){return Nn({editor:e,from:s,to:o,text:l,rules:t,plugin:n})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{const{$cursor:s}=i.state.selection;s&&Nn({editor:e,from:s.pos,to:s.pos,text:"",rules:t,plugin:n})}),!1)},handleKeyDown(i,s){if(s.key!=="Enter")return!1;const{$cursor:o}=i.state.selection;return o?Nn({editor:e,from:o.pos,to:o.pos,text:`
`,rules:t,plugin:n}):!1}},isInputRules:!0});return n}function gc(r){return typeof r=="number"}class yc{constructor(e){this.find=e.find,this.handler=e.handler}}const bc=(r,e)=>{if(dr(e))return[...r.matchAll(e)];const t=e(r);return t?t.map(n=>{const i=[];return i.push(n.text),i.index=n.index,i.input=r,i.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),i.push(n.replaceWith)),i}):[]};function kc(r){const{editor:e,state:t,from:n,to:i,rule:s}=r,{commands:o,chain:l,can:a}=new en({editor:e,state:t}),c=[];return t.doc.nodesBetween(n,i,(u,f)=>{if(!u.isTextblock||u.type.spec.code)return;const h=Math.max(n,f),p=Math.min(i,f+u.content.size),m=u.textBetween(h-f,p-f,void 0,"￼");bc(m,s.find).forEach(b=>{if(b.index===void 0)return;const S=h+b.index+1,E=S+b[0].length,q={from:t.tr.mapping.map(S),to:t.tr.mapping.map(E)},P=s.handler({state:t,range:q,match:b,commands:o,chain:l,can:a});c.push(P)})}),c.every(u=>u!==null)}function xc(r){const{editor:e,rules:t}=r;let n=null,i=!1,s=!1;return t.map(l=>new ee({view(a){const c=d=>{var u;n=!((u=a.dom.parentElement)===null||u===void 0)&&u.contains(d.target)?a.dom.parentElement:null};return window.addEventListener("dragstart",c),{destroy(){window.removeEventListener("dragstart",c)}}},props:{handleDOMEvents:{drop:a=>(s=n===a.dom.parentElement,!1),paste:(a,c)=>{var d;const u=(d=c.clipboardData)===null||d===void 0?void 0:d.getData("text/html");return i=!!(u!=null&&u.includes("data-pm-slice")),!1}}},appendTransaction:(a,c,d)=>{const u=a[0],f=u.getMeta("uiEvent")==="paste"&&!i,h=u.getMeta("uiEvent")==="drop"&&!s;if(!f&&!h)return;const p=c.doc.content.findDiffStart(d.doc.content),m=c.doc.content.findDiffEnd(d.doc.content);if(!gc(p)||!m||p===m.b)return;const g=d.tr,b=Qt({state:d,transaction:g});if(!(!kc({editor:e,state:b,from:Math.max(p-1,0),to:m.b-1,rule:l})||!g.steps.length))return g}}))}function Sc(r){const e=r.filter((t,n)=>r.indexOf(t)!==n);return[...new Set(e)]}class Xe{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=Xe.resolve(e),this.schema=fc(this.extensions),this.extensions.forEach(n=>{var i;this.editor.extensionStorage[n.name]=n.storage;const s={name:n.name,options:n.options,storage:n.storage,editor:this.editor,type:Tn(n.name,this.schema)};n.type==="mark"&&(!((i=w(x(n,"keepOnSplit",s)))!==null&&i!==void 0)||i)&&this.splittableMarks.push(n.name);const o=x(n,"onBeforeCreate",s);o&&this.editor.on("beforeCreate",o);const l=x(n,"onCreate",s);l&&this.editor.on("create",l);const a=x(n,"onUpdate",s);a&&this.editor.on("update",a);const c=x(n,"onSelectionUpdate",s);c&&this.editor.on("selectionUpdate",c);const d=x(n,"onTransaction",s);d&&this.editor.on("transaction",d);const u=x(n,"onFocus",s);u&&this.editor.on("focus",u);const f=x(n,"onBlur",s);f&&this.editor.on("blur",f);const h=x(n,"onDestroy",s);h&&this.editor.on("destroy",h)})}static resolve(e){const t=Xe.sort(Xe.flatten(e)),n=Sc(t.map(i=>i.name));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map(i=>`'${i}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const n={name:t.name,options:t.options,storage:t.storage},i=x(t,"addExtensions",n);return i?[t,...this.flatten(i())]:t}).flat(10)}static sort(e){return e.sort((n,i)=>{const s=x(n,"priority")||100,o=x(i,"priority")||100;return s>o?-1:s<o?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:Tn(t.name,this.schema)},i=x(t,"addCommands",n);return i?{...e,...i()}:e},{})}get plugins(){const{editor:e}=this,t=Xe.sort([...this.extensions].reverse()),n=[],i=[],s=t.map(o=>{const l={name:o.name,options:o.options,storage:o.storage,editor:e,type:Tn(o.name,this.schema)},a=[],c=x(o,"addKeyboardShortcuts",l);let d={};if(o.type==="mark"&&o.config.exitable&&(d.ArrowRight=()=>Ae.handleExit({editor:e,mark:o})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,b])=>[g,()=>b({editor:e})]));d={...d,...m}}const u=$a(d);a.push(u);const f=x(o,"addInputRules",l);pi(o,e.options.enableInputRules)&&f&&n.push(...f());const h=x(o,"addPasteRules",l);pi(o,e.options.enablePasteRules)&&h&&i.push(...h());const p=x(o,"addProseMirrorPlugins",l);if(p){const m=p();a.push(...m)}return a}).flat();return[mc({editor:e,rules:n}),...xc({editor:e,rules:i}),...s]}get attributes(){return zs(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=tn(this.extensions);return Object.fromEntries(t.filter(n=>!!x(n,"addNodeView")).map(n=>{const i=this.attributes.filter(a=>a.type===n.name),s={name:n.name,options:n.options,storage:n.storage,editor:e,type:R(n.name,this.schema)},o=x(n,"addNodeView",s);if(!o)return[];const l=(a,c,d,u)=>{const f=Un(a,i);return o()({editor:e,node:a,getPos:d,decorations:u,HTMLAttributes:f,extension:n})};return[n.name,l]}))}}function Mc(r){return Object.prototype.toString.call(r).slice(8,-1)}function En(r){return Mc(r)!=="Object"?!1:r.constructor===Object&&Object.getPrototypeOf(r)===Object.prototype}function rn(r,e){const t={...r};return En(r)&&En(e)&&Object.keys(e).forEach(n=>{En(e[n])?n in r?t[n]=rn(r[n],e[n]):Object.assign(t,{[n]:e[n]}):Object.assign(t,{[n]:e[n]})}),t}class Y{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=w(x(this,"addOptions",{name:this.name}))),this.storage=w(x(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Y(e)}configure(e={}){const t=this.extend();return t.options=rn(this.options,e),t.storage=w(x(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Y(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=w(x(t,"addOptions",{name:t.name})),t.storage=w(x(t,"addStorage",{name:t.name,options:t.options})),t}}function Ls(r,e,t){const{from:n,to:i}=e,{blockSeparator:s=`

`,textSerializers:o={}}=t||{};let l="",a=!0;return r.nodesBetween(n,i,(c,d,u,f)=>{var h;const p=o==null?void 0:o[c.type.name];p?(c.isBlock&&!a&&(l+=s,a=!0),u&&(l+=p({node:c,pos:d,parent:u,index:f,range:e}))):c.isText?(l+=(h=c==null?void 0:c.text)===null||h===void 0?void 0:h.slice(Math.max(n,d)-d,i-d),a=!1):c.isBlock&&!a&&(l+=s,a=!0)}),l}function Vs(r){return Object.fromEntries(Object.entries(r.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const wc=Y.create({name:"clipboardTextSerializer",addProseMirrorPlugins(){return[new ee({key:new ve("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:r}=this,{state:e,schema:t}=r,{doc:n,selection:i}=e,{ranges:s}=i,o=Math.min(...s.map(d=>d.$from.pos)),l=Math.max(...s.map(d=>d.$to.pos)),a=Vs(t);return Ls(n,{from:o,to:l},{textSerializers:a})}}})]}}),Cc=()=>({editor:r,view:e})=>(requestAnimationFrame(()=>{var t;r.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),Oc=(r=!1)=>({commands:e})=>e.setContent("",r),Tc=()=>({state:r,tr:e,dispatch:t})=>{const{selection:n}=e,{ranges:i}=n;return t&&i.forEach(({$from:s,$to:o})=>{r.doc.nodesBetween(s.pos,o.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:d}=e,u=c.resolve(d.map(a)),f=c.resolve(d.map(a+l.nodeSize)),h=u.blockRange(f);if(!h)return;const p=dt(h);if(l.type.isTextblock){const{defaultType:m}=u.parent.contentMatchAt(u.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},Nc=r=>e=>r(e),Ec=()=>({state:r,dispatch:e})=>Ya(r,e),Ac=r=>({tr:e,state:t,dispatch:n})=>{const i=R(r,t.schema),s=e.selection.$anchor;for(let o=s.depth;o>0;o-=1)if(s.node(o).type===i){if(n){const a=s.before(o),c=s.after(o);e.delete(a,c).scrollIntoView()}return!0}return!1},vc=r=>({tr:e,dispatch:t})=>{const{from:n,to:i}=r;return t&&e.delete(n,i),!0},Dc=()=>({state:r,dispatch:e})=>Ja(r,e),Ic=()=>({commands:r})=>r.keyboardShortcut("Enter"),Rc=()=>({state:r,dispatch:e})=>_a(r,e);function qt(r,e,t={strict:!0}){const n=Object.keys(e);return n.length?n.every(i=>t.strict?e[i]===r[i]:dr(e[i])?e[i].test(r[i]):e[i]===r[i]):!0}function Gn(r,e,t={}){return r.find(n=>n.type===e&&qt(n.attrs,t))}function Pc(r,e,t={}){return!!Gn(r,e,t)}function ur(r,e,t={}){if(!r||!e)return;let n=r.parent.childAfter(r.parentOffset);if(r.parentOffset===n.offset&&n.offset!==0&&(n=r.parent.childBefore(r.parentOffset)),!n.node)return;const i=Gn([...n.node.marks],e,t);if(!i)return;let s=n.index,o=r.start()+n.offset,l=s+1,a=o+n.node.nodeSize;for(Gn([...n.node.marks],e,t);s>0&&i.isInSet(r.parent.child(s-1).marks);)s-=1,o-=r.parent.child(s).nodeSize;for(;l<r.parent.childCount&&Pc([...r.parent.child(l).marks],e,t);)a+=r.parent.child(l).nodeSize,l+=1;return{from:o,to:a}}function Ie(r,e){if(typeof r=="string"){if(!e.marks[r])throw Error(`There is no mark type named '${r}'. Maybe you forgot to add the extension?`);return e.marks[r]}return r}const Bc=(r,e={})=>({tr:t,state:n,dispatch:i})=>{const s=Ie(r,n.schema),{doc:o,selection:l}=t,{$from:a,from:c,to:d}=l;if(i){const u=ur(a,s,e);if(u&&u.from<=c&&u.to>=d){const f=O.create(o,u.from,u.to);t.setSelection(f)}}return!0},zc=r=>e=>{const t=typeof r=="function"?r(e):r;for(let n=0;n<t.length;n+=1)if(t[n](e))return!0;return!1};function Hs(r){return r instanceof O}function Be(r=0,e=0,t=0){return Math.min(Math.max(r,e),t)}function $s(r,e=null){if(!e)return null;const t=C.atStart(r),n=C.atEnd(r);if(e==="start"||e===!0)return t;if(e==="end")return n;const i=t.from,s=n.to;return e==="all"?O.create(r,Be(0,i,s),Be(r.content.size,i,s)):O.create(r,Be(e,i,s),Be(e,i,s))}function fr(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Fc=(r=null,e={})=>({editor:t,view:n,tr:i,dispatch:s})=>{e={scrollIntoView:!0,...e};const o=()=>{fr()&&n.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(n.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(n.hasFocus()&&r===null||r===!1)return!0;if(s&&r===null&&!Hs(t.state.selection))return o(),!0;const l=$s(i.doc,r)||t.state.selection,a=t.state.selection.eq(l);return s&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),o()),!0},Lc=(r,e)=>t=>r.every((n,i)=>e(n,{...t,index:i})),Vc=(r,e)=>({tr:t,commands:n})=>n.insertContentAt({from:t.selection.from,to:t.selection.to},r,e);function mi(r){const e=`<body>${r}</body>`;return new window.DOMParser().parseFromString(e,"text/html").body}function Kt(r,e,t){if(t={slice:!0,parseOptions:{},...t},typeof r=="object"&&r!==null)try{return Array.isArray(r)?y.fromArray(r.map(n=>e.nodeFromJSON(n))):e.nodeFromJSON(r)}catch(n){return console.warn("[tiptap warn]: Invalid content.","Passed value:",r,"Error:",n),Kt("",e,t)}if(typeof r=="string"){const n=nt.fromSchema(e);return t.slice?n.parseSlice(mi(r),t.parseOptions).content:n.parse(mi(r),t.parseOptions)}return Kt("",e,t)}function Hc(r,e,t){const n=r.steps.length-1;if(n<e)return;const i=r.steps[n];if(!(i instanceof V||i instanceof I))return;const s=r.mapping.maps[n];let o=0;s.forEach((l,a,c,d)=>{o===0&&(o=d)}),r.setSelection(C.near(r.doc.resolve(o),t))}const $c=r=>r.toString().startsWith("<"),Jc=(r,e,t)=>({tr:n,dispatch:i,editor:s})=>{if(i){t={parseOptions:{},updateSelection:!0,...t};const o=Kt(e,s.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions}});if(o.toString()==="<>")return!0;let{from:l,to:a}=typeof r=="number"?{from:r,to:r}:r,c=!0,d=!0;if(($c(o)?o:[o]).forEach(f=>{f.check(),c=c?f.isText&&f.marks.length===0:!1,d=d?f.isBlock:!1}),l===a&&d){const{parent:f}=n.doc.resolve(l);f.isTextblock&&!f.type.spec.code&&!f.childCount&&(l-=1,a+=1)}c?n.insertText(e,l,a):n.replaceWith(l,a,o),t.updateSelection&&Hc(n,n.steps.length-1,-1)}return!0},Wc=()=>({state:r,dispatch:e})=>Wa(r,e),qc=()=>({state:r,dispatch:e})=>Ka(r,e);function Js(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function Kc(r){const e=r.split(/-(?!$)/);let t=e[e.length-1];t==="Space"&&(t=" ");let n,i,s,o;for(let l=0;l<e.length-1;l+=1){const a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))fr()||Js()?o=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return n&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),o&&(t=`Meta-${t}`),s&&(t=`Shift-${t}`),t}const jc=r=>({editor:e,view:t,tr:n,dispatch:i})=>{const s=Kc(r).split(/-(?!$)/),o=s.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:o==="Space"?" ":o,altKey:s.includes("Alt"),ctrlKey:s.includes("Ctrl"),metaKey:s.includes("Meta"),shiftKey:s.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a==null||a.steps.forEach(c=>{const d=c.map(n.mapping);d&&i&&n.maybeStep(d)}),!0};function Ct(r,e,t={}){const{from:n,to:i,empty:s}=r.selection,o=e?R(e,r.schema):null,l=[];r.doc.nodesBetween(n,i,(u,f)=>{if(u.isText)return;const h=Math.max(n,f),p=Math.min(i,f+u.nodeSize);l.push({node:u,from:h,to:p})});const a=i-n,c=l.filter(u=>o?o.name===u.node.type.name:!0).filter(u=>qt(u.node.attrs,t,{strict:!1}));return s?!!c.length:c.reduce((u,f)=>u+f.to-f.from,0)>=a}const Uc=(r,e={})=>({state:t,dispatch:n})=>{const i=R(r,t.schema);return Ct(t,i,e)?Ua(t,n):!1},Gc=()=>({state:r,dispatch:e})=>Xa(r,e),_c=r=>({state:e,dispatch:t})=>{const n=R(r,e.schema);return sc(n)(e,t)},Yc=()=>({state:r,dispatch:e})=>Ga(r,e);function sn(r,e){return e.nodes[r]?"node":e.marks[r]?"mark":null}function gi(r,e){const t=typeof e=="string"?[e]:e;return Object.keys(r).reduce((n,i)=>(t.includes(i)||(n[i]=r[i]),n),{})}const Xc=(r,e)=>({tr:t,state:n,dispatch:i})=>{let s=null,o=null;const l=sn(typeof r=="string"?r:r.name,n.schema);return l?(l==="node"&&(s=R(r,n.schema)),l==="mark"&&(o=Ie(r,n.schema)),i&&t.selection.ranges.forEach(a=>{n.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,d)=>{s&&s===c.type&&t.setNodeMarkup(d,void 0,gi(c.attrs,e)),o&&c.marks.length&&c.marks.forEach(u=>{o===u.type&&t.addMark(d,d+c.nodeSize,o.create(gi(u.attrs,e)))})})}),!0):!1},Zc=()=>({tr:r,dispatch:e})=>(e&&r.scrollIntoView(),!0),Qc=()=>({tr:r,commands:e})=>e.setTextSelection({from:0,to:r.doc.content.size}),ed=()=>({state:r,dispatch:e})=>qa(r,e),td=()=>({state:r,dispatch:e})=>ja(r,e),nd=()=>({state:r,dispatch:e})=>Za(r,e),rd=()=>({state:r,dispatch:e})=>tc(r,e),id=()=>({state:r,dispatch:e})=>ec(r,e);function Ws(r,e,t={}){return Kt(r,e,{slice:!1,parseOptions:t})}const sd=(r,e=!1,t={})=>({tr:n,editor:i,dispatch:s})=>{const{doc:o}=n,l=Ws(r,i.schema,t);return s&&n.replaceWith(0,o.content.size,l).setMeta("preventUpdate",!e),!0};function od(r){for(let e=0;e<r.edgeCount;e+=1){const{type:t}=r.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function ld(r,e){for(let t=r.depth;t>0;t-=1){const n=r.node(t);if(e(n))return{pos:t>0?r.before(t):0,start:r.start(t),depth:t,node:n}}}function hr(r){return e=>ld(e.$from,r)}function ad(r,e){const t=de.fromSchema(e).serializeFragment(r),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}function cd(r,e){const t={from:0,to:r.content.size};return Ls(r,t,e)}function qs(r,e){const t=Ie(e,r.schema),{from:n,to:i,empty:s}=r.selection,o=[];s?(r.storedMarks&&o.push(...r.storedMarks),o.push(...r.selection.$head.marks())):r.doc.nodesBetween(n,i,a=>{o.push(...a.marks)});const l=o.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function dd(r,e){const t=R(e,r.schema),{from:n,to:i}=r.selection,s=[];r.doc.nodesBetween(n,i,l=>{s.push(l)});const o=s.reverse().find(l=>l.type.name===t.name);return o?{...o.attrs}:{}}function ud(r,e){const t=sn(typeof e=="string"?e:e.name,r.schema);return t==="node"?dd(r,e):t==="mark"?qs(r,e):{}}function Ks(r,e,t){const n=[];return r===e?t.resolve(r).marks().forEach(i=>{const s=t.resolve(r-1),o=ur(s,i.type);o&&n.push({mark:i,...o})}):t.nodesBetween(r,e,(i,s)=>{n.push(...i.marks.map(o=>({from:s,to:s+i.nodeSize,mark:o})))}),n}function _n(r,e,t={}){const{empty:n,ranges:i}=r.selection,s=e?Ie(e,r.schema):null;if(n)return!!(r.storedMarks||r.selection.$from.marks()).filter(u=>s?s.name===u.type.name:!0).find(u=>qt(u.attrs,t,{strict:!1}));let o=0;const l=[];if(i.forEach(({$from:u,$to:f})=>{const h=u.pos,p=f.pos;r.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;const b=Math.max(h,g),S=Math.min(p,g+m.nodeSize),E=S-b;o+=E,l.push(...m.marks.map(q=>({mark:q,from:b,to:S})))})}),o===0)return!1;const a=l.filter(u=>s?s.name===u.mark.type.name:!0).filter(u=>qt(u.mark.attrs,t,{strict:!1})).reduce((u,f)=>u+f.to-f.from,0),c=l.filter(u=>s?u.mark.type!==s&&u.mark.type.excludes(s):!0).reduce((u,f)=>u+f.to-f.from,0);return(a>0?a+c:a)>=o}function fd(r,e,t={}){if(!e)return Ct(r,null,t)||_n(r,null,t);const n=sn(e,r.schema);return n==="node"?Ct(r,e,t):n==="mark"?_n(r,e,t):!1}function yi(r,e){const{nodeExtensions:t}=tn(e),n=t.find(o=>o.name===r);if(!n)return!1;const i={name:n.name,options:n.options,storage:n.storage},s=w(x(n,"group",i));return typeof s!="string"?!1:s.split(" ").includes("list")}function hd(r){var e;const t=(e=r.type.createAndFill())===null||e===void 0?void 0:e.toJSON(),n=r.toJSON();return JSON.stringify(t)===JSON.stringify(n)}function pd(r,e,t){var n;const{selection:i}=e;let s=null;if(Hs(i)&&(s=i.$cursor),s){const l=(n=r.storedMarks)!==null&&n!==void 0?n:s.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}const{ranges:o}=i;return o.some(({$from:l,$to:a})=>{let c=l.depth===0?r.doc.inlineContent&&r.doc.type.allowsMarkType(t):!1;return r.doc.nodesBetween(l.pos,a.pos,(d,u,f)=>{if(c)return!1;if(d.isInline){const h=!f||f.type.allowsMarkType(t),p=!!t.isInSet(d.marks)||!d.marks.some(m=>m.type.excludes(t));c=h&&p}return!c}),c})}const md=(r,e={})=>({tr:t,state:n,dispatch:i})=>{const{selection:s}=t,{empty:o,ranges:l}=s,a=Ie(r,n.schema);if(i)if(o){const c=qs(n,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const d=c.$from.pos,u=c.$to.pos;n.doc.nodesBetween(d,u,(f,h)=>{const p=Math.max(h,d),m=Math.min(h+f.nodeSize,u);f.marks.find(b=>b.type===a)?f.marks.forEach(b=>{a===b.type&&t.addMark(p,m,a.create({...b.attrs,...e}))}):t.addMark(p,m,a.create(e))})});return pd(n,t,a)},gd=(r,e)=>({tr:t})=>(t.setMeta(r,e),!0),yd=(r,e={})=>({state:t,dispatch:n,chain:i})=>{const s=R(r,t.schema);return s.isTextblock?i().command(({commands:o})=>ui(s,e)(t)?!0:o.clearNodes()).command(({state:o})=>ui(s,e)(o,n)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},bd=r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,i=Be(r,0,n.content.size),s=M.create(n,i);e.setSelection(s)}return!0},kd=r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,{from:i,to:s}=typeof r=="number"?{from:r,to:r}:r,o=O.atStart(n).from,l=O.atEnd(n).to,a=Be(i,o,l),c=Be(s,o,l),d=O.create(n,a,c);e.setSelection(d)}return!0},xd=r=>({state:e,dispatch:t})=>{const n=R(r,e.schema);return ac(n)(e,t)};function Dt(r,e,t){return Object.fromEntries(Object.entries(t).filter(([n])=>{const i=r.find(s=>s.type===e&&s.name===n);return i?i.attribute.keepOnSplit:!1}))}function bi(r,e){const t=r.storedMarks||r.selection.$to.parentOffset&&r.selection.$from.marks();if(t){const n=t.filter(i=>e==null?void 0:e.includes(i.type.name));r.tr.ensureMarks(n)}}const Sd=({keepMarks:r=!0}={})=>({tr:e,state:t,dispatch:n,editor:i})=>{const{selection:s,doc:o}=e,{$from:l,$to:a}=s,c=i.extensionManager.attributes,d=Dt(c,l.node().type.name,l.node().attrs);if(s instanceof M&&s.node.isBlock)return!l.parentOffset||!Qe(o,l.pos)?!1:(n&&(r&&bi(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;if(n){const u=a.parentOffset===a.parent.content.size;s instanceof O&&e.deleteSelection();const f=l.depth===0?void 0:od(l.node(-1).contentMatchAt(l.indexAfter(-1)));let h=u&&f?[{type:f,attrs:d}]:void 0,p=Qe(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&Qe(e.doc,e.mapping.map(l.pos),1,f?[{type:f}]:void 0)&&(p=!0,h=f?[{type:f,attrs:d}]:void 0),p&&(e.split(e.mapping.map(l.pos),1,h),f&&!u&&!l.parentOffset&&l.parent.type!==f)){const m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,f)&&e.setNodeMarkup(e.mapping.map(l.before()),f)}r&&bi(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return!0},Md=r=>({tr:e,state:t,dispatch:n,editor:i})=>{var s;const o=R(r,t.schema),{$from:l,$to:a}=t.selection,c=t.selection.node;if(c&&c.isBlock||l.depth<2||!l.sameParent(a))return!1;const d=l.node(-1);if(d.type!==o)return!1;const u=i.extensionManager.attributes;if(l.parent.content.size===0&&l.node(-1).childCount===l.indexAfter(-1)){if(l.depth===2||l.node(-3).type!==o||l.index(-2)!==l.node(-2).childCount-1)return!1;if(n){let g=y.empty;const b=l.index(-1)?1:l.index(-2)?2:3;for(let te=l.depth-b;te>=l.depth-3;te-=1)g=y.from(l.node(te).copy(g));const S=l.indexAfter(-1)<l.node(-2).childCount?1:l.indexAfter(-2)<l.node(-3).childCount?2:3,E=Dt(u,l.node().type.name,l.node().attrs),q=((s=o.contentMatch.defaultType)===null||s===void 0?void 0:s.createAndFill(E))||void 0;g=g.append(y.from(o.createAndFill(null,q)||void 0));const P=l.before(l.depth-(b-1));e.replace(P,l.after(-S),new k(g,4-b,0));let ge=-1;e.doc.nodesBetween(P,e.doc.content.size,(te,T)=>{if(ge>-1)return!1;te.isTextblock&&te.content.size===0&&(ge=T+1)}),ge>-1&&e.setSelection(O.near(e.doc.resolve(ge))),e.scrollIntoView()}return!0}const f=a.pos===l.end()?d.contentMatchAt(0).defaultType:null,h=Dt(u,d.type.name,d.attrs),p=Dt(u,l.node().type.name,l.node().attrs);e.delete(l.pos,a.pos);const m=f?[{type:o,attrs:h},{type:f,attrs:p}]:[{type:o,attrs:h}];return Qe(e.doc,l.pos,2)?(n&&e.split(l.pos,2,m).scrollIntoView(),!0):!1},ki=(r,e)=>{const t=hr(o=>o.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(n===void 0)return!0;const i=r.doc.nodeAt(n);return t.node.type===(i==null?void 0:i.type)&&ut(r.doc,t.pos)&&r.join(t.pos),!0},xi=(r,e)=>{const t=hr(o=>o.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(t.start).after(t.depth);if(n===void 0)return!0;const i=r.doc.nodeAt(n);return t.node.type===(i==null?void 0:i.type)&&ut(r.doc,n)&&r.join(n),!0},wd=(r,e)=>({editor:t,tr:n,state:i,dispatch:s,chain:o,commands:l,can:a})=>{const{extensions:c}=t.extensionManager,d=R(r,i.schema),u=R(e,i.schema),{selection:f}=i,{$from:h,$to:p}=f,m=h.blockRange(p);if(!m)return!1;const g=hr(b=>yi(b.type.name,c))(f);if(m.depth>=1&&g&&m.depth-g.depth<=1){if(g.node.type===d)return l.liftListItem(u);if(yi(g.node.type.name,c)&&d.validContent(g.node.content)&&s)return o().command(()=>(n.setNodeMarkup(g.pos,d),!0)).command(()=>ki(n,d)).command(()=>xi(n,d)).run()}return o().command(()=>a().wrapInList(d)?!0:l.clearNodes()).wrapInList(d).command(()=>ki(n,d)).command(()=>xi(n,d)).run()},Cd=(r,e={},t={})=>({state:n,commands:i})=>{const{extendEmptyMarkRange:s=!1}=t,o=Ie(r,n.schema);return _n(n,o,e)?i.unsetMark(o,{extendEmptyMarkRange:s}):i.setMark(o,e)},Od=(r,e,t={})=>({state:n,commands:i})=>{const s=R(r,n.schema),o=R(e,n.schema);return Ct(n,s,t)?i.setNode(o):i.setNode(s,t)},Td=(r,e={})=>({state:t,commands:n})=>{const i=R(r,t.schema);return Ct(t,i,e)?n.lift(i):n.wrapIn(i,e)},Nd=()=>({state:r,dispatch:e})=>{const t=r.plugins;for(let n=0;n<t.length;n+=1){const i=t[n];let s;if(i.spec.isInputRules&&(s=i.getState(r))){if(e){const o=r.tr,l=s.transform;for(let a=l.steps.length-1;a>=0;a-=1)o.step(l.steps[a].invert(l.docs[a]));if(s.text){const a=o.doc.resolve(s.from).marks();o.replaceWith(s.from,s.to,r.schema.text(s.text,a))}else o.delete(s.from,s.to)}return!0}}return!1},Ed=()=>({tr:r,dispatch:e})=>{const{selection:t}=r,{empty:n,ranges:i}=t;return n||e&&i.forEach(s=>{r.removeMark(s.$from.pos,s.$to.pos)}),!0},Ad=(r,e={})=>({tr:t,state:n,dispatch:i})=>{var s;const{extendEmptyMarkRange:o=!1}=e,{selection:l}=t,a=Ie(r,n.schema),{$from:c,empty:d,ranges:u}=l;if(!i)return!0;if(d&&o){let{from:f,to:h}=l;const p=(s=c.marks().find(g=>g.type===a))===null||s===void 0?void 0:s.attrs,m=ur(c,a,p);m&&(f=m.from,h=m.to),t.removeMark(f,h,a)}else u.forEach(f=>{t.removeMark(f.$from.pos,f.$to.pos,a)});return t.removeStoredMark(a),!0},vd=(r,e={})=>({tr:t,state:n,dispatch:i})=>{let s=null,o=null;const l=sn(typeof r=="string"?r:r.name,n.schema);return l?(l==="node"&&(s=R(r,n.schema)),l==="mark"&&(o=Ie(r,n.schema)),i&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,d=a.$to.pos;n.doc.nodesBetween(c,d,(u,f)=>{s&&s===u.type&&t.setNodeMarkup(f,void 0,{...u.attrs,...e}),o&&u.marks.length&&u.marks.forEach(h=>{if(o===h.type){const p=Math.max(f,c),m=Math.min(f+u.nodeSize,d);t.addMark(p,m,o.create({...h.attrs,...e}))}})})}),!0):!1},Dd=(r,e={})=>({state:t,dispatch:n})=>{const i=R(r,t.schema);return nc(i,e)(t,n)},Id=(r,e={})=>({state:t,dispatch:n})=>{const i=R(r,t.schema);return rc(i,e)(t,n)};var Rd=Object.freeze({__proto__:null,blur:Cc,clearContent:Oc,clearNodes:Tc,command:Nc,createParagraphNear:Ec,deleteNode:Ac,deleteRange:vc,deleteSelection:Dc,enter:Ic,exitCode:Rc,extendMarkRange:Bc,first:zc,focus:Fc,forEach:Lc,insertContent:Vc,insertContentAt:Jc,joinBackward:Wc,joinForward:qc,keyboardShortcut:jc,lift:Uc,liftEmptyBlock:Gc,liftListItem:_c,newlineInCode:Yc,resetAttributes:Xc,scrollIntoView:Zc,selectAll:Qc,selectNodeBackward:ed,selectNodeForward:td,selectParentNode:nd,selectTextblockEnd:rd,selectTextblockStart:id,setContent:sd,setMark:md,setMeta:gd,setNode:yd,setNodeSelection:bd,setTextSelection:kd,sinkListItem:xd,splitBlock:Sd,splitListItem:Md,toggleList:wd,toggleMark:Cd,toggleNode:Od,toggleWrap:Td,undoInputRule:Nd,unsetAllMarks:Ed,unsetMark:Ad,updateAttributes:vd,wrapIn:Dd,wrapInList:Id});const Pd=Y.create({name:"commands",addCommands(){return{...Rd}}}),Bd=Y.create({name:"editable",addProseMirrorPlugins(){return[new ee({key:new ve("editable"),props:{editable:()=>this.editor.options.editable}})]}}),zd=Y.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:r}=this;return[new ee({key:new ve("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{r.isFocused=!0;const n=r.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1},blur:(e,t)=>{r.isFocused=!1;const n=r.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1}}}})]}}),Fd=Y.create({name:"keymap",addKeyboardShortcuts(){const r=()=>this.editor.commands.first(({commands:o})=>[()=>o.undoInputRule(),()=>o.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:d,$anchor:u}=a,{pos:f,parent:h}=u,p=C.atStart(c).from===f;return!d||!p||!h.type.isTextblock||h.textContent.length?!1:o.clearNodes()}),()=>o.deleteSelection(),()=>o.joinBackward(),()=>o.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:o})=>[()=>o.deleteSelection(),()=>o.joinForward(),()=>o.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:o})=>[()=>o.newlineInCode(),()=>o.createParagraphNear(),()=>o.liftEmptyBlock(),()=>o.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:r,"Mod-Backspace":r,"Shift-Backspace":r,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i={...n},s={...n,"Ctrl-h":r,"Alt-Backspace":r,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return fr()||Js()?s:i},addProseMirrorPlugins(){return[new ee({key:new ve("clearDocument"),appendTransaction:(r,e,t)=>{if(!(r.some(p=>p.docChanged)&&!e.doc.eq(t.doc)))return;const{empty:i,from:s,to:o}=e.selection,l=C.atStart(e.doc).from,a=C.atEnd(e.doc).to,c=s===l&&o===a,d=t.doc.textBetween(0,t.doc.content.size," "," ").length===0;if(i||!c||!d)return;const u=t.tr,f=Qt({state:t,transaction:u}),{commands:h}=new en({editor:this.editor,state:f});if(h.clearNodes(),!!u.steps.length)return u}})]}}),Ld=Y.create({name:"tabindex",addProseMirrorPlugins(){return[new ee({key:new ve("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});var Vd=Object.freeze({__proto__:null,ClipboardTextSerializer:wc,Commands:Pd,Editable:Bd,FocusEvents:zd,Keymap:Fd,Tabindex:Ld});const Hd=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 1px !important;
  height: 1px !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function $d(r,e){const t=document.querySelector("style[data-tiptap-style]");if(t!==null)return t;const n=document.createElement("style");return e&&n.setAttribute("nonce",e),n.setAttribute("data-tiptap-style",""),n.innerHTML=r,document.getElementsByTagName("head")[0].appendChild(n),n}let Jd=class extends cc{constructor(e={}){super(),this.isFocused=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}))},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=$d(Hd,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e){this.setOptions({editable:e}),this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=Fs(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],i=this.state.reconfigure({plugins:n});this.view.updateState(i)}unregisterPlugin(e){if(this.isDestroyed)return;const t=typeof e=="string"?`${e}$`:e.key,n=this.state.reconfigure({plugins:this.state.plugins.filter(i=>!i.key.startsWith(t))});this.view.updateState(n)}createExtensionManager(){const t=[...this.options.enableCoreExtensions?Object.values(Vd):[],...this.options.extensions].filter(n=>["extension","node","mark"].includes(n==null?void 0:n.type));this.extensionManager=new Xe(t,this)}createCommandManager(){this.commandManager=new en({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){const e=Ws(this.options.content,this.schema,this.options.parseOptions),t=$s(e,this.options.autofocus);this.view=new Da(this.options.element,{...this.options.editorProps,dispatchTransaction:this.dispatchTransaction.bind(this),state:Ge.create({doc:e,selection:t||void 0})});const n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews();const i=this.view.dom;i.editor=this}createNodeViews(){this.view.setProps({nodeViews:this.extensionManager.nodeViews})}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(o=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(o)});return}const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const i=e.getMeta("focus"),s=e.getMeta("blur");i&&this.emit("focus",{editor:this,event:i.event,transaction:e}),s&&this.emit("blur",{editor:this,event:s.event,transaction:e}),!(!e.docChanged||e.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return ud(this.state,e)}isActive(e,t){const n=typeof e=="string"?e:null,i=typeof e=="string"?t:e;return fd(this.state,n,i)}getJSON(){return this.state.doc.toJSON()}getHTML(){return ad(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:n={}}=e||{};return cd(this.state.doc,{blockSeparator:t,textSerializers:{...n,...Vs(this.schema)}})}get isEmpty(){return hd(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){this.emit("destroy"),this.view&&this.view.destroy(),this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}};function at(r){return new nn({find:r.find,handler:({state:e,range:t,match:n})=>{const i=w(r.getAttributes,void 0,n);if(i===!1||i===null)return null;const{tr:s}=e,o=n[n.length-1],l=n[0];let a=t.to;if(o){const c=l.search(/\S/),d=t.from+l.indexOf(o),u=d+o.length;if(Ks(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===r.type&&m!==h.mark.type)).filter(h=>h.to>d).length)return null;u<t.to&&s.delete(u,t.to),d>t.from&&s.delete(t.from+c,d),a=t.from+c+o.length,s.addMark(t.from+c,a,r.type.create(i||{})),s.removeStoredMark(r.type)}}})}function Wd(r){return new nn({find:r.find,handler:({state:e,range:t,match:n})=>{const i=w(r.getAttributes,void 0,n)||{},{tr:s}=e,o=t.from;let l=t.to;if(n[1]){const a=n[0].lastIndexOf(n[1]);let c=o+a;c>l?c=l:l=c+n[1].length;const d=n[0][n[0].length-1];s.insertText(d,o+n[0].length-1),s.replaceWith(c,l,r.type.create(i))}else n[0]&&s.replaceWith(o,l,r.type.create(i))}})}function Yn(r){return new nn({find:r.find,handler:({state:e,range:t,match:n})=>{const i=e.doc.resolve(t.from),s=w(r.getAttributes,void 0,n)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),r.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,r.type,s)}})}function pr(r){return new nn({find:r.find,handler:({state:e,range:t,match:n})=>{const i=w(r.getAttributes,void 0,n)||{},s=e.tr.delete(t.from,t.to),l=s.doc.resolve(t.from).blockRange(),a=l&&Zn(l,r.type,i);if(!a)return null;s.wrap(l,a);const c=s.doc.resolve(t.from-1).nodeBefore;c&&c.type===r.type&&ut(s.doc,t.from-1)&&(!r.joinPredicate||r.joinPredicate(n,c))&&s.join(t.from-1)}})}class Ae{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=w(x(this,"addOptions",{name:this.name}))),this.storage=w(x(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Ae(e)}configure(e={}){const t=this.extend();return t.options=rn(this.options,e),t.storage=w(x(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Ae(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=w(x(t,"addOptions",{name:t.name})),t.storage=w(x(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){const o=i.marks();if(!!!o.find(c=>(c==null?void 0:c.type.name)===t.name))return!1;const a=o.find(c=>(c==null?void 0:c.type.name)===t.name);return a&&n.removeStoredMark(a),n.insertText(" ",i.pos),e.view.dispatch(n),!0}return!1}}class U{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=w(x(this,"addOptions",{name:this.name}))),this.storage=w(x(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new U(e)}configure(e={}){const t=this.extend();return t.options=rn(this.options,e),t.storage=w(x(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new U(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=w(x(t,"addOptions",{name:t.name})),t.storage=w(x(t,"addStorage",{name:t.name,options:t.options})),t}}function ct(r){return new yc({find:r.find,handler:({state:e,range:t,match:n})=>{const i=w(r.getAttributes,void 0,n);if(i===!1||i===null)return null;const{tr:s}=e,o=n[n.length-1],l=n[0];let a=t.to;if(o){const c=l.search(/\S/),d=t.from+l.indexOf(o),u=d+o.length;if(Ks(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===r.type&&m!==h.mark.type)).filter(h=>h.to>d).length)return null;u<t.to&&s.delete(u,t.to),d>t.from&&s.delete(t.from+c,d),a=t.from+c+o.length,s.addMark(t.from+c,a,r.type.create(i||{})),s.removeStoredMark(r.type)}}})}function Si(r){return lo((e,t)=>({get(){return e(),r},set(n){r=n,requestAnimationFrame(()=>{requestAnimationFrame(()=>{t()})})}}))}class qd extends Jd{constructor(e={}){return super(e),this.vueRenderers=Xs(new Map),this.contentComponent=null,this.reactiveState=Si(this.view.state),this.reactiveExtensionStorage=Si(this.extensionStorage),this.on("transaction",()=>{this.reactiveState.value=this.view.state,this.reactiveExtensionStorage.value=this.extensionStorage}),Zs(this)}get state(){return this.reactiveState?this.reactiveState.value:this.view.state}get storage(){return this.reactiveExtensionStorage?this.reactiveExtensionStorage.value:super.storage}registerPlugin(e,t){super.registerPlugin(e,t),this.reactiveState.value=this.view.state}unregisterPlugin(e){super.unregisterPlugin(e),this.reactiveState.value=this.view.state}}const Kd=Qs({name:"EditorContent",props:{editor:{default:null,type:Object}},setup(r){const e=eo(),t=to();return no(()=>{const n=r.editor;n&&n.options.element&&e.value&&ro(()=>{if(!e.value||!n.options.element.firstChild)return;const i=io(e.value);e.value.append(...n.options.element.childNodes),n.contentComponent=t.ctx._,n.setOptions({element:i}),n.createNodeViews()})}),so(()=>{const n=r.editor;if(!n||(n.isDestroyed||n.view.setProps({nodeViews:{}}),n.contentComponent=null,!n.options.element.firstChild))return;const i=document.createElement("div");i.append(...n.options.element.childNodes),n.setOptions({element:i})}),{rootEl:e}},render(){const r=[];return this.editor&&this.editor.vueRenderers.forEach(e=>{const t=ln(oo,{to:e.teleportElement,key:e.id},ln(e.component,{ref:e.id,...e.props}));r.push(t)}),ln("div",{ref:e=>{this.rootEl=e}},...r)}}),jd=/^\s*>\s$/,Ud=U.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:r}){return["blockquote",W(this.options.HTMLAttributes,r),0]},addCommands(){return{setBlockquote:()=>({commands:r})=>r.wrapIn(this.name),toggleBlockquote:()=>({commands:r})=>r.toggleWrap(this.name),unsetBlockquote:()=>({commands:r})=>r.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[pr({find:jd,type:this.type})]}}),Gd=/(?:^|\s)((?:\*\*)((?:[^*]+))(?:\*\*))$/,_d=/(?:^|\s)((?:\*\*)((?:[^*]+))(?:\*\*))/g,Yd=/(?:^|\s)((?:__)((?:[^__]+))(?:__))$/,Xd=/(?:^|\s)((?:__)((?:[^__]+))(?:__))/g,Zd=Ae.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:r=>r.style.fontWeight!=="normal"&&null},{style:"font-weight",getAttrs:r=>/^(bold(er)?|[5-9]\d{2,})$/.test(r)&&null}]},renderHTML({HTMLAttributes:r}){return["strong",W(this.options.HTMLAttributes,r),0]},addCommands(){return{setBold:()=>({commands:r})=>r.setMark(this.name),toggleBold:()=>({commands:r})=>r.toggleMark(this.name),unsetBold:()=>({commands:r})=>r.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[at({find:Gd,type:this.type}),at({find:Yd,type:this.type})]},addPasteRules(){return[ct({find:_d,type:this.type}),ct({find:Xd,type:this.type})]}}),Qd=/^\s*([-+*])\s$/,eu=U.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{}}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:r}){return["ul",W(this.options.HTMLAttributes,r),0]},addCommands(){return{toggleBulletList:()=>({commands:r})=>r.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){return[pr({find:Qd,type:this.type})]}}),tu=/(?:^|\s)((?:`)((?:[^`]+))(?:`))$/,nu=/(?:^|\s)((?:`)((?:[^`]+))(?:`))/g,ru=Ae.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:r}){return["code",W(this.options.HTMLAttributes,r),0]},addCommands(){return{setCode:()=>({commands:r})=>r.setMark(this.name),toggleCode:()=>({commands:r})=>r.toggleMark(this.name),unsetCode:()=>({commands:r})=>r.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[at({find:tu,type:this.type})]},addPasteRules(){return[ct({find:nu,type:this.type})]}}),iu=/^```([a-z]+)?[\s\n]$/,su=/^~~~([a-z]+)?[\s\n]$/,ou=U.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:null,parseHTML:r=>{var e;const{languageClassPrefix:t}=this.options,s=[...((e=r.firstElementChild)===null||e===void 0?void 0:e.classList)||[]].filter(o=>o.startsWith(t)).map(o=>o.replace(t,""))[0];return s||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:r,HTMLAttributes:e}){return["pre",W(this.options.HTMLAttributes,e),["code",{class:r.attrs.language?this.options.languageClassPrefix+r.attrs.language:null},0]]},addCommands(){return{setCodeBlock:r=>({commands:e})=>e.setNode(this.name,r),toggleCodeBlock:r=>({commands:e})=>e.toggleNode(this.name,"paragraph",r)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:r,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!r||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:r})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=r,{selection:t}=e,{$from:n,empty:i}=t;if(!i||n.parent.type!==this.type)return!1;const s=n.parentOffset===n.parent.nodeSize-2,o=n.parent.textContent.endsWith(`

`);return!s||!o?!1:r.chain().command(({tr:l})=>(l.delete(n.pos-2,n.pos),!0)).exitCode().run()},ArrowDown:({editor:r})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=r,{selection:t,doc:n}=e,{$from:i,empty:s}=t;if(!s||i.parent.type!==this.type||!(i.parentOffset===i.parent.nodeSize-2))return!1;const l=i.after();return l===void 0||n.nodeAt(l)?!1:r.commands.exitCode()}}},addInputRules(){return[Yn({find:iu,type:this.type,getAttributes:r=>({language:r[1]})}),Yn({find:su,type:this.type,getAttributes:r=>({language:r[1]})})]},addProseMirrorPlugins(){return[new ee({key:new ve("codeBlockVSCodeHandler"),props:{handlePaste:(r,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;const t=e.clipboardData.getData("text/plain"),n=e.clipboardData.getData("vscode-editor-data"),i=n?JSON.parse(n):void 0,s=i==null?void 0:i.mode;if(!t||!s)return!1;const{tr:o}=r.state;return o.replaceSelectionWith(this.type.create({language:s})),o.setSelection(O.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.insertText(t.replace(/\r\n?/g,`
`)),o.setMeta("paste",!0),r.dispatch(o),!0}}})]}}),lu=U.create({name:"doc",topNode:!0,content:"block+"});function au(r={}){return new ee({view(e){return new cu(e,r)}})}class cu{constructor(e,t){this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=t.width||1,this.color=t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(n=>{let i=s=>{this[n](s)};return e.dom.addEventListener(n,i),{name:n,handler:i}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t;if(!e.parent.inlineContent){let o=e.nodeBefore,l=e.nodeAfter;if(o||l){let a=this.editorView.nodeDOM(this.cursorPos-(o?o.nodeSize:0)).getBoundingClientRect(),c=o?a.bottom:a.top;o&&l&&(c=(c+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),t={left:a.left,right:a.right,top:c-this.width/2,bottom:c+this.width/2}}}if(!t){let o=this.editorView.coordsAtPos(this.cursorPos);t={left:o.left-this.width/2,right:o.left+this.width/2,top:o.top,bottom:o.bottom}}let n=this.editorView.dom.offsetParent;this.element||(this.element=n.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none; background-color: "+this.color);let i,s;if(!n||n==document.body&&getComputedStyle(n).position=="static")i=-pageXOffset,s=-pageYOffset;else{let o=n.getBoundingClientRect();i=o.left-n.scrollLeft,s=o.top-n.scrollTop}this.element.style.left=t.left-i+"px",this.element.style.top=t.top-s+"px",this.element.style.width=t.right-t.left+"px",this.element.style.height=t.bottom-t.top+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=n&&n.type.spec.disableDropCursor,s=typeof i=="function"?i(this.editorView,t):i;if(t&&!s){let o=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice&&(o=Ki(this.editorView.state.doc,o,this.editorView.dragging.slice),o==null))return this.setCursor(null);this.setCursor(o),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){(e.target==this.editorView.dom||!this.editorView.dom.contains(e.relatedTarget))&&this.setCursor(null)}}const du=Y.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[au(this.options)]}});class A extends C{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return A.valid(n)?new A(n):C.near(n)}content(){return k.empty}eq(e){return e instanceof A&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new A(e.resolve(t.pos))}getBookmark(){return new mr(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!uu(e)||!fu(e))return!1;let n=t.type.spec.allowGapCursor;if(n!=null)return n;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,n=!1){e:for(;;){if(!n&&A.valid(e))return e;let i=e.pos,s=null;for(let o=e.depth;;o--){let l=e.node(o);if(t>0?e.indexAfter(o)<l.childCount:e.index(o)>0){s=l.child(t>0?e.indexAfter(o):e.index(o)-1);break}else if(o==0)return null;i+=t;let a=e.doc.resolve(i);if(A.valid(a))return a}for(;;){let o=t>0?s.firstChild:s.lastChild;if(!o){if(s.isAtom&&!s.isText&&!M.isSelectable(s)){e=e.doc.resolve(i+s.nodeSize*t),n=!1;continue e}break}s=o,i+=t;let l=e.doc.resolve(i);if(A.valid(l))return l}return null}}}A.prototype.visible=!1;A.findFrom=A.findGapCursorFrom;C.jsonID("gapcursor",A);class mr{constructor(e){this.pos=e}map(e){return new mr(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return A.valid(t)?new A(t):C.near(t)}}function uu(r){for(let e=r.depth;e>=0;e--){let t=r.index(e),n=r.node(e);if(t==0){if(n.type.spec.isolating)return!0;continue}for(let i=n.child(t-1);;i=i.lastChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function fu(r){for(let e=r.depth;e>=0;e--){let t=r.indexAfter(e),n=r.node(e);if(t==n.childCount){if(n.type.spec.isolating)return!0;continue}for(let i=n.child(t);;i=i.firstChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function hu(){return new ee({props:{decorations:yu,createSelectionBetween(r,e,t){return e.pos==t.pos&&A.valid(t)?new A(t):null},handleClick:mu,handleKeyDown:pu,handleDOMEvents:{beforeinput:gu}}})}const pu=vs({ArrowLeft:At("horiz",-1),ArrowRight:At("horiz",1),ArrowUp:At("vert",-1),ArrowDown:At("vert",1)});function At(r,e){const t=r=="vert"?e>0?"down":"up":e>0?"right":"left";return function(n,i,s){let o=n.selection,l=e>0?o.$to:o.$from,a=o.empty;if(o instanceof O){if(!s.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=n.doc.resolve(e>0?l.after():l.before())}let c=A.findGapCursorFrom(l,e,a);return c?(i&&i(n.tr.setSelection(new A(c))),!0):!1}}function mu(r,e,t){if(!r||!r.editable)return!1;let n=r.state.doc.resolve(e);if(!A.valid(n))return!1;let i=r.posAtCoords({left:t.clientX,top:t.clientY});return i&&i.inside>-1&&M.isSelectable(r.state.doc.nodeAt(i.inside))?!1:(r.dispatch(r.state.tr.setSelection(new A(n))),!0)}function gu(r,e){if(e.inputType!="insertCompositionText"||!(r.state.selection instanceof A))return!1;let{$from:t}=r.state.selection,n=t.parent.contentMatchAt(t.index()).findWrapping(r.state.schema.nodes.text);if(!n)return!1;let i=y.empty;for(let o=n.length-1;o>=0;o--)i=y.from(n[o].createAndFill(null,i));let s=r.state.tr.replace(t.pos,t.pos,new k(i,0,0));return s.setSelection(O.near(s.doc.resolve(t.pos+1))),r.dispatch(s),!1}function yu(r){if(!(r.selection instanceof A))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",v.create(r.doc,[Q.widget(r.selection.head,e,{key:"gapcursor"})])}const bu=Y.create({name:"gapCursor",addProseMirrorPlugins(){return[hu()]},extendNodeSchema(r){var e;const t={name:r.name,options:r.options,storage:r.storage};return{allowGapCursor:(e=w(x(r,"allowGapCursor",t)))!==null&&e!==void 0?e:null}}}),ku=U.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:r}){return["br",W(this.options.HTMLAttributes,r)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:r,chain:e,state:t,editor:n})=>r.first([()=>r.exitCode(),()=>r.command(()=>{const{selection:i,storedMarks:s}=t;if(i.$from.parent.type.spec.isolating)return!1;const{keepMarks:o}=this.options,{splittableMarks:l}=n.extensionManager,a=s||i.$to.parentOffset&&i.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:d})=>{if(d&&a&&o){const u=a.filter(f=>l.includes(f.type.name));c.ensureMarks(u)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),xu=U.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(r=>({tag:`h${r}`,attrs:{level:r}}))},renderHTML({node:r,HTMLAttributes:e}){return[`h${this.options.levels.includes(r.attrs.level)?r.attrs.level:this.options.levels[0]}`,W(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:r=>({commands:e})=>this.options.levels.includes(r.level)?e.setNode(this.name,r):!1,toggleHeading:r=>({commands:e})=>this.options.levels.includes(r.level)?e.toggleNode(this.name,"paragraph",r):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((r,e)=>({...r,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(r=>Yn({find:new RegExp(`^(#{1,${r}})\\s$`),type:this.type,getAttributes:{level:r}}))}});var jt=200,F=function(){};F.prototype.append=function(e){return e.length?(e=F.from(e),!this.length&&e||e.length<jt&&this.leafAppend(e)||this.length<jt&&e.leafPrepend(this)||this.appendInner(e)):this};F.prototype.prepend=function(e){return e.length?F.from(e).append(this):this};F.prototype.appendInner=function(e){return new Su(this,e)};F.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?F.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};F.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};F.prototype.forEach=function(e,t,n){t===void 0&&(t=0),n===void 0&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)};F.prototype.map=function(e,t,n){t===void 0&&(t=0),n===void 0&&(n=this.length);var i=[];return this.forEach(function(s,o){return i.push(e(s,o))},t,n),i};F.from=function(e){return e instanceof F?e:e&&e.length?new js(e):F.empty};var js=function(r){function e(n){r.call(this),this.values=n}r&&(e.__proto__=r),e.prototype=Object.create(r&&r.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(i,s){return i==0&&s==this.length?this:new e(this.values.slice(i,s))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(i,s,o,l){for(var a=s;a<o;a++)if(i(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(i,s,o,l){for(var a=s-1;a>=o;a--)if(i(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(i){if(this.length+i.length<=jt)return new e(this.values.concat(i.flatten()))},e.prototype.leafPrepend=function(i){if(this.length+i.length<=jt)return new e(i.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(F);F.empty=new js([]);var Su=function(r){function e(t,n){r.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return r&&(e.__proto__=r),e.prototype=Object.create(r&&r.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(n){return n<this.left.length?this.left.get(n):this.right.get(n-this.left.length)},e.prototype.forEachInner=function(n,i,s,o){var l=this.left.length;if(i<l&&this.left.forEachInner(n,i,Math.min(s,l),o)===!1||s>l&&this.right.forEachInner(n,Math.max(i-l,0),Math.min(this.length,s)-l,o+l)===!1)return!1},e.prototype.forEachInvertedInner=function(n,i,s,o){var l=this.left.length;if(i>l&&this.right.forEachInvertedInner(n,i-l,Math.max(s,l)-l,o+l)===!1||s<l&&this.left.forEachInvertedInner(n,Math.min(i,l),s,o)===!1)return!1},e.prototype.sliceInner=function(n,i){if(n==0&&i==this.length)return this;var s=this.left.length;return i<=s?this.left.slice(n,i):n>=s?this.right.slice(n-s,i-s):this.left.slice(n,s).append(this.right.slice(0,i-s))},e.prototype.leafAppend=function(n){var i=this.right.leafAppend(n);if(i)return new e(this.left,i)},e.prototype.leafPrepend=function(n){var i=this.left.leafPrepend(n);if(i)return new e(i,this.right)},e.prototype.appendInner=function(n){return this.left.depth>=Math.max(this.right.depth,n.depth)+1?new e(this.left,new e(this.right,n)):new e(this,n)},e}(F),Us=F;const Mu=500;class ne{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let n=this.items.length;for(;;n--)if(this.items.get(n-1).selection){--n;break}let i,s;t&&(i=this.remapping(n,this.items.length),s=i.maps.length);let o=e.tr,l,a,c=[],d=[];return this.items.forEach((u,f)=>{if(!u.step){i||(i=this.remapping(n,f+1),s=i.maps.length),s--,d.push(u);return}if(i){d.push(new le(u.map));let h=u.step.map(i.slice(s)),p;h&&o.maybeStep(h).doc&&(p=o.mapping.maps[o.mapping.maps.length-1],c.push(new le(p,void 0,void 0,c.length+d.length))),s--,p&&i.appendMap(p,s)}else o.maybeStep(u.step);if(u.selection)return l=i?u.selection.map(i.slice(s)):u.selection,a=new ne(this.items.slice(0,n).append(d.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:o,selection:l}}addTransform(e,t,n,i){let s=[],o=this.eventCount,l=this.items,a=!i&&l.length?l.get(l.length-1):null;for(let d=0;d<e.steps.length;d++){let u=e.steps[d].invert(e.docs[d]),f=new le(e.mapping.maps[d],u,t),h;(h=a&&a.merge(f))&&(f=h,d?s.pop():l=l.slice(0,l.length-1)),s.push(f),t&&(o++,t=void 0),i||(a=f)}let c=o-n.depth;return c>Cu&&(l=wu(l,c),o-=c),new ne(l.append(s),o)}remapping(e,t){let n=new Ze;return this.items.forEach((i,s)=>{let o=i.mirrorOffset!=null&&s-i.mirrorOffset>=e?n.maps.length-i.mirrorOffset:void 0;n.appendMap(i.map,o)},e,t),n}addMaps(e){return this.eventCount==0?this:new ne(this.items.append(e.map(t=>new le(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],i=Math.max(0,this.items.length-t),s=e.mapping,o=e.steps.length,l=this.eventCount;this.items.forEach(f=>{f.selection&&l--},i);let a=t;this.items.forEach(f=>{let h=s.getMirror(--a);if(h==null)return;o=Math.min(o,h);let p=s.maps[h];if(f.step){let m=e.steps[h].invert(e.docs[h]),g=f.selection&&f.selection.map(s.slice(a+1,h));g&&l++,n.push(new le(p,m,g))}else n.push(new le(p))},i);let c=[];for(let f=t;f<o;f++)c.push(new le(s.maps[f]));let d=this.items.slice(0,i).append(c).append(n),u=new ne(d,l);return u.emptyItemCount()>Mu&&(u=u.compress(this.items.length-n.length)),u}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,i=[],s=0;return this.items.forEach((o,l)=>{if(l>=e)i.push(o),o.selection&&s++;else if(o.step){let a=o.step.map(t.slice(n)),c=a&&a.getMap();if(n--,c&&t.appendMap(c,n),a){let d=o.selection&&o.selection.map(t.slice(n));d&&s++;let u=new le(c.invert(),a,d),f,h=i.length-1;(f=i.length&&i[h].merge(u))?i[h]=f:i.push(u)}}else o.map&&n--},this.items.length,0),new ne(Us.from(i.reverse()),s)}}ne.empty=new ne(Us.empty,0);function wu(r,e){let t;return r.forEach((n,i)=>{if(n.selection&&e--==0)return t=i,!1}),r.slice(t)}class le{constructor(e,t,n,i){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new le(t.getMap().invert(),t,this.selection)}}}class ke{constructor(e,t,n,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=i}}const Cu=20;function Ou(r,e,t,n){let i=t.getMeta(Te),s;if(i)return i.historyState;t.getMeta(Nu)&&(r=new ke(r.done,r.undone,null,0));let o=t.getMeta("appendedTransaction");if(t.steps.length==0)return r;if(o&&o.getMeta(Te))return o.getMeta(Te).redo?new ke(r.done.addTransform(t,void 0,n,It(e)),r.undone,Mi(t.mapping.maps[t.steps.length-1]),r.prevTime):new ke(r.done,r.undone.addTransform(t,void 0,n,It(e)),null,r.prevTime);if(t.getMeta("addToHistory")!==!1&&!(o&&o.getMeta("addToHistory")===!1)){let l=r.prevTime==0||!o&&(r.prevTime<(t.time||0)-n.newGroupDelay||!Tu(t,r.prevRanges)),a=o?An(r.prevRanges,t.mapping):Mi(t.mapping.maps[t.steps.length-1]);return new ke(r.done.addTransform(t,l?e.selection.getBookmark():void 0,n,It(e)),ne.empty,a,t.time)}else return(s=t.getMeta("rebased"))?new ke(r.done.rebased(t,s),r.undone.rebased(t,s),An(r.prevRanges,t.mapping),r.prevTime):new ke(r.done.addMaps(t.mapping.maps),r.undone.addMaps(t.mapping.maps),An(r.prevRanges,t.mapping),r.prevTime)}function Tu(r,e){if(!e)return!1;if(!r.docChanged)return!0;let t=!1;return r.mapping.maps[0].forEach((n,i)=>{for(let s=0;s<e.length;s+=2)n<=e[s+1]&&i>=e[s]&&(t=!0)}),t}function Mi(r){let e=[];return r.forEach((t,n,i,s)=>e.push(i,s)),e}function An(r,e){if(!r)return null;let t=[];for(let n=0;n<r.length;n+=2){let i=e.map(r[n],1),s=e.map(r[n+1],-1);i<=s&&t.push(i,s)}return t}function Gs(r,e,t,n){let i=It(e),s=Te.get(e).spec.config,o=(n?r.undone:r.done).popEvent(e,i);if(!o)return;let l=o.selection.resolve(o.transform.doc),a=(n?r.done:r.undone).addTransform(o.transform,e.selection.getBookmark(),s,i),c=new ke(n?a:o.remaining,n?o.remaining:a,null,0);t(o.transform.setSelection(l).setMeta(Te,{redo:n,historyState:c}).scrollIntoView())}let vn=!1,wi=null;function It(r){let e=r.plugins;if(wi!=e){vn=!1,wi=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){vn=!0;break}}return vn}const Te=new ve("history"),Nu=new ve("closeHistory");function Eu(r={}){return r={depth:r.depth||100,newGroupDelay:r.newGroupDelay||500},new ee({key:Te,state:{init(){return new ke(ne.empty,ne.empty,null,0)},apply(e,t,n){return Ou(t,n,e,r)}},config:r,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,i=n=="historyUndo"?_s:n=="historyRedo"?Ys:null;return i?(t.preventDefault(),i(e.state,e.dispatch)):!1}}}})}const _s=(r,e)=>{let t=Te.getState(r);return!t||t.done.eventCount==0?!1:(e&&Gs(t,r,e,!1),!0)},Ys=(r,e)=>{let t=Te.getState(r);return!t||t.undone.eventCount==0?!1:(e&&Gs(t,r,e,!0),!0)},Au=Y.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:r,dispatch:e})=>_s(r,e),redo:()=>({state:r,dispatch:e})=>Ys(r,e)}},addProseMirrorPlugins(){return[Eu(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Mod-y":()=>this.editor.commands.redo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),vu=U.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:r}){return["hr",W(this.options.HTMLAttributes,r)]},addCommands(){return{setHorizontalRule:()=>({chain:r})=>r().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{var n;if(t){const{$to:i}=e.selection,s=i.end();if(i.nodeAfter)e.setSelection(O.create(e.doc,i.pos));else{const o=(n=i.parent.type.contentMatch.defaultType)===null||n===void 0?void 0:n.create();o&&(e.insert(s,o),e.setSelection(O.create(e.doc,s)))}e.scrollIntoView()}return!0}).run()}},addInputRules(){return[Wd({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Du=/(?:^|\s)((?:\*)((?:[^*]+))(?:\*))$/,Iu=/(?:^|\s)((?:\*)((?:[^*]+))(?:\*))/g,Ru=/(?:^|\s)((?:_)((?:[^_]+))(?:_))$/,Pu=/(?:^|\s)((?:_)((?:[^_]+))(?:_))/g,Bu=Ae.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:r=>r.style.fontStyle!=="normal"&&null},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:r}){return["em",W(this.options.HTMLAttributes,r),0]},addCommands(){return{setItalic:()=>({commands:r})=>r.setMark(this.name),toggleItalic:()=>({commands:r})=>r.toggleMark(this.name),unsetItalic:()=>({commands:r})=>r.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[at({find:Du,type:this.type}),at({find:Ru,type:this.type})]},addPasteRules(){return[ct({find:Iu,type:this.type}),ct({find:Pu,type:this.type})]}}),zu=U.create({name:"listItem",addOptions(){return{HTMLAttributes:{}}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:r}){return["li",W(this.options.HTMLAttributes,r),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Fu=/^(\d+)\.\s$/,Lu=U.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{}}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:r=>r.hasAttribute("start")?parseInt(r.getAttribute("start")||"",10):1}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:r}){const{start:e,...t}=r;return e===1?["ol",W(this.options.HTMLAttributes,t),0]:["ol",W(this.options.HTMLAttributes,r),0]},addCommands(){return{toggleOrderedList:()=>({commands:r})=>r.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){return[pr({find:Fu,type:this.type,getAttributes:r=>({start:+r[1]}),joinPredicate:(r,e)=>e.childCount+e.attrs.start===+r[1]})]}}),Vu=U.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:r}){return["p",W(this.options.HTMLAttributes,r),0]},addCommands(){return{setParagraph:()=>({commands:r})=>r.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Hu=/(?:^|\s)((?:~~)((?:[^~]+))(?:~~))$/,$u=/(?:^|\s)((?:~~)((?:[^~]+))(?:~~))/g,Ju=Ae.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:r=>r.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:r}){return["s",W(this.options.HTMLAttributes,r),0]},addCommands(){return{setStrike:()=>({commands:r})=>r.setMark(this.name),toggleStrike:()=>({commands:r})=>r.toggleMark(this.name),unsetStrike:()=>({commands:r})=>r.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-x":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[at({find:Hu,type:this.type})]},addPasteRules(){return[ct({find:$u,type:this.type})]}}),Wu=U.create({name:"text",group:"inline"}),qu=Y.create({name:"starterKit",addExtensions(){var r,e,t,n,i,s,o,l,a,c,d,u,f,h,p,m,g,b;const S=[];return this.options.blockquote!==!1&&S.push(Ud.configure((r=this.options)===null||r===void 0?void 0:r.blockquote)),this.options.bold!==!1&&S.push(Zd.configure((e=this.options)===null||e===void 0?void 0:e.bold)),this.options.bulletList!==!1&&S.push(eu.configure((t=this.options)===null||t===void 0?void 0:t.bulletList)),this.options.code!==!1&&S.push(ru.configure((n=this.options)===null||n===void 0?void 0:n.code)),this.options.codeBlock!==!1&&S.push(ou.configure((i=this.options)===null||i===void 0?void 0:i.codeBlock)),this.options.document!==!1&&S.push(lu.configure((s=this.options)===null||s===void 0?void 0:s.document)),this.options.dropcursor!==!1&&S.push(du.configure((o=this.options)===null||o===void 0?void 0:o.dropcursor)),this.options.gapcursor!==!1&&S.push(bu.configure((l=this.options)===null||l===void 0?void 0:l.gapcursor)),this.options.hardBreak!==!1&&S.push(ku.configure((a=this.options)===null||a===void 0?void 0:a.hardBreak)),this.options.heading!==!1&&S.push(xu.configure((c=this.options)===null||c===void 0?void 0:c.heading)),this.options.history!==!1&&S.push(Au.configure((d=this.options)===null||d===void 0?void 0:d.history)),this.options.horizontalRule!==!1&&S.push(vu.configure((u=this.options)===null||u===void 0?void 0:u.horizontalRule)),this.options.italic!==!1&&S.push(Bu.configure((f=this.options)===null||f===void 0?void 0:f.italic)),this.options.listItem!==!1&&S.push(zu.configure((h=this.options)===null||h===void 0?void 0:h.listItem)),this.options.orderedList!==!1&&S.push(Lu.configure((p=this.options)===null||p===void 0?void 0:p.orderedList)),this.options.paragraph!==!1&&S.push(Vu.configure((m=this.options)===null||m===void 0?void 0:m.paragraph)),this.options.strike!==!1&&S.push(Ju.configure((g=this.options)===null||g===void 0?void 0:g.strike)),this.options.text!==!1&&S.push(Wu.configure((b=this.options)===null||b===void 0?void 0:b.text)),S}}),Ku={components:{EditorContent:Kd},props:{modelValue:{type:String,default:""}},data(){return{editor:null}},watch:{modelValue(r){this.editor.getHTML()!==r&&this.editor.commands.setContent(r,!1)}},mounted(){this.editor=new qd({extensions:[qu],editorProps:{attributes:{class:"p-2 bg-white shadow-sm block w-full sm:text-sm border-gray-300 rounded-md"}},content:this.modelValue,onUpdate:()=>{this.$emit("update:modelValue",this.editor.getHTML())}})},beforeUnmount(){this.editor.destroy()}},ju={key:0};function Uu(r,e,t,n,i,s){const o=co("editor-content");return gr(),yr(ho,null,[i.editor?(gr(),yr("div",ju,[oe("button",{onClick:e[0]||(e[0]=l=>i.editor.chain().focus().toggleHeading({level:1}).run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("heading",{level:1})})}," h1 ",2),oe("button",{onClick:e[1]||(e[1]=l=>i.editor.chain().focus().toggleHeading({level:2}).run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("heading",{level:2})})}," h2 ",2),oe("button",{onClick:e[2]||(e[2]=l=>i.editor.chain().focus().toggleHeading({level:3}).run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("heading",{level:3})})}," h3 ",2),oe("button",{onClick:e[3]||(e[3]=l=>i.editor.chain().focus().toggleHeading({level:4}).run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("heading",{level:4})})}," h4 ",2),oe("button",{onClick:e[4]||(e[4]=l=>i.editor.chain().focus().toggleHeading({level:5}).run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("heading",{level:5})})}," h5 ",2),oe("button",{onClick:e[5]||(e[5]=l=>i.editor.chain().focus().toggleHeading({level:6}).run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("heading",{level:6})})}," h6 ",2),oe("button",{onClick:e[6]||(e[6]=l=>i.editor.chain().focus().setParagraph().run()),class:he({"bg-white p-1 mb-1 mr-4 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("paragraph")})}," p ",2),oe("button",{onClick:e[7]||(e[7]=l=>i.editor.chain().focus().toggleBold().run()),class:he({"bg-white p-1 mb-1 mr-1 border-1 text-center w-8":!0,"font-bold":i.editor.isActive("bold")})}," B ",2),oe("button",{onClick:e[8]||(e[8]=l=>i.editor.chain().focus().toggleItalic().run()),class:he({"bg-white p-1 mb-1 mr-4 border-1 text-center w-8 italic":!0,"font-bold":i.editor.isActive("italic")})}," I ",2),oe("button",{onClick:e[9]||(e[9]=l=>i.editor.chain().focus().setHardBreak().run()),class:"bg-white p-1 mb-1 mr-1 border-1 text-center w-8"}," ¶ ")])):uo("",!0),fo(o,{editor:i.editor},null,8,["editor"])],64)}const Yu=ao(Ku,[["render",Uu]]);export{Yu as S};
