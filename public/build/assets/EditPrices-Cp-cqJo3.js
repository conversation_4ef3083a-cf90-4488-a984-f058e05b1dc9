import{_ as D,S as x,c as L,w as f,r as n,b as o,d as u,e as d,t as c,l as h,v as b,h as a,i as g,F as S,j as m,g as X,k as N}from"./app-Cm2beRkj.js";import{S as C}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import k from"./ProductEditorsDropdown-PputzcmH.js";import{S as I}from"./SBXInput-C8dZEgZe.js";import{S as j}from"./SBXSelect-RMXlX9En.js";import{S as F}from"./SBXEditor-C2jSibHW.js";import{S as V}from"./SBXNotification-Bm69sG5a.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const O={components:{SBXDefaultPageLayout:C,ProductEditorsDropdown:k,SBXInput:I,SBXSelect:j,SBXEditor:F,SBXButton:x,SBXNotification:V},props:{product:Object,productUnits:Object,vatRates:Object,priceLists:Object,defaultPriceListID:Number,currencies:Object,defaultCurrencyID:Number,prices:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,this.form.price=this.selectedPrice,console.log("Product",this.product),console.log("prices",this.prices)},updated(){this.$page.props.page_info.title_label=this.pageTitle,this.form.price=this.selectedPrice},remember:"form",data(){return{form:this.$inertia.form({product_unit_id:this.product.data.product_unit_id,vat_rate_id:this.product.data.vat_rate_id,selected_price_list_id:this.defaultPriceListID,selected_currency_id:this.defaultCurrencyID,price:"0"}),showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`},selectedPrice(){for(var t=0,e=0;e<this.prices.data.length;e++){let p=this.prices.data[e];p.price_list_id==this.form.selected_price_list_id&&p.currency_id==this.form.selected_currency_id&&(t=p.price)}return t.toLocaleString("se-SV",{minimumFractionDigits:2,maximumFractionDigits:2,useGrouping:!1})},includedProductsPrice(){for(var t=0,e=0;e<this.prices.data.length;e++){let s=this.prices.data[e];s.price_list_id==this.form.selected_price_list_id&&s.currency_id==this.form.selected_currency_id&&(t=s.included_products_price)}return t>0?t.toLocaleString("se-SV",{minimumFractionDigits:2,maximumFractionDigits:2,useGrouping:!1}):null},selectedPriceList(){for(var t=null,e=0;e<this.priceLists.data.length;e++){let s=this.priceLists.data[e];s.id==this.form.selected_price_list_id&&(t=s)}return t}},methods:{update(){this.form.processing||this.form.put(this.route("products.prices.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:t=>{this.showSaveSuccess=!0}})},saveNotificationCancelled(){this.showSaveSuccess=!1}}},U={class:"grid grid-cols-4 gap-4"},E={class:"mt-16 text-base font-medium"},T={class:"grid grid-cols-4 gap-4"},G=["value"],M=["value"],R={class:"grid grid-cols-4 gap-4"},A={key:0,class:"text-sm font-medium"},q={key:1,class:"text-sm font-medium"},z={key:2,class:"mt-2 text-sm font-medium"};function H(t,e,s,p,i,l){const v=n("ProductEditorsDropdown"),_=n("SBXSelect"),P=n("SBXInput"),w=n("SBXButton"),B=n("SBXNotification"),y=n("SBXDefaultPageLayout");return o(),L(y,null,{default:f(()=>[u(v,{pages:s.pageComponents,currentPage:"prices",productID:s.product.data.id},null,8,["pages","productID"]),d("form",{onSubmit:e[5]||(e[5]=N((...r)=>l.update&&l.update(...r),["prevent"]))},[d("div",U,[u(_,{model:i.form.product_unit_id,"onUpdate:model":e[0]||(e[0]=r=>i.form.product_unit_id=r),items:s.productUnits.data,labelFieldName:"unit",label:t.$t("sbxwebshop.products.unit_label")},null,8,["model","items","label"]),u(_,{model:i.form.vat_rate_id,"onUpdate:model":e[1]||(e[1]=r=>i.form.vat_rate_id=r),items:s.vatRates.data,labelFieldName:"name",label:t.$t("sbxwebshop.products.vat_rate_label")},null,8,["model","items","label"])]),d("h3",E,c(t.$t("sbxwebshop.products.price_list_currency_price_prompt")),1),d("div",T,[h(d("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>i.form.selected_price_list_id=r),class:"mt-1 block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(o(!0),a(S,null,g(s.priceLists.data,r=>(o(),a("option",{value:r.id},c(r.name),9,G))),256))],512),[[b,i.form.selected_price_list_id]]),h(d("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>i.form.selected_currency_id=r),class:"mt-1 block w-24 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(o(!0),a(S,null,g(s.currencies.data,r=>(o(),a("option",{value:r.id},c(r.name),9,M))),256))],512),[[b,i.form.selected_currency_id]])]),d("div",R,[u(P,{model:i.form.price,"onUpdate:model":e[4]||(e[4]=r=>i.form.price=r),label:t.$t("sbxwebshop.products.price_label"),error:s.errors.price},null,8,["model","label","error"])]),l.selectedPriceList.prices_include_vat?m("",!0):(o(),a("p",A,c(t.$t("sbxwebshop.products.price_excluding_vat_prompt")),1)),l.selectedPriceList.prices_include_vat?(o(),a("p",q,c(t.$t("sbxwebshop.products.price_including_vat_prompt")),1)):m("",!0),l.includedProductsPrice?(o(),a("p",z,c(t.$t("sbxwebshop.products.price_included_products_prompt"))+" "+c(l.includedProductsPrice),1)):m("",!0),u(w,{class:"mt-4"},{default:f(()=>[X(c(t.$t("sbxwebshop.global.save")),1)]),_:1})],32),u(B,{show:i.showSaveSuccess,onNotificationCancelled:l.saveNotificationCancelled},null,8,["show","onNotificationCancelled"])]),_:1})}const se=D(O,[["render",H]]);export{se as default};
