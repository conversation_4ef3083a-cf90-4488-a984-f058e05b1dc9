import{_ as m,b as c,h as d,l as B,v,e as f,F as w,i as P,t as g,S as X,c as D,w as u,r as i,d as l,g as N,k as x}from"./app-Cm2beRkj.js";import{S as C}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as k}from"./SBXInput-C8dZEgZe.js";import{S as y}from"./SBXSelect-RMXlX9En.js";import{S as I}from"./SBXEditor-C2jSibHW.js";import{S as $}from"./SBXTextArea-D09nQvWc.js";import{S as E}from"./SBXNotification-Bm69sG5a.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const j={props:["pages","currentPage","productID"],data(){return{selectedPage:this.currentPage}},computed:{selectedPageObject(){for(var t=null,e=0;e<this.pages.length;e++){let o=this.pages[e];if(o.value==this.selectedPage){t=o;break}}return t}},methods:{switchPage(){let t=this.route(this.selectedPageObject.route,[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.productID]);this.$inertia.get(t)}}},T={class:"flex justify-end"},L=["value"];function O(t,e,o,h,s,a){return c(),d("div",T,[B(f("select",{onChange:e[0]||(e[0]=(...r)=>a.switchPage&&a.switchPage(...r)),"onUpdate:modelValue":e[1]||(e[1]=r=>s.selectedPage=r),class:"mt-1 block w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(c(!0),d(w,null,P(o.pages,r=>(c(),d("option",{value:r.value},g(t.$t(r.label)),9,L))),256))],544),[[v,s.selectedPage]])])}const V=m(j,[["render",O]]),U={components:{SBXDefaultPageLayout:C,ProductEditorsDropdown:V,SBXInput:k,SBXSelect:y,SBXEditor:I,SBXButton:X,SBXTextArea:$,SBXNotification:E},props:{product:Object,pageComponents:Array,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.product.data.name,short_description:this.product.data.short_description}),showSaveSuccess:!1}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("translation_products.basic_information.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.product.data.id]),{preserveScroll:!0,onSuccess:t=>{this.showSaveSuccess=!0}})},saveNotificationCancelled(){this.showSaveSuccess=!1}}};function A(t,e,o,h,s,a){const r=i("ProductEditorsDropdown"),p=i("SBXInput"),_=i("SBXButton"),S=i("SBXNotification"),b=i("SBXDefaultPageLayout");return c(),D(b,null,{default:u(()=>[l(r,{pages:o.pageComponents,currentPage:"basic_information",productID:o.product.data.id},null,8,["pages","productID"]),f("form",{onSubmit:e[2]||(e[2]=x((...n)=>a.update&&a.update(...n),["prevent"]))},[l(p,{model:s.form.name,"onUpdate:model":e[0]||(e[0]=n=>s.form.name=n),label:t.$t("sbxwebshop.products.name_label"),error:o.errors.name},null,8,["model","label","error"]),l(p,{model:s.form.short_description,"onUpdate:model":e[1]||(e[1]=n=>s.form.short_description=n),label:t.$t("translations.translation_products.short_description_label"),error:o.errors.short_description},null,8,["model","label","error"]),l(_,{class:"mt-4"},{default:u(()=>[N(g(t.$t("sbxwebshop.global.save")),1)]),_:1})],32),l(S,{show:s.showSaveSuccess,onNotificationCancelled:a.saveNotificationCancelled},null,8,["show","onNotificationCancelled"])]),_:1})}const W=m(U,[["render",A]]);export{W as default};
