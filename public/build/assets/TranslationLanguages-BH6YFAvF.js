import{_ as m,h as i,d as l,w as f,e as o,F as c,r,b as g,l as _,v as b,i as v,t as B}from"./app-Cm2beRkj.js";import{S as w}from"./SBXDataTable-C66uxM7K.js";import{S as x}from"./SBXFilterBar-w4dDtxld.js";import{S}from"./SBXGenericNotification-BrDLezmQ.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";import"./XCircleIcon-Bi2eGVmc.js";import"./ArrowRightOnRectangleIcon-UpnkB4Zc.js";const D={components:{SBXDataTable:w,SBXFilterBar:x,SBXGenericNotification:S},props:{translationLanguages:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("translations.translation_languages.title"),this.$page.props.flash.authorization!=null&&(this.showAuthorizationNotification=!0)},updated(){this.$page.props.page_info.title_label=this.$t("translations.translation_languages.title")},data(){return{columns:[{key:"name",label:this.$t("translations.translation_languages.name_label")}],translationLanguageFilters:{search:this.filters.search,active:this.filters.active},activeOptions:[{value:!0,text:this.$t("translations.global.active")},{value:!1,text:this.$t("translations.global.inactive")}],showAuthorizationNotification:!1}},computed:{authorizationMessage(){return this.$page.props.flash.authorization==null?"":this.$page.props.flash.authorization}},methods:{changeFilters(){this.$inertia.get(this.route("translation_languages",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]),this.translationLanguageFilters,{preserveState:!1,replace:!0})},searchValueChanged(t){this.translationLanguageFilters.search=t},authorizationNotificationCancelled(){this.showAuthorizationNotification=!1}}},F={class:"flex justify-end"},C=["value"],N={class:"flex flex-col"},T={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},z={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},L={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function $(t,n,u,y,e,s){const d=r("SBXFilterBar"),h=r("SBXDataTable"),p=r("SBXGenericNotification");return g(),i(c,null,[l(d,{onSearchValueChanged:s.searchValueChanged,filters:e.translationLanguageFilters,searchRoute:"translation_languages",placeholder:t.$t("translations.global.search")},{filterArea:f(()=>[o("div",F,[_(o("select",{onChange:n[0]||(n[0]=(...a)=>s.changeFilters&&s.changeFilters(...a)),"onUpdate:modelValue":n[1]||(n[1]=a=>e.translationLanguageFilters.active=a),class:"mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},[(g(!0),i(c,null,v(e.activeOptions,a=>(g(),i("option",{value:a.value},B(a.text),9,C))),256))],544),[[b,e.translationLanguageFilters.active]])])]),_:1},8,["onSearchValueChanged","filters","placeholder"]),o("div",N,[o("div",T,[o("div",z,[o("div",L,[l(h,{columns:e.columns,items:u.translationLanguages.data,showAddButton:!0,addRoute:"translation_languages.create",addButtonText:t.$t("translations.translation_languages.add_button"),showEditButton:!0,editRoute:"translation_languages.edit",showDeleteButton:!0,deleteRoute:"translation_languages.destroy",deleteDialogTitle:t.$t("translations.translation_languages.delete_dialog_title"),deleteDialogMessage:t.$t("translations.translation_languages.delete_dialog_message"),deleteDialogOKText:t.$t("translations.translation_languages.delete_dialog_ok_button"),deleteDialogCancelText:t.$t("translations.global.cancel"),paginator:u.translationLanguages.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])]),l(p,{show:e.showAuthorizationNotification,variant:"warning",message:s.authorizationMessage,onNotificationCancelled:s.authorizationNotificationCancelled},null,8,["show","message","onNotificationCancelled"])],64)}const U=m(D,[["render",$]]);export{U as default};
