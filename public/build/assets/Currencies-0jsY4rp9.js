import{_ as n,h as c,d as o,e as t,F as d,r as l,b as u}from"./app-Cm2beRkj.js";import{S as p}from"./SBXDataTable-C66uxM7K.js";import{S as m}from"./SBXFilterBar-w4dDtxld.js";import"./CheckCircleIcon-SzRA1Ei3.js";import"./TrashIcon-fhKAjDA0.js";import"./sweetalert2.all-i0W-sCgv.js";import"./SBXTable-CaSPMjSb.js";import"./debounce-Bpn6Ai4k.js";const b={components:{SBXDataTable:p,SBXFilterBar:m},props:{currencies:Object,filters:Object},mounted(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.currencies.title")},updated(){this.$page.props.page_info.title_label=this.$t("sbxwebshop.currencies.title")},data(){return{columns:[{key:"name",label:this.$t("sbxwebshop.currencies.name_label")}]}},methods:{}},h={class:"flex flex-col"},g={class:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},_={class:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"},f={class:"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"};function x(e,w,s,B,r,D){const a=l("SBXFilterBar"),i=l("SBXDataTable");return u(),c(d,null,[o(a,{filters:s.filters,searchRoute:"currencies",placeholder:e.$t("sbxwebshop.global.search")},null,8,["filters","placeholder"]),t("div",h,[t("div",g,[t("div",_,[t("div",f,[o(i,{columns:r.columns,items:s.currencies.data,showAddButton:!0,addButtonText:e.$t("sbxwebshop.currencies.new_currency_button"),addRoute:"currencies.create",showEditButton:!0,editRoute:"currencies.edit",showDeleteButton:!0,deleteRoute:"currencies.destroy",deleteDialogTitle:e.$t("sbxwebshop.currencies.delete_dialog_title"),deleteDialogMessage:e.$t("sbxwebshop.currencies.delete_dialog_message"),deleteDialogOKText:e.$t("sbxwebshop.currencies.delete_dialog_ok"),deleteDialogCancelText:e.$t("sbxwebshop.global.cancel"),paginator:s.currencies.meta},null,8,["columns","items","addButtonText","deleteDialogTitle","deleteDialogMessage","deleteDialogOKText","deleteDialogCancelText","paginator"])])])])])],64)}const C=n(b,[["render",x]]);export{C as default};
