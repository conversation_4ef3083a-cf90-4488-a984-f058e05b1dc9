import{_ as d,S as f,c as b,w as i,r as s,b as _,e as g,d as a,g as h,t as B,k as S}from"./app-Cm2beRkj.js";import{S as y}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as X}from"./SBXInput-C8dZEgZe.js";import{S as v}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const w={components:{SBXDefaultPageLayout:y,SBXInput:X,SBXButton:f,SBXToggle:v},props:{currency:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle,console.log("currency: ",this.currency)},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:this.currency.data.name,is_active:this.currency.data.is_active,is_default:this.currency.data.is_default})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.global.edit")} - ${this.currency.data.name}`}},methods:{update(){this.form.processing||this.form.put(this.route("currencies.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.currency.data.id]))}}};function T(r,e,p,$,o,l){const m=s("SBXInput"),n=s("SBXToggle"),u=s("SBXButton"),c=s("SBXDefaultPageLayout");return _(),b(c,null,{default:i(()=>[g("form",{onSubmit:e[3]||(e[3]=S((...t)=>l.update&&l.update(...t),["prevent"]))},[a(m,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:r.$t("sbxwebshop.currencies.name_label"),error:p.errors.name},null,8,["model","label","error"]),a(n,{model:o.form.is_active,"onUpdate:model":e[1]||(e[1]=t=>o.form.is_active=t),label:r.$t("sbxwebshop.currencies.is_active_label")},null,8,["model","label"]),a(n,{model:o.form.is_default,"onUpdate:model":e[2]||(e[2]=t=>o.form.is_default=t),label:r.$t("sbxwebshop.currencies.is_default_label")},null,8,["model","label"]),a(u,{class:"mt-4"},{default:i(()=>[h(B(r.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const P=d(w,[["render",T]]);export{P as default};
