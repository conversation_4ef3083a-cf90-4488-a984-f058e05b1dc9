import{b as a,h as s,e as r,_ as y,$ as C,r as m,t,g as p,F as c,i as h,c as _,w as $,d as b,j as d,n as f,a4 as L,J as x}from"./app-Cm2beRkj.js";function B(e,n){return a(),s("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[r("path",{"fill-rule":"evenodd",d:"M7.72 12.53a.75.75 0 010-1.06l7.5-7.5a.75.75 0 111.06 1.06L9.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5z","clip-rule":"evenodd"})])}function P(e,n){return a(),s("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},[r("path",{"fill-rule":"evenodd",d:"M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z","clip-rule":"evenodd"})])}const S={components:{Link:C,ChevronLeftIcon:B,ChevronRightIcon:P},props:{paginator:{type:Object,default:null}},methods:{buttonClass(e){if(e.label.includes("Previous")){var n="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500";return this.componentType(e)=="Link"&&(n+=" hover:bg-gray-50"),n}if(e.label.includes("Next")){var n="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500";return this.componentType(e)=="Link"&&(n+=" hover:bg-gray-50"),n}return e.active?"z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium"},componentType(e){return e.active||e.url==null||this.paginator.current_page==1&&e.label.includes("Previous")||this.paginator.current_page==this.paginator.last_page&&e.label.includes("Next")?"span":"Link"}}},j={class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},I={class:"flex-1 flex justify-between sm:hidden"},N={href:"#",class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},T={href:"#",class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},k={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},z={class:"text-sm text-gray-700"},O={class:"font-medium"},V={class:"font-medium"},X={class:"font-medium"},R={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},q={key:0},A={class:"sr-only"},D={key:1},F={key:2},M={class:"sr-only"};function E(e,n,i,v,w,u){const g=m("ChevronLeftIcon"),l=m("ChevronRightIcon");return a(),s("div",j,[r("div",I,[r("a",N,t(e.$t("sbxpaginator.previous")),1),r("a",T,t(e.$t("sbxpaginator.next")),1)]),r("div",k,[r("div",null,[r("p",z,[p(t(e.$t("sbxpaginator.showing"))+" "+t(" ")+" ",1),r("span",O,t(i.paginator.from),1),p(" "+t(" ")+" "+t(e.$t("sbxpaginator.to"))+" "+t(" ")+" ",1),r("span",V,t(i.paginator.to),1),p(" "+t(" ")+" "+t(e.$t("sbxpaginator.of"))+" "+t(" ")+" ",1),r("span",X,t(i.paginator.total),1),p(" "+t(" ")+" "+t(e.$t("sbxpaginator.rows")),1)])]),r("div",null,[r("nav",R,[(a(!0),s(c,null,h(i.paginator.links,o=>(a(),_(L(u.componentType(o)),{href:o.url,class:f(u.buttonClass(o))},{default:$(()=>[o.label.toLowerCase().includes("previous")?(a(),s("div",q,[r("span",A,t(e.$t("sbxpaginator.previous")),1),b(g,{class:"h-5 w-5","aria-hidden":"true"})])):d("",!0),!o.label.toLowerCase().includes("previous")&&!o.label.toLowerCase().includes("next")?(a(),s("div",D,t(o.label),1)):d("",!0),o.label.toLowerCase().includes("next")?(a(),s("div",F,[r("span",M,t(e.$t("sbxpaginator.next")),1),b(l,{class:"h-5 w-5","aria-hidden":"true"})])):d("",!0)]),_:2},1032,["href","class"]))),256))])])])])}const J=y(S,[["render",E]]),G={components:{SBXPaginator:J},props:{columns:{type:Array,required:!0,default:[]},items:{type:Array,required:!0,default:[]},paginator:{type:Object,default:null}},methods:{headerClass(e){var n="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider";return e.hasOwnProperty("headeralignment")?(e.headeralignment=="left"&&(n+=" text-left"),e.headeralignment=="center"&&(n+=" text-center"),e.headeralignment=="right"&&(n+=" text-right")):n+=" text-left",n}}},H={class:"min-w-full divide-y divide-gray-200"},K={class:"bg-gray-50"},Q={key:0},U={class:"bg-white divide-y divide-gray-200"},W={class:"px-6 py-4 whitespace-nowrap"},Y={key:0,class:"text-sm"};function Z(e,n,i,v,w,u){const g=m("SBXPaginator");return a(),s(c,null,[r("table",H,[r("thead",K,[r("tr",null,[(a(!0),s(c,null,h(i.columns,l=>(a(),s("th",{scope:"col",class:f(u.headerClass(l))},[l.hasOwnProperty("label")?(a(),s("span",Q,t(l.label),1)):d("",!0),x(e.$slots,"header-"+l.key)],2))),256))])]),r("tbody",U,[(a(!0),s(c,null,h(i.items,l=>(a(),s("tr",null,[(a(!0),s(c,null,h(i.columns,o=>(a(),s("td",W,[r("div",{class:f("flex items-center "+o.class)},[l.hasOwnProperty(o.key)?(a(),s("span",Y,t(l[o.key]),1)):d("",!0),x(e.$slots,o.key,{item:l})],2)]))),256))]))),256))])]),i.paginator?(a(),_(g,{key:0,paginator:i.paginator},null,8,["paginator"])):d("",!0)],64)}const te=y(G,[["render",Z]]);export{te as S,J as a,P as b,B as r};
