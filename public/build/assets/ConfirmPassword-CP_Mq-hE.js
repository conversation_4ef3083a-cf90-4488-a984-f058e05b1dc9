import{ac as i,s as m,h as u,d as a,C as t,w as r,F as f,b as p,af as c,e as o,n as _,g as w,k as g}from"./app-Cm2beRkj.js";import{A as k}from"./AuthenticationCard-BbMgTIf9.js";import{_ as b}from"./AuthenticationCardLogo-DOAxyrlM.js";import{_ as v,a as x}from"./TextInput-Dx25uC_E.js";import{_ as y}from"./InputLabel-dwoQhTuq.js";import{_ as V}from"./PrimaryButton-7FvKQ4S5.js";const C={class:"flex justify-end mt-4"},$={layout:null},h=Object.assign($,{__name:"ConfirmPassword",setup(B){const s=i({password:""}),n=m(null),l=()=>{s.post(route("password.confirm"),{onFinish:()=>{s.reset(),n.value.focus()}})};return(A,e)=>(p(),u(f,null,[a(t(c),{title:"Översättare.nu | Säkert område"}),a(k,null,{logo:r(()=>[a(b)]),default:r(()=>[e[2]||(e[2]=o("div",{class:"mb-4 text-sm text-gray-600"}," Detta är en säker del av applikationen. Ange ditt lösenord innan du förtsätter. ",-1)),o("form",{onSubmit:g(l,["prevent"])},[o("div",null,[a(y,{for:"password",value:"Lösenord"}),a(v,{id:"password",ref_key:"passwordInput",ref:n,modelValue:t(s).password,"onUpdate:modelValue":e[0]||(e[0]=d=>t(s).password=d),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(x,{class:"mt-2",message:t(s).errors.password},null,8,["message"])]),o("div",C,[a(V,{class:_(["ml-4",{"opacity-25":t(s).processing}]),disabled:t(s).processing},{default:r(()=>e[1]||(e[1]=[w(" Bekräfta ")])),_:1},8,["class","disabled"])])],32)]),_:1})],64))}});export{h as default};
