import{_ as a,h as o,e,t as r,g as n,F as d,b as l}from"./app-Cm2beRkj.js";import"./client-BWFz6ICJ.js";import{r as m,a as g,b as c,y as p,g as x,A as _}from"./CheckIcon-De50fY7n.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const y={components:{CheckIcon:m,CheckCircleIcon:g,DocumentTextIcon:c,RadioGroup:p,RadioGroupOption:x,Disclosure:_},props:{bid:Object},data(){return{processingData:!1,statuses:{Complete:"text-green-700 bg-green-50 ring-green-600/20","In progress":"text-gray-600 bg-gray-50 ring-gray-500/10",Archived:"text-yellow-800 bg-yellow-50 ring-yellow-600/20"},project:{id:1,name:"GraphQL API",href:"#",status:"Complete",createdBy:"<PERSON>",dueDate:"March 17, 2023",dueDateTime:"2023-03-17T00:00Z"}}},mounted(){},computed:{},methods:{}},u={class:"mb-4 py-1 border-b border-gray-200"},b={class:"text-base font-medium text-gray-500"},f={class:"flex items-center justify-between gap-x-6 py-5 bg-gray-50 ring-1 ring-inset ring-gray-200 rounded-lg p-5"},h={class:"min-w-0"},v={class:"flex items-start gap-x-3"},w={class:"text-lg/6 font-semibold text-gray-900"},k={class:"mt-1 flex items-center gap-x-2 text-xs/5 text-gray-500"},B={class:"whitespace-nowrap"},D=["datetime"],C={class:"truncate"},I=["datetime"],A={class:"flex flex-none items-center gap-x-4"},T={class:"text-lg font-medium text-gray-900 rounded-md ring-1 ring-inset ring-gray-50 px-2.5 py-1.5 bg-gray-50"};function j(i,t,s,G,O,V){return l(),o(d,null,[e("div",u,[e("h3",b,r(i.$t("translations.translation_store.payment_title"))+" översikt ",1)]),e("div",f,[e("div",h,[e("div",v,[e("p",w,"Order #"+r(s.bid.payment_information.order_no),1),t[0]||(t[0]=e("p",{class:"text-green-700 bg-green-50 ring-green-600/20 mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset"}," Betalad",-1))]),e("div",k,[e("p",B,[t[1]||(t[1]=n(" Betalad den ")),e("time",{datetime:s.bid.payment_information.date},r(s.bid.payment_information.date),9,D)]),t[3]||(t[3]=e("svg",{viewBox:"0 0 2 2",class:"h-0.5 w-0.5 fill-current"},[e("circle",{cx:"1",cy:"1",r:"1"})],-1)),e("p",C,[t[2]||(t[2]=n("Beräknad leverans ")),e("time",{datetime:s.bid.estimated_delivery},r(s.bid.estimated_delivery),9,I)])])]),e("div",A,[e("span",T,[n(r(s.bid.price+s.bid.price*this.$page.props.market_vat_rate/100)+" kr ",1),t[4]||(t[4]=e("span",{class:"text-xs text-gray-400"},"inkl.moms",-1))])])])],64)}const E=a(y,[["render",j]]);export{E as default};
