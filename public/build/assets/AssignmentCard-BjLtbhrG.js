import{_,ai as b,aj as u,h as l,e,d as c,t,g as a,j as m,r as h,b as g}from"./app-Cm2beRkj.js";import{r as y,a as x,b as f}from"./UsersIcon-ClDae0qo.js";const v={components:{FireIcon:y,UsersIcon:x,DocumentIcon:f,LanguageIcon:b},props:{assignment:Object,selectedAssignment:Object,mobile:{type:Boolean,default:!1}},methods:{selectAssignment(){this.$emit("selectAssignment",this.assignment)},selectAssignmentMobile(){u.visit(this.route("my_assignments.mobile_detail.show",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.assignment.assignment_id]))}}},p={class:"flex items-center justify-between"},k={class:"flex"},A={class:"text-sm text-gray-700 leading-6 font-semibold"},w={class:"flex rounded-md bg-white group-hover:bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 group-hover:text-gray-700 ring-1 ring-inset ring-gray-400 group-hover:ring-gray-400"},j={class:"flex items-center mt-3 border-t border-gray-300 group-hover:border-gray-300 pt-3"},C={class:"text-xs text-gray-600"},I={class:"font-semibold"},B={class:"text-xs text-gray-600 ml-3"},N={class:"font-semibold break-keep"},V={class:"flex items-center justify-between"},L={class:"flex"},M={class:"text-sm text-gray-700 leading-6 font-semibold"},O={class:"flex rounded-md bg-white group-hover:bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 group-hover:text-gray-700 ring-1 ring-inset ring-gray-400 group-hover:ring-gray-400"},D={class:"flex items-center mt-3 border-t border-gray-300 group-hover:border-gray-300 pt-3"},E={class:"text-xs text-gray-600"},F={class:"font-semibold"},S={class:"text-xs text-gray-600 ml-3"},T={class:"font-semibold break-keep"},U={class:"flex items-center justify-between"},q={class:"flex"},z={class:"text-sm text-gray-700 leading-6 font-semibold"},G={class:"flex rounded-md bg-white group-hover:bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 group-hover:text-gray-700 ring-1 ring-inset ring-gray-400 group-hover:ring-gray-400"},H={class:"flex items-center mt-3 border-t border-gray-300 group-hover:border-gray-300 pt-3"},J={class:"text-xs text-gray-600"},K={class:"font-semibold"},P={class:"text-xs text-gray-600 ml-3"},Q={class:"font-semibold break-keep"},R={key:3,class:"group bg-gray-100 px-5 py-6 border-b-gray-300 border-b cursor-pointer group-hover:cursor-pointer"},W={class:"flex items-center justify-between"},X={class:"flex"},Y={class:"text-sm text-gray-700 leading-6 font-semibold"},Z={class:"inline-flex content-end rounded-md bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 ring-1 ring-inset ring-gray-400"},$={class:"flex items-center mt-3 border-t border-gray-300 pt-3"},ee={class:"text-xs text-gray-600"},te={class:"font-semibold"},se={class:"text-xs text-gray-600 ml-3"},ne={class:"font-semibold break-keep"};function ae(n,r,s,re,ie,i){const d=h("LanguageIcon");return g(),l("div",null,[s.mobile?(g(),l("div",{key:0,onClick:r[0]||(r[0]=(...o)=>i.selectAssignmentMobile&&i.selectAssignmentMobile(...o)),class:"group bg-gray-100 hover:bg-gray-100 px-5 py-6 border-gray-300 border rounded-xl mb-4 cursor-pointer group-hover:cursor-pointer"},[e("div",p,[e("div",k,[e("span",null,[c(d,{class:"h-6 w-6 mr-1 text-gray-400 group-hover:text-gray-400","aria-hidden":"true"})]),e("p",A,t(s.assignment.from_translation_language)+" "+t(n.$t("translations.translation_assignment_market.to_label"))+" "+t(s.assignment.to_translation_language),1)]),e("span",w,t(s.assignment.assignment_id),1)]),e("div",j,[e("p",C,[a(t(n.$t("translations.translation_categories.title"))+": ",1),e("span",I,t(s.assignment.translation_category),1)]),e("p",B,[a(t(n.$t("translations.translation_assignments.date_label"))+": ",1),e("span",N,t(s.assignment.created_date),1)])])])):m("",!0),!s.mobile&&s.selectedAssignment==null?(g(),l("div",{key:1,onClick:r[1]||(r[1]=(...o)=>i.selectAssignment&&i.selectAssignment(...o)),class:"group bg-white hover:bg-gray-100 px-5 py-6 border-b-gray-300 border-b cursor-pointer group-hover:cursor-pointer"},[e("div",V,[e("div",L,[e("span",null,[c(d,{class:"h-6 w-6 mr-1 text-gray-400 group-hover:text-gray-400","aria-hidden":"true"})]),e("p",M,t(s.assignment.from_translation_language)+" "+t(n.$t("translations.translation_assignment_market.to_label"))+" "+t(s.assignment.to_translation_language),1)]),e("span",O,t(s.assignment.assignment_id),1)]),e("div",D,[e("p",E,[a(t(n.$t("translations.translation_categories.title"))+": ",1),e("span",F,t(s.assignment.translation_category),1)]),e("p",S,[a(t(n.$t("translations.translation_assignments.date_label"))+": ",1),e("span",T,t(s.assignment.created_date),1)])])])):m("",!0),!s.mobile&&s.selectedAssignment!=null&&s.assignment.id!=s.selectedAssignment.id?(g(),l("div",{key:2,onClick:r[2]||(r[2]=(...o)=>i.selectAssignment&&i.selectAssignment(...o)),class:"group bg-white hover:bg-gray-100 px-5 py-6 border-b-gray-300 border-b cursor-pointer group-hover:cursor-pointer"},[e("div",U,[e("div",q,[e("span",null,[c(d,{class:"h-6 w-6 mr-1 text-gray-400 group-hover:text-gray-400","aria-hidden":"true"})]),e("p",z,t(s.assignment.from_translation_language)+" "+t(n.$t("translations.translation_assignment_market.to_label"))+" "+t(s.assignment.to_translation_language),1)]),e("span",G,t(s.assignment.assignment_id),1)]),e("div",H,[e("p",J,[a(t(n.$t("translations.translation_categories.title"))+": ",1),e("span",K,t(s.assignment.translation_category),1)]),e("p",P,[a(t(n.$t("translations.translation_assignments.date_label"))+": ",1),e("span",Q,t(s.assignment.created_date),1)])])])):m("",!0),!s.mobile&&s.selectedAssignment!=null&&s.assignment.id==s.selectedAssignment.id?(g(),l("div",R,[e("div",W,[e("div",X,[e("span",null,[c(d,{class:"h-6 w-6 mr-1 text-gray-400","aria-hidden":"true"})]),e("p",Y,t(s.assignment.from_translation_language)+" "+t(n.$t("translations.translation_assignment_market.to_label"))+" "+t(s.assignment.to_translation_language),1)]),e("span",Z,t(s.assignment.assignment_id),1)]),e("div",$,[e("p",ee,[a(t(n.$t("translations.translation_categories.title"))+": ",1),e("span",te,t(s.assignment.translation_category),1)]),e("p",se,[a(t(n.$t("translations.translation_assignments.date_label"))+": ",1),e("span",ne,t(s.assignment.created_date),1)])])])):m("",!0)])}const ge=_(v,[["render",ae]]);export{ge as default};
