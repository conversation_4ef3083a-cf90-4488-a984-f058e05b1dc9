import{_,h as i,e as s,t,j as o,F as g,i as r,r as c,b as a,c as y}from"./app-Cm2beRkj.js";import h from"./FileRow-C_c_9UoD.js";import{r as x}from"./PaperClipIcon-7jOC84nb.js";import"./client-BWFz6ICJ.js";const b={components:{FileRow:h,PaperClipIcon:x},props:{assignment:Object},mounted(){console.log("Assignment",this.assignment)}},f={class:"px-3"},p={class:"py-5 flex justify-between"},u={class:"inline-flex rounded-md bg-white px-4 py-3 text-xl font-semibold text-gray-700 ring-1 ring-inset ring-gray-400"},v={class:"text-sm text-gray-500 content-end items-baseline"},k={class:"border-t border-gray-300 py-2"},w={class:"grid grid-cols-1 lg:grid-cols-2 gap-4"},F={class:"grid grid-cols-2"},B={class:"py-5 sm:col-span-1 sm:px-0"},j={class:"text-sm font-semibold leading-6 text-gray-900"},z={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},C={class:"py-5 sm:col-span-1 sm:px-0"},R={class:"text-sm font-semibold leading-6 text-gray-900"},q={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},A={class:"border-t border-gray-200 py-5 sm:col-span-1 sm:px-0"},D={class:"text-sm font-semibold leading-6 text-gray-900"},I={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},N={class:"border-t border-gray-200 py-5 sm:col-span-1 sm:px-0"},V={class:"text-sm font-semibold leading-6 text-gray-900"},E={class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},L={key:0,class:"inline-flex items-center rounded-md bg-moss-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-moss-600/20"},O={key:1,class:"inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10"},P={class:"col-span-2 border-t border-gray-200 py-6 sm:px-0"},S={class:"text-sm font-semibold leading-6 text-gray-900"},G={key:0,class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},H={key:1,class:"mt-1 text-sm leading-6 text-gray-700 sm:mt-2"},J={class:"col-span-2 border-t border-gray-200 py-6 sm:px-0"},K={class:"text-sm font-semibold leading-6 text-gray-900"},M={class:"mt-3 text-sm text-gray-900"},Q={key:0,role:"list",class:"divide-y divide-gray-100 rounded-md border border-gray-200"},T={key:1,class:"py-8 border-2 rounded-md border-dashed border-gray-300 text-center"},U={class:"sm:border-l mt-2"},W={class:"sm:ml-4 p-4 rounded bg-gray-100 border border-gray-400/2"},X={class:"mb-2 text-base font-semibold text-gray-700"},Y={class:"border-t border-gray-300 px-4 pt-4 pb-1 sm:px-0"},Z={class:"text-sm font-semibold leading-6 text-gray-900"},$={key:0,class:"text-sm leading-6 text-gray-700"},ss={key:1,class:"text-sm leading-6 text-gray-700"},ts={class:"px-4 py-1 sm:px-0"},es={class:"text-sm font-semibold leading-6 text-gray-900"},ns={class:"text-sm leading-6 text-gray-700"},as={key:0,class:"px-4 py-1 sm:px-0"},is={class:"text-sm font-semibold leading-6 text-gray-900"},os={class:"text-sm leading-6 text-gray-700"},ds={key:1,class:"px-4 py-1 sm:px-0"},ls={class:"text-sm font-semibold leading-6 text-gray-900"},ms={class:"text-sm leading-6 text-gray-700"},_s={class:"px-4 py-1 sm:px-0"},gs={class:"text-sm font-semibold leading-6 text-gray-900"},rs={class:"text-sm leading-6 text-gray-700"},cs=["href"],ys={class:"px-4 py-1 sm:px-0"},hs={class:"text-sm font-semibold leading-6 text-gray-900"},xs={key:0,class:"text-sm leading-6 text-gray-700"},bs=["href"],fs={class:"px-4 py-1 sm:px-0"},ps={class:"text-sm font-semibold leading-6 text-gray-900"},us={key:0,class:"text-sm leading-6 text-gray-700"},vs=["href"],ks={key:1,class:"text-sm leading-6 text-gray-700"},ws=["href"],Fs={class:"mt-3 rounded"},Bs={class:"mb-2 border-b-2 pb-2 font-semibold text-gray-700"},js={class:"mt-3 rounded"},zs={class:"mb-2 border-b-2 pb-2 font-semibold text-gray-700"};function Cs(n,d,e,Rs,qs,As){const l=c("FileRow");return a(),i("div",f,[s("div",p,[s("span",u,t(e.assignment.assignment_id),1),s("span",v,t(e.assignment.created_date),1)]),s("div",k,[s("div",w,[s("dl",F,[s("div",B,[s("dt",j,t(n.$t("translations.my_assignments.assignment_detail.to_language_label")),1),s("dd",z,t(e.assignment.from_translation_language),1)]),s("div",C,[s("dt",R,t(n.$t("translations.my_assignments.assignment_detail.from_language_label")),1),s("dd",q,t(e.assignment.to_translation_language),1)]),s("div",A,[s("dt",D,t(n.$t("translations.my_assignments.assignment_detail.category_label")),1),s("dd",I,t(e.assignment.translation_category),1)]),s("div",N,[s("dt",V,t(n.$t("translations.my_assignments.assignment_detail.authorization_label")),1),s("dd",E,[e.assignment.is_authorization_required?(a(),i("span",L,t(n.$t("translations.global.yes")),1)):o("",!0),e.assignment.is_authorization_required?o("",!0):(a(),i("span",O,t(n.$t("translations.global.no")),1))])]),s("div",P,[s("dt",S,t(n.$t("translations.my_assignments.assignment_detail.notes_label")),1),e.assignment.notes!=null&&e.assignment.notes.length!=""?(a(),i("dd",G,t(e.assignment.notes),1)):(a(),i("dd",H,"-"))]),s("div",J,[s("dt",K,t(n.$t("translations.my_assignments.assignment_detail.files_label")),1),s("dd",M,[e.assignment.files.length>0?(a(),i("ul",Q,[(a(!0),i(g,null,r(e.assignment.files,m=>(a(),y(l,{file:m},null,8,["file"]))),256))])):(a(),i("div",T,d[0]||(d[0]=[s("span",{class:"mt-2 text-sm font-semibold text-gray-400"},"Inga bifogade filer",-1)])))])])]),s("div",U,[s("div",W,[s("h4",X,t(n.$t("translations.my_assignments.assignment_detail.contact_info_title")),1),s("dl",null,[s("div",Y,[s("dt",Z,t(n.$t("translations.my_assignments.assignment_detail.costumer_type_label")),1),e.assignment.assignment_type=="company"?(a(),i("dd",$,t(n.$t("translations.my_assignments.assignment_detail.company_label")),1)):o("",!0),e.assignment.assignment_type=="personal"?(a(),i("dd",ss,t(n.$t("translations.my_assignments.assignment_detail.private_person_label")),1)):o("",!0)]),s("div",ts,[s("dt",es,t(n.$t("translations.my_assignments.assignment_detail.name_label")),1),s("dd",ns,t(e.assignment.first_name)+" "+t(e.assignment.last_name),1)]),e.assignment.assignment_type=="company"?(a(),i("div",as,[s("dt",is,t(n.$t("translations.my_assignments.assignment_detail.company_label")),1),s("dd",os,t(e.assignment.company),1)])):o("",!0),e.assignment.assignment_type=="company"?(a(),i("div",ds,[s("dt",ls,t(n.$t("translations.my_assignments.assignment_detail.company_no_label")),1),s("dd",ms,t(e.assignment.company_no),1)])):o("",!0),s("div",_s,[s("dt",gs,t(n.$t("translations.my_assignments.assignment_detail.email_label")),1),s("dd",rs,[s("a",{href:"mailto:"+e.assignment.email},t(e.assignment.email),9,cs)])]),s("div",ys,[s("dt",hs,t(n.$t("translations.my_assignments.assignment_detail.phone_label")),1),e.assignment.is_email_contact_allowed?(a(),i("dd",xs,[s("a",{href:"tel:"+e.assignment.phone_no},t(e.assignment.phone_no),9,bs)])):o("",!0)]),s("div",fs,[s("dt",ps,t(n.$t("translations.my_assignments.assignment_detail.contact_preferences_label")),1),e.assignment.is_email_contact_allowed?(a(),i("dd",us,[s("a",{href:"mailto:"+e.assignment.email},t(n.$t("translations.my_assignments.assignment_detail.email_label")),9,vs)])):o("",!0),e.assignment.is_phone_contact_allowed?(a(),i("dd",ks,[s("a",{href:"tel:"+e.assignment.phone_no},t(n.$t("translations.my_assignments.assignment_detail.phone_label")),9,ws)])):o("",!0)])])])])]),s("div",Fs,[s("h3",Bs,t(n.$t("translations.my_assignments.assignment_detail.offer_title")),1)]),s("div",js,[s("h3",zs,t(n.$t("translations.my_assignments.assignment_detail.complete_assigmemt")),1)])])])}const Es=_(b,[["render",Cs]]);export{Es as default};
