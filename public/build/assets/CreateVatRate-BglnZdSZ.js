import{_ as d,S as b,c as _,w as i,r as l,b as g,e as c,d as a,g as B,t as S,k as v}from"./app-Cm2beRkj.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as h}from"./SBXInput-C8dZEgZe.js";import{S as w}from"./SBXToggle-CrVi0Yuw.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";import"./label-D4lfsZnZ.js";import"./use-controllable-D9fh3JbV.js";const T={components:{SBXDefaultPageLayout:X,SBXInput:h,SBXButton:b,SBXToggle:w},props:{errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({name:null,rate:null,is_active:!1,is_default:!1})}},computed:{pageTitle(){return`${this.$t("sbxwebshop.vat_rates.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("vat_rates.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}};function x(r,e,s,k,o,m){const n=l("SBXInput"),p=l("SBXToggle"),f=l("SBXButton"),u=l("SBXDefaultPageLayout");return g(),_(u,null,{default:i(()=>[c("form",{onSubmit:e[4]||(e[4]=v((...t)=>m.create&&m.create(...t),["prevent"]))},[a(n,{model:o.form.name,"onUpdate:model":e[0]||(e[0]=t=>o.form.name=t),label:r.$t("sbxwebshop.vat_rates.name_label"),error:s.errors.name},null,8,["model","label","error"]),a(n,{model:o.form.rate,"onUpdate:model":e[1]||(e[1]=t=>o.form.rate=t),label:r.$t("sbxwebshop.vat_rates.rate_label"),error:s.errors.rate},null,8,["model","label","error"]),a(p,{model:o.form.is_active,"onUpdate:model":e[2]||(e[2]=t=>o.form.is_active=t),label:r.$t("sbxwebshop.vat_rates.is_active_label")},null,8,["model","label"]),a(p,{model:o.form.is_default,"onUpdate:model":e[3]||(e[3]=t=>o.form.is_default=t),label:r.$t("sbxwebshop.vat_rates.is_default_label")},null,8,["model","label"]),a(f,{class:"mt-4"},{default:i(()=>[B(S(r.$t("sbxwebshop.global.save")),1)]),_:1})],32)]),_:1})}const L=d(T,[["render",x]]);export{L as default};
