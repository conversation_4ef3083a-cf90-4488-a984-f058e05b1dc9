import{_ as b,S as g,c as _,w as d,r as a,b as S,e as l,d as t,g as v,t as B,k as w}from"./app-Cm2beRkj.js";import{S as X}from"./SBXDefaultPageLayout-Dl87U6Ei.js";import{S as c}from"./SBXSelect-RMXlX9En.js";import{S as U}from"./SBXInput-C8dZEgZe.js";import"./ExclamationCircleIcon-C4_TqLjZ.js";const x={components:{SBXDefaultPageLayout:X,SBXButton:g,SBXSelect:c,SBXInput:U},props:{roles:Object,errors:Object},mounted(){this.$page.props.page_info.title_label=this.pageTitle},updated(){this.$page.props.page_info.title_label=this.pageTitle},remember:"form",data(){return{form:this.$inertia.form({role_id:1,first_name:null,last_name:null,email:null,password:null,password_confirmation:null})}},computed:{pageTitle(){return`${this.$t("sbxadmin.users.create_title")}`}},methods:{create(){this.form.processing||this.form.post(this.route("users.store",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code]))}}},k={class:"grid grid-cols-2 gap-4"},y={class:"grid grid-cols-2 gap-4"},D={class:"grid grid-cols-2 gap-4"},N={class:"grid grid-cols-2 gap-4"},T={class:"grid grid-cols-2 gap-4"},C={class:"grid grid-cols-2 gap-4"};function I(s,e,n,L,r,m){const u=a("SBXSelect"),i=a("SBXInput"),p=a("SBXButton"),f=a("SBXDefaultPageLayout");return S(),_(f,null,{default:d(()=>[l("form",{onSubmit:e[6]||(e[6]=w((...o)=>m.create&&m.create(...o),["prevent"]))},[l("div",k,[t(u,{model:r.form.role_id,"onUpdate:model":e[0]||(e[0]=o=>r.form.role_id=o),items:n.roles.data,labelFieldName:"name",label:s.$t("sbxadmin.users.role_label")},null,8,["model","items","label"]),e[7]||(e[7]=l("div",null,null,-1))]),l("div",y,[l("div",null,[t(i,{model:r.form.first_name,"onUpdate:model":e[1]||(e[1]=o=>r.form.first_name=o),label:s.$t("sbxadmin.users.first_name_label"),error:n.errors.first_name},null,8,["model","label","error"])]),e[8]||(e[8]=l("div",null,null,-1))]),l("div",D,[l("div",null,[t(i,{model:r.form.last_name,"onUpdate:model":e[2]||(e[2]=o=>r.form.last_name=o),label:s.$t("sbxadmin.users.last_name_label"),error:n.errors.last_name},null,8,["model","label","error"])]),e[9]||(e[9]=l("div",null,null,-1))]),l("div",N,[l("div",null,[t(i,{model:r.form.email,"onUpdate:model":e[3]||(e[3]=o=>r.form.email=o),label:s.$t("sbxadmin.users.email_label"),error:n.errors.email},null,8,["model","label","error"])]),e[10]||(e[10]=l("div",null,null,-1))]),l("div",T,[l("div",null,[t(i,{model:r.form.password,"onUpdate:model":e[4]||(e[4]=o=>r.form.password=o),label:s.$t("sbxadmin.users.password_label"),error:n.errors.password},null,8,["model","label","error"])]),e[11]||(e[11]=l("div",null,null,-1))]),l("div",C,[l("div",null,[t(i,{model:r.form.password_confirmation,"onUpdate:model":e[5]||(e[5]=o=>r.form.password_confirmation=o),label:s.$t("sbxadmin.users.password_confirmation_label"),error:n.errors.password_confirmation},null,8,["model","label","error"])]),e[12]||(e[12]=l("div",null,null,-1))]),t(p,{class:"mt-4"},{default:d(()=>[v(B(s.$t("sbxadmin.users.create_button")),1)]),_:1})],32)]),_:1})}const M=b(x,[["render",I]]);export{M as default};
