import{h as a,d as s,C as r,e,F as n,b as o,af as i}from"./app-Cm2beRkj.js";import{_ as c}from"./AuthenticationCardLogo-DOAxyrlM.js";const l={class:"font-sans text-gray-900 antialiased"},m={class:"pt-4 bg-gray-100"},d={class:"min-h-screen flex flex-col items-center pt-6 sm:pt-0"},_=["innerHTML"],v={__name:"TermsOfService",props:{terms:String},setup(t){return(f,p)=>(o(),a(n,null,[s(r(i),{title:"Terms of Service"}),e("div",l,[e("div",m,[e("div",d,[e("div",null,[s(c)]),e("div",{class:"w-full sm:max-w-2xl mt-6 p-6 bg-white shadow-md overflow-hidden sm:rounded-lg prose",innerHTML:t.terms},null,8,_)])])])],64))}};export{v as default};
