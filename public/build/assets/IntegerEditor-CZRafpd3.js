import{_ as c,S as p,c as f,w as r,r as n,b as g,e,t as i,l as _,m as v,d as b,g as h,k as B}from"./app-Cm2beRkj.js";import{S as x}from"./SBXDefaultPageLayout-Dl87U6Ei.js";const S={components:{SBXDefaultPageLayout:x,SBXButton:p},props:{setting:Object},remember:"form",data(){return{form:this.$inertia.form({integer_value:this.setting.data.integer_value})}},computed:{},methods:{update(){this.form.processing||this.form.put(this.route("settings.update",[this.$page.props.locale.selected_market_code,this.$page.props.locale.selected_language_code,this.setting.data.id]))}}},y={class:"mt-4"},k={for:"integer_value",class:"block text-sm font-medium text-gray-700"},w={class:"mt-1"};function X(l,t,u,D,o,a){const d=n("SBXButton"),m=n("SBXDefaultPageLayout");return g(),f(m,null,{default:r(()=>[e("form",{onSubmit:t[1]||(t[1]=B((...s)=>a.update&&a.update(...s),["prevent"]))},[e("div",y,[e("label",k,i(u.setting.data.name),1),e("div",w,[_(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>o.form.integer_value=s),type:"text",name:"integer_value",id:"integer_value",class:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"},null,512),[[v,o.form.integer_value]])])]),b(d,{class:"mt-4"},{default:r(()=>[h(i(l.$t("sbxadmin.global.save")),1)]),_:1})],32)]),_:1})}const N=c(S,[["render",X]]);export{N as default};
