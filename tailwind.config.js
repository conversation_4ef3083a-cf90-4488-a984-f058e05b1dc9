/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import defaultTheme from "tailwindcss/defaultTheme";
import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";
import colors from "@tailwindcss/colors";
import slider from "@vueform/slider/tailwind";

/*
|--------------------------------------------------------------------------
| Tailwind Setup
|--------------------------------------------------------------------------
*/

const newColors = {
    gray: {
        25: "#FCFCFD",
        50: "#F9FAFB",
        100: "#F2F4F7",
        200: "#EAECF0",
        300: "#D0D5DD",
        400: "#98A2B3",
        500: "#667085",
        600: "#475467",
        700: "#344054",
        800: "#182230",
        900: "#101828",
        950: "#0C111D",
    },
    bluegray: {
        50: "#EEEFF7",
        100: "#DCDFEF",
        200: "#B6BCDD",
        300: "#939CCC",
        400: "#6D79BB",
        500: "#4E5BA6",
        600: "#3E4884",
        700: "#2F3765",
        800: "#1F2442",
        900: "#101323",
        950: "#080A11",
    },
    greengray: {
        50: "#E4F6EE",
        100: "#C9EEDD",
        200: "#8FDBB9",
        300: "#59CA97",
        400: "#34A371",
        500: "#226B4A",
        600: "#1B553B",
        700: "#15422E",
        800: "#0E2B1E",
        900: "#071710",
        950: "#040C08",
    },
    cyan: {
        50: "#ECFDFF",
        100: "#CFF9FE",
        200: "#A5F0FC",
        300: "#67E3F9",
        400: "#22CCEE",
        500: "#06AED4",
        600: "#088AB2",
        700: "#0E7090",
        800: "#155B75",
        900: "#164C63",
        950: "#0D2D3A",
    },

    moss: {
        50: "#F5FBEE",
        100: "#E6F4D7",
        200: "#CEEAB0",
        300: "#ACDC79",
        400: "#86CB3C",
        500: "#669F2A",
        600: "#4F7A21",
        700: "#3F621A",
        800: "#335015",
        900: "#2B4212",
        950: "#11280B",
    },
};

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./vendor/laravel/jetstream/**/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/js/**/*.vue",
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ["Heebo", ...defaultTheme.fontFamily.sans],
            },

            colors: {
                gray: newColors.gray,
                bluegray: newColors.bluegray,
                greengray: newColors.greengray,
                cyan: newColors.cyan,
                moss: newColors.moss,
                sbx: {
                    button: {
                        primary: {
                            DEFAULT: newColors.moss[500],
                            text: "#fff",
                            focus: newColors.moss[600],

                            hover: {
                                DEFAULT: "#02B07D",
                                text: "#fff",
                            },

                            disabled: {
                                DEFAULT: "#9ca3af",
                                text: "#fff",
                            },
                        },

                        secondary: {
                            DEFAULT: "#0474CA",
                            text: "#fff",
                            focus: "#0474CA",

                            hover: {
                                DEFAULT: "#2563eb",
                                text: "#fff",
                            },

                            disabled: {
                                DEFAULT: "#9ca3af",
                                text: "#fff",
                            },
                        },

                        danger: {
                            DEFAULT: "#b91c1c",
                            text: "#fff",
                            focus: "#2563eb",

                            hover: {
                                DEFAULT: "#991b1b",
                                text: "#fff",
                            },

                            disabled: {
                                DEFAULT: "#9ca3af",
                                text: "#fff",
                            },
                        },

                        success: {
                            DEFAULT: "#15803d",
                            text: "#fff",
                            focus: "#2563eb",

                            hover: {
                                DEFAULT: "#166534",
                                text: "#fff",
                            },

                            disabled: {
                                DEFAULT: "#9ca3af",
                                text: "#fff",
                            },
                        },
                    },

                    topbar: {
                        primary: {
                            DEFAULT: "#fff",
                            text: "#000",
                            border: newColors.gray[300],
                        },

                        secondary: {
                            DEFAULT: "#fff",
                            text: "#fff",
                        },
                    },

                    sidebar: {
                        primary: {
                            DEFAULT: newColors.gray[600],
                            text: "#fff",
                            divider: "#9ca3af",

                            logo: {
                                DEFAULT: "#fff",
                            },

                            menuitem: {
                                text: "#fff",

                                hover: {
                                    background: newColors.gray[700],
                                    text: "#fff",
                                },

                                selected: {
                                    background: newColors.gray[700],
                                    text: "#fff",
                                },

                                icon: {
                                    DEFAULT: "#fff",
                                    hover: "#fff",
                                    selected: newColors.gray[100],
                                },
                            },
                        },

                        secondary: {
                            DEFAULT: "#38bdf8",
                            text: "#fff",
                            divider: "#9ca3af",

                            logo: {
                                DEFAULT: "#111827",
                            },

                            menuitem: {
                                text: "#fff",

                                hover: {
                                    background: "#b91c1c",
                                    text: "#fff",
                                },

                                selected: {
                                    background: "#111827",
                                    text: "#fff",
                                },

                                icon: {
                                    DEFAULT: "#9ca3af",
                                    hover: "#d1d5db",
                                    selected: "#d1d5db",
                                },
                            },
                        },
                    },
                },

                oversattare: {
                    orange: "#FF9738",
                    green: {
                        DEFAULT: newColors.moss[500],
                        hover: newColors.moss[600],
                        light: "#ecf7f2",
                    },
                    blue: {
                        DEFAULT: newColors.gray[600],
                        hover: "#2563eb",
                        light: "#edf1fb",
                    },
                    text: {
                        white: "#fff",
                        black: "#111827",
                    },
                },
            },
        },
    },

    plugins: [forms, typography, slider],
};
