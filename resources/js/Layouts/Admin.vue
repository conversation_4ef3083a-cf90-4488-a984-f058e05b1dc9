<script setup>
import { PlusCircleIcon } from '@heroicons/vue/20/solid';
</script>

<script>
/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from "vue";
import { router } from "@inertiajs/vue3";

import {
    ArrowRightOnRectangleIcon,
    BriefcaseIcon,
    CubeIcon,
    CurrencyEuroIcon,
    DocumentDuplicateIcon,
    LanguageIcon,
    PhoneIcon,
    TagIcon,
    UserIcon
} from "@heroicons/vue/24/outline";


import SBXAdminApplication from "@sbxadmin/Layouts/SBXAdminApplication.vue";
import SBXButton from "@sbxui/Buttons/SBXButton.vue";

/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        SBXAdminApplication,
        SBXButton,
    },

    mounted() {
        console.log("Page", this.$page);
    },

    data() {
        return {
            adminNavigation: [
                {
                    id: "translation_assignment_approvals",
                    name: this.$t(
                        "translations.menu.translation_assignment_approvals"
                    ),
                    href: "translation_assignment_approvals",
                    icon: BriefcaseIcon,
                    badgeCountProperty: "unapproved_assignments_count",
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translator_applications",
                    name: this.$t("translations.menu.translator_applications"),
                    href: "translator_applications",
                    icon: UserIcon,
                    method: "GET",
                    badgeCountProperty: "unapproved_translators_count",
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translation_assignments",
                    name: this.$t("translations.menu.translation_assignments"),
                    href: "translation_assignments",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translation_assignments/ongoing",
                    name: "Pågående uppdrag",
                    href: "translation_assignments/ongoing",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translation_assignments/won",
                    name: "Vunna uppdrag",
                    href: "translation_assignments/won",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translation_assignments/completed",
                    name: "Avslutade uppdrag",
                    href: "translation_assignments/completed",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translation_assignments/unwon",
                    name: "Nekade uppdrag",
                    href: "translation_assignments/unwon",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translators",
                    name: this.$t("translations.menu.translators"),
                    href: "translators",
                    icon: UserIcon,
                    method: "GET",
                    type: "SBXSidebarMenuItem",
                },
                // {
                //     id: "products",
                //     name: this.$t("translations.menu.translation_products"),
                //     href: "translation_products",
                //     icon: CubeIcon,
                //     method: "GET",
                //     type: "SBXSidebarMenuItem",
                // },
                // {
                //     id: "translation_categories",
                //     name: this.$t("translations.menu.translation_categories"),
                //     href: "translation_categories",
                //     icon: TagIcon,
                //     type: "SBXSidebarMenuItem",
                // },
                {
                    id: "translation_languages",
                    name: this.$t("translations.menu.translation_languages"),
                    href: "translation_languages",
                    icon: LanguageIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "translation_profile",
                    name: this.$t("translations.menu.translation_profile"),
                    href: "min-profil",
                    icon: UserIcon,
                    method: "GET",
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "logout",
                    name: this.$t("sbxadmin.menu.logout"),
                    href: "logout",
                    icon: ArrowRightOnRectangleIcon,
                    method: "POST",
                    type: "SBXSidebarMenuItem",
                },
            ],

            translatorNavigation: [
                {
                    id: "matchande-uppdrag",
                    name: this.$t(
                        "translations.menu.translation_assignment_market"
                    ),
                    href: "matchande-uppdrag",
                    icon: DocumentDuplicateIcon,
                    badgeCountProperty: "assignment_count",
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "mina-offererade-uppdrag",
                    name: this.$t("translations.menu.my_assignments"),
                    href: "mina-offererade-uppdrag",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "mina-pagaende-uppdrag",
                    name: this.$t("translations.menu.my_ongoing_assignments"),
                    href: "mina-pagaende-uppdrag",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "mina-avslutade-uppdrag",
                    name: this.$t("translations.menu.my_completed_assignments"),
                    href: "mina-avslutade-uppdrag",
                    icon: BriefcaseIcon,
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "min-profil",
                    name: this.$t("translations.menu.translation_profile"),
                    href: "min-profil",
                    icon: UserIcon,
                    method: "GET",
                    type: "SBXSidebarMenuItem",
                },
                {
                    id: "kundservice",
                    name: this.$t("translations.menu.customer_service"),
                    href: "https://www.oversattare.nu/kundservice/",
                    icon: PhoneIcon,
                    method: "GET",
                    type: "SBXSidebarExternalLink",
                },
                {
                    id: "logout",
                    name: this.$t("sbxadmin.menu.logout"),
                    href: "logout",
                    icon: ArrowRightOnRectangleIcon,
                    method: "POST",
                    type: "SBXSidebarMenuItem",
                },
            ],
        };
    },

    computed: {
        shownNavigation() {
            if (this.$page.props.isAdmin) {
                return this.adminNavigation;
            }

            return this.translatorNavigation;
        },
    },

    methods: {
    },
};
</script>

<template>
    <SBXAdminApplication :navigation="shownNavigation" logo="/graphics/oversattare_topbar_logo.png"
        logoLight="/graphics/oversattare_topbar_logo_white.png">
        <template v-if="!$page.props.isAdmin" #sidebar-top="{ closeEvent }">
            <hr class="h-px mt-0 bg-gray-700 border-0">
            <div class="my-4">
                <p class="text-base text-oversattare-text-white font-semibold select-none">
                    {{ $page.props.auth.user.first_name }}
                    {{ $page.props.auth.user.last_name }} ({{
                        $page.props.auth.user.translator_id
                    }})
                </p>
                <p class="text-sm text-gray-300 select-none">
                    {{ $page.props.auth.user.email }}
                </p>
            </div>
        </template>

        <!-- Main content -->
        <slot />
    </SBXAdminApplication>
</template>
