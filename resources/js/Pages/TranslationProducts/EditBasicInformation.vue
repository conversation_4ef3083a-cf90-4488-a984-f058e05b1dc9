<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';

  import ProductEditorsDropdown from '@sbxwebshop/Pages/Products/Components/ProductEditorsDropdown.vue';

  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXSelect from '@sbxui/Form/SBXSelect.vue';
  import SBXEditor from '@sbxui/Form/SBXEditor.vue';
  import SBXButton from '@sbxui/Buttons/SBXButton.vue';
  import SBXTextArea from '@sbxui/Form/SBXTextArea.vue';
  import SBXNotification from '@sbxui/Notifications/SBXNotification.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDefaultPageLayout,
      ProductEditorsDropdown,
      SBXInput,
      SBXSelect,
      SBXEditor,
      SBXButton,
      SBXTextArea,
      SBXNotification
    },

    props: {
      product: Object,
      pageComponents: Array,

      errors: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    updated() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    remember: 'form',

    data() {
      return {
        form: this.$inertia.form({
          name: this.product.data.name,
          short_description: this.product.data.short_description
        }),

        showSaveSuccess: false
      }
    },

    computed: {
      pageTitle() {
        return `${this.$t("sbxwebshop.global.edit")} - ${this.product.data.name}`;
      }
    },

    methods: {
      update() {
        if (!this.form.processing) {
          this.form.put(this.route('translation_products.basic_information.update', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.product.data.id]), {
              preserveScroll: true,
              onSuccess: (page) => {
                this.showSaveSuccess = true;
              }
          });
        }
      },

      saveNotificationCancelled() {
        this.showSaveSuccess = false;
      },
    }
  }
</script>

<template>
  <SBXDefaultPageLayout>
    <ProductEditorsDropdown :pages="pageComponents" currentPage="basic_information" :productID="product.data.id" />

    <form @submit.prevent="update">
      <SBXInput v-model:model="form.name" :label="$t('sbxwebshop.products.name_label')" :error="errors.name" />
      <SBXInput v-model:model="form.short_description" :label="$t('translations.translation_products.short_description_label')" :error="errors.short_description" />
      <SBXButton class="mt-4">{{ $t('sbxwebshop.global.save') }}</SBXButton>
    </form>

    <SBXNotification :show="showSaveSuccess" @notificationCancelled="saveNotificationCancelled" />
  </SBXDefaultPageLayout>
</template>
