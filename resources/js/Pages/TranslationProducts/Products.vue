<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
  import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';

  import { CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDataTable,
      SBXFilterBar,
      CheckCircleIcon,
      XCircleIcon
    },

    props: {
      products: Object,
      editRoute: String,
      createRoute: String,

      filters: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t('translations.translation_products.title');

      console.log("Products: ", this.products.data);
    },

    updated() {
      this.$page.props.page_info.title_label = this.$t('translations.translation_products.title');
    },

    data() {
      return {
        columns: [
          { key: 'productslot', label: this.$t('translations.translation_products.name_label') },
        ],

        productFilters: {
          search: this.filters.search,
          selectedFilter: this.filters.selectedFilter
        }
      }
    },

    computed: {
      filterOptions() {
        return [
          { text: 'Alla produkter', value: 'all' },
          { text: 'Aktiva', value: 'active' },
          { text: 'Inaktiva', value: 'inactive' }
        ];
      },
    },

    methods: {
      changeFilters() {
        this.$inertia.get(this.route('translation_products', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.productFilters, {
          preserveState: false,
          replace: true
        });
      },

      searchValueChanged(search) {
        this.productFilters.search = search;
      },

      activateProduct(productID) {
        this.$inertia.put(this.route('products.activate', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, productID]), {
          preserveState: false,
          replace: true
        });
      },

      deactivateProduct(productID) {
        this.$inertia.put(this.route('products.deactivate', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, productID]), {
          preserveState: false,
          replace: true
        });
      },
    }
  }
</script>

<template>
  <!-- <SBXFilterBar :filters="filters" searchRoute="products" :placeholder="$t('sbxwebshop.global.search')"></SBXFilterBar> -->
  <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="productFilters" searchRoute="translation_products" :placeholder="$t('translations.global.search')">
    <!-- <template v-slot:filterArea>
      <div class="flex justify-end">
        <select @change="changeFilters" v-model="productFilters.selectedFilter" class="mt-1 block w-64 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
          <option v-for="filter in filterOptions" :value="filter.value">
            {{ filter.text }}
          </option>
        </select>
      </div>
    </template> -->
  </SBXFilterBar>

  <div class="flex flex-col">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <SBXDataTable
            :columns="columns"
            :items="products.data"
            :showAddButton="false"
            :addButtonText="$t('sbxwebshop.products.create_button')"
            :addRoute="createRoute"
            :showEditButton="true"
            :editRoute="editRoute"
            :showDeleteButton="false"
            deleteRoute="products.destroy"
            :deleteDialogTitle="$t('sbxwebshop.products.delete_dialog_title')"
            :deleteDialogMessage="$t('sbxwebshop.products.delete_dialog_message')"
            :deleteDialogOKText="$t('sbxwebshop.products.delete_dialog_ok')"
            :deleteDialogCancelText="$t('translations.global.cancel')"
            :paginator="products.meta"
          >

            <template v-slot:productslot="row">
              <div>
                <p class="text-sm font-semibold">{{ row.item.name }}</p>
                <p class="text-xs">{{ row.item.manufacturer }}</p>
                <p class="text-xs">{{ row.item.packet_size }}</p>
              </div>
            </template>

            <template v-slot:activeslot="row">
              <span @click="activateProduct(row.item.id)" v-if="!row.item.is_active" class="inline-flex flex-shrink-0 items-center justify-center cursor-pointer">
                <XCircleIcon class="h-8 w-8 text-red-600 group-hover:text-red-800" aria-hidden="true" />
              </span>

              <span @click="deactivateProduct(row.item.id)" v-if="row.item.is_active" class="inline-flex flex-shrink-0 items-center justify-center cursor-pointer">
                <CheckCircleIcon class="h-8 w-8 text-green-600 group-hover:text-green-800" aria-hidden="true" />
              </span>
            </template>
          </SBXDataTable>
        </div>
      </div>
    </div>
  </div>
</template>
