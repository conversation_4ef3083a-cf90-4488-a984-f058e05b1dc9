<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue'
  import { Head, Link } from '@inertiajs/vue3';;



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default defineComponent({
    layout: null,

    components: {
      Head,
      Link
    },

    mounted() {
      this.$inertia.post(route('logout'));
    }
  })
</script>

<template>
  <Head title="Översättare.nu" />

  <div class="flex flex-col justify-center items-center h-screen bg-oversattare-green-light">
    <div class="mx-8">
      <img src="/graphics/oversattare_logo.png" />
    </div>

    <Link href="/login">
      <button class="mt-8 py-2 px-4 inline-flex justify-center border border-oversattare-green shadow-sm text-sm font-medium rounded-md text-white bg-oversattare-green hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"><slot />Logga in</button>
    </Link>
  </div>
</template>
