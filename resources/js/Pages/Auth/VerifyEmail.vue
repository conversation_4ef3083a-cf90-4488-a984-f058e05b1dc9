<script setup>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { computed } from 'vue';
  import { Head, Link, useForm } from '@inertiajs/vue3';
  import AuthenticationCard from '@/Components/AuthenticationCard.vue';
  import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
  import PrimaryButton from '@/Components/PrimaryButton.vue';



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  const props = defineProps({
    status: String
  });

  const form = useForm();

  const submit = () => {
    form.post(route('verification.send'));
  };

  const verificationLinkSent = computed(() => props.status === 'verification-link-sent');
</script>

<script>
  export default {
    layout: null
  }
</script>

<template>
  <Head title="Översättare.nu | Bekfräfta e-postadress" />

  <AuthenticationCard>
    <template #logo>
      <AuthenticationCardLogo />
    </template>

    <div class="mb-4 text-sm text-gray-600">
      Bekräfta din e-postadress genom att klicka på länken vi har skickat till dig med e-post innan du fortsätter. Om du inte har fått länken, skickar vi en ny.
    </div>

    <div v-if="verificationLinkSent" class="mb-4 font-medium text-sm text-green-600">
      En ny bekräftelselänk har skickats till den e-postadress du har angivit i din profil.
    </div>

    <form @submit.prevent="submit">
      <div class="mt-4 flex items-center justify-between">
        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
          Skicka ny bekräftelselänk
        </PrimaryButton>

        <div>
          <Link
            :href="route('profile.show')"
            class="underline text-sm text-gray-600 hover:text-gray-900"
          >
            Redigera profil</Link>

          <Link
            :href="route('logout')"
            method="post"
            as="button"
            class="underline text-sm text-gray-600 hover:text-gray-900 ml-2"
          >
            Log Out
          </Link>
        </div>
      </div>
    </form>
  </AuthenticationCard>
</template>
