<script setup>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { Head, useForm } from '@inertiajs/vue3';
  import AuthenticationCard from '@/Components/AuthenticationCard.vue';
  import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
  import InputError from '@/Components/InputError.vue';
  import InputLabel from '@/Components/InputLabel.vue';
  import PrimaryButton from '@/Components/PrimaryButton.vue';
  import TextInput from '@/Components/TextInput.vue';



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  defineProps({
    status: String
  });

  const form = useForm({
    email: ''
  });

  const submit = () => {
    form.post(route('password.email'));
  };
</script>

<script>
  export default {
    layout: null
  }
</script>

<template>
  <Head title="Översättare.nu | Glömt lösenord" />

  <AuthenticationCard>
    <template #logo>
      <AuthenticationCardLogo />
    </template>

    <div class="mb-4 text-sm text-gray-600">
      Glömt ditt lösenord? Ange din e-postadress nedan så skickar vi en länk som låter dig återställa ditt lösenord och ange ett nytt.
    </div>

    <div v-if="status" class="mb-4 font-medium text-sm text-green-600">
      {{ status }}
    </div>

    <form @submit.prevent="submit">
      <div>
        <InputLabel for="email" value="E-post" />
        <TextInput
          id="email"
          v-model="form.email"
          type="email"
          class="mt-1 block w-full"
          required
          autofocus
        />
        <InputError class="mt-2" :message="form.errors.email" />
      </div>

      <div class="flex items-center justify-end mt-4">
        <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
          Skicka länk för att återställa lösenord
        </PrimaryButton>
      </div>
    </form>
  </AuthenticationCard>
</template>
