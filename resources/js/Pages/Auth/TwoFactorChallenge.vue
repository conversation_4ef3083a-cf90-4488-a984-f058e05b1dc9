<script setup>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { nextTick, ref } from 'vue';
  import { Head, useForm } from '@inertiajs/vue3';
  import AuthenticationCard from '@/Components/AuthenticationCard.vue';
  import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
  import InputError from '@/Components/InputError.vue';
  import InputLabel from '@/Components/InputLabel.vue';
  import PrimaryButton from '@/Components/PrimaryButton.vue';
  import TextInput from '@/Components/TextInput.vue';



  /*
  |--------------------------------------------------------------------------
  | Property Definitions
  |--------------------------------------------------------------------------
  */

  const recovery = ref(false);

  const form = useForm({
    code: '',
    recovery_code: ''
  });

  const recoveryCodeInput = ref(null);
  const codeInput = ref(null);

  const toggleRecovery = async () => {
    recovery.value ^= true;

    await nextTick();

    if (recovery.value) {
      recoveryCodeInput.value.focus();
      form.code = '';
    } else {
      codeInput.value.focus();
      form.recovery_code = '';
    }
  };

  const submit = () => {
    form.post(route('two-factor.login'));
  };
</script>

<script>
  export default {
    layout: null
  }
</script>

<template>
  <Head title="Översättare.nu | Bekräftelse 2-faktors autentisering" />

  <AuthenticationCard>
    <template #logo>
      <AuthenticationCardLogo />
    </template>

    <div class="mb-4 text-sm text-gray-600">
      <template v-if="! recovery">
        Bekräfta åtkomst till ditt konto genom att ange koden som tillhandahålls av din autentiseringsapplikation.
      </template>

      <template v-else>
        Bekräfta åtkomst till ditt konto genom att ange en av dina nödkoder.
      </template>
    </div>

    <form @submit.prevent="submit">
      <div v-if="! recovery">
        <InputLabel for="code" value="Kod" />
        <TextInput
          id="code"
          ref="codeInput"
          v-model="form.code"
          type="text"
          inputmode="numeric"
          class="mt-1 block w-full"
          autofocus
          autocomplete="one-time-code"
        />
        <InputError class="mt-2" :message="form.errors.code" />
      </div>

        <div v-else>
          <InputLabel for="recovery_code" value="Nödkod" />
          <TextInput
            id="recovery_code"
            ref="recoveryCodeInput"
            v-model="form.recovery_code"
            type="text"
            class="mt-1 block w-full"
            autocomplete="one-time-code"
          />
          <InputError class="mt-2" :message="form.errors.recovery_code" />
        </div>

        <div class="flex items-center justify-end mt-4">
          <button type="button" class="text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer" @click.prevent="toggleRecovery">
            <template v-if="! recovery">
              Använd en nödkod
            </template>

            <template v-else>
              Använd en engångskod
            </template>
          </button>

          <PrimaryButton class="ml-4" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
            Logga in
          </PrimaryButton>
        </div>
    </form>
  </AuthenticationCard>
</template>
