<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import FileRow from './FileRow.vue';
import { PaperClipIcon } from '@heroicons/vue/24/outline';
import { CheckCircleIcon } from '@heroicons/vue/24/solid';
import AssignmentCompleteStep from './AssignmentCompleteStep.vue';


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {

    components: {
        FileRow,
        PaperClipIcon,
        CheckCircleIcon,
        AssignmentCompleteStep
    },

    props: {
        assignment: Object,
    },

    mounted() {
        console.log("Assignment", this.assignment);
    },

    data() {
        return {
            uploadMissingFiles: false,
        };
    },
}
</script>

<template>
    <div class="px-3">
        <div class="py-5 flex justify-between">
            <span
                class="inline-flex rounded-md bg-white px-4 py-3 text-xl font-semibold text-gray-700 ring-1 ring-inset ring-gray-400">
                {{ assignment.assignment_id }}</span>

            <span class="text-sm text-gray-500 content-end items-baseline">
                {{
                    assignment.created_date }}
            </span>

        </div>
        <div class="border-t border-gray-300 py-2">
            <div v-if="assignment.completed_at" class="rounded-md bg-green-50 p-4">
                <div class="flex">
                    <div class="shrink-0">
                        <CheckCircleIcon class="size-5 text-green-400" aria-hidden="true" />
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">{{
                            $t('translations.my_assignments.assignment_detail.completed_assigmemt_title') }}
                        </h3>
                        <!-- <div class="mt-2 text-sm text-green-700">
                            <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur,
                                ipsum similique veniam.</p>
                        </div> -->
                        <!-- <div class="mt-4">
                                        <div class="-mx-2 -my-1.5 flex">
                                            <button type="button"
                                                class="rounded-md bg-green-50 px-2 py-1.5 text-sm font-medium text-green-800 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2 focus:ring-offset-green-50">View
                                                status</button>
                                            <button type="button"
                                                class="ml-3 rounded-md bg-green-50 px-2 py-1.5 text-sm font-medium text-green-800 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2 focus:ring-offset-green-50">Dismiss</button>
                                        </div>
                                    </div> -->
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <dl class="grid grid-cols-2">
                    <div class="py-5 sm:col-span-1 sm:px-0">
                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                            $t('translations.my_assignments.assignment_detail.to_language_label') }}</dt>
                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                            assignment.from_translation_language }}</dd>
                    </div>
                    <div class="py-5 sm:col-span-1 sm:px-0">
                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                            $t('translations.my_assignments.assignment_detail.from_language_label') }}</dt>
                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                            assignment.to_translation_language }}</dd>
                    </div>
                    <div class="border-t border-gray-200 py-5 sm:col-span-1 sm:px-0">
                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                            $t('translations.my_assignments.assignment_detail.latest_delivery_date_label')
                        }}</dt>
                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                            assignment.last_delivery_date }}</dd>
                    </div>
                    <div class="border-t border-gray-200 py-5 sm:col-span-1 sm:px-0">
                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                            $t('translations.my_assignments.assignment_detail.authorization_label') }}</dt>
                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                            <span v-if="assignment.is_authorization_required"
                                class="inline-flex items-center rounded-md bg-moss-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-moss-600/20">{{
                                    $t('translations.global.yes') }}</span>
                            <span v-if="!assignment.is_authorization_required"
                                class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10">
                                {{ $t('translations.global.no') }}</span>
                        </dd>
                    </div>
                    <div class="col-span-2 border-t border-gray-200 py-6  sm:px-0">
                        <dt class="text-sm font-semibold leading-6 text-gray-900">
                            {{ $t('translations.my_assignments.assignment_detail.notes_label') }}</dt>
                        <dd v-if="assignment.notes != null && assignment.notes.length != ''"
                            class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{ assignment.notes }}</dd>
                        <dd v-else class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">-</dd>
                    </div>
                    <div class="col-span-2 border-t border-gray-200 py-6 sm:px-0">
                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                            $t('translations.my_assignments.assignment_detail.files_label') }}</dt>
                        <dd class="mt-3 text-sm text-gray-900">
                            <ul v-if="assignment.files.length > 0" role="list"
                                class="divide-y divide-gray-100 rounded-md border border-gray-200">
                                <FileRow v-for="file in assignment.files" :file="file" />
                            </ul>
                            <div v-else class="py-8 border-2 rounded-md border-dashed border-gray-300 text-center">
                                <span class="mt-2 text-sm font-semibold text-gray-400">Inga bifogade filer</span>
                            </div>
                        </dd>
                    </div>
                </dl>
                <div class="mt-2">
                    <h3 class="mb-3 border-b pb font-semibold text-gray-700 ">{{
                        $t('translations.my_assignments.assignment_detail.offer_title') }}</h3>
                    <div v-for="bid in assignment.answered_by"
                        class="flex items-center justify-between gap-x-6 py-5 bg-gray-50 ring-1 ring-inset ring-gray-200 rounded-lg p-5 mb-2">
                        <div class="min-w-0">
                            <div class="flex items-start gap-x-3">
                                <p class="text-lg/6 font-semibold text-gray-900">Offert #{{ bid.id }}</p>
                                <p v-if="bid.payment_complete"
                                    class="text-green-700 bg-green-50 ring-green-600/20 mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset">
                                    Betalad
                                </p>
                            </div>
                            <div v-if="!bid.payment_complete"
                                class="mt-1 flex items-center gap-x-2 text-xs/5 text-gray-500">
                                <p class="whitespace-nowrap">
                                    Estimerad leverans {{ bid.estimated_delivery }}
                                </p>
                            </div>
                            <div v-if="bid.payment_complete"
                                class="mt-1 flex items-center gap-x-2 text-xs/5 text-gray-500">
                                <p class="whitespace-nowrap">
                                    Betalad den <time :datetime="bid.payment_information.date">{{
                                        bid.payment_information.date
                                    }}</time><br />Estimerad leverans {{ bid.estimated_delivery }}
                                </p>
                            </div>
                        </div>
                        <div class="flex flex-none items-center gap-x-4">
                            <span
                                class="text-lg font-medium text-gray-900 rounded-md ring-1 ring-inset ring-gray-50 px-2.5 py-1.5 bg-gray-50">
                                {{
                                    bid.price }} kr <span class="text-xs text-gray-400">{{
                                    $t('translations.translation_assignment_market.excl_vat') }}</span>
                            </span>
                        </div>
                    </div>
                    <div class="mt-4" v-if="assignment.completed_at">
                        <h3 class="mb-3 border-b pb font-semibold text-gray-700 ">{{
                            $t('translations.my_assignments.assignment_detail.translated_documents') }}</h3>
                        <div>
                            <ul v-if="assignment.translated_files.length > 0" role="list"
                                class="divide-y divide-gray-100 rounded-md border border-gray-200">
                                <FileRow v-for="file in assignment.translated_files" :file="file" />

                            </ul>
                            <div v-else class="py-8 border-2 rounded-md border-dashed border-gray-300 text-center">
                                <span class="mt-2 text-sm font-semibold text-gray-400">Inga bifogade filer</span>
                            </div>
                            <div class="mt-2 pl-1"><a href="#"
                                    class="text-sm font-semibold text-red-600 hover:text-red-700"
                                    @click="uploadMissingFiles = !uploadMissingFiles">{{
                                        $t('translations.my_assignments.assignment_detail.add_missing_files')
                                    }}</a> </div>
                        </div>
                    </div>

                    <div class="mt-4" v-if="assignment.answered_by[0].payment_complete">
                        <h3 class="mb-3 border-b pb font-semibold text-gray-700 ">{{
                            $t('translations.my_assignments.assignment_detail.delivery_address') }}</h3>
                        <div class="bg-green-50 p-4 font-semibold text-gray-900">
                            {{ assignment.answered_by[0].payment_information.delivery_address.name }} <br />
                            {{ assignment.answered_by[0].payment_information.delivery_address.address }} <br />
                            {{ assignment.answered_by[0].payment_information.delivery_address.zip }} {{
                                assignment.answered_by[0].payment_information.delivery_address.city }} <br />
                            {{ assignment.answered_by[0].payment_information.delivery_address.country }}
                        </div>
                    </div>
                    <div class="mt-4">
                        <AssignmentCompleteStep :assignment="assignment" :uploadMissingFiles="uploadMissingFiles"
                            v-if="assignment.answered_by[0].payment_complete && !assignment.completed_at || uploadMissingFiles" />
                    </div>
                </div>

                <!-- <div class="sm:border-l mt-2">
                    <div class="sm:ml-4 p-4 rounded bg-gray-100 border border-gray-400/2">
                        <h4 class="mb-2 text-base font-semibold text-gray-700 ">{{
                            $t('translations.my_assignments.assignment_detail.contact_info_title') }}</h4>
                        <dl>
                            <div class="border-t border-gray-300 px-4 pt-4 pb-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.costumer_type_label') }}</dt>
                                <dd v-if="assignment.assignment_type == 'company'"
                                    class="text-sm leading-6 text-gray-700">{{
                                        $t('translations.my_assignments.assignment_detail.company_label') }}</dd>
                                <dd v-if="assignment.assignment_type == 'personal'"
                                    class="text-sm leading-6 text-gray-700 ">{{
                                        $t('translations.my_assignments.assignment_detail.private_person_label') }}
                                </dd>
                            </div>
                            <div class="px-4 py-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.name_label') }}</dt>
                                <dd class="text-sm leading-6 text-gray-700">{{
                                    assignment.first_name }} {{ assignment.last_name }}</dd>
                            </div>
                            <div v-if="assignment.assignment_type == 'company'" class="px-4 py-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.company_label') }}</dt>
                                <dd class="text-sm leading-6 text-gray-700">{{
                                    assignment.company }}</dd>
                            </div>
                            <div v-if="assignment.assignment_type == 'company'" class="px-4 py-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.company_no_label') }}</dt>
                                <dd class="text-sm leading-6 text-gray-700">{{
                                    assignment.company_no }}</dd>e
                            </div>
                            <div class="px-4 py-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.email_label') }}</dt>
                                <dd class="text-sm leading-6 text-gray-700"><a :href="'mailto:' + assignment.email">{{
                                    assignment.email }}</a></dd>
                            </div>
                            <div class="px-4 py-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.phone_label') }}</dt>
                                <dd v-if="assignment.is_email_contact_allowed" class="text-sm leading-6 text-gray-700">
                                    <a :href="'tel:' + assignment.phone_no">{{
                                        assignment.phone_no }}</a>
                                </dd>
                            </div>
                            <div class="px-4 py-1 sm:px-0">
                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                    $t('translations.my_assignments.assignment_detail.contact_preferences_label') }}
                                </dt>
                                <dd v-if="assignment.is_email_contact_allowed" class="text-sm leading-6 text-gray-700">
                                    <a :href="'mailto:' + assignment.email"> {{
                                        $t('translations.my_assignments.assignment_detail.email_label') }}</a>
                                </dd>
                                <dd v-if="assignment.is_phone_contact_allowed" class="text-sm leading-6 text-gray-700">
                                    <a :href="'tel:' + assignment.phone_no"> {{
                                        $t('translations.my_assignments.assignment_detail.phone_label') }}</a>
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div> -->

            </div>
        </div>
    </div>
</template>
