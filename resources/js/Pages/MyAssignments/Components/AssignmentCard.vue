<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { FireIcon, UsersIcon, DocumentIcon, BanknotesIcon, LanguageIcon } from '@heroicons/vue/24/outline';


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {

    components: {
        FireIcon,
        UsersIcon,
        DocumentIcon,
        LanguageIcon
    },
    props: {
        assignment: Object,
        selectedAssignment: Object,
        mobile: {
            type: Boolean,
            default: false
        }
    },

    methods: {
        selectAssignment() {
            this.$emit('selectAssignment', this.assignment);
        },

        selectAssignmentMobile() {
            router.visit(this.route('my_assignments.mobile_detail.show', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.assignment_id]));
        }
    }
}
</script>

<template>
    <div>
        <div v-if="mobile" @click="selectAssignmentMobile"
            class="group bg-gray-100 hover:bg-gray-100 px-5 py-6 border-gray-300 border rounded-xl mb-4 cursor-pointer group-hover:cursor-pointer">
            <div class="flex items-center justify-between">
                <div class="flex">
                    <span>
                        <LanguageIcon class="h-6 w-6 mr-1 text-gray-400 group-hover:text-gray-400" aria-hidden="true" />
                    </span>
                    <p class="text-sm text-gray-700 leading-6 font-semibold">{{
                        assignment.from_translation_language }} {{
                            $t('translations.translation_assignment_market.to_label') }} {{
                            assignment.to_translation_language
                        }}
                    </p>
                </div>
                <span
                    class="flex rounded-md bg-white group-hover:bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 group-hover:text-gray-700 ring-1 ring-inset ring-gray-400 group-hover:ring-gray-400">
                    {{ assignment.assignment_id }}</span>
            </div>
            <div class="flex items-center mt-3 border-t border-gray-300 group-hover:border-gray-300 pt-3">
                <p class="text-xs text-gray-600">
                    {{ $t('translations.translation_categories.title') }}:
                    <span class="font-semibold">{{ assignment.translation_category }} </span>
                </p>
                <p class="text-xs text-gray-600 ml-3">
                    {{ $t('translations.translation_assignments.date_label') }}:
                    <span class="font-semibold break-keep">{{ assignment.created_date }} </span>
                </p>
            </div>
        </div>

        <div v-if="!mobile && selectedAssignment == null" @click="selectAssignment"
            class="group bg-white hover:bg-gray-100 px-5 py-6 border-b-gray-300 border-b cursor-pointer group-hover:cursor-pointer">
            <div class="flex items-center justify-between">
                <div class="flex">
                    <span>
                        <LanguageIcon class="h-6 w-6 mr-1 text-gray-400 group-hover:text-gray-400" aria-hidden="true" />
                    </span>
                    <p class="text-sm text-gray-700 leading-6 font-semibold">{{
                        assignment.from_translation_language }} {{
                            $t('translations.translation_assignment_market.to_label') }} {{
                            assignment.to_translation_language
                        }}
                    </p>
                </div>
                <span
                    class="flex rounded-md bg-white group-hover:bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 group-hover:text-gray-700 ring-1 ring-inset ring-gray-400 group-hover:ring-gray-400">
                    {{ assignment.assignment_id }}</span>
            </div>
            <div class="flex items-center mt-3 border-t border-gray-300 group-hover:border-gray-300 pt-3">
                <p class="text-xs text-gray-600">
                    {{ $t('translations.translation_categories.title') }}:
                    <span class="font-semibold">{{ assignment.translation_category }} </span>
                </p>
                <p class="text-xs text-gray-600 ml-3">
                    {{ $t('translations.translation_assignments.date_label') }}:
                    <span class="font-semibold break-keep">{{ assignment.created_date }} </span>
                </p>
            </div>
        </div>
        <!-- None selected mode -->
        <div v-if="!mobile && selectedAssignment != null && assignment.id != selectedAssignment.id"
            @click="selectAssignment"
            class="group bg-white hover:bg-gray-100 px-5 py-6 border-b-gray-300 border-b cursor-pointer group-hover:cursor-pointer">
            <div class="flex items-center justify-between">
                <div class="flex">
                    <span>
                        <LanguageIcon class="h-6 w-6 mr-1 text-gray-400 group-hover:text-gray-400" aria-hidden="true" />
                    </span>
                    <p class="text-sm text-gray-700 leading-6 font-semibold">{{
                        assignment.from_translation_language }} {{
                            $t('translations.translation_assignment_market.to_label') }} {{
                            assignment.to_translation_language
                        }}
                    </p>
                </div>
                <span
                    class="flex rounded-md bg-white group-hover:bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 group-hover:text-gray-700 ring-1 ring-inset ring-gray-400 group-hover:ring-gray-400">
                    {{ assignment.assignment_id }}</span>
            </div>
            <div class="flex items-center mt-3 border-t border-gray-300 group-hover:border-gray-300 pt-3">
                <p class="text-xs text-gray-600">
                    {{ $t('translations.translation_categories.title') }}:
                    <span class="font-semibold">{{ assignment.translation_category }} </span>
                </p>
                <p class="text-xs text-gray-600 ml-3">
                    {{ $t('translations.translation_assignments.date_label') }}:
                    <span class="font-semibold break-keep">{{ assignment.created_date }} </span>
                </p>
            </div>
        </div>

        <!-- Selected mode -->
        <div v-if="!mobile && selectedAssignment != null && assignment.id == selectedAssignment.id"
            class="group bg-gray-100 px-5 py-6 border-b-gray-300 border-b cursor-pointer group-hover:cursor-pointer">
            <div class="flex items-center justify-between">
                <div class="flex">
                    <span>
                        <LanguageIcon class="h-6 w-6 mr-1 text-gray-400 " aria-hidden="true" />
                    </span>
                    <p class="text-sm text-gray-700 leading-6 font-semibold">{{
                        assignment.from_translation_language }} {{
                            $t('translations.translation_assignment_market.to_label') }} {{
                            assignment.to_translation_language
                        }}
                    </p>
                </div>
                <span
                    class="inline-flex content-end rounded-md bg-gray-100 px-2 py-1 text-sm font-medium text-gray-700 ring-1 ring-inset ring-gray-400 ">
                    {{ assignment.assignment_id }}</span>
            </div>
            <div class="flex items-center mt-3 border-t border-gray-300 pt-3">
                <p class="text-xs text-gray-600">
                    {{ $t('translations.translation_categories.title') }}:
                    <span class="font-semibold">{{ assignment.translation_category }} </span>
                </p>
                <p class="text-xs text-gray-600 ml-3">
                    {{ $t('translations.translation_assignments.date_label') }}:
                    <span class="font-semibold break-keep">{{ assignment.created_date }} </span>
                </p>
            </div>
        </div>
    </div>
</template>
