<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import { ArrowLeftIcon } from '@heroicons/vue/24/solid';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';

import AssignmentDetail from './Components/AssignmentDetail.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        Link,
        ArrowLeftIcon,
        SBXDefaultPageLayout,
        AssignmentDetail
    },

    props: {
        assignment: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.my_assignments.title");
    },

    updated() {
        this.$page.props.page_info.title_label = this.$t("translations.my_assignments.title");
    }
}
</script>

<template>
    <SBXDefaultPageLayout>
        <div class="flex items-center">
            <ArrowLeftIcon class="h-4 w-4 ml-2 mr-1" />
            <Link href="/se/sv/mina-offererade-uppdrag"><span class="font-semibold">{{ $t('translations.global.back')
                }}</span></Link>
        </div>

        <AssignmentDetail :assignment="assignment.data" />
    </SBXDefaultPageLayout>
</template>
