<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';

import AssignmentCard from './Components/AssignmentCard.vue';
import AssignmentDetail from './Components/AssignmentDetail.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        SBXFilterBar,
        AssignmentCard,
        AssignmentDetail
    },

    props: {
        assignments: Object,

        selectedAssignment: {
            type: Object,
            default: null
        },

        filters: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.my_assignments.title");

        if (this.assignments.data.length > 0) {
            this.currentAssignment = this.assignments.data[0];
        }

        if (this.selectedAssignment != null) {
            this.currentAssignment = this.selectedAssignment.data;
        }
    },

    updated() {
        this.$page.props.page_info.title_label = this.$t("translations.my_assignments.title");
    },

    data() {
        return {
            currentAssignment: null,

            myAssignmentsFilters: {
                search: this.filters.search
            },
        }
    },

    methods: {
        changeFilters() {
            this.$inertia.get(this.route('my_assignments', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.myAssignmentsFilters, {
                preserveState: false,
                replace: true
            });
        },

        searchValueChanged(search) {
            this.myAssignmentsFilters.search = search;
        },

        selectAssignment(assignment) {
            this.currentAssignment = assignment;
        }
    }
}
</script>

<template>
    <!-- <div class="mt-6">
        <div class="text-left border-b border-gray-300 pb-5">
            <p class=" px-4 text-3xl leading-7 font-bold tracking-tight text-gray-800 sm:text-4xl">{{
                $t('translations.my_assignments.title') }}</p>

        </div>
    </div> -->

    <!-- <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="myAssignmentsFilters" searchRoute="my_assignments"
        :placeholder="$t('translations.global.search')">
    </SBXFilterBar> -->

    <div class="hidden xl:block h-screen">

        <div class="grid grid-cols-12 gap-4 h-full">
            <div class="col-span-3 border-r border-gray-300 h-full">
                <AssignmentCard v-for="assignment in assignments.data" :assignment="assignment"
                    :selectedAssignment="currentAssignment" @selectAssignment="selectAssignment" />
            </div>

            <div class="col-span-9">
                <AssignmentDetail v-if="currentAssignment" :assignment="currentAssignment" />
            </div>
        </div>
    </div>

    <div class="xl:hidden p-4">
        <AssignmentCard v-for="assignment in assignments.data" :assignment="assignment"
            :selectedAssignment="currentAssignment" @selectAssignment="selectAssignment" :mobile="true" />
    </div>
</template>
