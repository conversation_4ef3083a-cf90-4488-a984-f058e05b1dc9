<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import Swal from 'sweetalert2';

import { CheckIcon } from '@heroicons/vue/20/solid';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        CheckIcon,
        SBXDefaultPageLayout,
        SBXButton
    },

    props: {
        discountActive: Boolean,
        discountUsed: Boolean,
        products: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_store.title");

        console.log("discountUsed", this.discountUsed);
        console.log("Products", this.products.data);
    },

    updated() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_store.title");
    },

    data() {
        return {
            selectedProduct: null,
            selectedProductID: null,
            discountCode: null,

            // buyCreditForms: useForm('post', this.route('svea.create_order', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), {
            //   product_id: null,
            //   quantity: 1
            // }),

            tiers: [
                {
                    name: this.$t('translations.translation_store.tier_start_title'),
                    id: 'tier-start',
                    quantity: 5,
                    priceMonthly: this.products.data[0].price,
                    description: this.products.data[0].short_description,
                    features: [],
                    mostPopular: false,
                },
                {
                    name: this.$t('translations.translation_store.tier_pro_title'),
                    id: 'tier-pro',
                    quantity: 10,
                    priceMonthly: this.products.data[1].price,
                    description: this.products.data[1].short_description,
                    features: [],
                    mostPopular: true,
                },
            ],
        }
    },

    computed: {
        cleanedDiscountCode() {
            if (this.discountCode == null) {
                return null;
            }

            return this.discountCode.toLowerCase().replaceAll(' ', '');
        }
    },

    methods: {
        buyCredits(tier) {
            var self = this;

            Swal.fire({
                text: self.$t("translations.translation_store.svea_information"),
                icon: 'warning',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#02B07D',
                confirmButtonText: self.$t("translations.global.continue")
            }).then((result) => {
                if (result.value) {
                    if (tier.id == 'tier-start') {
                        self.selectedProduct = self.products.data[0];
                    }

                    if (tier.id == 'tier-pro') {
                        self.selectedProduct = self.products.data[1];
                    }

                    if (tier.id == 'tier-enterprise') {
                        self.selectedProduct = self.products.data[2];
                    }

                    self.selectedProductID = self.selectedProduct.id;

                    var discount = 0;

                    if (self.discountActive && !self.discountUsed && self.cleanedDiscountCode == 'nyreg15') {
                        discount = 1;
                    }

                    window.location.href = '/se/sv/svea/create_order/product/' + self.selectedProduct.id + '/' + discount;
                }
            });
        }
    }
}
</script>

<template>
    <SBXDefaultPageLayout>
        <div class="mt-12 mb-12 bg-white">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-4xl text-center">
                    <p class="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">{{
                        $t('translations.translation_store.products_title') }}</p>
                </div>

                <p class="mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600">{{
                    $t('translations.translation_store.products_subtitle_1') }}</p>

                <!-- Discount code -->
                <div v-if="discountActive && !discountUsed"
                    class="mx-auto mt-6 max-w-2xl flex flex-col justify-center items-center">
                    <p class="text-base font-semibold text-center text-gray-900">{{
                        $t('translations.translation_store.discount_code_label') }}</p>
                    <input type="text" v-model="discountCode"
                        :placeholder="$t('translations.translation_store.discount_code_placeholder')"
                        class="mt-2 block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 w-1/2 sm:w-1/4" />
                    <p v-if="cleanedDiscountCode == 'nyreg15'" class="mt-2 text-sm text-center text-gray-900">{{
                        $t('translations.translation_store.discount_code_activated') }}</p>
                </div>

                <!-- Pricing Table -->
                <div
                    class="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-y-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">
                    <div v-for="(tier, tierIdx) in tiers" :key="tier.id"
                        :class="[tierIdx === 0 ? 'lg:rounded-r-none' : '', tierIdx === tiers.length - 1 ? 'lg:rounded-l-none' : '', 'flex flex-col justify-between rounded-3xl bg-white p-8 ring-1 ring-gray-500 xl:p-10']">
                        <div>
                            <div class="flex items-center justify-between gap-x-4">
                                <h3 :id="tier.id"
                                    :class="[tier.mostPopular ? 'text-oversattare-blue' : 'text-oversattare-text-black', 'text-lg font-semibold leading-8']">
                                    {{ tier.name }}</h3>
                                <p v-if="tier.mostPopular"
                                    class="rounded-full bg-oversattare-blue/10 px-2.5 py-1 text-xs font-semibold leading-5 text-indigo-600">
                                    {{ $t('translations.translation_store.most_popular_label') }}</p>
                            </div>
                            <p class="mt-4 text-sm font-bold leading-6 text-gray-600">{{ tier.description }}</p>

                            <p v-if="tier.id == 'tier-enterprise'"
                                class="mt-4 text-sm leading-6 font-semibold text-gray-600">{{
                                    $t('translations.translation_store.tier_enterprise_1') }}</p>

                            <p v-if="tier.id != 'tier-enterprise'" class="mt-6 flex items-baseline gap-x-1">
                                <span class="text-4xl font-bold tracking-tight text-gray-900">{{ tier.priceMonthly }} {{
                                    $t('translations.translation_store.currency_label') }}</span>
                                <span class="ml-2 text-sm font-semibold leading-6 text-gray-600">{{
                                    $t('translations.translation_store.including_vat_label') }}</span>
                            </p>
                            <p v-if="tier.id != 'tier-enterprise'"
                                class="mt-6 flex items-baseline gap-x-1 text-xs font-semibold leading-6 text-gray-600">
                                {{ $t('translations.translation_store.footnote') }} <br /> {{
                                    $t('translations.translation_store.footnote_2') }}
                            </p>
                            <ul role="list" class="mt-8 space-y-3 text-sm leading-6 text-gray-600">
                                <li v-for="feature in tier.features" :key="feature" class="flex gap-x-3">
                                    <CheckIcon class="h-6 w-5 flex-none text-indigo-600" aria-hidden="true" />
                                    {{ feature }}
                                </li>
                            </ul>
                        </div>
                        <a v-if="tier.id != 'tier-enterprise'" @click="buyCredits(tier)" :aria-describedby="tier.id"
                            :class="[tier.mostPopular ? 'bg-oversattare-green text-white select-none shadow-sm hover:bg-oversattare-green-hover cursor-pointer' : 'text-oversattare-green select-none cursor-pointer ring-1 ring-inset ring-oversattare-green-hover hover:ring-oversattare-green-hover', 'mt-8 block rounded-md py-2 px-3 text-center text-sm select-none cursor-pointer font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-oversattare-green']">{{
                                $t('translations.translation_store.buy_button') }}</a>
                        <a v-if="tier.id == 'tier-enterprise'" href="https://www.oversattare.nu/offert/" target="_blank"
                            :aria-describedby="tier.id"
                            :class="[tier.mostPopular ? 'bg-oversattare-green text-white select-none shadow-sm hover:bg-oversattare-green-hover cursor-pointer' : 'text-oversattare-green select-none cursor-pointer ring-1 ring-inset ring-oversattare-green-hover hover:ring-oversattare-green-hover', 'mt-8 block rounded-md py-2 px-3 text-center text-sm select-none cursor-pointer font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-oversattare-green']">{{
                                $t('translations.translation_store.contact_button') }}</a>
                    </div>
                </div>

                <div class="mt-8 flex flex-col justify-center items-center">
                    <p class="text-lg text-gray-900 font-semibold">{{ $t('translations.translation_store.safe_payments')
                        }}</p>
                    <img class="sm:w-2/5" src="/graphics/svea_payment-options_sv_pos.png" />
                </div>
            </div>
        </div>
    </SBXDefaultPageLayout>
</template>
