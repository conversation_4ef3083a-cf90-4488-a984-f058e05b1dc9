<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { CheckIcon } from '@heroicons/vue/20/solid';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';

import StoreProduct from './Components/StoreProduct.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        CheckIcon,
        SBXDefaultPageLayout,
        StoreProduct,
        SBXButton
    },

    props: {
        order: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_store.title");
        this.$page.props.credits = this.order.data.user_credit_count;
    },

    updated() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_store.title");
        this.$page.props.credits = this.order.data.user_credit_count;
    },

    computed: {
        rowTotal() {
            return this.order.data.rows[0].row_total.toLocaleString('SE-sv', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        },

        vatTotal() {
            return this.order.data.rows[0].row_total_vat.toLocaleString('SE-sv', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
    }
}
</script>

<template>
    <SBXDefaultPageLayout>
        <div class="mt-12 bg-white">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-4xl text-center">
                    <p class="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">{{
                        $t("translations.order_confirmation.title") }}</p>
                </div>

                <p class="mt-4 text-base text-oversattare-text-black text-center">{{
                    $t('translations.order_confirmation.subtitle_1') }}</p>
                <p class="text-base text-oversattare-text-black text-center">{{
                    $t('translations.order_confirmation.subtitle_2') }}</p>

                <p v-if="order.data.rows[0].credit_count > 1"
                    class="mt-4 text-base text-oversattare-text-black text-center">{{
                        $t('translations.order_confirmation.leads_prompt_1') }} {{ order.data.rows[0].credit_count }} {{
                        $t('translations.order_confirmation.leads_prompt_2') }}</p>
                <p v-if="order.data.rows[0].credit_count == 1"
                    class="mt-4 text-base text-oversattare-text-black text-center">{{
                        $t('translations.order_confirmation.leads_prompt_1') }} {{ order.data.rows[0].credit_count }} {{
                        $t('translations.order_confirmation.leads_prompt_3') }}</p>

                <h4 class="mt-8 text-xl font-semibold text-oversattare-text-black text-center">{{
                    $t('translations.order_confirmation.product_label') }}</h4>
                <p class="text-sm text-oversattare-text-black text-center">{{ order.data.rows[0].product_name }}</p>

                <h4 class="mt-4 text-xl font-semibold text-oversattare-text-black text-center">{{
                    $t('translations.order_confirmation.price_label') }}</h4>
                <p class="text-sm text-oversattare-text-black text-center">{{ rowTotal }} {{
                    $t('translations.order_confirmation.currency_label') }} <span
                        class="text-xs text-oversattare-text-black">({{ $t('translations.order_confirmation.vat_prompt')
                        }} {{ vatTotal }} {{ $t('translations.order_confirmation.currency_label') }})</span></p>
            </div>
        </div>
    </SBXDefaultPageLayout>
</template>
