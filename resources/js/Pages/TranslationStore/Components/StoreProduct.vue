<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
    },

    props: {
      product: Object,
      selectedProductID: Number
    },

    computed: {
      mainClass() {
        var mainClass = 'mt-2 p-2 flex justify-between w-3/5 cursor-pointer';

        if (this.selectedProductID == this.product.id) {
          mainClass += ' bg-gray-200';
        }

        return mainClass;
      }
    },

    methods: {
      selectProduct() {
        this.$emit('selectProduct', this.product);
      }
    }
  }
</script>

<template>
  <div @click="selectProduct" :class="mainClass">
    <div class="flex flex-col">
      <p class="text-xl font-medium text-gray-900">{{ product.name }}</p>
      <p class="text-sm text-gray-900">{{ product.short_description }}</p>
    </div>

    <p class="ml-16 text-xl font-medium text-gray-900 text-right">{{ product.price }} kr</p>
  </div>
</template>
