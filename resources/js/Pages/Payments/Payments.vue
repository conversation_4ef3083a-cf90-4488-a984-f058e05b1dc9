<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import { EnvelopeIcon } from '@heroicons/vue/24/solid';

import Swal from 'sweetalert2';

import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';
import SBXPaginator from '@sbxui/Tables/SBXPaginator.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';

import MobilePayment from './Components/MobilePayment.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        EnvelopeIcon,
        SBXDataTable,
        SBXFilterBar,
        MobilePayment,
        SBXPaginator,
        SBXButton
    },

    props: {
        payments: Object,
        filters: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.payments.title");

        if (this.$page.props.flash.authorization != null) {
            this.showAuthorizationNotification = true;
        }
    },

    updated() {
        this.$page.props.page_info.title_label = this.$t("translations.payments.title");
    },

    data() {
        return {
            columns: [
                { key: 'order_no', label: this.$t('translations.payments.order_no_label') },
                { key: 'date', label: this.$t('translations.payments.date_label') },
                { key: 'productslot', label: this.$t('translations.payments.product_label') },
                { key: 'priceslot', label: this.$t('translations.payments.price_label'), headeralignment: 'right', class: 'justify-end' },
                { key: 'buttonslot', label: '' },
            ],

            translationAssignmentFilters: {
                search: this.filters.search
            },
        }
    },

    methods: {
        changeFilters() {
            this.$inertia.get(this.route('payments', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.translationAssignmentFilters, {
                preserveState: false,
                replace: true
            });
        },

        searchValueChanged(search) {
            this.translationAssignmentFilters.search = search;
        },

        formatNumber(value) {
            return value.toLocaleString('SE-sv', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        },

        resendReceipt(payment) {
            var self = this;

            Swal.fire({
                title: self.$t("translations.payments.resend_receipt_dialog_title"),
                text: self.$t("translations.payments.resend_receipt_dialog_message"),
                icon: 'info',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#02B07D',
                confirmButtonText: self.$t("translations.payments.resend_receipt_dialog_send_button")
            }).then((result) => {
                if (result.value) {
                    self.$inertia.post(self.route('payments.resend_receipt', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, payment.id]), {
                    }, {
                        preserveScroll: true,
                        onSuccess: () => {
                        }
                    });
                }
            });
        }
    }
}
</script>

<template>
    <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="translationAssignmentFilters"
        searchRoute="payments" :placeholder="$t('translations.global.search')">
    </SBXFilterBar>

    <div class="hidden sm:flex sm:flex-col">
        <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                    <SBXDataTable :columns="columns" :items="payments.data" :showAddButton="false"
                        :showEditButton="false" :showDeleteButton="false" :showViewButton="false"
                        :paginator="payments.meta">

                        <template v-slot:productslot="row">
                            <div>
                                <p class="text-sm">{{ row.item.rows[0].product_name }}</p>
                            </div>
                        </template>

                        <template v-slot:priceslot="row">
                            <div>
                                <p class="text-sm">{{ formatNumber(row.item.price) }} {{
                                    $t('translations.payments.currency_label') }} ({{
                                        $t('translations.payments.vat_label') }} {{ formatNumber(row.item.vat) }} {{
                                        $t('translations.payments.currency_label') }})</p>
                            </div>
                        </template>

                        <template v-slot:buttonslot="row">
                            <div>
                                <SBXButton @click="resendReceipt(row.item)">
                                    <EnvelopeIcon class="w-4 h-4 text-white" />
                                </SBXButton>
                            </div>
                        </template>

                    </SBXDataTable>
                </div>
            </div>
        </div>
    </div>

    <div class="sm:hidden">
        <MobilePayment v-for="payment in payments.data" :payment="payment" />

        <SBXPaginator :paginator="payments.meta" />
    </div>
</template>
