<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import Swal from 'sweetalert2';

  import { EnvelopeIcon } from '@heroicons/vue/24/solid';

  import SBXButton from '@sbxui/Buttons/SBXButton.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      EnvelopeIcon,
      SBXButton
    },

    props: {
      payment: Object
    },

    data() {
      return {
      }
    },

    methods: {
      formatNumber(value) {
        return value.toLocaleString('SE-sv', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
      },

      resendReceipt() {
        var self = this;

        Swal.fire({
          title: self.$t("translations.payments.resend_receipt_dialog_title"),
          text: self.$t("translations.payments.resend_receipt_dialog_message"),
          icon: 'info',
          showCancelButton: true,
          cancelButtonText: self.$t("translations.global.cancel"),
          confirmButtonColor: '#02B07D',
          confirmButtonText: self.$t("translations.payments.resend_receipt_dialog_send_button")
        }).then((result) => {
          if (result.value) {
            self.$inertia.post(self.route('payments.resend_receipt', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, this.payment.id]), {
            }, {
              preserveScroll: true,
              onSuccess: () => {
              }
            });
          }
        });
      }
    }
  }
</script>

<template>
  <div class="p-4 border-b">
    <p class="text-base"><strong>{{ this.$t('translations.payments.order_no_label') }}</strong> {{ payment.order_no }}</p>
    <p class="text-base"><strong>{{ this.$t('translations.payments.date_label') }}</strong> {{ payment.date }}</p>
    <p class="text-base"><strong>{{ this.$t('translations.payments.product_label') }}</strong> {{ payment.rows[0].product_name }}</p>
    <p class="text-base"><strong>{{ this.$t('translations.payments.price_label') }}</strong> {{ formatNumber(payment.price) }} {{ $t('translations.payments.currency_label') }} ({{ $t('translations.payments.vat_label') }} {{ formatNumber(payment.vat) }} {{ $t('translations.payments.currency_label') }})</p>
    <SBXButton @click="resendReceipt()" class="mt-2"><EnvelopeIcon class="w-4 h-4 text-white" /></SBXButton>
  </div>
</template>
