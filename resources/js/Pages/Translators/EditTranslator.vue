<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineComponent } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';
import SBXSelect from '@sbxui/Form/SBXSelect.vue';
import SBXInput from '@sbxui/Form/SBXInput.vue';
import SBXComboBox from '@sbxui/Form/SBXComboBox.vue';
import SBXAlert from '@sbxui/Notifications/SBXAlert.vue';
import SBXToggle from '@sbxui/Form/SBXToggle.vue';

import StepsDivider from '../../Global/Components/StepsDivider.vue';
import SBXGenericNotification from '@sbxui/Notifications/SBXGenericNotification.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        SBXDefaultPageLayout,
        SBXButton,
        SBXToggle,
        SBXSelect,
        SBXInput,
        SBXComboBox,
        StepsDivider,
        SBXGenericNotification,
        SBXAlert
    },

    props: {
        user: Object,
        translationLanguages: Object,
        translationCategories: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.pageTitle;
        this.initializeFormData();
    },

    updated() {
        this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
        return {
            tabs: [
                { id: 'contact_info', name: this.$t('translations.profile.contact_info_tab') },
                { id: 'information', name: this.$t('translations.profile.information_tab') },
                { id: 'leads', name: this.$t('translations.profile.leads_tab') }
            ],

            selectedTab: 'contact_info',

            profileSaved: false,

            contactInfoForm: useForm('put', this.route('translators.update', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.user.data.id]), {
                company: this.user.data.company,
                company_no: this.user.data.company_no,
                first_name: this.user.data.first_name,
                last_name: this.user.data.last_name,
                address_1: this.user.data.address_1,
                address_2: this.user.data.address_2,
                postal_code: this.user.data.postal_code,
                city: this.user.data.city,
                email: this.user.data.email,
                phone_no: this.user.data.phone_no,
                is_authorized: this.user.data.is_authorized,
                authorization_id: this.user.data.authorization_id,
                allow_text_messages: this.user.data.allow_text_messages
            }),

            informationForm: useForm('put', this.route('translators.update.information', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.user.data.id]), {
                from_translation_languages: [],
                to_translation_languages: [],
                translation_categories: []
            }),

            leadsForm: useForm('put', this.route('translators.update.leads', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.user.data.id]), {
                lead_count: ''
            }),
        }
    },

    computed: {
        pageTitle() {
            return `${this.$t("translations.translators.edit_title")}`;
        }
    },

    methods: {
        initializeFormData() {
            // Create lookup maps for better performance with large datasets
            const languageMap = new Map();
            this.translationLanguages.data.forEach(lang => {
                languageMap.set(lang.id.toString(), lang);
            });

            const categoryMap = new Map();
            this.translationCategories.data.forEach(cat => {
                categoryMap.set(cat.id.toString(), cat);
            });

            // Efficiently map user's existing relationships
            const currentFromLanguages = this.user.data.from_translation_languages
                .map(item => languageMap.get(item.id.toString()))
                .filter(Boolean);

            const currentToLanguages = this.user.data.to_translation_languages
                .map(item => languageMap.get(item.id.toString()))
                .filter(Boolean);

            const currentCategories = this.user.data.translation_categories
                .map(item => categoryMap.get(item.id.toString()))
                .filter(Boolean);

            // Set form data directly
            this.informationForm.from_translation_languages = currentFromLanguages;
            this.informationForm.to_translation_languages = currentToLanguages;
            this.informationForm.translation_categories = currentCategories;
        },

        updateContactInfo() {
            var self = this;

            if (!this.contactInfoForm.processing) {
                this.contactInfoForm.submit({
                    preserveScroll: true,
                    onSuccess: page => {
                        self.profileSaved = true;
                        self.contactInfoForm.password = null;
                        self.contactInfoForm.password_confirmation = null;

                        setTimeout(function () {
                            self.profileSaved = false;
                        }, 3000);
                    }
                });
            }
        },

        updateInformation() {
            var self = this;

            // Debug: Log what's actually in the form before submission
            console.log('Form data before submission:', {
                from_translation_languages: this.informationForm.from_translation_languages,
                to_translation_languages: this.informationForm.to_translation_languages,
                translation_categories: this.informationForm.translation_categories
            });

            if (!this.informationForm.processing) {
                this.informationForm.submit({
                    preserveScroll: true,
                    onSuccess: () => {
                        self.profileSaved = true;

                        setTimeout(function () {
                            self.profileSaved = false;
                        }, 3000);
                    }
                });
            }
        },

        updateLeads() {
            var self = this;

            if (!this.leadsForm.processing) {
                this.leadsForm.submit({
                    preserveScroll: true,
                    onSuccess: page => {
                        self.profileSaved = true;

                        setTimeout(function () {
                            self.profileSaved = false;
                        }, 3000);
                    }
                });
            }
        },
    }
}
</script>

<template>
    <SBXDefaultPageLayout>
        <div class="pt-8 sm:pt-0">
            <div class="sm:hidden">
                <!-- Use an "onChange" listener to redirect the user to the selected tab URL. -->
                <select v-model="selectedTab" id="tabs" name="tabs"
                    class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm">
                    <option v-for="tab in tabs" :key="tab.id" :value="tab.id">{{ tab.name }}</option>
                </select>
            </div>
            <div class="hidden sm:block">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <p v-for="tab in tabs" @click="selectedTab = tab.id" :key="tab.name"
                        :class="[tab.id == selectedTab ? 'border-oversattare-blue text-oversattare-blue' : 'border-transparent text-oversattare-text-black hover:border-gray-300 hover:text-gray-700', 'whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium cursor-pointer select-none']"
                        :aria-current="tab.id == selectedTab ? 'page' : undefined">{{ tab.name }}</p>
                </nav>
            </div>
        </div>

        <div v-if="selectedTab == 'contact_info'" class="mt-8 mb-8 sm:mb-0">
            <form @submit.prevent="updateContactInfo">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXInput v-model:model="contactInfoForm.company" :label="$t('translations.profile.company_label')"
                        @change="contactInfoForm.validate('company')" :error="contactInfoForm.errors.company" />
                    <SBXInput v-model:model="contactInfoForm.company_no"
                        :label="$t('translations.profile.company_no_label')"
                        @change="contactInfoForm.validate('company_no')" :error="contactInfoForm.errors.company_no" />
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXInput v-model:model="contactInfoForm.first_name"
                        :label="$t('translations.profile.first_name_label')"
                        @change="contactInfoForm.validate('first_name')" :error="contactInfoForm.errors.first_name"
                        :instruction="$t('translations.global.mandatory')" />
                    <SBXInput v-model:model="contactInfoForm.last_name"
                        :label="$t('translations.profile.last_name_label')"
                        @change="contactInfoForm.validate('last_name')" :error="contactInfoForm.errors.last_name"
                        :instruction="$t('translations.global.mandatory')" />
                </div>

                <div class="grid grid-cols-1 gap-4">
                    <SBXInput v-model:model="contactInfoForm.address_1"
                        :label="$t('translations.profile.address_1_label')"
                        @change="contactInfoForm.validate('address_1')" :error="contactInfoForm.errors.address_1"
                        :instruction="$t('translations.global.mandatory')" />
                </div>

                <div class="grid grid-cols-1 gap-4">
                    <SBXInput v-model:model="contactInfoForm.address_2"
                        :label="$t('translations.profile.address_2_label')"
                        @change="contactInfoForm.validate('address_2')" :error="contactInfoForm.errors.address_2" />
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXInput v-model:model="contactInfoForm.postal_code"
                        :label="$t('translations.profile.postal_code_label')"
                        @change="contactInfoForm.validate('postal_code')" :error="contactInfoForm.errors.postal_code"
                        :instruction="$t('translations.global.mandatory')" />
                    <SBXInput v-model:model="contactInfoForm.city" :label="$t('translations.profile.city_label')"
                        @change="contactInfoForm.validate('city')" :error="contactInfoForm.errors.city"
                        :instruction="$t('translations.global.mandatory')" />
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXInput v-model:model="contactInfoForm.email" :label="$t('sbxadmin.profile.email_label')"
                        @change="contactInfoForm.validate('email')" :error="contactInfoForm.errors.email"
                        :instruction="$t('translations.global.mandatory')" />
                    <SBXInput v-model:model="contactInfoForm.phone_no"
                        :label="$t('translations.profile.phone_no_label')"
                        @change="contactInfoForm.validate('phone_no')" :error="contactInfoForm.errors.phone_no"
                        :instruction="$t('translations.global.mandatory')" />
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXToggle v-model:model="contactInfoForm.is_authorized"
                        :label="$t('translations.profile.is_authorized_label')" class="mt-4" />
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXInput v-model:model="contactInfoForm.authorization_id"
                        :label="$t('translations.profile.authorization_id_label')"
                        @change="contactInfoForm.validate('authorization_id')"
                        :error="contactInfoForm.errors.authorization_id" />
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <SBXToggle v-model:model="contactInfoForm.allow_text_messages"
                        :label="$t('translations.profile.sms_label')" class="mt-4" />
                </div>

                <SBXButton class="mt-4" :enabled="!contactInfoForm.processing">{{
                    $t('translations.profile.update_contact_info_button') }}</SBXButton>

                <SBXAlert v-if="profileSaved" :title="$t('translations.profile.saved_title')"
                    :message="$t('translations.profile.saved_message')" class="mt-4" />
            </form>
        </div>

        <div v-if="selectedTab == 'information'" class="mt-8 mb-8 sm:mb-0">
            <form @submit.prevent="updateInformation">
                <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-semibold">{{ $t('translations.profile.from_languages_label') }}</p>
                        <SBXComboBox v-model:model="informationForm.from_translation_languages"
                            :items="translationLanguages.data" valueFieldName="id" labelFieldName="name"
                            :selectedLabel="$t('translations.translator_applications.selected_languages_prompt')"
                            :error="informationForm.errors.from_translation_languages" />
                    </div>
                </div>

                <div class="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-semibold">{{ $t('translations.profile.to_languages_label') }}</p>
                        <SBXComboBox v-model:model="informationForm.to_translation_languages"
                            :items="translationLanguages.data" valueFieldName="id" labelFieldName="name"
                            :selectedLabel="$t('translations.translator_applications.selected_languages_prompt')"
                            :error="informationForm.errors.to_translation_languages" />
                    </div>
                </div>

                <div class="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-semibold">{{ $t('translations.profile.categories_label') }}</p>
                        <SBXComboBox v-model:model="informationForm.translation_categories"
                            :items="translationCategories.data" valueFieldName="id" labelFieldName="name"
                            :selectedLabel="$t('translations.translator_applications.selected_categories_prompt')"
                            :error="informationForm.errors.translation_categories" />
                    </div>
                </div>

                <SBXButton class="mt-8" :enabled="!informationForm.processing">{{
                    $t('translations.profile.update_information_button') }}</SBXButton>

                <SBXAlert v-if="profileSaved" :title="$t('translations.profile.saved_title')"
                    :message="$t('translations.profile.saved_message')" class="mt-4" />
            </form>
        </div>

        <div v-if="selectedTab == 'leads'" class="mt-8 mb-8 sm:mb-0">
            <div class="p-8 border rounded-2xl">
                <p class="text-5xl text-center font-semibold">{{ user.data.leads_count }}</p>
                <p class="text-xl text-center">{{ $t('translations.profile.current_lead_count_label') }}</p>
            </div>

            <form @submit.prevent="updateLeads">
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <SBXInput v-model:model="leadsForm.lead_count" :label="$t('translations.profile.lead_count_label')"
                        @change="leadsForm.validate('lead_count')" :error="leadsForm.errors.lead_count" />
                </div>

                <SBXButton class="mt-4" :enabled="!leadsForm.processing">{{
                    $t('translations.profile.update_leads_button') }}</SBXButton>

                <SBXAlert v-if="profileSaved" :title="$t('translations.profile.saved_title')"
                    :message="$t('translations.profile.saved_message')" class="mt-4" />
            </form>
        </div>


        <!-- <SBXGenericNotification :show="profileSaved" variant="success" :message="$t('translations.profile.saved_message')" @notificationCancelled="profileSaved = false" /> -->
    </SBXDefaultPageLayout>
</template>
