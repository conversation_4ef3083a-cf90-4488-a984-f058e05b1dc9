<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
  import SBXButton from '@sbxui/Buttons/SBXButton.vue';
  import SBXSelect from '@sbxui/Form/SBXSelect.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXToggle from '@sbxui/Form/SBXToggle.vue';

  import StepsDivider from '../../Global/Components/StepsDivider.vue';
  import SBXGenericNotification from '@sbxui/Notifications/SBXGenericNotification.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDefaultPageLayout,
      SBXButton,
      SBXSelect,
      SBXInput,
      SBXToggle,
      StepsDivider,
      SBXGenericNotification
    },

    props: {
      translationLanguages: Object,
      translationCategories: Object,
      errors: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.pageTitle;

      this.fromTranslationLanguages = [];
      this.toTranslationLanguages = [];
      for (var i = 0; i < this.translationLanguages.data.length; i++) {
        let currentTranslationLanguage = this.translationLanguages.data[i];

        this.fromTranslationLanguages.push({ id: currentTranslationLanguage.id, value: currentTranslationLanguage.name, isSelected: false });
        this.toTranslationLanguages.push({ id: currentTranslationLanguage.id, value: currentTranslationLanguage.name, isSelected: false });
      }

      this.categories = [];
      for (var i = 0; i < this.translationCategories.data.length; i++) {
        let currentTranslationCategory = this.translationCategories.data[i];

        this.categories.push({ id: currentTranslationCategory.id, value: currentTranslationCategory.name, isSelected: false });
      }
    },

    updated() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
      return {
        profileSaved: false,

        form: useForm('post', this.route('translators.store', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), {
          company: null,
          company_no: null,
          first_name: null,
          last_name: null,
          address_1: null,
          address_2: null,
          postal_code: null,
          city: null,
          email: null,
          phone_no: null,
          is_authorized: false,
          authorization_id: null,
          from_translation_languages: [],
          to_translation_languages: [],
          translation_categories: [],
          password: null,
          password_confirmation: null
        }),

        fromTranslationLanguages: [],
        toTranslationLanguages: [],
        categories: [],
      }
    },

    computed: {
      pageTitle() {
        return `${this.$t("translations.translators.create_title")}`;
      }
    },

    methods: {
      update() {
        var self = this;

        this.form.from_translation_languages = [];
        for (var i = 0; i < this.fromTranslationLanguages.length; i++) {
          let currentLanguage = this.fromTranslationLanguages[i];

          if (currentLanguage.isSelected) {
            this.form.from_translation_languages.push(currentLanguage.id);
          }
        }

        this.form.to_translation_languages = [];
        for (var i = 0; i < this.toTranslationLanguages.length; i++) {
          let currentLanguage = this.toTranslationLanguages[i];

          if (currentLanguage.isSelected) {
            this.form.to_translation_languages.push(currentLanguage.id);
          }
        }

        this.form.translation_categories = [];
        for (var i = 0; i < this.categories.length; i++) {
          let currentCategory = this.categories[i];

          if (currentCategory.isSelected) {
            this.form.translation_categories.push(currentCategory.id);
          }
        }

        if (!this.form.processing) {
          this.form.submit({
            preserveScroll: true,
            onSuccess: page => {
              self.profileSaved = true;
              self.form.password = null;
              self.form.password_confirmation = null;

              setTimeout(function () {
                self.profileSaved = false;
              }, 3000);
            }
          });
        }
      },
    }
  }
</script>

<template>
  <SBXDefaultPageLayout>
    <form @submit.prevent="update">
      <div class="grid grid-cols-2 gap-12">
        <div>
          <SBXInput v-model:model="form.company" :label="$t('translations.profile.company_label')" @change="form.validate('company')" :error="form.errors.company" />
          <SBXInput v-model:model="form.company_no" :label="$t('translations.profile.company_no_label')" @change="form.validate('company_no')" :error="form.errors.company_no" />
          <SBXInput v-model:model="form.first_name" :label="$t('sbxadmin.profile.first_name_label')" @change="form.validate('first_name')" :error="form.errors.first_name" :instruction="$t('translations.global.mandatory')"/>
          <SBXInput v-model:model="form.last_name" :label="$t('sbxadmin.profile.last_name_label')" @change="form.validate('last_name')" :error="form.errors.last_name" :instruction="$t('translations.global.mandatory')" />
          <SBXInput v-model:model="form.address_1" :label="$t('translations.profile.address_1_label')" @change="form.validate('address_1')" :error="form.errors.address_1" :instruction="$t('translations.global.mandatory')" />
          <SBXInput v-model:model="form.address_2" :label="$t('translations.profile.address_2_label')" @change="form.validate('address_2')" :error="form.errors.address_2" />
          <SBXInput v-model:model="form.postal_code" :label="$t('translations.profile.postal_code_label')" @change="form.validate('postal_code')" :error="form.errors.postal_code" :instruction="$t('translations.global.mandatory')" />
          <SBXInput v-model:model="form.city" :label="$t('translations.profile.city_label')" @change="form.validate('city')" :error="form.errors.city" :instruction="$t('translations.global.mandatory')" />
          <SBXInput v-model:model="form.email" :label="$t('sbxadmin.profile.email_label')"  @change="form.validate('email')" :error="form.errors.email" :instruction="$t('translations.global.mandatory')" />
          <SBXInput v-model:model="form.phone_no" :label="$t('translations.profile.phone_no_label')"  @change="form.validate('phone_no')" :error="form.errors.phone_no" :instruction="$t('translations.global.mandatory')" />
          <SBXToggle v-model:model="form.is_authorized" :label="$t('translations.profile.is_authorized_label')" class="mt-4" />
          <SBXInput v-model:model="form.authorization_id" :label="$t('translations.profile.authorization_id_label')"  @change="form.validate('authorization_id')" :error="form.errors.authorization_id" />
          <SBXInput type="password" v-model:model="form.password" :label="$t('sbxadmin.profile.password_label')" :instruction="$t('sbxadmin.profile.password_instruction')" @change="form.validate('password')" :error="form.errors.password" />
          <SBXInput type="password" v-model:model="form.password_confirmation" :label="$t('sbxadmin.profile.password_confirmation_label')" :instruction="$t('sbxadmin.profile.password_instruction')"  @change="form.validate('password_confirmation')" :error="form.errors.password_confirmation" />
        </div>

        <div>
          <!-- START Translate from Languages -->
          <h3 class="text-xl font-semibold mt-4">{{ $t('translations.translator_applications.step_2_title') }}</h3>

          <div class="mt-2">
            <fieldset>
              <div class="space-y-1">
                <div v-for="(language, languageIndex) in fromTranslationLanguages" class="relative flex items-start">
                  <div class="flex h-6 items-center">
                    <input v-model="fromTranslationLanguages[languageIndex].isSelected" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
                  </div>

                  <div class="ml-3 text-sm leading-6">
                    <label class="font-medium text-gray-900">{{ language.value }}</label>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
          <!-- END Translate from Languages -->

          <!-- START Translate to Languages -->
          <h3 class="mt-8 text-xl font-semibold mt-4">{{ $t('translations.translator_applications.step_3_title') }}</h3>

          <div class="mt-2">
            <fieldset>
              <div class="space-y-1">
                <div v-for="(language, languageIndex) in toTranslationLanguages" class="relative flex items-start">
                  <div class="flex h-6 items-center">
                    <input v-model="toTranslationLanguages[languageIndex].isSelected" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
                  </div>

                  <div class="ml-3 text-sm leading-6">
                    <label class="font-medium text-gray-900">{{ language.value }}</label>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
          <!-- END Translate to Languages -->

          <!-- START Translate Services -->
          <h3 class="mt-8 text-xl font-semibold mt-4">{{ $t('translations.translator_applications.step_4_title') }}</h3>

          <div class="mt-2">
            <fieldset>
              <div class="space-y-1">
                <div v-for="(category, categoryIndex) in categories" class="relative flex items-start">
                  <div class="flex h-6 items-center">
                    <input v-model="categories[categoryIndex].isSelected" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
                  </div>

                  <div class="ml-3 text-sm leading-6">
                    <label class="font-medium text-gray-900">{{ category.value }}</label>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
          <!-- END Translate Services -->
        </div>
      </div>

      <SBXButton class="mt-4">{{ $t('translations.translators.update_button') }}</SBXButton>
    </form>

    <SBXGenericNotification :show="profileSaved" variant="success" :message="$t('translations.profile.saved_message')" @notificationCancelled="profileSaved = false" />
  </SBXDefaultPageLayout>
</template>
