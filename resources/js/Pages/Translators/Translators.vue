<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
  import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';
  import SBXGenericNotification from '@sbxui/Notifications/SBXGenericNotification.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDataTable,
      SBXFilterBar,
      SBXGenericNotification
    },

    props: {
      translationLanguages: Object,
      translationCategories: Object,
      translators: Object,
      filters: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t("translations.translators.title");

      if (this.$page.props.flash.authorization != null) {
        this.showAuthorizationNotification = true;
      }

      console.log("Translators:", this.translators);
    },

    updated() {
      this.$page.props.page_info.title_label = this.$t("translations.translators.title");
    },

    data() {
      return {
        columns: [
          { key: 'customerslot', label: this.$t('translations.translators.translator_label') },
          { key: 'credit_count', label: this.$t('translations.translators.credit_count_label') },
          { key: 'created_at', label: this.$t('translations.translation_assignments.date_label') },
          { key: 'contactslot', label: this.$t('translations.translator_applications.admin.contact_label') },
          { key: 'infoslot', label: this.$t('translations.translators.other_info_label') }
        ],

        translatorFilters: {
          search: this.filters.search,
          authorized: this.filters.authorized,
          from_language: this.filters.from_language,
          to_language: this.filters.to_language,
          category: this.filters.category
        },

        showAuthorizationNotification: false
      }
    },

    computed: {
      authorizationMessage() {
        if (this.$page.props.flash.authorization == null) {
          return '';
        }

        return this.$page.props.flash.authorization;
      }
    },

    methods: {
      changeFilters() {
        this.$inertia.get(this.route('translators', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.translatorFilters, {
          preserveState: false,
          replace: true
        });
      },

      searchValueChanged(search) {
        this.translatorFilters.search = search;
      },

      authorizationNotificationCancelled() {
        this.showAuthorizationNotification = false;
      },
    }
  }
</script>

<template>
  <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="translatorFilters" searchRoute="translators" :placeholder="$t('translations.global.search')">
    <template v-slot:filterArea>
      <div class="flex justify-end">
        <select @change="changeFilters" v-model="translatorFilters.authorized" class="mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
          <option value="all">Alla</option>
          <option value="authorized">Auktoriserade</option>
          <option value="unauthorized">Ej auktoriserade</option>
        </select>
      </div>

      <div class="flex justify-end">
        <select @change="changeFilters" v-model="translatorFilters.from_language" class="mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
          <option value="all">Alla</option>
          <option v-for="language in translationLanguages.data" :value="language.id">
            {{ language.name }}
          </option>
        </select>
      </div>

      <div class="flex justify-end">
        <select @change="changeFilters" v-model="translatorFilters.to_language" class="mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
          <option value="all">Alla</option>
          <option v-for="language in translationLanguages.data" :value="language.id">
            {{ language.name }}
          </option>
        </select>
      </div>

      <div class="flex justify-end">
        <select @change="changeFilters" v-model="translatorFilters.category" class="mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
          <option value="all">Alla</option>
          <option v-for="category in translationCategories.data" :value="category.id">
            {{ category.name }}
          </option>
        </select>
      </div>
    </template>
  </SBXFilterBar>

  <div class="flex flex-col">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <SBXDataTable
            :columns="columns"
            :items="translators.data"
            :showAddButton="true"
            addRoute="translators.create"
            :addButtonText="$t('translations.translators.add_button')"
            :showEditButton="true"
            editRoute="translators.edit"
            :showDeleteButton="true"
            deleteRoute="translators.destroy"
            :deleteDialogTitle="$t('translations.translators.delete_dialog_title')"
            :deleteDialogMessage="$t('translations.translators.delete_dialog_message')"
            :deleteDialogOKText="$t('translations.translators.delete_dialog_ok_button')"
            :deleteDialogCancelText="$t('translations.global.cancel')"
            :paginator="translators.meta"
          >

            <template v-slot:customerslot="row">
              <div>
                <p class="text-sm font-semibold">{{ $t('translations.global.customer_id') }}: {{ row.item.translator_id }}</p>
                <p class="text-sm font-semibold">{{ row.item.company }}</p>
                <p class="text-sm font-semibold">{{ row.item.first_name }} {{ row.item.last_name }}</p>
                <p class="text-sm">{{ row.item.address_1 }}</p>
                <p class="text-sm">{{ row.item.address_2 }}</p>
                <p class="text-sm">{{ row.item.postal_code }} {{ row.item.city }}</p>
              </div>
            </template>

            <template v-slot:contactslot="row">
              <div>
                <p class="text-sm"><strong>E-post: </strong><a :href="`mailto:${row.item.email}`">{{ row.item.email }}</a></p>
                <p class="text-sm"><strong>Telefon: </strong><a :href="`tel:${row.item.phone_no}`">{{ row.item.phone_no }}</a></p>
              </div>
            </template>

            <template v-slot:infoslot="row">
              <div>
                <p class="text-sm"><strong>{{ $t('translations.translators.from_label') }}: </strong>{{ row.item.from_translation_languages }}</p>
                <p class="text-sm"><strong>{{ $t('translations.translators.to_label') }}: </strong>{{ row.item.to_translation_languages }}</p>
                <p class="text-sm"><strong>{{ $t('translations.translators.categories_label') }}: </strong>{{ row.item.translation_categories }}</p>
                <p v-if="row.item.is_authorized" class="text-sm"><strong>{{ $t('translations.translators.authorized_id_label') }}: </strong>{{ row.item.authorization_id }}</p>
                <p v-else class="text-sm"><strong>{{ $t('translations.translators.authorized_id_label') }}: </strong> -</p>
              </div>
            </template>

          </SBXDataTable>
        </div>
      </div>
    </div>
  </div>

  <SBXGenericNotification :show="showAuthorizationNotification" variant="warning" :message="authorizationMessage" @notificationCancelled="authorizationNotificationCancelled" />
</template>
