<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { CheckIcon, CheckCircleIcon } from '@heroicons/vue/24/solid';
import { DocumentTextIcon } from '@heroicons/vue/24/outline';
import { RadioGroup, RadioGroupOption, Disclosure } from '@headlessui/vue';


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {

    components: {
        CheckIcon,
        CheckCircleIcon,
        DocumentTextIcon,
        RadioGroup,
        RadioGroupOption,
        Disclosure
    },
    props: {
        assignment: Object,
    },

    data() {
        console.log(this.assignment.data);
        return {
            selectedOffer: null,
            processingData: false,
            timeUntilDeadline: 0,

            form: useForm('put', this.route('translation_assignment_market.accept_bid', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.data.assignment_id, this.assignment.data.customer_email]), {
                accepted_bid_id: null,

            }),
        }
    },

    mounted() {
        this.timeUntilDeadline = new Date(this.assignment.data.deadline) - new Date();
        this.selectedOffer = this.assignment.data.answered_by.id;

    },
    computed: {
        disableAcceptButton() {
            if (this.selectedOffer == null || this.processingData) {
                return true;
            }
            return false;
        }
    },

    methods: {
        acceptOffer() {
            console.log("Accepting offer is possible for:", this.selectedOffer);
            console.log("Assignment", this.form)

            if (!this.form.processing) {
                this.processingData = true;
                setTimeout(() => this.processingData = false, 900);
                this.form.accepted_bid_id = this.selectedOffer;
                this.form.submit({
                    preserveScroll: true,
                    preserveState: false,
                    onSuccess: page => {
                        // self.updateNoteOpen = false; {marketCode}/{languageCode}/customer/{translationAssignmentID}/{customerEmail}/payment/{translationAssignmentBidID}
                        window.location.href = (this.route('translation_assignment_market.process_bid_payment', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.data.assignment_id, this.assignment.data.customer_email, this.form.accepted_bid_id]));
                        console.log("Page Updated", page);
                    },
                    onError: errors => {
                        console.log("Page Errors", errors);
                    },
                });
            };
        }
    }
}
</script>

<template>
    <div class="py-3">
        <div v-if="assignment.data.bid_count > 0" class="py1 sm:py-4 border-t border-gray-200">
            <span class="text-sm font-semibold text-red-500">För att garantera leverans i tid behöver du bekräfta och
                betala
                snarast.</span>
            <form @submit.prevent="acceptOffer">
                <fieldset aria-label="Translations offers">
                    <RadioGroup v-model="selectedOffer" class="my-3 grid grid-cols-1 gap-y-6 md:grid-cols-3 sm:gap-x-4">
                        <RadioGroupOption as="template" :key="assignment.data.answered_by.id"
                            :value="assignment.data.answered_by.id" v-slot="{ active, checked }">
                            <div
                                :class="[active ? 'border-green-600 ring-2 ring-green-600' : 'border-gray-300', 'relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none']">
                                <span class="flex flex-1 ">
                                    <span class="flex flex-col gap-y-1">
                                        <span class="block text-large font-medium text-gray-900">{{
                                            $t('translations.customer_view.offer_title') }}
                                        </span>
                                        <span class="flex items-center text-sm text-gray-500">
                                            <span v-if="assignment.data.answered_by.is_authorized"
                                                class="rounded-md bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-700 text-wrap">
                                                {{
                                                    $t('translations.translator_applications.is_authorized_label')
                                                }}</span>
                                        </span>
                                        <span v-if="assignment.data.answered_by.estimated_delivery"
                                            class="flex rounded-md ring-1 ring-inset ring-gray-200 px-1.5 py-0.5 text-xs font-medium text-gray-700 text-wrap">{{
                                                $t('translations.translator_applications.estimated_delivery_label')
                                            }}
                                            {{
                                                assignment.data.answered_by.estimated_delivery
                                            }}</span>
                                        <span class="mt-auto pt-6 text-3xl font-medium text-gray-900">
                                            {{
                                                (assignment.data.answered_by.price +
                                                    (assignment.data.answered_by.price * this.$page.props.market_vat_rate /
                                                        100)).toFixed(2) }} kr <span class="text-sm text-gray-400">inkl.moms</span>
                                        </span>
                                    </span>
                                </span>
                                <CheckCircleIcon :class="[!checked ? 'invisible' : '', 'h-5 w-5 text-green-600']"
                                    aria-hidden="true" />
                                <span
                                    :class="[active ? 'border' : 'border-2', checked ? 'border-green-600' : 'border-transparent', 'pointer-events-none absolute -inset-px rounded-lg']"
                                    aria-hidden="true" />

                            </div>
                        </RadioGroupOption>
                        <RadioGroupOption v-if="assignment.data.next_best_bid" as="template"
                            :key="assignment.data.next_best_bid.id" :value="assignment.data.next_best_bid.id"
                            v-slot="{ active, checked }">
                            <div
                                :class="[active ? 'border-green-600 ring-2 ring-green-600' : 'border-gray-300', 'relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none']">
                                <span class="flex flex-1 ">
                                    <span class="flex flex-col gap-y-1">
                                        <span class="block text-large font-medium text-gray-900">{{
                                            $t('translations.customer_view.offer_faster_title') }}
                                        </span>
                                        <span class="flex items-center text-sm text-gray-500">
                                            <span v-if="assignment.data.next_best_bid.is_authorized"
                                                class="rounded-md bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-700 text-wrap">
                                                {{
                                                    $t('translations.translator_applications.is_authorized_label')
                                                }}</span>
                                        </span>
                                        <span v-if="assignment.data.next_best_bid.estimated_delivery"
                                            class="flex rounded-md ring-1 ring-inset ring-gray-200 px-1.5 py-0.5 text-xs font-medium text-gray-700 text-wrap">{{
                                                $t('translations.translator_applications.estimated_delivery_label')
                                            }}
                                            {{
                                                assignment.data.next_best_bid.estimated_delivery
                                            }}</span>
                                        <span class="mt-auto pt-6 text-3xl font-medium text-gray-900">
                                            {{
                                                (assignment.data.next_best_bid.price +
                                                    (assignment.data.next_best_bid.price * this.$page.props.market_vat_rate /
                                                        100)).toFixed(2) }} kr <span class="text-sm text-gray-400">inkl.moms</span>
                                        </span>
                                    </span>
                                </span>
                                <CheckCircleIcon :class="[!checked ? 'invisible' : '', 'h-5 w-5 text-green-600']"
                                    aria-hidden="true" />
                                <span
                                    :class="[active ? 'border' : 'border-2', checked ? 'border-green-600' : 'border-transparent', 'pointer-events-none absolute -inset-px rounded-lg']"
                                    aria-hidden="true" />

                            </div>
                        </RadioGroupOption>
                        <!-- <div v-if="assignment.data.bid_count < assignment.data.bids_allowed"
                            v-for="i in assignment.data.bids_allowed - assignment.data.bid_count" :key="i"
                            class="relative block rounded-lg border-dashed border-2 border-gray-300 bg-white p-12">
                        </div> -->
                    </RadioGroup>
                </fieldset>
                <div class="mt-1 flex items-center justify-end gap-x-6 border-t py-4">
                    <button type="submit" :disabled="disableAcceptButton"
                        :class="disableAcceptButton ? 'cursor-not-allowed' : 'cursor-pointer'"
                        class="transition inline-flex items-center rounded-md bg-green-700 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600 disabled:bg-gray-300">
                        <span v-if="!processingData">{{
                            $t('translations.translation_assignments.accept_pay_button') }}
                        </span>
                        <span v-else class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            {{
                                $t('translations.translation_assignments.proccessing_payment')
                            }}
                        </span>
                    </button>
                </div>
            </form>
        </div>
        <div v-else class="py-1 sm:py-4">
            <div class="relative block w-full rounded-lg border-2 p-12 border-dashed border-gray-300 text-center">
                <DocumentTextIcon class="h-12 w-12 text-gray-400 mx-auto" />
                <span class="mt-2 block text-sm font-semibold text-gray-900"> {{
                    $t('translations.assignments.no_offers')
                    }}</span>
            </div>
        </div>
    </div>

</template>
