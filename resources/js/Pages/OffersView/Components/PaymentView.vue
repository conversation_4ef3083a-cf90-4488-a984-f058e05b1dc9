<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { CheckIcon, CheckCircleIcon } from '@heroicons/vue/24/solid';
import { DocumentTextIcon } from '@heroicons/vue/24/outline';
import { RadioGroup, RadioGroupOption, Disclosure } from '@headlessui/vue';


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {

    components: {
        CheckIcon,
        CheckCircleIcon,
        DocumentTextIcon,
        RadioGroup,
        RadioGroupOption,
        Disclosure
    },
    props: {
        bid: Object,
    },

    data() {
        return {
            processingData: false,
            statuses: {
                Complete: 'text-green-700 bg-green-50 ring-green-600/20',
                'In progress': 'text-gray-600 bg-gray-50 ring-gray-500/10',
                Archived: 'text-yellow-800 bg-yellow-50 ring-yellow-600/20',
            },

            project: {
                id: 1,
                name: 'GraphQL API',
                href: '#',
                status: 'Complete',
                createdBy: 'Leslie Alexander',
                dueDate: 'March 17, 2023',
                dueDateTime: '2023-03-17T00:00Z',
            },
        }
    },

    mounted() {

    },
    computed: {
    },

    methods: {

    }
}
</script>

<template>
    <div class="mb-4 py-1 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-500 ">{{
            $t('translations.translation_store.payment_title') }} översikt
        </h3>
    </div>
    <div
        class="flex items-center justify-between gap-x-6 py-5 bg-gray-50 ring-1 ring-inset ring-gray-200 rounded-lg p-5">
        <div class="min-w-0 ">
            <div class="flex items-start gap-x-3">
                <p class="text-lg/6 font-semibold text-gray-900">Order #{{ bid.payment_information.order_no }}</p>
                <p
                    class="text-green-700 bg-green-50 ring-green-600/20 mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset">
                    Betalad</p>
            </div>
            <div class="mt-1 flex items-center gap-x-2 text-xs/5 text-gray-500">
                <p class="whitespace-nowrap">
                    Betalad den <time :datetime="bid.payment_information.date">{{ bid.payment_information.date
                        }}</time>
                </p>
                <svg viewBox="0 0 2 2" class="h-0.5 w-0.5 fill-current">
                    <circle cx="1" cy="1" r="1" />
                </svg>
                <p class="truncate">Beräknad leverans <time :datetime="bid.estimated_delivery">{{
                    bid.estimated_delivery
                        }}</time></p>
            </div>
        </div>
        <div class="flex flex-none items-center gap-x-4">
            <span
                class="text-lg font-medium text-gray-900 rounded-md ring-1 ring-inset ring-gray-50 px-2.5 py-1.5 bg-gray-50">
                {{
                    bid.price +
                    (bid.price * this.$page.props.market_vat_rate
                        /
                        100) }} kr <span class="text-xs text-gray-400">inkl.moms</span>
            </span>
        </div>
    </div>
    <!-- <div class="py-3">
        <div class="py-1 border-b border-gray-200">
            <h3 class="text-xl font-semibold leading-6 text-gray-800 ">{{
                $t('translations.translation_store.payment_title') }} översikt
            </h3>
        </div>
        <div class="border-green-600 relative flex cursor-pointer rounded-lg border bg-white p-4 mt-4">
            <span class="flex flex-1 ">
                <span class="flex flex-col gap-y-1">
                    <span class="block text-large font-medium text-gray-900">Offert
                    </span>
                    <span class="flex items-center text-sm text-gray-500 ">
                        <span v-if="assignment.data.accepted_bid.is_authorized"
                            class="rounded-md bg-green-100 px-1.5 py-0.5 text-xs font-medium text-green-700 text-wrap">
                            {{
                                $t('translations.translator_applications.is_authorized_label')
                            }}</span>
                    </span>
                    <span v-if="assignment.data.accepted_bid.estimated_delivery"
                        class="flex rounded-md ring-1 ring-inset ring-gray-200 px-1.5 py-0.5 text-xs font-medium text-gray-700 text-wrap">{{
                            $t('translations.translator_applications.estimated_delivery_label')
                        }}
                        {{
                            assignment.data.accepted_bid.estimated_delivery
                        }}</span>
                    <span class="mt-6 text-3xl font-medium text-gray-900">
                        {{
                            assignment.data.accepted_bid.price +
                            (assignment.data.accepted_bid.price * this.$page.props.market_vat_rate
                                /
                                100) }} kr <span class="text-sm text-gray-400">inkl.moms</span>
                    </span>
                </span>
            </span>
            <CheckCircleIcon class="h-5 w-5 text-green-600" aria-hidden="true" />


        </div>
        <div class="mt-1 flex items-center justify-end gap-x-6 border-t py-4">

        </div>
    </div> -->
</template>
