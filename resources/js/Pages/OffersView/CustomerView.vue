<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/


import { defineComponent, ref, onMounted, computed } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import OffersView from './Components/OffersView.vue';
import PaymentView from './Components/PaymentView.vue';

import { CheckIcon, CheckCircleIcon } from '@heroicons/vue/24/solid';
import { DocumentTextIcon, LanguageIcon, ArchiveBoxArrowDownIcon } from '@heroicons/vue/24/outline';
import { RadioGroup, RadioGroupOption, Disclosure } from '@headlessui/vue';

export default {

    components: {
        SBXDefaultPageLayout,
        OffersView,
        PaymentView,
        CheckIcon,
        CheckCircleIcon,
        DocumentTextIcon,
        ArchiveBoxArrowDownIcon,
        RadioGroup,
        RadioGroupOption,
        Disclosure,
        LanguageIcon
    },

    props: {
        assignment: Object,
        errors: Object
    },

    layout: null,

    setup(props) {
        const steps = ref([
            { id: '01', name: 'Granskning', href: '#', status: '' },
            { id: '02', name: 'Offert', href: '#', status: '' },
            { id: '03', name: 'Betalning', href: '#', status: '' },
            { id: '04', name: 'Översättning påbörjat', href: '#', status: '' },
            { id: '05', name: 'Uppdraget slutfört', href: '#', status: '' },
        ]);

        const updatedSteps = computed(() => {
            return steps.value.map((step, index) => {
                if (index === 0) {
                    step.status = props.assignment.data.approved_at ? 'complete' : 'current';
                } else if (index === 4) {
                    const previousStep = steps.value[index - 1];
                    if (props.assignment.data.completed_at) {
                        previousStep.status = 'complete';
                        step.status = 'complete';
                    }
                } else {
                    const previousStep = steps.value[index - 1];
                    step.status = previousStep.status === 'complete'
                        ? (shouldBeComplete(step) ? 'complete' : 'current')
                        : 'upcoming';
                }
                return step;
            });
        });

        function shouldBeComplete(step) {

            return step.id === '02' || step.id === '03' ? props.assignment.data.bid_accepted : false;

        }

        onMounted(() => {
            steps.value = updatedSteps.value;
            document.body.classList.add("bg-oversattare-green-light");

        });

        return {
            steps,
            updatedSteps,
        };
    },

    methods: {
        downloadTranslation() {
            console.log('Download translation');
            window.location.href = this.route('assignment.download_translations', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.data.assignment_id, this.assignment.data.customer_email]);
        }
    },

    data() {
        return {
            pageTitle: 'Create Translation Assignment',

        }
    },

    mounted() {
        this.$page.props.page_info.title_label = this.pageTitle;
    },

    updated() {
        this.$page.props.page_info.title_label = this.pageTitle;
    },
}
</script>

<template>
    <main class="flex-1 bg-oversattare-green-light h-screen">
        <Disclosure as="nav" class="bg-white shadow">
            <div class="mx-auto px-2 sm:px-6 lg:px-8">
                <div class="flex h-16 items-center">
                    <div class="inset-y-0 flex items-center">
                        <div class="flex flex-shrink-0 items-center pl-2 sm:pl-0">
                            <img class="h-8 w-auto items-center" src="/graphics/oversattare_nu.png"
                                alt="Översättare.nu" />
                        </div>
                    </div>
                </div>
            </div>
        </Disclosure>

        <div class="py-4">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                <div class="min-h-full py-12 sm:px-6 lg:px-8 bg-grey ">
                    <div class="flex flex-col md:flex-row justify-between max-w-7xl py-3 px-1 sm:px-3 lg:px-4  ">
                        <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-800">
                            {{ $t('translations.customer_view.order_title') }} #{{
                                assignment.data.assignment_id
                            }}</h1>
                        <div class="flex md:justify-end md:items-end text-sm text-gray-500 mt-1 md:mt-0">
                            {{ $t('translations.customer_view.order_created_at_title') }} <span
                                class="ml-1 font-bold ">{{ assignment.data.created_date }}</span>
                        </div>
                    </div>
                    <div class="sm:mx-auto sm:w-full">
                        <div class="bg-white px-3 py-3 shadow sm:w-full sm:rounded-lg sm:px-6">
                            <div class="bg-white justify-center">
                                <div class="flex flex-col md:flex-row gap-4 md:gap-0 mb-1">
                                    <div
                                        class="flex-none content-center border-b md:border-b-0 md:border-r px-4 py-12 sm:px-6 lg:px-8">
                                        <nav class="flex justify-center" aria-label="Progress">
                                            <ol role="list" class="space-y-6">
                                                <li v-for="step in steps" :key="step.name">
                                                    <div v-if="step.status === 'complete'" class="group">
                                                        <span class="flex items-start">
                                                            <span
                                                                class="relative flex h-5 w-5 flex-shrink-0 items-center justify-center">
                                                                <CheckCircleIcon class="h-full w-full text-green-600 "
                                                                    aria-hidden="true" />
                                                            </span>
                                                            <span class="ml-3 text-sm font-medium text-gray-500 ">{{
                                                                step.name
                                                            }}</span>
                                                        </span>
                                                    </div>
                                                    <div v-else-if="step.status === 'current'" class="flex items-start"
                                                        aria-current="step">
                                                        <span
                                                            class="animate-pulse relative flex h-5 w-5 flex-shrink-0 items-center justify-center"
                                                            aria-hidden="true">
                                                            <span class="absolute h-4 w-4 rounded-full bg-green-200" />
                                                            <span
                                                                class="relative block h-2 w-2 rounded-full bg-green-600" />
                                                        </span>
                                                        <span class="ml-3 text-sm font-medium text-green-700">{{
                                                            step.name }}</span>
                                                    </div>
                                                    <div v-else class="group">
                                                        <div class="flex items-start">
                                                            <div class="relative flex h-5 w-5 flex-shrink-0 items-center justify-center"
                                                                aria-hidden="true">
                                                                <div class="h-2 w-2 rounded-full bg-gray-300 " />
                                                            </div>
                                                            <p class="ml-3 text-sm font-medium text-gray-500">
                                                                {{ step.name }}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ol>
                                        </nav>
                                    </div>
                                    <div class="flex-auto px-4 sm:px-6 lg:px-8">
                                        <dl class="grid grid-cols-1 sm:grid-cols-2 px-4 sm:px-6 lg:px-8">
                                            <div class="py-3 sm:col-span-1 sm:px-0">
                                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                    $t('translations.my_assignments.assignment_detail.to_language_label')
                                                }}</dt>
                                                <dd class="mt-1 text-sm leading-6 text-gray-700 ">{{
                                                    assignment.data.from_translation_language }}</dd>
                                            </div>
                                            <div class="py-3 sm:col-span-1 sm:px-0">
                                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                    $t('translations.my_assignments.assignment_detail.from_language_label')
                                                }}</dt>
                                                <dd class="mt-1 text-sm leading-6 text-gray-700 ">{{
                                                    assignment.data.to_translation_language }}</dd>
                                            </div>

                                            <div class="py-3 sm:px-0">
                                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                    $t('translations.my_assignments.assignment_detail.file_count_label')
                                                }}</dt>
                                                <dd v-if="assignment.data.files_count != null && assignment.data.files_count > 0"
                                                    class="mt-1 text-sm leading-6 text-gray-700 ">{{
                                                        assignment.data.files_count }}</dd>
                                                <dd v-else class="mt-1 text-sm leading-6 text-gray-700 ">
                                                    -</dd>
                                            </div>
                                            <div class="py-3 sm:px-0">
                                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                    $t('translations.my_assignments.assignment_detail.latest_delivery_date_label')
                                                }}</dt>
                                                <dd v-if="assignment.data.last_delivery_date"
                                                    class="mt-1 text-sm leading-6 text-gray-700 ">{{
                                                        assignment.data.last_delivery_date }}</dd>
                                                <dd v-else class="mt-1 text-sm leading-6 text-gray-700 ">-</dd>
                                            </div>
                                            <div class="py-3 sm:col-span-1 sm:px-0">
                                                <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                    $t('translations.my_assignments.assignment_detail.authorization_label')

                                                }}
                                                </dt>

                                                <dd v-if="assignment.data.is_authorization_required"
                                                    class="text-sm font-semibold leading-6 text-green-700 ">
                                                    {{
                                                        $t('translations.global.yes') }}<br /><span
                                                        class="text-xs text-gray-500 font-normal">*Dina
                                                        dokument
                                                        översätts, stämplas och signeras av en
                                                        auktoriserad translator</span></dd>
                                                <dd v-if="!assignment.data.is_authorization_required"
                                                    class="text-sm font-semibold leading-6 text-red-700">
                                                    {{ $t('translations.global.no') }}</dd>

                                            </div>
                                        </dl>
                                    </div>
                                    <div class="flex-auto px-4 sm:px-6 lg:px-8 border-t md:border-l md:border-t-0 py-3 md:py-0"
                                        v-if="assignment.data.accepted_bid">
                                        <h3 class="py-3 font-semibold text-gray-700 ">{{
                                            $t('translations.my_assignments.assignment_detail.delivery_address') }}</h3>
                                        <div class="bg-green-50 p-4 font-medium text-sm text-gray-900 rounded-lg ">
                                            {{ assignment.data.accepted_bid.payment_information.delivery_address.name }}
                                            <br />
                                            {{ assignment.data.accepted_bid.payment_information.delivery_address.address
                                            }}
                                            <br />
                                            {{ assignment.data.accepted_bid.payment_information.delivery_address.zip }}
                                            {{
                                                assignment.data.accepted_bid.payment_information.delivery_address.city }}
                                            <br />
                                            {{ assignment.data.accepted_bid.payment_information.delivery_address.country
                                            }}
                                        </div>
                                        <div class="py-1">
                                            <a :href="'mailto:<EMAIL>?subject=' + $t('translations.my_assignments.assignment_detail.mail_change_adress_subject') + ' ' + assignment.data.assignment_id + '&body=' + $t('translations.my_assignments.assignment_detail.mail_change_adress_body')"
                                                class="text-gray-500 hover:hover:text-gray-900 text-sm py-3">{{
                                                    $t('translations.my_assignments.assignment_detail.change_delivery_address')
                                                }}</a>
                                        </div>
                                    </div>
                                </div>
                                <OffersView v-if="!assignment.data.bid_accepted" :assignment="assignment" />

                                <PaymentView v-if="assignment.data.bid_accepted" :bid="assignment.data.accepted_bid" />
                                <div class="my-4 p-12 border-t border-dashed border-gray-300"
                                    v-if="assignment.data.bid_accepted && !assignment.data.completed_at">
                                    <div class="text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
                                            aria-hidden="true" viewBox="2.5 2.5 19 19">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802">
                                            </path>
                                        </svg>
                                        <h2 class="mt-3 text-lg font-semibold text-gray-900">Översättningen är
                                            påbörjad</h2>
                                        <p class="mt-1 text-sm text-gray-500">Din översättare har fått
                                            betalningsbekräftelsen och har nu påbörjat översättningsprocessen. </p>
                                        <p class="mt-1 text-xs text-gray-500">Preliminär leverans är beräknad till
                                            <strong>{{
                                                assignment.data.accepted_bid.estimated_delivery }} </strong>
                                        </p>
                                    </div>
                                </div>
                                <div class="my-4 p-12 border-t border-dashed border-gray-300"
                                    v-if="assignment.data.completed_at">
                                    <div class="text-center">
                                        <CheckCircleIcon class="mx-auto h-24 w-24 text-green-700" aria-hidden="true" />

                                        <h2 class="mt-3 text-lg font-semibold text-green-700">Översättningen är
                                            avsluad</h2>
                                        <p class="mt-1 text-sm text-gray-500">Din översättning är nu redo att
                                            hämtas,
                                            tryck på knappen nedan för att påbörja nerladdning </p>
                                        <div class="flex justify-center border-t py-4 mt-4">
                                            <button type="button"
                                                class="inline-flex items-center gap-x-2 rounded-md bg-green-700 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-500"
                                                @click="downloadTranslation()">
                                                <ArchiveBoxArrowDownIcon class="-ml-0.5 size-5" aria-hidden="true" />
                                                Ladda ner översättningen
                                            </button>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</template>
