<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
  import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';

  import { CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDataTable,
      SBXFilterBar,
      CheckCircleIcon,
      XCircleIcon
    },

    props: {
      users: Object,
      filters: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t('sbxadmin.users.title');
    },

    updated() {
      this.$page.props.page_info.title_label = this.$t('sbxadmin.users.title');
    },

    data() {
      return {
        columns: [
          { key: 'name', label: this.$t('sbxadmin.users.name_label') },
          { key: 'emailslot', label: this.$t('sbxadmin.users.email_label') },
          { key: 'role_name', label: this.$t('sbxadmin.users.role_label') }
        ],

        userFilters: {
          search: this.filters.search
        }
      }
    },

    methods: {
      changeFilters() {
        this.$inertia.get(this.route('sydfisk_users', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.userFilters, {
          preserveState: false,
          replace: true
        });
      },

      searchValueChanged(search) {
        this.userFilters.search = search;
      },
    }
  }
</script>

<template>
  <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="userFilters" searchRoute="sydfisk_users" :placeholder="$t('sbxadmin.global.search')">
  </SBXFilterBar>

  <div class="flex flex-col">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <SBXDataTable
            :columns="columns"
            :items="users.data"
            :showAddButton="true"
            :addButtonText="$t('sbxadmin.users.create_button')"
            addRoute="sydfisk_users.create"
            :showEditButton="true"
            editRoute="sydfisk_users.edit"
            :showDeleteButton="true"
            deleteRoute="sydfisk_users.destroy"
            :deleteDialogTitle="$t('sbxadmin.users.delete_dialog_title')"
            :deleteDialogMessage="$t('sbxadmin.users.delete_dialog_message')"
            :deleteDialogOKText="$t('sbxadmin.users.delete_dialog_ok')"
            :deleteDialogCancelText="$t('sbxadmin.global.cancel')"
            :paginator="users.meta"
          >

            <template v-slot:emailslot="row">
              <a class="text-sm" :href="'mailto:' + row.item.email">{{ row.item.email }}</a>
            </template>
          </SBXDataTable>
        </div>
      </div>
    </div>
  </div>
</template>
