<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
  import SBXButton from '@sbxui/Buttons/SBXButton.vue';
  import SBXSelect from '@sbxui/Form/SBXSelect.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDefaultPageLayout,
      SBXButton,
      SBXSelect,
      SBXInput
    },

    props: {
      user: Object,
      roles: Object,
      salesPeople: Object,
      errors: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    updated() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
      return {
        form: useForm('put', this.route('sydfisk_users.update', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.user.data.id]), {
          role_id: this.user.data.role_id,
          first_name: this.user.data.first_name,
          last_name: this.user.data.last_name,
          email: this.user.data.email,
          phone_no: this.user.data.phone_no,
          sales_person_id: this.user.data.sales_person_id
        }),
      }
    },

    computed: {
      pageTitle() {
        return `${this.$t("sbxadmin.users.edit_title")}`;
      },

      salesPeopleOptions() {
        var options = [];

        options.push({ id: null, name: 'Ingen säljare vald' });

        for (var i = 0; i < this.salesPeople.data.length; i++) {
          let currentSalesPerson = this.salesPeople.data[i];

          options.push({ id: currentSalesPerson.id, name: currentSalesPerson.name });
        }

        return options;
      }
    },

    methods: {
      update() {
        if (!this.form.processing) {
          this.form.submit();
        }
      },
    }
  }
</script>

<template>
  <SBXDefaultPageLayout>
    <form @submit.prevent="update">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <SBXSelect v-model:model="form.role_id" :items="roles.data" labelFieldName="name" :label="$t('sbxadmin.users.role_label')" @change="form.validate('role_id')" :error="form.errors.role_id" />
          <SBXSelect v-model:model="form.sales_person_id" :items="salesPeopleOptions" labelFieldName="name" :label="$t('sydfisk.sydfisk_users.sales_person_label')" @change="form.validate('sales_person_id')" :error="form.errors.sales_person_id" />
          <SBXInput v-model:model="form.first_name" :label="$t('sbxadmin.users.first_name_label')" @change="form.validate('first_name')" :error="form.errors.first_name" />
          <SBXInput v-model:model="form.last_name" :label="$t('sbxadmin.users.last_name_label')" @change="form.validate('last_name')" :error="form.errors.last_name" />
          <SBXInput v-model:model="form.email" :label="$t('sbxadmin.users.email_label')" @change="form.validate('email')" :error="form.errors.email" />
          <SBXInput v-model:model="form.phone_no" :label="$t('sydfisk.sydfisk_users.phone_no_label')" @change="form.validate('phone_no')" :error="form.errors.phone_no" />

          <SBXButton class="mt-4">{{ $t('sydfisk.sydfisk_users.update_button') }}</SBXButton>
        </div>
      </div>
    </form>
  </SBXDefaultPageLayout>
</template>
