<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
  import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';
  import SBXGenericNotification from '@sbxui/Notifications/SBXGenericNotification.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDataTable,
      SBXFilterBar,
      SBXGenericNotification
    },

    props: {
      translationLanguages: Object,
      filters: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t("translations.translation_languages.title");

      if (this.$page.props.flash.authorization != null) {
        this.showAuthorizationNotification = true;
      }
    },

    updated() {
      this.$page.props.page_info.title_label = this.$t("translations.translation_languages.title");
    },

    data() {
      return {
        columns: [
          { key: 'name', label: this.$t('translations.translation_languages.name_label') }
        ],

        translationLanguageFilters: {
          search: this.filters.search,
          active: this.filters.active
        },

        activeOptions: [
          { value: true, text: this.$t("translations.global.active") },
          { value: false, text: this.$t("translations.global.inactive") }
        ],

        showAuthorizationNotification: false
      }
    },

    computed: {
      authorizationMessage() {
        if (this.$page.props.flash.authorization == null) {
          return '';
        }

        return this.$page.props.flash.authorization;
      }
    },

    methods: {
      changeFilters() {
        this.$inertia.get(this.route('translation_languages', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.translationLanguageFilters, {
          preserveState: false,
          replace: true
        });
      },

      searchValueChanged(search) {
        this.translationLanguageFilters.search = search;
      },

      authorizationNotificationCancelled() {
        this.showAuthorizationNotification = false;
      },
    }
  }
</script>

<template>
  <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="translationLanguageFilters" searchRoute="translation_languages" :placeholder="$t('translations.global.search')">
    <template v-slot:filterArea>
      <div class="flex justify-end">
        <select @change="changeFilters" v-model="translationLanguageFilters.active" class="mt-1 mr-2 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
          <option v-for="active in activeOptions" :value="active.value">
            {{ active.text }}
          </option>
        </select>
      </div>
    </template>
  </SBXFilterBar>

  <div class="flex flex-col">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <SBXDataTable
            :columns="columns"
            :items="translationLanguages.data"
            :showAddButton="true"
            addRoute="translation_languages.create"
            :addButtonText="$t('translations.translation_languages.add_button')"
            :showEditButton="true"
            editRoute="translation_languages.edit"
            :showDeleteButton="true"
            deleteRoute="translation_languages.destroy"
            :deleteDialogTitle="$t('translations.translation_languages.delete_dialog_title')"
            :deleteDialogMessage="$t('translations.translation_languages.delete_dialog_message')"
            :deleteDialogOKText="$t('translations.translation_languages.delete_dialog_ok_button')"
            :deleteDialogCancelText="$t('translations.global.cancel')"
            :paginator="translationLanguages.meta"
          >

          </SBXDataTable>
        </div>
      </div>
    </div>
  </div>

  <SBXGenericNotification :show="showAuthorizationNotification" variant="warning" :message="authorizationMessage" @notificationCancelled="authorizationNotificationCancelled" />
</template>
