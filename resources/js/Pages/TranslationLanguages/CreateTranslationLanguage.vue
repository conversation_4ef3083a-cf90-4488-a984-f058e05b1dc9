<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXToggle from '@sbxui/Form/SBXToggle.vue';
  import SBXButton from '@sbxui/Buttons/SBXButton.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDefaultPageLayout,
      SBXInput,
      SBXToggle,
      SBXButton
    },

    mounted() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    updated() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
      return {
        form: useForm('post', this.route('translation_languages.store', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), {
          name: null,
          is_active: true
        }),
      }
    },

    computed: {
      pageTitle() {
        return `${this.$t("translations.translation_languages.create_title")}`;
      }
    },

    methods: {
      create() {
        if (!this.form.processing) {
          this.form.submit();
        }
      },
    }
  }
</script>

<template>
  <SBXDefaultPageLayout>
    <form @submit.prevent="create">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <SBXInput v-model:model="form.name" :label="$t('translations.translation_languages.name_label')" @change="form.validate('name')" :error="form.errors.name" />
          <SBXToggle v-model:model="form.is_active" :label="$t('translations.translation_languages.active_label')" class="mt-4" />

          <SBXButton class="mt-4">{{ $t('translations.translation_languages.create_button') }}</SBXButton>
        </div>
      </div>
    </form>
  </SBXDefaultPageLayout>
</template>
