<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
  import SBXSelect from '@sbxui/Form/SBXSelect.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXToggle from '@sbxui/Form/SBXToggle.vue';
  import SBXTextArea from '@sbxui/Form/SBXTextArea.vue';
  import SBXButton from '@sbxui/Buttons/SBXButton.vue';

  import AssignmentFileRow from './Components/AssignmentFileRow.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDefaultPageLayout,
      SBXSelect,
      SBXInput,
      SBXToggle,
      SBXTextArea,
      SBXButton,
      AssignmentFileRow
    },

    props: {
      translationCategories: Object,
      translationLanguages: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    updated() {
      this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
      return {
        form: useForm('post', this.route('translation_assignments.store', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), {
          translation_category_id: null,
          from_translation_language_id: null,
          to_translation_language_id: null,
          is_authorization_required: false,
          number_of_words: null,
          is_company: false,
          assignment_type: 'personal',
          first_name: null,
          last_name: null,
          company: null,
          company_no: null,
          email: null,
          phone_no: null,
          notes: null,
          is_email_contact_allowed: false,
          is_phone_contact_allowed: false,
          assignment_files: []
        }),
      }
    },

    computed: {
      pageTitle() {
        return `${this.$t("translations.translation_assignments.create_title")}`;
      },

      categoryOptions() {
        var options = [];

        options.push({ value: null, text: this.$t("translations.translation_assignments.category_prompt"), enabled: false });

        for (var i = 0; i < this.translationCategories.data.length; i++) {
          let currentCategory = this.translationCategories.data[i];

          options.push({ value: currentCategory.id, text: currentCategory.name });
        }

        return options;
      },

      languageOptions() {
        var options = [];

        options.push({ value: null, text: this.$t("translations.translation_assignments.language_prompt"), enabled: false });

        for (var i = 0; i < this.translationLanguages.data.length; i++) {
          let currentLanguage = this.translationLanguages.data[i];

          options.push({ value: currentLanguage.id, text: currentLanguage.name });
        }

        return options;
      }
    },

    methods: {
      create() {
        this.form.assignment_type = 'personal';
        if (this.form.is_company) {
          this.form.assignment_type = 'company';
        }

        if (!this.form.processing) {
          this.form.submit();
        }
      },

      addFile() {
        this.form.assignment_files.push({
          id: this.form.assignment_files.length + 1,
          file: null
        });
      },

      removeFile(fileID) {
        var removeIndex = -1;

        for (var i = 0; i < this.form.assignment_files.length; i++) {
          let file = this.form.assignment_files[i];

          if (file.id == fileID) {
            removeIndex = i;
          }
        }

        if (removeIndex != -1) {
          this.form.assignment_files.splice(removeIndex, 1);
        }
      },

      updateFile(fileID, file) {
        for (var i = 0; i < this.form.assignment_files.length; i++) {
          var currentFile = this.form.assignment_files[i];

          if (currentFile.id == fileID) {
            currentFile.file = file;
          }
        }
      },
    }
  }
</script>

<template>
  <SBXDefaultPageLayout>
    <form @submit.prevent="create">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <SBXSelect v-model:model="form.translation_category_id" :items="categoryOptions" valueFieldName="value" :label="$t('translations.translation_assignments.category_label')" @change="form.validate('translation_category_id')" :error="form.errors.translation_category_id" />

          <SBXSelect v-model:model="form.from_translation_language_id" :items="languageOptions" valueFieldName="value" :label="$t('translations.translation_assignments.from_language_label')" @change="form.validate('from_translation_language_id')" :error="form.errors.from_translation_language_id" />

          <SBXSelect v-model:model="form.to_translation_language_id" :items="languageOptions" valueFieldName="value" :label="$t('translations.translation_assignments.to_language_label')" @change="form.validate('to_translation_language_id')" :error="form.errors.to_translation_language_id" />

          <SBXToggle v-model:model="form.is_authorization_required" :label="$t('translations.translation_assignments.is_authorization_required_label')" class="mt-4" />

          <SBXInput v-model:model="form.number_of_words" :label="$t('translations.translation_assignments.number_of_words_label')" @change="form.validate('number_of_words')" :error="form.errors.number_of_words" />

          <div class="mt-4">
            <AssignmentFileRow
              v-for="(file, index) in form.assignment_files"
              :file="file"
              :key="file.id"
              :row-no="index"
              @removeFile="removeFile"
              @updateFile="updateFile"
            ></AssignmentFileRow>

            <p v-if="form.errors['assignment_files.0.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 1 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.1.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 2 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.2.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 3 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.3.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 4 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.4.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 5 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.5.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 6 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.6.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 7 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.7.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 8 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.8.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 9 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>
            <p v-if="form.errors['assignment_files.9.file']" class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 10 måste vara i ett giltigt format och får inte vara större än 50Mb.</p>

            <button @click.prevent="addFile" class="col-span-8 sm:col-start-3 sm:col-span-4 inline-flex justify-center border border-transparent shadow-sm text-sm font-medium rounded-md text-white py-2 px-4 bg-moss-500 hover:bg-moss-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-moss-500">{{ $t('translations.translation_assignments.add_file_button') }}</button>
         
            <p class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-gray-900">{{ $t('translations.translation_assignments.file_instructions') }}</p>

            <p v-if="form.invalid('assignment_files')" class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{ form.errors.assignment_files }}</p>
          </div>

          <SBXToggle v-model:model="form.is_company" :label="$t('translations.translation_assignments.company_label')" class="mt-4" />

          <SBXInput v-model:model="form.first_name" :label="$t('translations.translation_assignments.first_name_label')" @change="form.validate('first_name')" :error="form.errors.first_name" />

          <SBXInput v-model:model="form.last_name" :label="$t('translations.translation_assignments.last_name_label')" @change="form.validate('last_name')" :error="form.errors.last_name" />

          <SBXInput v-model:model="form.company" :label="$t('translations.translation_assignments.company_label')" @change="form.validate('company')" :error="form.errors.company" />

          <SBXInput v-model:model="form.company_no" :label="$t('translations.translation_assignments.company_no_label')" @change="form.validate('company_no')" :error="form.errors.company_no" />

          <SBXInput v-model:model="form.email" :label="$t('translations.translation_assignments.email_label')" @change="form.validate('email')" :error="form.errors.email" />

          <SBXInput v-model:model="form.phone_no" :label="$t('translations.translation_assignments.phone_no_label')" @change="form.validate('phone_no')" :error="form.errors.phone_no" />

          <SBXTextArea v-model:model="form.notes" :label="$t('translations.translation_assignments.notes_label')" :rows="5" :error="form.errors.notes" />

          <SBXToggle v-model:model="form.is_email_contact_allowed" :label="$t('translations.translation_assignments.contact_option_email_label')" class="mt-4" :error="form.errors.is_email_contact_allowed" />

          <SBXToggle v-model:model="form.is_phone_contact_allowed" :label="$t('translations.translation_assignments.contact_option_phone_label')" class="mt-4" :error="form.errors.is_phone_contact_allowed"/>

          <SBXButton class="mt-4">{{ $t('translations.translation_assignments.create_button') }}</SBXButton>
        </div>
      </div>
    </form>
  </SBXDefaultPageLayout>
</template>
