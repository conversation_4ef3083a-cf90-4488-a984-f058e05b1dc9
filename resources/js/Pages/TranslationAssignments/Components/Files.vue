<script>
/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

import FileRow from './FileRow.vue';
import { PaperClipIcon, ArrowUpOnSquareIcon, TrashIcon } from '@heroicons/vue/24/outline';
import { PhotoIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/solid';
import axios from 'axios';

export default {
    components: {
        FileRow,
        PaperClipIcon,
        PhotoIcon,
        CheckCircleIcon,
        ArrowUpOnSquareIcon,
        TrashIcon,
        XCircleIcon
    },

    props: {
        assignment: Object
    },

    data() {
        return {
            isUploadingError: false,
            errorMessage: '',
            selectedFiles: [],
            isUploading: false,
            fileType: 0, // 0 = Files for translation, 1 = Translated files
            showUploadForm: false
        };
    },

    computed: {
        fileTypeLabel() {
            return this.fileType === 0 ? 'Filer för översättning' : 'Översatta filer';
        }
    },

    methods: {
        toggleUploadForm() {
            this.showUploadForm = !this.showUploadForm;
            if (!this.showUploadForm) {
                this.selectedFiles = [];
                this.isUploadingError = false;
            }
        },

        setFileType(type) {
            this.fileType = type;
        },

        handleFileSelection(event) {
            this.selectedFiles = Array.from(event.target.files);
            this.isUploadingError = false;
        },

        removeFile(index) {
            this.selectedFiles.splice(index, 1);
        },

        uploadFiles() {
            if (this.selectedFiles.length === 0) return;

            this.isUploading = true;
            this.isUploadingError = false;

            const formData = new FormData();

            // Add each file to the form data
            this.selectedFiles.forEach((file) => {
                formData.append('files[]', file);
            });

            // Add the file type to the form data
            formData.append('file_type', this.fileType);

            // Send the request to the server
            axios.post(route('translation_assignments.addFile', {
                marketCode: this.$page.props.locale.selected_market_code,
                languageCode: this.$page.props.locale.selected_language_code,
                translationAssignment: this.assignment.data.id
            }), formData)
                .then(response => {
                    // Clear the selected files
                    this.selectedFiles = [];
                    // Hide the upload form
                    this.showUploadForm = false;
                    // Reload the page to show the new files
                    location.reload();
                })
                .catch(error => {
                    console.error('Error uploading files:', error);
                    this.isUploadingError = true;
                    this.errorMessage = error.response?.data?.error || 'Ett fel uppstod vid uppladdning av filer';
                })
                .finally(() => {
                    this.isUploading = false;
                });
        }
    }
}
</script>

<template>
    <div>
        <!-- File Lists -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Filer för översättning</h4>
            <div v-if="assignment.data.files.length > 0" class="border border-gray-200 rounded-md overflow-hidden">
                <FileRow v-for="file in assignment.data.files" :file="file" :key="file.id" />
            </div>
            <div v-else class="text-sm text-gray-500 italic mb-4">Inga filer för översättning har laddats upp.</div>
        </div>

        <div class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Översatta filer</h4>
            <div v-if="assignment.data.translated_files.length > 0"
                class="border border-gray-200 rounded-md overflow-hidden">
                <FileRow v-for="file in assignment.data.translated_files" :file="file" :key="file.id" />
            </div>
            <div v-else class="text-sm text-gray-500 italic mb-4">Inga översatta filer har laddats upp.</div>
        </div>

        <!-- Upload Button -->
        <div class="mt-6">
            <button @click="toggleUploadForm"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <ArrowUpOnSquareIcon class="h-5 w-5 mr-2" />
                {{ showUploadForm ? 'Avbryt uppladdning' : 'Ladda upp filer' }}
            </button>
        </div>

        <!-- Upload Form -->
        <div v-if="showUploadForm" class="mt-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div v-if="isUploadingError" class="mb-4 rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="shrink-0">
                        <XCircleIcon class="size-5 text-red-400" aria-hidden="true" />
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Fel vid uppladdning</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>{{ errorMessage }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Type Selection -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Filtyp</label>
                <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio" v-model="fileType" :value="0" class="form-radio h-4 w-4 text-green-600" />
                        <span class="ml-2 text-sm text-gray-700">Filer för översättning</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" v-model="fileType" :value="1" class="form-radio h-4 w-4 text-green-600" />
                        <span class="ml-2 text-sm text-gray-700">Översatta filer</span>
                    </label>
                </div>
            </div>

            <!-- File Selection Area -->
            <div
                class="flex justify-center p-6 bg-white rounded-lg border border-dashed border-gray-300 hover:border-gray-400">
                <div v-if="selectedFiles.length > 0" class="w-full">
                    <ul role="list" class="divide-y divide-gray-200">
                        <li v-for="(file, index) in selectedFiles" :key="index"
                            class="flex items-center justify-between py-3 pl-3 pr-4 text-sm">
                            <div class="flex items-center flex-1 w-0">
                                <PaperClipIcon class="h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
                                <span class="ml-2 flex-1 w-0 truncate">{{ file.name }}</span>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <button @click="removeFile(index)" type="button"
                                    class="font-medium text-red-600 hover:text-red-500">
                                    <TrashIcon class="h-5 w-5" />
                                </button>
                            </div>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <button @click="uploadFiles" :disabled="isUploading"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
                            {{ isUploading ? 'Laddar upp...' : 'Ladda upp ' + selectedFiles.length + ' filer' }}
                        </button>
                    </div>
                </div>
                <div v-else class="text-center">
                    <PhotoIcon class="mx-auto h-12 w-12 text-gray-400" aria-hidden="true" />
                    <div class="mt-4 flex text-sm text-gray-600">
                        <label for="file-upload"
                            class="relative cursor-pointer rounded-md bg-white font-medium text-green-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-green-500 focus-within:ring-offset-2 hover:text-green-500">
                            <span>Ladda upp {{ fileTypeLabel }}</span>
                            <input id="file-upload" name="file-upload" type="file" class="sr-only"
                                @change="handleFileSelection"
                                accept="image/png, image/jpeg, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .pdf"
                                multiple />
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">PNG, JPG, PDF, DOC, DOCX, XLS, XLSX upp till 100MB</p>
                </div>
            </div>
        </div>
    </div>
</template>
