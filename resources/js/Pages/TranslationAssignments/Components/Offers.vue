<script>

/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

import { CheckBadgeIcon } from '@heroicons/vue/24/solid';
import { EllipsisVerticalIcon, XMarkIcon } from '@heroicons/vue/24/outline';
import { Menu, MenuButton, MenuItems, MenuItem, Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import axios from 'axios';
import Swal from 'sweetalert2';

export default {
    components: {
        CheckBadgeIcon,
        EllipsisVerticalIcon,
        XMarkIcon,
        Menu,
        MenuButton,
        MenuItems,
        MenuItem,
        Dialog,
        DialogPanel,
        DialogTitle,
        TransitionChild,
        TransitionRoot
    },

    props: {
        assignment: Object
    },

    data() {
        return {
            isEditDialogOpen: false,
            currentBid: null,
            editedPrice: '',
            editedDeliveryDate: '',
            editedEndCustomerPrice: ''
        }
    },

    computed: {
        // Check if any bid has completed payment
        hasCompletedPayment() {
            return this.assignment.data.answered_by.some(bid => bid.payment_complete);
        }
    },

    methods: {
        openEditDialog(bid) {
            // Check if any bid has completed payment
            if (this.hasCompletedPayment || bid.payment_complete) {
                Swal.fire({
                    title: 'Cannot Edit',
                    text: 'Cannot edit bid details when payment is completed',
                    icon: 'warning',
                    confirmButtonColor: '#d33',
                });
                return;
            }

            this.currentBid = bid;
            this.editedPrice = bid.price;
            this.editedDeliveryDate = bid.estimated_delivery;
            this.isEditDialogOpen = true;
        },

        closeEditDialog() {
            this.isEditDialogOpen = false;
            this.currentBid = null;
        },

        saveBidDetails() {
            // Get the current route parameters
            const marketCode = this.$page.props.locale.selected_market_code;
            const languageCode = this.$page.props.locale.selected_language_code;

            // Prepare the request data
            const requestData = {};

            // Only include fields that have been changed
            if (this.editedPrice !== this.currentBid.price) {
                requestData.price = this.editedPrice;
            }

            if (this.editedDeliveryDate !== this.currentBid.estimated_delivery) {
                requestData.estimated_delivery = this.editedDeliveryDate;
            }

            // If nothing changed, just close the dialog
            if (Object.keys(requestData).length === 0) {
                this.closeEditDialog();
                return;
            }

            // Call the API to update the bid details
            axios.put(route('translation_assignments.bids.update_details', {
                marketCode: marketCode,
                languageCode: languageCode,
                bid: this.currentBid.id
            }), requestData)
                .then(response => {
                    // Show success message
                    Swal.fire({
                        title: 'Success',
                        text: 'Bid details updated successfully',
                        icon: 'success',
                        confirmButtonColor: '#3085d6',
                    });

                    // Update the bid in the UI
                    const bidIndex = this.assignment.data.answered_by.findIndex(bid => bid.id === this.currentBid.id);
                    if (bidIndex !== -1) {
                        // Update price if it was changed
                        if (requestData.price) {
                            this.assignment.data.answered_by[bidIndex].price = response.data.bid.price;
                            this.assignment.data.answered_by[bidIndex].end_customer_price = response.data.bid.price_end_customer;
                        }

                        // Update delivery date if it was changed
                        if (requestData.estimated_delivery) {
                            this.assignment.data.answered_by[bidIndex].estimated_delivery = response.data.bid.estimated_delivery;
                        }
                    }

                    this.closeEditDialog();
                })
                .catch(error => {
                    // Show error message
                    Swal.fire({
                        title: 'Error',
                        text: error.response?.data?.error || 'Failed to update bid details',
                        icon: 'error',
                        confirmButtonColor: '#d33',
                    });
                });
        }
    },
}
</script>

<template>
    <div class="table w-full table-auto border-collapse text-sm">
        <div class="table-header-group bg-gray-100">
            <div class="table-row bg-gray-100">
                <div
                    class="table-cell bg-gray-100 border-b border-gray-200 p-4 pt-3 pb-3 pl-8 text-left font-medium text-gray-600">
                    Översättare</div>
                <div
                    class="table-cell bg-gray-100 border-b border-gray-200 p-4 pt-3 pb-3 text-left font-medium text-gray-600">
                    Skapad</div>
                <div
                    class="table-cell bg-gray-100 border-b border-gray-200 p-4 pt-3 pr-8 pb-3 text-left font-medium text-gray-600">
                    Leveransdatum</div>
                <div
                    class="table-cell bg-gray-100 border-b border-gray-200 p-4 pt-3 pr-8 pb-3 text-left font-medium text-gray-600">
                    Pris</div>
                <div
                    class="table-cell bg-gray-100 border-b border-gray-200 p-4 pt-3 pr-8 pb-3 text-left font-medium text-gray-600">
                    Slutkundpris</div>
                <div
                    class="table-cell bg-gray-100 border-b border-gray-200 p-4 pt-3 pr-8 pb-3 text-right font-medium text-gray-600">
                </div>
            </div>
        </div>
        <div class="table-row-group">
            <div v-if="assignment.data.answered_by.length > 0" v-for="bid in assignment.data.answered_by"
                :class="['table-row', bid.payment_complete ? 'bg-green-50' : '']">
                <div
                    :class="['table-cell border-b border-gray-100 p-4 pl-8', bid.payment_complete ? 'text-gray-700' : 'text-gray-500']">
                    {{ bid.name }}
                    <CheckBadgeIcon v-if="bid.is_authorized"
                        class="inline-flex items-middle size-5 shrink-0 self-center text-green-600"
                        aria-hidden="true" />
                </div>
                <div
                    :class="['table-cell border-b border-gray-100 p-4', bid.payment_complete ? 'text-gray-700' : 'text-gray-500']">
                    {{ bid.created_at }}</div>
                <div
                    :class="['table-cell border-b border-gray-100 p-4 pr-8', bid.payment_complete ? 'text-gray-700' : 'text-gray-500']">
                    {{ bid.estimated_delivery }}</div>
                <div
                    :class="['table-cell border-b border-gray-100 p-4 pr-8', bid.payment_complete ? 'text-gray-700' : 'text-gray-500']">
                    {{ bid.price }}</div>
                <div
                    :class="['table-cell border-b border-gray-100 p-4 pr-8', bid.payment_complete ? 'text-gray-700' : 'text-gray-500']">
                    {{ bid.end_customer_price }}</div>
                <div
                    :class="['table-cell border-b border-gray-100 p-4 pr-8 text-right', bid.payment_complete ? 'text-gray-700' : 'text-gray-500']">
                    <!-- Only show menu if no bid has completed payment and this specific bid doesn't have completed payment -->
                    <Menu v-if="!hasCompletedPayment && !bid.payment_complete" as="div"
                        class="relative inline-block text-left">
                        <MenuButton class="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
                            <span class="sr-only">Open options</span>
                            <EllipsisVerticalIcon class="size-5" aria-hidden="true" />
                        </MenuButton>
                        <transition enter-active-class="transition ease-out duration-100"
                            enter-from-class="transform opacity-0 scale-95"
                            enter-to-class="transform opacity-100 scale-100"
                            leave-active-class="transition ease-in duration-75"
                            leave-from-class="transform opacity-100 scale-100"
                            leave-to-class="transform opacity-0 scale-95">
                            <MenuItems
                                class="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                                <MenuItem v-slot="{ active }">
                                <button @click="openEditDialog(bid)"
                                    :class="[active ? 'bg-gray-50 outline-none' : '', 'block w-full text-left px-3 py-1 text-sm/6 text-gray-900']">Ändra<span
                                        class="sr-only">{{ bid.id }}</span></button>
                                </MenuItem>
                                <MenuItem v-slot="{ active }">
                                <a href="#"
                                    :class="[active ? 'bg-gray-50 outline-none' : '', 'block px-3 py-1 text-sm/6 text-gray-900']">Radera<span
                                        class="sr-only">{{ bid.id }}</span></a>
                                </MenuItem>
                            </MenuItems>
                        </transition>
                    </Menu>
                    <!-- Show payment status indicator when payment is complete -->
                    <span v-if="bid.payment_complete"
                        class="inline-flex items-center rounded-md bg-green-100 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-800/20">
                        Betald
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Price Dialog -->
    <TransitionRoot as="template" :show="isEditDialogOpen">
        <Dialog as="div" class="relative z-10" @close="closeEditDialog">
            <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100"
                leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </TransitionChild>

            <div class="fixed inset-0 z-10 overflow-y-auto">
                <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <TransitionChild as="template" enter="ease-out duration-300"
                        enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                        enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200"
                        leave-from="opacity-100 translate-y-0 sm:scale-100"
                        leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                        <DialogPanel
                            class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                            <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                                <button type="button"
                                    class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                                    @click="closeEditDialog">
                                    <span class="sr-only">Close</span>
                                    <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                </button>
                            </div>
                            <div>
                                <div class="mt-3 text-center sm:mt-0 sm:text-left">
                                    <DialogTitle as="h3" class="text-base font-semibold leading-6 text-gray-900">
                                        Redigera buddetaljer
                                    </DialogTitle>
                                    <div class="mt-4">
                                        <form @submit.prevent="saveBidDetails" class="space-y-4">
                                            <div>
                                                <label for="price"
                                                    class="block text-sm font-medium leading-6 text-gray-900">Pris</label>
                                                <div class="mt-2">
                                                    <input type="text" name="price" id="price" v-model="editedPrice"
                                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-green-600 sm:text-sm sm:leading-6"
                                                        placeholder="0.00" />
                                                </div>
                                            </div>
                                            <div>
                                                <label for="delivery-date"
                                                    class="block text-sm font-medium leading-6 text-gray-900">Leveransdatum</label>
                                                <div class="mt-2">
                                                    <input type="date" name="delivery-date" id="delivery-date"
                                                        v-model="editedDeliveryDate"
                                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-green-600 sm:text-sm sm:leading-6" />
                                                </div>
                                            </div>
                                            <div
                                                class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                                                <button type="submit"
                                                    class="inline-flex w-full justify-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600 sm:col-start-2">
                                                    Spara
                                                </button>
                                                <button type="button"
                                                    class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                                                    @click="closeEditDialog">
                                                    Avbryt
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>
