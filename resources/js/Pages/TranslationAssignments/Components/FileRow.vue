<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineComponent } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { PaperClipIcon } from '@heroicons/vue/24/outline';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        PaperClipIcon
    },

    props: {
        file: Object
    },

    methods: {
        openDocument() {
            window.open(this.file.url, '_blank').focus();
        }
    }
}
</script>

<template>
    <div @click="openDocument" class="flex items-center justify-between py-2 pr-5 text-sm leading-6">
        <div class="flex flex-1 items-center">
            <PaperClipIcon class="h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
            <div class="ml-4 flex min-w-0 flex-1 gap-2">
                <span class="truncate font-medium">{{ file.original_filename }}</span>
            </div>
        </div>
        <div class="ml-4 flex-shrink-0">
            <a href="#" class="font-medium text-gray-600 hover:text-gray-800">{{
                $t('translations.my_assignments.assignment_detail.file_download_label')
            }}</a>
        </div>
    </div>
</template>
