<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import axios from 'axios';

import Swal from 'sweetalert2';

import { TrashIcon } from '@heroicons/vue/24/outline';

import SBXButton from '@sbxui/Buttons/SBXButton.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        TrashIcon,
        SBXButton
    },

    props: [
        'rowNo',
        'file'
    ],

    mounted() {
        // console.log("File", this.file);
    },

    data() {
        return {
            currentFile: null
        }
    },

    computed: {
        isCompleted() {
            return (this.file.article_id != null);
        },

        filename() {
            return '/translation_assignments/' + this.file.filename;
        }
    },

    methods: {
        removeFile() {
            this.$emit('removeFile', this.file.id);
        },

        updateFile(event) {
            this.currentFile = event.target.files[0];

            // console.log('Current file:', this.currentFile);

            this.$emit('updateFile', this.file.id, this.currentFile);
        }
    }
};
</script>

<template>
    <div class="grid grid-cols-12 mb-2">
        <div class="col-span-1 flex justify-center items-center">
            <p class="text-xl font-semibold">{{ rowNo + 1 }}</p>
        </div>

        <div class="col-span-10 flex justify-start items-center" v-if="!isCompleted">
            <div
                class="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 bg-white mr-4">
                <div class="text-left">
                    <div v-if="currentFile == null" class="flex text-sm text-gray-600">
                        <label
                            class="relative ml-2 cursor-pointer rounded-md bg-white font-semibold text-gray-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500">
                            <span>{{ $t('translations.translation_assignments.file_upload_label') }}</span>
                            <input id="file-upload" type="file" @change="updateFile" class="sr-only" />
                        </label>
                        <!-- <p class="pl-1">{{ $t('translations.translation_assignments.file_upload_2_label') }}</p> -->
                    </div>

                    <div v-if="currentFile != null" class="flex text-sm text-gray-600">
                        <p class="ml-2">{{ currentFile.name }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-span-9" v-if="isCompleted">
            <a v-if="filename" :href="filename" target="_blank">
                <span class="mt-2">{{ this.file.original_filename }}</span>
            </a>
        </div>
        <div class="col-span-1 flex justify-end items-center">
            <SBXButton variant="danger" size="s" @click.prevent="removeFile">
                <TrashIcon class="h-5 w-5 text-white" aria-hidden="true" />
            </SBXButton>
        </div>
    </div>
</template>
