<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';
import axios from 'axios';

import { PaperClipIcon, ArrowUpOnSquareIcon, TrashIcon } from '@heroicons/vue/24/outline';
import { PhotoIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/solid'


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        PaperClipIcon,
        PhotoIcon,
        CheckCircleIcon,
        ArrowUpOnSquareIcon,
        TrashIcon,
        XCircleIcon
    },

    props: {
        assignment: Object
    },

    mounted() {
        console.log("Assignment", this.assignment);
    },

    data() {
        return {
            isUploadingError: false,
            selectedFiles: [],
            isUploading: false,
        };
    },

    methods: {

        handleFileSelection(event) {
            this.selectedFiles = Array.from(event.target.files);
        },

        removeFile(index) {
            this.selectedFiles.splice(index, 1);
        },

        uploadFiles() {
            if (this.selectedFiles.length === 0) return;

            this.isUploading = true;
            const formData = new FormData();

            this.selectedFiles.forEach((file) => {
                formData.append('files[]', file);
            });

            console.log(formData);

            axios.post(this.route('translation_assignments.addFile', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.id]), formData)
                .then(response => {
                    this.selectedFiles = [];
                    location.reload();
                })
                .catch(error => {
                    console.log(error)
                    this.isUploadingError = true;
                    this.isUploading = false;
                })
                .finally(() => this.isUploading = false);
        },
    }
};
</script>
<template>
    <div class=" mt-3 rounded-lg">
        <fieldset>
            <div class="space-y-5">
                <div v-if="isUploadingError" class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="shrink-0">
                            <XCircleIcon class="size-5 text-red-400" aria-hidden="true" />
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Fel vid uppladdning</h3>
                        </div>
                    </div>
                </div>
                <div
                    class="flex justify-center p-3 bg-white rounded-lg border border-dashed bg-green-50/2 border-gray-900/25">
                    <div v-if="selectedFiles.length > 0" class="w-full">
                        <ul v-if="selectedFiles.length > 0" role="list">
                            <li v-for="(file, index) in selectedFiles"
                                class="flex items-center rounded-lg justify-between py-4 pl-4 pr-5 mb-2 text-sm leading-6 bg-gray-200">
                                <div class="flex w-0 flex-1 items-center">
                                    <PaperClipIcon class="h-5 w-5 flex-shrink-0 text-gray-700" aria-hidden="true" />
                                    <div class="ml-4 flex min-w-0 flex-1 gap-2">
                                        <span class="truncate font-medium">{{ file.name }}</span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-shrink-0">
                                    <button @click="removeFile(index)"
                                        class="font-medium text-gray-600 hover:text-gray-800">
                                        <TrashIcon class="h-5 w-5 text-red-600 hover:text-red-600" aria-hidden="true" />
                                    </button>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <div class="flex flex-col items-center text-sm/6 text-gray-600 py-2 my-2"
                            v-if="selectedFiles.length == 0">
                            <label for="file-upload"
                                class="flex bg-white cursor-pointer hover:bg-white p-4 font-semibold text-gray-700 focus-within:outline-none focus-within:ring-2 focus-within:ring-gray-600 focus-within:ring-offset-2 hover:text-gray-900 hover:border-b hover:border-gray-700">
                                <ArrowUpOnSquareIcon class="h-6 w-6 float-start align-top" aria-hidden="true" />{{
                                    $t('translations.translation_assignments.file_upload_label') }}
                                <input id="file-upload" name="file-upload" type="file"
                                    accept="image/png, image/jpeg, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, .pdf"
                                    class="sr-only" @change="handleFileSelection" multiple />
                            </label>
                            <div class="content-center ml-2 text-xs text-gray-600">{{
                                $t('translations.translation_assignments.file_instructions') }}</div>
                        </div>

                    </div>
                </div>
                <div class="gap-3 w-full" v-if="selectedFiles.length > 0">
                    <div class="mt-6">
                        <button :disabled="selectedFiles.length === 0 || isUploading" @click="uploadFiles"
                            class="flex items-center w-full justify-center rounded-md border border-transparent bg-green-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed">
                            {{ isUploading ? 'Laddar upp...' : 'Ladda upp' }}
                        </button>
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
</template>
