<template>
    <div>
        <div v-if="!hasOrders" class="text-center py-8">
            <p class="text-gray-500">{{ $t('translations.translation_assignments.no_orders')
                || "No orders found for assignment." }}</p>
        </div>

        <div v-else>
            <!-- Desktop View -->
            <div class="hidden md:block overflow-hidden rounded-lg shadow">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                {{ $t('translations.translation_assignments.order_id') || 'Order ID' }}
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                {{ $t('translations.translation_assignments.translator') || 'Translator' }}
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                {{ $t('translations.translation_assignments.payment_date') || 'Payment Date' }}
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                {{ $t('translations.translation_assignments.amount') || 'Amount' }}
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                                {{ $t('translations.translation_assignments.status') || 'Status' }}
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">{{ $t('translations.translation_assignments.actions') || 'Actions'
                                    }}</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="bid in paidBids" :key="bid.id"
                            class="bg-green-50 hover:bg-green-100 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ bid.order?.order_no || 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                <div class="flex items-center">
                                    <span>{{ bid.name }}</span>
                                    <CheckBadgeIcon v-if="bid.is_authorized" class="ml-1 h-5 w-5 text-green-600"
                                        aria-hidden="true" />
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                {{ formatDate(bid.payment_date) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ formatPrice(bid.price) }} SEK
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span
                                    class="inline-flex items-center rounded-md bg-green-100 px-2.5 py-0.5 text-sm font-medium text-green-800 ring-1 ring-inset ring-green-600/20">
                                    {{ $t('translations.translation_assignments.paid') || 'Paid' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button v-if="bid.order" @click="viewOrderDetails(bid.order.order_no)"
                                    class="text-indigo-600 hover:text-indigo-900 focus:outline-none focus:underline">
                                    {{ $t('translations.translation_assignments.view_details') || 'View Details' }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Mobile View -->
            <div class="md:hidden space-y-4">
                <div v-for="bid in paidBids" :key="bid.id"
                    class="bg-white rounded-lg shadow overflow-hidden border border-gray-200">
                    <div class="bg-green-50 px-4 py-3 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-base font-medium text-gray-900 flex items-center">
                                {{ $t('translations.translation_assignments.order_id') || 'Order ID' }}:
                                <span class="ml-1 font-bold">{{ bid.order?.order_no || 'N/A' }}</span>
                                <CheckBadgeIcon v-if="bid.is_authorized" class="ml-1 h-4 w-4 text-green-600"
                                    aria-hidden="true" />
                            </h3>
                            <span
                                class="inline-flex items-center rounded-md bg-green-100 px-2 py-1 text-xs font-medium text-green-800 ring-1 ring-inset ring-green-600/20">
                                {{ $t('translations.translation_assignments.paid') || 'Paid' }}
                            </span>
                        </div>
                    </div>
                    <div class="px-4 py-3 space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">{{ $t('translations.translation_assignments.translator')
                                || 'Translator' }}:</span>
                            <span class="text-sm font-medium text-gray-900">{{ bid.name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">{{
                                $t('translations.translation_assignments.payment_date') || 'Payment Date' }}:</span>
                            <span class="text-sm font-medium text-gray-900">{{ formatDate(bid.payment_date) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">{{ $t('translations.translation_assignments.amount') ||
                                'Amount' }}:</span>
                            <span class="text-sm font-medium text-gray-900">{{ formatPrice(bid.price) }} SEK</span>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 flex justify-end border-t border-gray-200">
                        <button v-if="bid.order" @click="viewOrderDetails(bid.order.order_no)"
                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            {{ $t('translations.translation_assignments.view_details') || 'View Details' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { CheckBadgeIcon } from '@heroicons/vue/24/solid';

export default {
    components: {
        CheckBadgeIcon
    },

    props: {
        assignment: Object
    },

    computed: {
        paidBids() {
            return this.assignment.data.answered_by.filter(bid => bid.payment_complete);
        },

        hasOrders() {
            return this.paidBids.length > 0;
        }
    },

    methods: {
        formatDate(dateString) {
            if (!dateString) return 'N/A';

            const date = new Date(dateString);
            return new Intl.DateTimeFormat('sv-SE', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }).format(date);
        },

        formatPrice(price) {
            if (price === undefined || price === null) return '0.00';

            return parseFloat(price).toLocaleString('sv-SE', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        },

        viewOrderDetails(orderId) {
            // Implement order details view - could open a modal or navigate to order details page
            console.log('View order details for:', orderId);
            // You could implement this with a modal or navigation to a details page
        }
    }
};
</script>
