<template>
    <div>
        <button @click="openModal"
            class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
            {{ assignment.assigned_translator ? 'Change Assigned Translator' : 'Assign Translator' }}
        </button>

        <TransitionRoot appear :show="isOpen" as="template">
            <Dialog as="div" @close="closeModal" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black/30" aria-hidden="true" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                                    {{
                                        assignment.assigned_translator ? 'Change Assigned Translator' : 'Assign Translator'
                                    }}
                                </DialogTitle>

                                <div class="mt-4">
                                    <div v-if="loading" class="flex justify-center">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600">
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div class="mb-4">
                                            <label for="translator"
                                                class="block text-sm font-medium text-gray-700">Select
                                                Translator</label>
                                            <select id="translator" v-model="selectedTranslator"
                                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                                <option value="">-- Unassigned (Available to all translators) --
                                                </option>
                                                <option v-for="translator in matchingTranslators" :key="translator.id"
                                                    :value="translator.id">
                                                    {{ translator.name }} ({{ translator.email }})
                                                </option>
                                            </select>
                                        </div>

                                        <div v-if="error" class="mb-4 text-red-600 text-sm">
                                            {{ error }}
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 flex justify-end space-x-3">
                                    <button type="button"
                                        class="inline-flex justify-center rounded-md border border-transparent bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2"
                                        @click="closeModal">
                                        Cancel
                                    </button>
                                    <button type="button"
                                        class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2"
                                        @click="assignTranslator" :disabled="loading">
                                        Save
                                    </button>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { Dialog, DialogPanel, DialogTitle, TransitionRoot, TransitionChild } from '@headlessui/vue';
import axios from 'axios';

export default {
    components: {
        Dialog,
        DialogPanel,
        DialogTitle,
        TransitionRoot,
        TransitionChild
    },

    props: {
        assignment: {
            type: Object,
            required: true
        }
    },

    setup(props) {
        const isOpen = ref(false);
        const loading = ref(false);
        const error = ref('');
        const matchingTranslators = ref([]);
        const selectedTranslator = ref(props.assignment.assigned_translator_id || '');

        const openModal = () => {
            isOpen.value = true;
            fetchMatchingTranslators();
        };

        const closeModal = () => {
            isOpen.value = false;
            error.value = '';
        };

        const fetchMatchingTranslators = async () => {
            loading.value = true;
            try {
                // Get market code and language code from the URL
                const url = window.location.pathname;
                const segments = url.split('/');
                const marketCode = segments[1];
                const languageCode = segments[2];

                // console.log('Fetching matching translators for assignment:', props.assignment.id);
                // console.log('URL segments:', segments);
                // console.log('Market code:', marketCode);
                // console.log('Language code:', languageCode);

                const response = await axios.get(route('translation_assignments.matching_translators', {
                    marketCode: marketCode,
                    languageCode: languageCode,
                    assignment: props.assignment.id
                }));

                matchingTranslators.value = response.data;
            } catch (e) {
                error.value = 'Failed to load translators. Please try again.';
                console.error(e);
            } finally {
                loading.value = false;
            }
        };

        const assignTranslator = async () => {
            loading.value = true;
            error.value = '';

            try {
                // Get market code and language code from the URL
                const url = window.location.pathname;
                const segments = url.split('/');
                const marketCode = segments[1];
                const languageCode = segments[2];

                await axios.put(route('translation_assignments.assign_translator', {
                    marketCode: marketCode,
                    languageCode: languageCode,
                    assignment: props.assignment.id
                }), {
                    translator_id: selectedTranslator.value || null
                });

                console.log('Translator assigned successfully');

                // Show success message and close modal
                closeModal();

                // Reload the page to reflect changes
                window.location.reload();
            } catch (e) {
                error.value = e.response?.data?.error || 'Failed to assign translator. Please try again.';
                console.error(e);
            } finally {
                loading.value = false;
            }
        };

        onMounted(() => {
            selectedTranslator.value = props.assignment.assigned_translator_id || '';
        });

        return {
            isOpen,
            loading,
            error,
            matchingTranslators,
            selectedTranslator,
            openModal,
            closeModal,
            assignTranslator,
            fetchMatchingTranslators
        };
    }
};
</script>
