<script>
/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

import { ClockIcon, UserIcon, DocumentTextIcon } from '@heroicons/vue/24/outline';
import axios from 'axios';

export default {
    components: {
        ClockIcon,
        UserIcon,
        DocumentTextIcon
    },

    props: {
        assignment: Object
    },

    data() {
        return {
            logs: [],
            loading: true,
            error: null
        }
    },

    mounted() {
        this.fetchActivityLogs();
    },

    methods: {
        fetchActivityLogs() {
            this.loading = true;
            this.error = null;

            axios.get(route('activity_logs.assignment', {
                assignment: this.assignment.data.id
            }))
                .then(response => {
                    this.logs = response.data.logs;
                    this.loading = false;
                })
                .catch(error => {
                    this.error = 'Failed to load activity logs: ' + (error.response?.data?.error || error.message);
                    this.loading = false;
                    console.error('Error fetching activity logs:', error);
                });
        },

        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return new Intl.DateTimeFormat('sv-SE', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }).format(date);
        },

        getActivityIcon(action) {
            switch (action) {
                case 'bid_details_updated':
                case 'bid_price_updated':
                case 'bid_delivery_date_updated':
                    return DocumentTextIcon;
                case 'bid_change_email_sent':
                case 'offer_email_sent':
                    return UserIcon;
                default:
                    return ClockIcon;
            }
        },

        getActivityColor(action) {
            switch (action) {
                case 'bid_details_updated':
                case 'bid_price_updated':
                case 'bid_delivery_date_updated':
                    return 'bg-blue-100 text-blue-800';
                case 'bid_change_email_sent':
                case 'offer_email_sent':
                    return 'bg-green-100 text-green-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }
    }
}
</script>

<template>
    <div class="bg-white">
        <div v-if="loading" class="py-10 text-center">
            <div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-green-600 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                <span class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
            </div>
            <p class="mt-2 text-sm text-gray-500">Laddar aktivitetsloggar...</p>
        </div>

        <div v-else-if="error" class="py-10 text-center">
            <div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Ett fel uppstod</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>{{ error }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="logs.length === 0" class="py-10 text-center">
            <p class="text-sm text-gray-500">Inga aktivitetsloggar hittades för detta uppdrag.</p>
        </div>

        <div v-else class="flow-root">
            <ul role="list" class="-mb-8">
                <li v-for="(log, logIdx) in logs" :key="log.id">
                    <div class="relative pb-8">
                        <span v-if="logIdx !== logs.length - 1" class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                        <div class="relative flex space-x-3">
                            <div>
                                <span :class="['h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white', getActivityColor(log.action)]">
                                    <component :is="getActivityIcon(log.action)" class="h-5 w-5" aria-hidden="true" />
                                </span>
                            </div>
                            <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                                <div>
                                    <p class="text-sm text-gray-500">{{ log.description }}</p>
                                    <div v-if="log.properties" class="mt-1 text-xs text-gray-500">
                                        <div v-for="(value, key) in log.properties" :key="key" class="mt-0.5">
                                            <span class="font-medium">{{ key }}:</span> {{ value }}
                                        </div>
                                    </div>
                                </div>
                                <div class="whitespace-nowrap text-right text-sm text-gray-500">
                                    <time :datetime="log.created_at">{{ formatDate(log.created_at) }}</time>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
