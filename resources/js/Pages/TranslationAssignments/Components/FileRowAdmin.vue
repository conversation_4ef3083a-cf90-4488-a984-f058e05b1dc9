<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineComponent } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { PaperClipIcon, TrashIcon } from '@heroicons/vue/24/outline';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        PaperClipIcon,
        TrashIcon
    },

    props: {
        file: Object,
        assignmentId: Number
    },

    methods: {
        openDocument() {
            window.open(this.file.url, '_blank').focus();
        },
        removeFile() {
            console.log("Remove file", this.file);
            this.$inertia.delete(this.route('translation_assignments.deleteFile', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignmentId, this.file.id]), {
            }, {
                preserveScroll: true,
                onSuccess: () => {
                }
            });
        }
    }
}
</script>

<template>
    <div class="flex items-center justify-between py-2 pr-5 text-sm leading-6">
        <div class="flex flex-1 items-center">
            <PaperClipIcon class="h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
            <div class="ml-4 flex min-w-0 flex-1 gap-2">
                <a href="#" class="font-medium text-gray-600 hover:text-gray-800" @click="openDocument"><span
                        class="truncate font-medium">{{
                            file.original_filename }}</span> </a>
            </div>
        </div>
        <div class="ml-4 flex-shrink-0">
            <TrashIcon class="h-5 w-5 flex-shrink-0 text-red-500 hover:text-red-700 hover:cursor-pointer"
                @click="removeFile" aria-hidden="true" />

        </div>
    </div>
</template>
