<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineComponent } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { CheckIcon } from '@heroicons/vue/24/outline';

// import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

import SBXComboBoxSingleSelection from '@sbxui/Form/SBXComboBoxSingleSelection.vue';
import SBXAlert from '@sbxui/Notifications/SBXAlert.vue';

import AssignmentFileRow from './Components/AssignmentFileRow.vue';
import Multiselect from 'vue-multiselect'


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        Head,
        Link,
        SBXComboBoxSingleSelection,
        SBXAlert,
        AssignmentFileRow,
        CheckIcon,
        Multiselect

    },

    props: {
        translationCategories: Object,
        translationLanguages: Object,
        errors: Object
    },

    layout: null,

    mounted() {
        this.$page.props.page_info.title_label = this.pageTitle;
    },

    updated() {
        this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
        return {
            form: useForm('post', this.route('assignments.store', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), {
                translation_category_id: null,
                from_translation_language_id: null,
                to_translation_language_id: null,
                is_authorization_required: false,
                number_of_words: null,
                assignment_type: 'personal',
                first_name: null,
                email: null,
                phone_no: null,
                notes: null,
                is_email_contact_allowed: true,
                is_phone_contact_allowed: true,
                assignment_files: []
            }),

            assignmentSubmitted: false,

            authorizedTextExpanded: false,

            assignmentTypes: [
                { id: 'personal', title: this.$t("translations.translation_assignments.personal_label") },
                { id: 'company', title: this.$t("translations.translation_assignments.company_label") }
            ],

            termsAccepted: false,

            translation_data: {},
        }
    },

    computed: {
        pageTitle() {
            return `${this.$t("translations.translation_assignments.create_title")}`;
        },

        categoryOptions() {
            var options = [];

            options.push({ value: null, text: this.$t("translations.translation_assignments.category_prompt"), disabled: true });

            for (var i = 0; i < this.translationCategories.data.length; i++) {
                let currentCategory = this.translationCategories.data[i];

                options.push({ value: currentCategory.id, text: currentCategory.name });
            }

            return options;
        },

        languageOptions() {
            var options = [];

            options.push({ value: null, text: this.$t("translations.translation_assignments.language_prompt"), disabled: true });

            for (var i = 0; i < this.translationLanguages.data.length; i++) {
                let currentLanguage = this.translationLanguages.data[i];

                options.push({ value: currentLanguage.id, text: currentLanguage.name });
            }

            return options;
        },

        fromLanguageOptions() {
            var options = [];

            options.push({ value: null, text: this.$t("translations.translation_assignments.from_language_label"), disabled: true });

            for (var i = 0; i < this.translationLanguages.data.length; i++) {
                let currentLanguage = this.translationLanguages.data[i];

                options.push({ value: currentLanguage.id, text: currentLanguage.name });
            }

            return options;
        },

        toLanguageOptions() {
            var options = [];

            options.push({ value: null, text: this.$t("translations.translation_assignments.to_language_label"), disabled: true });

            for (var i = 0; i < this.translationLanguages.data.length; i++) {
                let currentLanguage = this.translationLanguages.data[i];

                options.push({ value: currentLanguage.id, text: currentLanguage.name });
            }

            return options;
        }
    },

    methods: {
        addFile() {
            this.form.assignment_files.push({
                id: this.form.assignment_files.length + 1,
                file: null
            });
        },

        removeFile(fileID) {
            var removeIndex = -1;

            for (var i = 0; i < this.form.assignment_files.length; i++) {
                let file = this.form.assignment_files[i];

                if (file.id == fileID) {
                    removeIndex = i;
                }
            }

            if (removeIndex != -1) {
                this.form.assignment_files.splice(removeIndex, 1);
            }
        },

        updateFile(fileID, file) {
            for (var i = 0; i < this.form.assignment_files.length; i++) {
                var currentFile = this.form.assignment_files[i];

                if (currentFile.id == fileID) {
                    currentFile.file = file;
                }
            }
        },

        create() {

            if (this.$gtm) {
                this.$gtm.trackEvent({
                    event: 'kontakt_pris',
                    category: 'category',
                    action: 'action',
                });
            } else {
                console.error('$gtm is not available');
            }

            var self = this;

            console.log("from_translation_language_id", this.form.from_translation_language_id);
            console.log("translation_data", this.translation_data);

            if (this.translation_data.from_translation_language_id != null || this.translation_data.to_translation_language_id != null) {
                this.form.from_translation_language_id = this.translation_data.from_translation_language_id.id;
                this.form.to_translation_language_id = this.translation_data.to_translation_language_id.id;
                // this.form.from_translation_language_id = this.form.from_translation_language_id.id;
            }


            if (!this.form.processing) {
                this.form.submit({
                    preserveScroll: true,
                    onSuccess: page => {
                        self.assignmentSubmitted = true;

                        self.form.translation_category_id = null;
                        self.form.from_translation_language_id = null;
                        self.form.to_translation_language_id = null;
                        self.form.is_authorization_required = false;
                        self.form.number_of_words = null;
                        self.form.assignment_type = 'company';
                        self.form.first_name = null;
                        self.form.email = null;
                        self.form.phone_no = null;
                        self.form.notes = null;
                        self.form.is_email_contact_allowed = true;
                        self.form.is_phone_contact_allowed = true;
                        self.form.assignment_files = [];

                        self.termsAccepted = false;

                        self.form.reset();

                        self.translation_data = {};

                        setTimeout(function () {
                            self.assignmentSubmitted = false;
                        }, 3000);
                    }
                });
            }
        },
    }
}
</script>

<template>
    <div class="bg-oversattare-green-light">

        <Head title="Översättare.nu | Skicka uppdrag" />

        <div class="relative overflow-hidden">
            <main>
                <div class="relative bg-oversattare-green-light py-4">
                    <div class="relative">
                        <div class="mx-auto px-4 mb-16 sm:px-6 lg:px-8">
                            <p class="text-2xl text-center font-bold tracking-tight text-gray-900 sm:text-4xl">{{
                                $t('translations.assignments.title') }}</p>
                            <p class="mt-2 text-xl text-center tracking-tight text-gray-900">{{
                                $t('translations.assignments.subtitle') }}</p>

                            <form @submit.prevent="create">
                                <div class="mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 sm:gap-4 gap-2">
                                        <div
                                            class="col-span-8 md:col-start-3 md:col-span-2 sm:col-start-3 sm:col-span-4">
                                            <multiselect v-model="translation_data.from_translation_language_id"
                                                :noResult="$t('translations.translation_assignments.no_language_found_label')"
                                                :placeholder="$t('translations.translation_assignments.from_language_search_label')"
                                                :selectLabel="$t('translations.translation_assignments.select_language_label')"
                                                :deselectLabel="$t('translations.translation_assignments.deselect_language_label')"
                                                :selectedLabel="$t('translations.translation_assignments.selected_language_label')"
                                                label="name" track-by="id" :options="translationLanguages.data"
                                                :multiple="false" :taggable="false" :showNoResults="false"
                                                class="block w-full text-gray-900">
                                            </multiselect>
                                            <p v-if="form.invalid('from_translation_language_id')"
                                                class="mt-1 text-xs text-red-500">{{
                                                    form.errors.from_translation_language_id }}</p>
                                        </div>

                                        <div class="col-span-8 md:col-span-2 sm:col-start-3 sm:col-span-4">
                                            <multiselect v-model="translation_data.to_translation_language_id"
                                                :noResult="$t('translations.translation_assignments.no_language_found_label')"
                                                :placeholder="$t('translations.translation_assignments.to_language_search_label')"
                                                :selectLabel="$t('translations.translation_assignments.select_language_label')"
                                                :deselectLabel="$t('translations.translation_assignments.deselect_language_label')"
                                                :selectedLabel="$t('translations.translation_assignments.selected_language_label')"
                                                label="name" track-by="id" :options="translationLanguages.data"
                                                :multiple="false" :taggable="false" :showNoResults="false"
                                                class="block w-full text-gray-900">
                                            </multiselect>
                                            <p v-if="form.invalid('to_translation_language_id')"
                                                class="mt-1 text-xs text-red-500">{{
                                                    form.errors.to_translation_language_id }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <select v-model="form.translation_category_id"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                            <option v-for="category in categoryOptions" :value="category.value"
                                                :disabled="(category.hasOwnProperty('disabled'))">
                                                {{ category.text }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('translation_category_id')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.translation_category_id }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.number_of_words"
                                            @change="form.validate('number_of_words')"
                                            :placeholder="$t('translations.translation_assignments.number_of_words_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('number_of_words')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.number_of_words }}</p>
                                    </div>
                                </div>

                                <div class="mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <div class="col-span-8 sm:col-start-3 sm:col-span-4 mt-2">
                                            <AssignmentFileRow v-for="(file, index) in form.assignment_files"
                                                :file="file" :key="file.id" :row-no="index" @removeFile="removeFile"
                                                @updateFile="updateFile"></AssignmentFileRow>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="errors['assignment_files.0.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 1 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.1.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 2 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.2.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 3 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.3.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 4 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.4.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 5 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.5.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 6 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.6.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 7 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.7.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 8 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.8.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 9 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                        <p v-if="errors['assignment_files.9.file']"
                                            class="mt-1 col-start-3 col-span-4 text-xs text-red-500">Dokument 10 måste
                                            vara i ett giltigt format och får inte vara större än 50Mb.</p>
                                    </div>
                                </div>

                                <div class="mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <button @click.prevent="addFile"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 inline-flex justify-center border border-transparent shadow-sm text-sm font-medium rounded-md text-white py-2 px-4 bg-oversattare-orange hover:bg-oversattare-orange focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-oversattare-orange">{{
                                                $t('translations.translation_assignments.add_file_button') }}</button>
                                    </div>

                                    <div class="grid grid-cols-8 gap-4">
                                        <p class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-gray-900">{{
                                            $t('translations.translation_assignments.file_instructions') }}</p>
                                    </div>

                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('assignment_files')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.assignment_files }}</p>
                                    </div>
                                </div>

                                <div class="mt-4 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <fieldset
                                            class="p-2 col-span-8 sm:col-start-3 sm:col-span-4 border border-gray-300">
                                            <div class="crelative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input v-model="form.is_authorization_required" type="checkbox"
                                                        class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
                                                </div>
                                                <div class="ml-3 text-sm leading-6">
                                                    <label for="terms" class="font-medium text-gray-900">{{
                                                        $t('translations.translation_assignments.is_authorization_required_label')
                                                    }}</label>
                                                    <p class="text-xs text-gray-900">{{
                                                        $t('translations.translation_assignments.authorization_description_short')
                                                    }} <span v-if="!authorizedTextExpanded"
                                                            @click="authorizedTextExpanded = true"
                                                            class="text-xs font-medium underline select-none cursor-pointer">{{
                                                                $t('translations.translation_assignments.read_more_button')
                                                            }}</span></p>
                                                    <p v-if="authorizedTextExpanded" class="mt-2 text-xs text-gray-900">
                                                        {{
                                                            $t('translations.translation_assignments.authorization_description_long_1')
                                                        }}</p>
                                                    <p v-if="authorizedTextExpanded" class="mt-2 text-xs text-gray-900">
                                                        {{
                                                            $t('translations.translation_assignments.authorization_description_long_2')
                                                        }} <span @click="authorizedTextExpanded = false"
                                                            class="text-xs font-medium underline select-none cursor-pointer">{{
                                                                $t('translations.translation_assignments.read_less_button')
                                                            }}</span></p>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <fieldset class="mt-4 col-span-8 sm:col-start-3 sm:col-span-4">
                                            <div class="space-y-0 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
                                                <div v-for="assignmentType in assignmentTypes" :key="assignmentType.id"
                                                    class="flex items-center">
                                                    <input :id="assignmentType.id" name="notification-method"
                                                        type="radio"
                                                        :checked="form.assignment_type == assignmentType.id"
                                                        class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-600"
                                                        @click="form.assignment_type = assignmentType.id" />
                                                    <label @click="form.assignment_type = assignmentType.id"
                                                        :for="assignmentType.id"
                                                        class="ml-3 block text-sm text-gray-900 font-medium leading-6 text-gray-900">{{
                                                            assignmentType.title }}</label>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>

                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('assignment_type')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.assignment_type }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.first_name"
                                            @change="form.validate('first_name')"
                                            :placeholder="$t('translations.translation_assignments.name_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('first_name')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.first_name }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-2 sm:gap-4">
                                        <div class="col-span-8 sm:col-start-3 sm:col-span-2">
                                            <input type="text" v-model="form.email" @change="form.validate('email')"
                                                :placeholder="$t('translations.translation_assignments.email_label')"
                                                class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                            <p v-if="form.invalid('email')"
                                                class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">
                                                {{ form.errors.email }}</p>
                                        </div>

                                        <div class="col-span-8 sm:col-span-2">
                                            <input type="text" v-model="form.phone_no"
                                                @change="form.validate('phone_no')"
                                                :placeholder="$t('translations.translation_assignments.phone_no_label')"
                                                class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                            <p v-if="form.invalid('phone_no')"
                                                class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">
                                                {{ form.errors.phone_no }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <textarea v-model="form.notes" @change="form.validate('notes')" rows="4"
                                            :placeholder="$t('translations.translation_assignments.notes_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('notes')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.notes }}</p>
                                    </div>
                                </div>

                                <div class="mt-4 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <fieldset class="col-span-8 sm:col-start-3 sm:col-span-4">
                                            <div class="space-y-5">
                                                <div class="relative flex items-start">
                                                    <div class="flex h-6 items-center">
                                                        <input v-model="termsAccepted" id="terms"
                                                            aria-describedby="terms-description" name="terms"
                                                            type="checkbox"
                                                            class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
                                                    </div>
                                                    <div class="ml-3 text-sm leading-6">
                                                        <label for="terms" class="font-medium text-gray-900">{{
                                                            $t('translations.translation_assignments.terms_1')
                                                        }}</label>
                                                        {{ ' ' }}
                                                        <span class="text-blue-700 select-none"><a
                                                                href="https://www.oversattare.nu/anvandarvillkor/"
                                                                target="_blank">{{
                                                                    $t('translations.translation_assignments.terms_2')
                                                                }}</a></span>
                                                        <span class="text-gray-900 select-none">{{
                                                            $t('translations.translation_assignments.terms_3') }}</span>
                                                        <span class="text-blue-700 select-none"><a
                                                                href="https://www.oversattare.nu/integretetspolicy/"
                                                                target="_blank">{{
                                                                    $t('translations.translation_assignments.terms_4')
                                                                }}</a>.</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>

                                <div class="mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <button type="submit"
                                            :class="{ 'w-full bg-oversattare-green hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-oversattare-green col-span-8 sm:col-start-3 sm:col-span-4': termsAccepted, 'bg-gray-400 cursor-not-allowed': !termsAccepted }"
                                            class="mt-4 inline-flex items-center gap-x-2 rounded-md py-2.5 px-3.5 text-sm font-semibold text-white shadow-sm col-span-8 sm:col-start-3 sm:col-span-4"
                                            :disabled="!termsAccepted">
                                            <div class="flex w-full justify-center">
                                                {{ $t('translations.translation_assignments.send_button') }}
                                            </div>
                                        </button>
                                    </div>
                                </div>

                                <div class="mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <SBXAlert v-if="assignmentSubmitted"
                                            :title="$t('translations.assignments.confirmation_title')"
                                            :message="$t('translations.assignments.confirmation_message')"
                                            class="mt-4 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</template>
