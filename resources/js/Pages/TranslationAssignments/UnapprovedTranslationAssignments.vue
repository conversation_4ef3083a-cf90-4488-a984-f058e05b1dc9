<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import { ArrowRightIcon } from '@heroicons/vue/24/solid';

  import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
  import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';
  import SBXGenericNotification from '@sbxui/Notifications/SBXGenericNotification.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      ArrowRightIcon,
      SBXDataTable,
      SBXFilterBar,
      SBXGenericNotification
    },

    props: {
      translationAssignments: Object,
      filters: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t("translations.translation_assignments.unapproved_title");

      if (this.$page.props.flash.authorization != null) {
        this.showAuthorizationNotification = true;
      }
    },

    updated() {
      this.$page.props.page_info.title_label = this.$t("translations.translation_assignments.unapproved_title");
    },

    data() {
      return {
        columns: [
          { key: 'assignmentslot', label: this.$t('translations.translation_assignments.title') },
          { key: 'customerslot', label: this.$t('translations.translation_assignments.customer_label') },
          { key: 'created_at', label: this.$t('translations.translation_assignments.date_label') },
        ],

        translationAssignmentFilters: {
          search: this.filters.search
        },

        showAuthorizationNotification: false
      }
    },

    computed: {
      authorizationMessage() {
        if (this.$page.props.flash.authorization == null) {
          return '';
        }

        return this.$page.props.flash.authorization;
      }
    },

    methods: {
      changeFilters() {
        this.$inertia.get(this.route('translation_assignment_approvals', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.translationAssignmentFilters, {
          preserveState: false,
          replace: true
        });
      },

      searchValueChanged(search) {
        this.translationAssignmentFilters.search = search;
      },

      authorizationNotificationCancelled() {
        this.showAuthorizationNotification = false;
      },
    }
  }
</script>

<template>
  <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="translationAssignmentFilters" searchRoute="translation_assignment_approvals" :placeholder="$t('translations.global.search')">
  </SBXFilterBar>

  <div class="flex flex-col">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <SBXDataTable
            :columns="columns"
            :items="translationAssignments.data"
            :showAddButton="false"
            :showEditButton="true"
            editRoute="translation_assignments.edit"
            :showDeleteButton="false"
            :showViewButton="true"
            viewRoute="translation_assignment_approvals.show"
            :paginator="translationAssignments.meta"
          >

            <template v-slot:assignmentslot="row">
              <div>
                <p class="text-sm font-semibold">{{ $t('translations.global.assignment_id') }}: {{ row.item.assignment_id }}</p>
                <p class="text-sm">{{ row.item.translation_category }}</p>
                <div class="flex items-center">
                  <p class="text-sm">{{ row.item.from_translation_language }}</p>
                  <ArrowRightIcon class="h-3 w-3 mx-2" />
                  <p class="text-sm">{{ row.item.to_translation_language }}</p>
                </div>
                <p class="text-sm">{{ $t('translations.translation_assignments.is_authorization_required_label') }}: {{ row.item.authorization_text }}</p>
              </div>
            </template>

            <template v-slot:customerslot="row">
              <div>
                <p v-if="row.item.assignment_type == 'company'" class="text-sm font-semibold">{{ $t('translations.my_assignments.assignment_detail.company_label') }}</p>
                <p v-if="row.item.assignment_type == 'personal'" class="text-sm font-semibold">{{ $t('translations.my_assignments.assignment_detail.private_person_label') }}</p>
                <p class="text-sm font-semibold">{{ row.item.company }}</p>
                <p class="text-sm font-semibold">{{ row.item.first_name }} {{ row.item.last_name }}</p>
                <p class="text-sm"><strong>{{ $t('translations.translation_assignments.email_not_required_label') }}:</strong> <a :href="'mailto:' + row.item.email">{{ row.item.email }}</a></p>
                <p class="text-sm"><strong>{{ $t('translations.translation_assignments.phone_no_not_required_label') }}:</strong> <a :href="'tel:' + row.item.phone_no">{{ row.item.phone_no }}</a></p>
              </div>
            </template>

          </SBXDataTable>
        </div>
      </div>
    </div>
  </div>

  <SBXGenericNotification :show="showAuthorizationNotification" variant="warning" :message="authorizationMessage" @notificationCancelled="authorizationNotificationCancelled" />
</template>
