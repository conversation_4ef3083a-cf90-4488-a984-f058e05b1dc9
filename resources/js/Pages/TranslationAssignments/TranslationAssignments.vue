<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import Swal from 'sweetalert2';

import { ArrowRightIcon, EnvelopeIcon } from '@heroicons/vue/24/solid';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline';

import SBXButton from '@sbxui/Buttons/SBXButton.vue';
import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';
import SBXGenericNotification from '@sbxui/Notifications/SBXGenericNotification.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        ArrowRightIcon,
        CheckCircleIcon,
        XCircleIcon,
        EnvelopeIcon,
        SBXButton,
        SBXDataTable,
        SBXFilterBar,
        SBXGenericNotification
    },

    props: {
        translationAssignments: Object,
        filters: Object,
        pageTitle: String
    },

    mounted() {
        if (this.pageTitle != null) {
            this.$page.props.page_info.title_label = this.pageTitle;
        } else {
            this.$page.props.page_info.title_label = this.$t("translations.translation_assignments.title");
        }

        if (this.$page.props.flash.authorization != null) {
            this.showAuthorizationNotification = true;
        }
    },

    updated() {
        if (this.pageTitle != null) {
            this.$page.props.page_info.title_label = this.pageTitle;
        } else {
            this.$page.props.page_info.title_label = this.$t("translations.translation_assignments.title");
        }
    },

    data() {
        return {
            columns: [
                { key: 'assignmentslot', label: this.$t('translations.translation_assignments.title') },
                { key: 'customerslot', label: this.$t('translations.translation_assignments.customer_label') },
                { key: 'number_of_bids', label: this.$t('translations.translation_assignments.bid_count') },
                { key: 'number_of_views', label: this.$t('translations.translation_assignments.view_count') },
                { key: 'created_at', label: this.$t('translations.translation_assignments.date_label') },
                { key: 'activeslot', label: this.$t('translations.translation_assignments.active_label') },
                { key: 'emailslot', label: this.$t('translations.translation_assignments.mail_label') }
            ],

            translationAssignmentFilters: {
                search: this.filters.search
            },

            showAuthorizationNotification: false
        }
    },

    computed: {
        authorizationMessage() {
            if (this.$page.props.flash.authorization == null) {
                return '';
            }

            return this.$page.props.flash.authorization;
        }
    },

    methods: {
        changeFilters() {
            this.$inertia.get(this.route('translation_assignments', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.translationAssignmentFilters, {
                preserveState: false,
                replace: true
            });
        },

        searchValueChanged(search) {
            this.translationAssignmentFilters.search = search;
        },

        authorizationNotificationCancelled() {
            this.showAuthorizationNotification = false;
        },

        activateAssignment(assignmentID) {
            this.$inertia.put(this.route('translation_assignments.activate', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, assignmentID]), {
                preserveState: false,
                replace: true
            });
        },

        deactivateAssignment(assignmentID) {
            this.$inertia.put(this.route('translation_assignments.deactivate', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, assignmentID]), {
                preserveState: false,
                replace: true
            });
        },

        resendMatchedAssignmentEmails(assignmentID) {
            var self = this;

            Swal.fire({
                title: self.$t("translations.translation_assignments.resend_dialog_title"),
                text: self.$t("translations.translation_assignments.resend_dialog_message"),
                icon: 'warning',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#d33',
                confirmButtonText: self.$t("translations.translation_assignments.resend_dialog_button")
            }).then((result) => {
                if (result.value) {
                    self.$inertia.post(self.route('translation_assignment_resend_match_email', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, assignmentID]), {
                    }, {
                        preserveScroll: true,
                        onSuccess: () => {
                        }
                    });
                }
            });
        },
    }
}
</script>

<template>
    <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="translationAssignmentFilters"
        searchRoute="translation_assignments" :placeholder="$t('translations.global.search')">
    </SBXFilterBar>

    <div class="flex flex-col">
        <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                    <SBXDataTable :columns="columns" :items="translationAssignments.data" :showAddButton="true"
                        addRoute="translation_assignments.create"
                        :addButtonText="$t('translations.translation_assignments.add_button')" :showEditButton="true"
                        editRoute="translation_assignments.edit" :showViewButton="true"
                        viewRoute="translation_assignments.view" :showDeleteButton="true"
                        deleteRoute="translation_assignments.destroy"
                        :deleteDialogTitle="$t('translations.translation_assignments.delete_dialog_title')"
                        :deleteDialogMessage="$t('translations.translation_assignments.delete_dialog_message')"
                        :deleteDialogOKText="$t('translations.translation_assignments.delete_dialog_ok_button')"
                        :deleteDialogCancelText="$t('translations.global.cancel')"
                        :paginator="translationAssignments.meta">

                        <template v-slot:assignmentslot="row">
                            <div>
                                <p class="text-sm font-semibold">{{ $t('translations.global.assignment_id') }}: {{
                                    row.item.assignment_id }}</p>
                                <p class="text-sm">{{ row.item.translation_category }}</p>
                                <div class="flex items-center">
                                    <p class="text-sm">{{ row.item.from_translation_language }}</p>
                                    <ArrowRightIcon class="h-3 w-3 mx-2" />
                                    <p class="text-sm">{{ row.item.to_translation_language }}</p>
                                </div>
                                <p class="text-sm">{{
                                    $t('translations.translation_assignments.is_authorization_required_label') }}: {{
                                        row.item.authorization_text }}</p>
                            </div>
                        </template>

                        <template v-slot:customerslot="row">
                            <div>
                                <p v-if="row.item.assignment_type == 'company'" class="text-sm font-semibold">{{
                                    $t('translations.my_assignments.assignment_detail.company_label') }}</p>
                                <p v-if="row.item.assignment_type == 'personal'" class="text-sm font-semibold">{{
                                    $t('translations.my_assignments.assignment_detail.private_person_label') }}</p>
                                <p class="text-sm font-semibold">{{ row.item.company }}</p>
                                <p class="text-sm font-semibold">{{ row.item.first_name }} {{ row.item.last_name }}</p>
                                <p class="text-sm"><strong>{{
                                    $t('translations.translation_assignments.email_not_required_label') }}:</strong>
                                    <a :href="'mailto:' + row.item.email">{{ row.item.email }}</a>
                                </p>
                                <p class="text-sm"><strong>{{
                                    $t('translations.translation_assignments.phone_no_not_required_label')
                                        }}:</strong> <a :href="'tel:' + row.item.phone_no">{{ row.item.phone_no }}</a>
                                </p>
                            </div>
                        </template>

                        <template v-slot:activeslot="row">
                            <span @click="activateAssignment(row.item.id)" v-if="!row.item.is_active"
                                class="inline-flex flex-shrink-0 items-center justify-center cursor-pointer">
                                <XCircleIcon class="h-8 w-8 text-red-600 group-hover:text-red-800" aria-hidden="true" />
                            </span>

                            <span @click="deactivateAssignment(row.item.id)" v-if="row.item.is_active"
                                class="inline-flex flex-shrink-0 items-center justify-center cursor-pointer">
                                <CheckCircleIcon class="h-8 w-8 text-green-600 group-hover:text-green-800"
                                    aria-hidden="true" />
                            </span>
                        </template>

                        <template v-slot:emailslot="row">
                            <SBXButton @click="resendMatchedAssignmentEmails(row.item.id)" size="s">
                                <EnvelopeIcon class="h-5 w-5 text-white" aria-hidden="true" />
                            </SBXButton>
                        </template>

                    </SBXDataTable>
                </div>
            </div>
        </div>
    </div>

    <SBXGenericNotification :show="showAuthorizationNotification" variant="warning" :message="authorizationMessage"
        @notificationCancelled="authorizationNotificationCancelled" />
</template>
