<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineComponent } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import SBXSelect from '@sbxui/Form/SBXSelect.vue';
import SBXInput from '@sbxui/Form/SBXInput.vue';
import SBXToggle from '@sbxui/Form/SBXToggle.vue';
import SBXTextArea from '@sbxui/Form/SBXTextArea.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';

import FileRow from '../MyAssignments/Components/FileRow.vue';
import FileRowAdmin from './Components/FileRowAdmin.vue';
import FileUpload from './Components/FileUpload.vue';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        SBXDefaultPageLayout,
        SBXSelect,
        SBXInput,
        SBXToggle,
        SBXTextArea,
        SBXButton,
        FileRow,
        FileRowAdmin,
        FileUpload
    },

    props: {
        translationAssignment: Object,
        translationCategories: Object,
        translationLanguages: Object,
        errors: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.pageTitle;

        console.log("Assignment", this.translationAssignment.data);
    },

    updated() {
        this.$page.props.page_info.title_label = this.pageTitle;
    },

    data() {
        return {
            form: useForm('put', this.route('translation_assignments.update', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.translationAssignment.data.id]), {
                translation_category_id: this.translationAssignment.data.translation_category_id,
                from_translation_language_id: this.translationAssignment.data.from_translation_language_id,
                to_translation_language_id: this.translationAssignment.data.to_translation_language_id,
                is_authorization_required: this.translationAssignment.data.is_authorization_required,
                number_of_words: this.translationAssignment.data.number_of_words,
                assignment_type: (this.translationAssignment.data.assignment_type == 'company'),
                first_name: this.translationAssignment.data.first_name,
                last_name: this.translationAssignment.data.last_name,
                company: this.translationAssignment.data.company,
                company_no: this.translationAssignment.data.company_no,
                email: this.translationAssignment.data.email,
                phone_no: this.translationAssignment.data.phone_no,
                notes: this.translationAssignment.data.notes,
                is_email_contact_allowed: this.translationAssignment.data.is_email_contact_allowed,
                is_phone_contact_allowed: this.translationAssignment.data.is_phone_contact_allowed
            }),
        }
    },

    computed: {
        pageTitle() {
            return `${this.$t("translations.translation_assignments.edit_title")}`;
        },

        categoryOptions() {
            var options = [];

            options.push({ value: null, text: this.$t("translations.translation_assignments.category_prompt"), enabled: false });

            for (var i = 0; i < this.translationCategories.data.length; i++) {
                let currentCategory = this.translationCategories.data[i];

                options.push({ value: currentCategory.id, text: currentCategory.name });
            }

            return options;
        },

        languageOptions() {
            var options = [];

            options.push({ value: null, text: this.$t("translations.translation_assignments.language_prompt"), enabled: false });

            for (var i = 0; i < this.translationLanguages.data.length; i++) {
                let currentLanguage = this.translationLanguages.data[i];

                options.push({ value: currentLanguage.id, text: currentLanguage.name });
            }

            return options;
        }
    },

    methods: {
        update() {
            if (!this.form.processing) {
                this.form.submit({
                    preserveScroll: true,
                    preserveState: false,
                    onSuccess: page => {
                        // self.updateNoteOpen = false;
                        console.log("Page Updated", page);
                    },
                    onError: errors => {
                        console.log("Page Errors", errors);
                    },
                });
            }
        },
    }
}
</script>

<template>
    <SBXDefaultPageLayout>
        <form @submit.prevent="update">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <SBXSelect v-model:model="form.translation_category_id" :items="categoryOptions"
                        valueFieldName="value" :label="$t('translations.translation_assignments.category_label')"
                        @change="form.validate('translation_category_id')"
                        :error="form.errors.translation_category_id" />

                    <SBXSelect v-model:model="form.from_translation_language_id" :items="languageOptions"
                        valueFieldName="value" :label="$t('translations.translation_assignments.from_language_label')"
                        @change="form.validate('from_translation_language_id')"
                        :error="form.errors.from_translation_language_id" />

                    <SBXSelect v-model:model="form.to_translation_language_id" :items="languageOptions"
                        valueFieldName="value" :label="$t('translations.translation_assignments.to_language_label')"
                        @change="form.validate('to_translation_language_id')"
                        :error="form.errors.to_translation_language_id" />

                    <SBXToggle v-model:model="form.is_authorization_required"
                        :label="$t('translations.translation_assignments.is_authorization_required_label')"
                        class="mt-4" />

                    <SBXInput v-model:model="form.number_of_words"
                        :label="$t('translations.translation_assignments.number_of_words_label')"
                        @change="form.validate('number_of_words')" :error="form.errors.number_of_words" />

                    <div v-if="translationAssignment.data.files.length > 0">
                        <h4 class="mt-4 text-lg font-semibold text-oversattare-text-black">{{
                            $t('translations.my_assignments.assignment_detail.files_label') }}</h4>
                        <FileRowAdmin v-for="file in translationAssignment.data.files" :file="file"
                            :assignmentId="translationAssignment.data.id" />
                        <FileUpload :assignment="translationAssignment.data" />
                    </div>

                    <div v-else>
                        <h4 class="mt-4 text-lg font-semibold text-oversattare-text-black">{{
                            $t('translations.my_assignments.assignment_detail.files_label') }}</h4>
                        <FileUpload :assignment="translationAssignment.data" />
                    </div>

                    <SBXToggle v-model:model="form.assignment_type"
                        :label="$t('translations.translation_assignments.company_label')" class="mt-4" />

                    <SBXInput v-model:model="form.first_name"
                        :label="$t('translations.translation_assignments.first_name_label')"
                        @change="form.validate('first_name')" :error="form.errors.first_name" />

                    <SBXInput v-model:model="form.last_name"
                        :label="$t('translations.translation_assignments.last_name_label')"
                        @change="form.validate('last_name')" :error="form.errors.last_name" />

                    <SBXInput v-model:model="form.company"
                        :label="$t('translations.translation_assignments.company_label')"
                        @change="form.validate('company')" :error="form.errors.company" />

                    <SBXInput v-model:model="form.company_no"
                        :label="$t('translations.translation_assignments.company_no_label')"
                        @change="form.validate('company_no')" :error="form.errors.company_no" />

                    <SBXInput v-model:model="form.email" :label="$t('translations.translation_assignments.email_label')"
                        @change="form.validate('email')" :error="form.errors.email" />

                    <SBXInput v-model:model="form.phone_no"
                        :label="$t('translations.translation_assignments.phone_no_label')"
                        @change="form.validate('phone_no')" :error="form.errors.phone_no" />

                    <SBXTextArea v-model:model="form.notes"
                        :label="$t('translations.translation_assignments.notes_label')" :rows="5"
                        :error="form.errors.notes" />

                    <SBXToggle v-model:model="form.is_email_contact_allowed"
                        :label="$t('translations.translation_assignments.contact_option_email_label')" class="mt-4"
                        :error="form.errors.is_email_contact_allowed" />

                    <SBXToggle v-model:model="form.is_phone_contact_allowed"
                        :label="$t('translations.translation_assignments.contact_option_phone_label')" class="mt-4"
                        :error="form.errors.is_phone_contact_allowed" />

                    <SBXButton class="mt-4">{{ $t('translations.translation_assignments.update_button') }}</SBXButton>
                </div>
            </div>
        </form>
    </SBXDefaultPageLayout>
</template>
