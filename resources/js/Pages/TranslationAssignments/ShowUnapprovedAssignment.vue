<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineComponent } from 'vue'
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import Swal from 'sweetalert2';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';

import FileRow from './Components/FileRow.vue';
import AssignTranslator from './Components/AssignTranslator.vue';


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default defineComponent({
    components: {
        FileRow,
        SBXDefaultPageLayout,
        SBXButton,
        AssignTranslator
    },

    props: {
        assignment: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_assignments.approvals_detail.title");

        console.log("Assignment", this.assignment.data);
        this.fetchPriceMarketAddon();
    },

    watch: {
        price(newPrice) {
            if (newPrice > 0) {
                this.priceEndCustomer = this.calculateEndCustomerPrice(newPrice, this.assignmentType);

            }
        }
    },

    data() {
        return {
            price: 0,
            priceEndCustomer: 0,
            assignmentType: this.assignment.data.assignment_type,
            priceMarketAddon: null
        }
    },

    computed: {
        disablePremarketPriceButton() {
            return this.price < 1;
        }
    },

    methods: {
        approveAssignment() {
            var self = this;

            Swal.fire({
                title: self.$t("translations.translation_assignments.approvals_detail.approve_dialog_title"),
                text: self.$t("translations.translation_assignments.approvals_detail.approve_dialog_message"),
                icon: 'warning',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#15803d',
                confirmButtonText: self.$t("translations.translation_assignments.approvals_detail.approve_button")
            }).then((result) => {
                if (result.value) {
                    self.$inertia.post(self.route('translation_assignment_approvals.approve', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, self.assignment.data.id]), {
                    }, {
                        preserveScroll: true,
                        onSuccess: () => {
                        }
                    });
                }
            });
        },

        rejectAssignment() {
            var self = this;

            Swal.fire({
                title: self.$t("translations.translation_assignments.approvals_detail.reject_dialog_title"),
                text: self.$t("translations.translation_assignments.approvals_detail.reject_dialog_message"),
                icon: 'warning',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#d33',
                confirmButtonText: self.$t("translations.translation_assignments.approvals_detail.reject_button")
            }).then((result) => {
                if (result.value) {
                    self.$inertia.post(self.route('translation_assignment_approvals.reject', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, self.assignment.data.id]), {
                    }, {
                        preserveScroll: true,
                        onSuccess: () => {
                        }
                    });
                }
            });
        },

        deleteAssignment() {
            var self = this;

            Swal.fire({
                title: self.$t("translations.translation_assignments.approvals_detail.delete_dialog_title"),
                text: self.$t("translations.translation_assignments.approvals_detail.delete_dialog_message"),
                icon: 'warning',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#d33',
                confirmButtonText: self.$t("translations.translation_assignments.approvals_detail.delete_button")
            }).then((result) => {
                if (result.value) {
                    self.$inertia.post(self.route('translation_assignment_approvals.delete', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, self.assignment.data.id]), {
                    }, {
                        preserveScroll: true,
                        onSuccess: () => {
                        }
                    });
                }
            });
        },

        sendPremarketPrice() {
            var self = this;

            Swal.fire({
                title: self.$t("translations.translation_assignments.approvals_detail.send_premarket_offer_title"),
                text: self.$t("translations.translation_assignments.approvals_detail.send_premarket_offer_message") + ' ' + self.price + ' SEK',
                icon: 'warning',
                showCancelButton: true,
                cancelButtonText: self.$t("translations.global.cancel"),
                confirmButtonColor: '#d33',
                confirmButtonText: self.$t("translations.translation_assignments.approvals_detail.send_premarket_offer_button")
            }).then((result) => {
                if (result.value) {
                    console.log(self.assignment.data.id);
                    self.$inertia.put(self.route('translation_assignments.add_premarket_bid', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, self.assignment.data.id]), {
                        price: self.price
                    }, {
                        preserveScroll: true,
                        onSuccess: () => {
                        }
                    });
                }
            });
        },

        fetchPriceMarketAddon() {
            var self = this;

            // Replace with your actual URL
            const url = self.route('translation_assignments.get_price_calculator_data', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code]);
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    this.priceMarketAddon = data;
                    console.log('Price market addon:', this.priceMarketAddon);
                })
                .catch(error => {
                    console.error('Error fetching price market addon:', error);
                });
        },

        calculateEndCustomerPrice(price, assignmentType) {

            if (!this.priceMarketAddon) {
                return 0;
            }

            const priceBreaks = this.priceMarketAddon.markup_levels[assignmentType];

            let margin = 0.0;
            let fee = 0.0;

            priceBreaks.sort((a, b) => b.min_value - a.min_value);

            for (const priceBreak of priceBreaks) {
                if (price >= priceBreak.min_value) {
                    fee = priceBreak.fee;
                    if (priceBreak.type === 'fixed') {
                        margin = priceBreak.markup;
                    } else if (priceBreak.type === 'percentage') {
                        margin = price * (priceBreak.markup / 100);
                    }
                    break;
                }
            }

            return price + fee + margin;
        },
    }
})
</script>

<template>
    <SBXDefaultPageLayout>
        <div class="p-3 sm:grid sm:grid-cols-2 gap-4">
            <div>
                <h3 class="text-2xl font-semibold text-oversattare-text-black">{{
                    $t('translations.my_assignments.assignment_detail.title') }}</h3>

                <h4 class="mt-4 text-lg font-semibold text-oversattare-text-black">{{
                    $t('translations.my_assignments.assignment_detail.description_label') }}</h4>
                <p class="text-sm text-oversattare-text-black">{{ assignment.data.assignment_id }}, {{
                    assignment.data.translation_category }}, {{ assignment.data.from_translation_language }} {{
                        $t('translations.my_assignments.assignment_detail.to_label') }} {{
                        assignment.data.to_translation_language }}.</p>

                <div v-if="assignment.data.number_of_words != null && assignment.data.number_of_words > 0">
                    <h4 class="mt-4 text-base font-semibold text-oversattare-text-black">{{
                        $t('translations.my_assignments.assignment_detail.word_count_label') }}</h4>
                    <p class="text-sm text-oversattare-text-black">{{ assignment.data.number_of_words }}</p>
                </div>

                <div v-if="assignment.data.files.length > 0">
                    <h4 class="mt-4 text-lg font-semibold text-oversattare-text-black">{{
                        $t('translations.my_assignments.assignment_detail.files_label') }}</h4>
                    <FileRow v-for="file in assignment.data.files" :file="file" />
                </div>

                <div>
                    <h4 class="mt-4 text-base font-semibold text-oversattare-text-black">{{
                        $t('translations.my_assignments.assignment_detail.authorization_label') }}</h4>
                    <p v-if="assignment.data.is_authorization_required" class="text-sm text-oversattare-text-black">{{
                        $t('translations.global.yes') }}</p>
                    <p v-if="!assignment.data.is_authorization_required" class="text-sm text-oversattare-text-black">{{
                        $t('translations.global.no') }}</p>
                </div>

                <h4 class="mt-8 mb-2 text-2xl font-semibold text-oversattare-text-black">{{
                    $t('translations.my_assignments.assignment_detail.contact_info_title') }}</h4>
                <p v-if="assignment.data.assignment_type == 'company'" class="text-sm text-oversattare-text-black">
                    <strong>{{ $t('translations.my_assignments.assignment_detail.company_label') }}</strong>
                </p>
                <p v-if="assignment.data.assignment_type == 'personal'" class="text-sm text-oversattare-text-black">
                    <strong>{{ $t('translations.my_assignments.assignment_detail.private_person_label') }}</strong>
                </p>
                <p class="text-sm text-oversattare-text-black"><strong>{{
                    $t('translations.my_assignments.assignment_detail.name_label') }}: </strong>{{
                            assignment.data.first_name }} {{ assignment.data.last_name }}</p>
                <p v-if="assignment.data.assignment_type == 'company'" class="text-sm text-oversattare-text-black">
                    <strong>{{ $t('translations.my_assignments.assignment_detail.company_label') }}: </strong>{{
                        assignment.data.company }}
                </p>
                <p v-if="assignment.data.assignment_type == 'company'" class="text-sm text-oversattare-text-black">
                    <strong>{{ $t('translations.my_assignments.assignment_detail.company_no_label') }}: </strong>{{
                        assignment.data.company_no }}
                </p>
                <p class="text-sm text-oversattare-text-black"><strong>{{
                    $t('translations.my_assignments.assignment_detail.email_label') }}: </strong><a
                        :href="'mailto:' + assignment.data.email">{{ assignment.data.email }}</a></p>
                <p class="text-sm text-oversattare-text-black"><strong>{{
                    $t('translations.my_assignments.assignment_detail.phone_label') }}: </strong><a
                        :href="'tel:' + assignment.data.phone_no">{{ assignment.data.phone_no }}</a></p>

                <!-- <h4 class="mt-4 text-lg font-semibold text-oversattare-text-black">{{
                    $t('translations.my_assignments.assignment_detail.contact_preferences_label') }}</h4>
                <p v-if="assignment.data.is_email_contact_allowed" class="text-sm text-oversattare-text-black">{{
                    $t('translations.my_assignments.assignment_detail.email_label') }}</p>
                <p v-if="assignment.data.is_phone_contact_allowed" class="text-sm text-oversattare-text-black">{{
                    $t('translations.my_assignments.assignment_detail.phone_label') }}</p> -->

                <div v-if="assignment.data.notes != null && assignment.data.notes.length != ''">
                    <h4 class="mt-8 text-2xl font-semibold text-oversattare-text-black">{{
                        $t('translations.my_assignments.assignment_detail.notes_label') }}</h4>
                    <p class="text-sm text-oversattare-text-black sm:w-1/2">{{ assignment.data.notes }}</p>
                </div>

                <div v-if="assignment.data.premarket_bid == null" className="bg-gray-100 sm:rounded-lg my-4">
                    <div className="px-4 py-5 sm:p-6">
                        <h3 className="text-base font-semibold text-gray-900">{{
                            $t('translations.translation_assignments.premarket_offer_label') }}</h3>
                        <div className="mt-2 max-w-xl text-sm text-gray-500">
                            <p>Skicka prisförslag innan uppdraget aktiveras</p>
                        </div>
                        <div className="mt-5 sm:flex sm:items-center">
                            <div className="w-full sm:max-w-xs">
                                <input id="price" name="price" type="number" v-model="price" placeholder="0,00"
                                    aria-label="Price" pattern="[0-9]*" inputmode="numeric"
                                    className="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-yellow-600 sm:text-sm/6" />
                            </div>
                            <button type="button" @click="sendPremarketPrice" :disabled="disablePremarketPriceButton"
                                className="mt-3 inline-flex w-full items-center justify-center rounded-md bg-yellow-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-yellow-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:ml-3 sm:mt-0 sm:w-auto disabled:bg-gray-300">
                                Skicka från pris
                            </button>
                        </div>
                        <div class="mt-2 text-sm text-gray-500">
                            <p>Slutkund pris: {{ priceEndCustomer.toFixed(2) }} SEK</p>
                        </div>
                    </div>
                </div>
                <div v-else className="my-4">
                    <div className="py-5 sm:py-6">
                        <h3 className="text-base font-semibold text-gray-900">{{
                            $t('translations.translation_assignments.premarket_offer_label') }}</h3>
                        <div class="mt-2 text-sm text-gray-500">
                            <p>Pris: {{ assignment.data.premarket_bid.price_end_customer }} SEK</p>
                        </div>
                    </div>
                </div>

                <div class="mt-4 flex flex-wrap">
                    <SBXButton @click="approveAssignment" variant="success" class="mb-4 mt-2 mr-2">{{
                        $t('translations.translation_assignments.approvals_detail.approve_button') }}</SBXButton>
                    <SBXButton @click="rejectAssignment" variant="danger" class="mb-4 mt-2 mr-2">{{
                        $t('translations.translation_assignments.approvals_detail.reject_button') }}</SBXButton>
                    <SBXButton @click="deleteAssignment" variant="danger" class="mb-4 mt-2 mr-2">{{
                        $t('translations.translation_assignments.approvals_detail.delete_button') }}</SBXButton>
                    <AssignTranslator :assignment="assignment.data" class="mb-4 mt-2" />
                </div>
            </div>
        </div>
    </SBXDefaultPageLayout>
</template>
