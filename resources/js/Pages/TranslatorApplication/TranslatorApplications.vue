<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import SBXDataTable from '@sbxui/Tables/SBXDataTable.vue';
  import SBXFilterBar from '@sbxui/Application/SBXFilterBar.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      SBXDataTable,
      SBXFilterBar
    },

    props: {
      translatorApplications: Object,
      filters: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t('translations.translator_applications.admin.title');

      console.log("translatorApplications", this.translatorApplications.data);
    },

    updated() {
      this.$page.props.page_info.admin_title_label = this.$t('translations.translator_applications.title');
    },

    data() {
      return {
        columns: [
          { key: 'customerslot', label: this.$t('translations.translators.translator_label') },
          { key: 'contactslot', label: this.$t('translations.translator_applications.admin.contact_label') },
          { key: 'infoslot', label: this.$t('translations.translators.other_info_label') },
          { key: 'created_at', label: this.$t('translations.translator_applications.admin.date_label') }
        ],

        translatorFilters: {
          search: this.filters.search
        },
      }
    },

    methods: {
      changeFilters() {
        this.$inertia.get(this.route('translator_applications', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), this.translatorFilters, {
          preserveState: false,
          replace: true
        });
      },

      searchValueChanged(search) {
        this.translatorFilters.search = search;
      },
    }
  }
</script>

<template>
  <SBXFilterBar @searchValueChanged="searchValueChanged" :filters="translatorFilters" searchRoute="translator_applications" :placeholder="$t('translations.global.search')">
  </SBXFilterBar>

  <div class="flex flex-col">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <SBXDataTable
            :columns="columns"
            :items="translatorApplications.data"
            :showViewButton="true"
            viewRoute="translator_applications.show"
            :showAddButton="false"
            :showEditButton="false"
            :showDeleteButton="false"
            :paginator="translatorApplications.meta"
          >

            <template v-slot:customerslot="row">
              <div>
                <p class="text-sm font-semibold">{{ $t('translations.global.customer_id') }}: {{ row.item.translator_id }}</p>
                <p class="text-sm font-semibold">{{ row.item.company }}</p>
                <p class="text-sm font-semibold">{{ row.item.first_name }} {{ row.item.last_name }}</p>
                <p class="text-sm">{{ row.item.address_1 }}</p>
                <p class="text-sm">{{ row.item.address_2 }}</p>
                <p class="text-sm">{{ row.item.postal_code }} {{ row.item.city }}</p>
              </div>
            </template>

            <template v-slot:contactslot="row">
              <div>
                <p class="text-sm"><strong>E-post: </strong><a :href="`mailto:${row.item.email}`">{{ row.item.email }}</a></p>
                <p class="text-sm"><strong>Telefon: </strong><a :href="`tel:${row.item.phone_no}`">{{ row.item.phone_no }}</a></p>
              </div>
            </template>

            <template v-slot:infoslot="row">
              <div>
                <p class="text-sm"><strong>{{ $t('translations.translators.from_label') }}: </strong>{{ row.item.from_translation_languages }}</p>
                <p class="text-sm"><strong>{{ $t('translations.translators.to_label') }}: </strong>{{ row.item.to_translation_languages }}</p>
                <p class="text-sm"><strong>{{ $t('translations.translators.categories_label') }}: </strong>{{ row.item.translation_categories }}</p>
                <p v-if="row.item.is_authorized" class="text-sm"><strong>{{ $t('translations.translators.authorized_id_label') }}: </strong>{{ row.item.authorization_id }}</p>
                <p v-else class="text-sm"><strong>{{ $t('translations.translators.authorized_id_label') }}: </strong> -</p>
              </div>
            </template>
          </SBXDataTable>
        </div>
      </div>
    </div>
  </div>
</template>
