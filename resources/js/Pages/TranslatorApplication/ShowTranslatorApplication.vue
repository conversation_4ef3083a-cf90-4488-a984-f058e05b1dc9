<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue'
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import Swal from 'sweetalert2';

  import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
  import SBXButton from '@sbxui/Buttons/SBXButton.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default defineComponent({
    components: {
      SBXDefaultPageLayout,
      SBXButton
    },

    props: {
      translatorApplication: Object
    },

    mounted() {
      this.$page.props.page_info.title_label = this.$t("translations.translator_applications.admin.show_title");
    },

    data() {
      return {
        deliveryLocation: this.translatorApplication.data.deliveryLocation
      }
    },

    computed: {
    },

    methods: {
      approveTranslatorApplication() {
        var self = this;

        Swal.fire({
          title: self.$t("translations.translator_applications.admin.approve_dialog_title"),
          text: self.$t("translations.translator_applications.admin.approve_dialog_message"),
          icon: 'warning',
          showCancelButton: true,
          cancelButtonText: self.$t("translations.global.cancel"),
          confirmButtonColor: '#15803d',
          confirmButtonText: self.$t("translations.translator_applications.admin.approve_button")
        }).then((result) => {
          if (result.value) {
            self.$inertia.post(self.route('translator_applications.approve', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, self.translatorApplication.data.id]), {
            }, {
              preserveScroll: true,
              onSuccess: () => {
              }
            });
          }
        });
      },

      rejectTranslatorApplication() {
        var self = this;

        Swal.fire({
          title: self.$t("translations.translator_applications.admin.reject_dialog_title"),
          text: self.$t("translations.translator_applications.admin.reject_dialog_message"),
          icon: 'warning',
          showCancelButton: true,
          cancelButtonText: self.$t("translations.global.cancel"),
          confirmButtonColor: '#d33',
          confirmButtonText: self.$t("translations.translator_applications.admin.reject_button")
        }).then((result) => {
          if (result.value) {
            self.$inertia.post(self.route('translator_applications.reject', [self.$page.props.locale.selected_market_code, self.$page.props.locale.selected_language_code, self.translatorApplication.data.id]), {
            }, {
              preserveScroll: true,
              onSuccess: () => {
              }
            });
          }
        });
      }
    }
})
</script>

<template>
  <SBXDefaultPageLayout>
    <p class="text-lg block font-bold">{{ $t("translations.translator_applications.admin.submitted_info_title") }}</p>

    <p class="text-base block font-regular">{{ translatorApplication.data.company }}</p>
    <p class="text-base block font-regular">{{ translatorApplication.data.first_name }} {{ translatorApplication.data.last_name }}</p>
    <p class="text-base block font-regular">{{ translatorApplication.data.address_1 }}</p>
    <p class="text-base block font-regular">{{ translatorApplication.data.address_2 }}</p>
    <p class="text-base block font-regular">{{ translatorApplication.data.postal_code }} {{ translatorApplication.data.city }}</p>
    <p v-if="translatorApplication.data.company_no != null && translatorApplication.data.company_no != ''" class="text-sm mt-4"><strong>{{ $t("translations.translator_applications.admin.company_no_label") }}: </strong>{{ translatorApplication.data.company_no }}</p>
    <p class="text-sm mt-4"><strong>{{ $t("translations.translator_applications.admin.email_label") }}: </strong><a :href="`mailto:${translatorApplication.data.email}`">{{ translatorApplication.data.email }}</a></p>
    <p class="text-sm"><strong>{{ $t("translations.translator_applications.admin.phone_no_label") }}: </strong><a :href="`tel:${translatorApplication.data.phone_no}`">{{ translatorApplication.data.phone_no }}</a></p>

    <div v-if="translatorApplication.data.services.length > 0" class="mt-4">
      <p class="text-sm block font-bold">{{ $t("translations.translator_applications.admin.services_label") }}</p>
      <p class="text-sm block" v-for="service in translatorApplication.data.services">{{ service.name }}</p>
    </div>

    <div v-if="translatorApplication.data.from_languages.length > 0" class="mt-4">
      <p class="text-sm block font-bold">{{ $t("translations.translator_applications.admin.from_languages_label") }}</p>
      <p class="text-sm block" v-for="language in translatorApplication.data.from_languages">{{ language.name }}</p>
    </div>

    <div v-if="translatorApplication.data.to_languages.length > 0" class="mt-4">
      <p class="text-sm block font-bold">{{ $t("translations.translator_applications.admin.to_languages_label") }}</p>
      <p class="text-sm block" v-for="language in translatorApplication.data.to_languages">{{ language.name }}</p>
    </div>

    <p v-if="translatorApplication.data.company_no != null && translatorApplication.data.company_no != ''" class="text-sm mt-4"><strong>{{ $t("translations.translator_applications.admin.authorized_id_label") }}: </strong>{{ translatorApplication.data.authorization_id }}</p>

   <div class="mt-4 flex">
    <SBXButton @click="approveTranslatorApplication" variant="success" class="mb-4 mt-2 mr-2">{{ $t('translations.translator_applications.admin.approve_button') }}</SBXButton>
    <SBXButton @click="rejectTranslatorApplication" variant="danger" class="mb-4 mt-2">{{ $t('translations.translator_applications.admin.reject_button') }}</SBXButton>
   </div>
  </SBXDefaultPageLayout>
</template>
