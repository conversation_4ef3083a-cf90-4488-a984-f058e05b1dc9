<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

import { CheckIcon } from '@heroicons/vue/24/outline';

import StepsDivider from '../../Global/Components/StepsDivider.vue';
import CheckboxGroup from '../../Global/Components/CheckboxGroup.vue';

import SBX<PERSON>lert from '@sbxui/Notifications/SBXAlert.vue';
import SBXComboBox from '@sbxui/Form/SBXComboBox.vue';

import Multiselect from 'vue-multiselect'



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        Head,
        Link,
        StepsDivider,
        CheckboxGroup,
        CheckIcon,
        SBXAlert,
        SBXComboBox,
        Multiselect
    },

    props: {
        translationLanguages: Object,
        translationCategories: Object,
        errors: Object
    },

    layout: null,

    data() {
        return {
            form: useForm('post', this.route('translator_applications.store', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]), {
                company: null,
                company_no: null,
                first_name: null,
                last_name: null,
                address_1: null,
                address_2: null,
                postal_code: null,
                city: null,
                email: null,
                phone_no: null,
                from_translation_languages: [],
                to_translation_languages: [],
                translation_categories: [],
                is_authorized: false,
                authorization_id: null,
            }),

            termsAccepted: false,

            applicationSubmitted: false,

            selectedFromLanguages: [],
            selectedToLanguages: [],
            selectedCategories: []
        }
    },

    computed: {
        confirmationMessage() {
            return this.$t('translations.translator_applications.confirmation_message');
        }
    },

    methods: {
        createApplication() {
            var self = this;

            if (!this.form.processing && this.termsAccepted) {
                this.form.submit({
                    preserveScroll: true,
                    onSuccess: page => {
                        self.applicationSubmitted = true;

                        self.form.company = null;
                        self.form.company_no = null;
                        self.form.first_name = null;
                        self.form.last_name = null;
                        self.form.address_1 = null;
                        self.form.address_2 = null;
                        self.form.postal_code = null;
                        self.form.city = null;
                        self.form.email = null;
                        self.form.phone_no = null;
                        self.termsAccepted = false;
                        self.form.from_translation_languages = [];
                        self.form.to_translation_languages = [];
                        self.form.translation_categories = [];
                        self.form.is_authorized = false;
                        self.form.authorization_id = null;

                        self.fromTranslationLanguages = [];
                        self.toTranslationLanguages = [];
                        for (var i = 0; i < self.translationLanguages.data.length; i++) {
                            let currentTranslationLanguage = self.translationLanguages.data[i];

                            self.fromTranslationLanguages.push({ id: currentTranslationLanguage.id, value: currentTranslationLanguage.name, isSelected: false });
                            self.toTranslationLanguages.push({ id: currentTranslationLanguage.id, value: currentTranslationLanguage.name, isSelected: false });
                        }

                        self.categories = [];
                        for (var i = 0; i < self.translationCategories.data.length; i++) {
                            let currentTranslationCategory = self.translationCategories.data[i];

                            self.categories.push({ id: currentTranslationCategory.id, value: currentTranslationCategory.name, isSelected: false });
                        }

                        self.form.reset();

                        setTimeout(function () {
                            self.applicationSubmitted = false;
                        }, 3000);
                    }
                });
            }
        },
    }
};
</script>

<template>
    <div class="bg-oversattare-blue-light">

        <Head title="Översättare.nu | Anslut" />

        <div class="relative overflow-hidden">
            <main>
                <div class="relative bg-oversattare-blue-light py-4">
                    <div class="relative">
                        <div class="mx-auto px-4 mb-16 sm:px-6 lg:px-8">
                            <p class="text-2xl text-center font-semibold tracking-tight text-gray-900 sm:text-3xl">{{
                                $t('translations.translator_applications.title') }}</p>
                            <p class="mt-8 text-md text-center tracking-tight text-gray-900 sm:text-lg">{{
                                $t('translations.translator_applications.subtitle') }}</p>

                            <form @submit.prevent="createApplication">
                                <!-- START Contact Information -->
                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <StepsDivider :step="1"
                                            :label="$t('translations.translator_applications.step_1_title')"
                                            class="mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>

                                <div class="mt-4 sm:mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.company" @change="form.validate('company')"
                                            :placeholder="$t('translations.translator_applications.company_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('company')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.company }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.first_name"
                                            @change="form.validate('first_name')"
                                            :placeholder="$t('translations.translator_applications.first_name_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('first_name')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.first_name }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.last_name" @change="form.validate('last_name')"
                                            :placeholder="$t('translations.translator_applications.last_name_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('last_name')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.last_name }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="email" v-model="form.email" @change="form.validate('email')"
                                            :placeholder="$t('translations.translator_applications.email_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('email')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.email }}</p>
                                    </div>
                                </div>

                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.phone_no" @change="form.validate('phone_no')"
                                            :placeholder="$t('translations.translator_applications.phone_no_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('phone_no')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.phone_no }}</p>
                                    </div>
                                </div>
                                <!-- END Contact Information -->

                                <!-- START Translate from Languages -->
                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <StepsDivider :step="2"
                                            :label="$t('translations.translator_applications.step_2_title')"
                                            class="mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>

                                <div class="mt-4 sm:mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">

                                        <multiselect v-model="form.from_translation_languages"
                                            :noResult="$t('translations.translation_assignments.no_language_found_label')"
                                            :placeholder="$t('translations.translation_assignments.from_language_search_label')"
                                            :selectLabel="$t('translations.translation_assignments.select_language_label')"
                                            :deselectLabel="$t('translations.translation_assignments.deselect_language_label')"
                                            :selectedLabel="$t('translations.translation_assignments.selected_language_label')"
                                            label="name" track-by="id" :options="translationLanguages.data"
                                            :multiple="true" :taggable="false" :showNoResults="false"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900">
                                        </multiselect>
                                        <p v-if="errors.from_translation_languages" class="text-xs text-red-500">{{
                                            errors.from_translation_languages }}</p>
                                    </div>
                                </div>
                                <!-- END Translate from Languages -->

                                <!-- START Translate to Languages -->
                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <StepsDivider :step="3"
                                            :label="$t('translations.translator_applications.step_3_title')"
                                            class="mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>

                                <div class="mt-4 sm:mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <multiselect v-model="form.to_translation_languages"
                                            :noResult="$t('translations.translation_assignments.no_language_found_label')"
                                            :placeholder="$t('translations.translation_assignments.to_language_search_label')"
                                            :selectLabel="$t('translations.translation_assignments.select_language_label')"
                                            :deselectLabel="$t('translations.translation_assignments.deselect_language_label')"
                                            :selectedLabel="$t('translations.translation_assignments.selected_language_label')"
                                            label="name" track-by="id" :options="translationLanguages.data"
                                            :multiple="true" :taggable="false" :showNoResults="false"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900">
                                        </multiselect>
                                        <p v-if="errors.to_translation_languages" class="text-xs text-red-500">{{
                                            errors.to_translation_languages }}</p>
                                    </div>
                                </div>
                                <!-- END Translate to Languages -->

                                <!-- START Translate Services -->
                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <StepsDivider :step="4"
                                            :label="$t('translations.translator_applications.step_4_title')"
                                            class="mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>

                                <div class="mt-4 sm:mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">

                                        <multiselect v-model="form.translation_categories"
                                            :noResult="$t('translations.translation_assignments.no_category_found_label')"
                                            :placeholder="$t('translations.translation_assignments.from_category_search_label')"
                                            :selectLabel="$t('translations.translator_applications.select_category_label')"
                                            :deselectLabel="$t('translations.translation_assignments.deselect_category_label')"
                                            :selectedLabel="$t('translations.translation_assignments.selected_language_label')"
                                            label="name" track-by="id" :options="translationCategories.data"
                                            :multiple="true" :taggable="false" :showNoResults="false"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full text-gray-900 ">
                                        </multiselect>
                                        <p v-if="errors.translation_categories" class="text-xs text-red-500">{{
                                            errors.translation_categories }}</p>
                                    </div>
                                </div>
                                <!-- END Translate Services -->

                                <!-- START Authorization -->
                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <StepsDivider :step="5"
                                            :label="$t('translations.translator_applications.step_5_title')"
                                            class="mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>

                                <div class="mt-4 sm:mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <fieldset class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full">
                                            <div class="space-y-5">
                                                <div class="relative flex items-start">
                                                    <div class="flex h-6 items-center">
                                                        <input v-model="form.is_authorized" id="authorization"
                                                            type="checkbox"
                                                            class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600 cursor-pointer" />
                                                    </div>
                                                    <div class="ml-3 text-sm leading-6">
                                                        <label for="authorization"
                                                            class="font-medium text-gray-900 cursor-pointer">{{
                                                                $t('translations.translator_applications.is_authorized_label')
                                                            }}</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>

                                <div v-if="form.is_authorized" class="mt-4 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <input type="text" v-model="form.authorization_id"
                                            @change="form.validate('authorization_id')"
                                            :placeholder="$t('translations.translator_applications.authorized_id_label')"
                                            class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div class="grid grid-cols-8 gap-4">
                                        <p v-if="form.invalid('authorization_id')"
                                            class="mt-1 col-span-8 sm:col-start-3 sm:col-span-4 text-xs text-red-500">{{
                                                form.errors.authorization_id }}</p>
                                    </div>
                                </div>
                                <!-- END Authorization -->

                                <!-- START Send Application -->
                                <div class="mt-2 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <StepsDivider :step="6"
                                            :label="$t('translations.translator_applications.step_6_title')"
                                            class="mt-6 sm:mt-12 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>

                                <div class="mt-4 sm:mt-8 mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <fieldset class="col-span-8 sm:col-start-3 sm:col-span-4 block w-full">
                                            <div class="space-y-5">
                                                <div class="relative flex items-start">
                                                    <div class="flex h-6 items-center">
                                                        <input v-model="termsAccepted" id="terms"
                                                            aria-describedby="terms-description" name="terms"
                                                            type="checkbox"
                                                            class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600 cursor-pointer" />
                                                    </div>
                                                    <div class="ml-3 text-sm leading-6">
                                                        <label for="terms"
                                                            class="font-medium text-gray-900 cursor-pointer">{{
                                                                $t('translations.translation_assignments.terms_1')
                                                            }}</label>
                                                        {{ ' ' }}
                                                        <span class="text-blue-700 select-none"><a
                                                                href="https://www.oversattare.nu/anvandarvillkor/"
                                                                target="_blank">{{
                                                                    $t('translations.translation_assignments.terms_2')
                                                                }}</a></span>
                                                        <span class="text-gray-900 select-none">{{
                                                            $t('translations.translation_assignments.terms_3') }}</span>
                                                        <span class="text-blue-700 select-none"><a
                                                                href="https://www.oversattare.nu/integretetspolicy/"
                                                                target="_blank">{{
                                                                    $t('translations.translation_assignments.terms_4')
                                                                }}</a>.</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>

                                <div class="mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <button type="submit"
                                            :class="{ 'w-full bg-oversattare-green hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-oversattare-green col-span-8 sm:col-start-3 sm:col-span-4': termsAccepted, 'bg-gray-400 cursor-not-allowed': !termsAccepted }"
                                            class="mt-4 inline-flex items-center gap-x-2 rounded-md py-2.5 px-3.5 text-sm font-semibold text-white shadow-sm col-span-8 sm:col-start-3 sm:col-span-4">
                                            <div class="flex w-full justify-center">
                                                {{ $t('translations.translator_applications.send_button') }}
                                            </div>
                                        </button>
                                    </div>
                                </div>
                                <!-- END Send Application -->

                                <div class="mx-2 sm:mx-6">
                                    <div class="grid grid-cols-8 gap-4">
                                        <SBXAlert v-if="applicationSubmitted"
                                            :title="$t('translations.translator_applications.confirmation_title')"
                                            :message="$t('translations.translator_applications.confirmation_message')"
                                            class="mt-4 col-span-8 sm:col-start-3 sm:col-span-4" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</template>
