<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { h, ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';
import Swal from 'sweetalert2';

import { FireIcon, UsersIcon, DocumentIcon, LanguageIcon, CogIcon, BuildingOfficeIcon, ClipboardDocumentCheckIcon, ClipboardIcon, DocumentChartBarIcon, ComputerDesktopIcon, BookOpenIcon, MegaphoneIcon, LightBulbIcon, XMarkIcon, InformationCircleIcon, PlusCircleIcon, PaperClipIcon } from '@heroicons/vue/24/outline';
import { ExclamationTriangleIcon, CheckBadgeIcon } from '@heroicons/vue/20/solid'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';
import { get } from 'lodash';


/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        FireIcon,
        UsersIcon,
        DocumentIcon,
        LanguageIcon,
        SBXButton,
        XMarkIcon,
        InformationCircleIcon,
        ExclamationTriangleIcon,
        PlusCircleIcon,
        PaperClipIcon,
        CheckBadgeIcon,
        Dialog,
        DialogPanel,
        DialogTitle,
        TransitionChild,
        TransitionRoot,
    },


    props: {
        assignment: Object,

    },


    data() {
        this.sideOverLay = ref(false);
        return {
            bidForm: useForm('put', this.route('translation_assignment_market.make_bid', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.id]), {
                price: '',
                delivery_date: '',
            }),

            viewForm: useForm('put', this.route('translation_assignment_market.show_details', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.id]), {
            }),
            price: '',
            isPriceValid: false,
            isPriceTouched: false,
        }
    },

    computed: {
        bidText() {
            if (this.assignment.bid_count == 0) {
                return this.$t('translations.translation_assignment_market.first_bidder_prompt');
            }

            return this.assignment.bids_left + ' ' + this.$t('translations.translation_assignment_market.bidder_prompt');
        },
        isButtonDisabled() {
            return !this.bidForm.price || !/^\d+$/.test(this.bidForm.price) || parseInt(this.bidForm.price) <= 0 || !this.bidForm.delivery_date;
        },
        priceInputClass() {
            return this.isPriceValid ? 'ring-gray-300' : 'ring-red-500';
        }
    },

    methods: {

        showDetails() {
            this.sideOverLay.value = true;

            if (!this.viewForm.processing) {
                this.viewForm.submit({
                    preserveScroll: true,
                    preserveState: true,
                    onSuccess: page => {
                        //console.log("Page UPDATED", page);
                    }
                });
            }
        },
        makeBid() {
            var self = this;

            if (!self.bidForm.processing) {
                self.bidForm.submit({
                    preserveScroll: true,
                    preserveState: false,
                    onSuccess: page => {
                        console.log("Page UPDATED", page);
                    }
                });
            }
        },

        showAssignment() {
            router.get(this.route('my_assignments.show', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.assignment.assignment_id]));
        },

        getAssignmentIcon(assignment_id) {
            var assignmentIcons = {
                1: {
                    icon: CogIcon,
                    color: 'text-cyan-700'
                },
                2: {
                    icon: BuildingOfficeIcon,
                    color: 'text-cyan-700'
                },
                4: {
                    icon: ClipboardDocumentCheckIcon,
                    color: 'text-cyan-700'
                },
                5: {
                    icon: ClipboardIcon,
                    color: 'text-cyan-700'
                },
                7: {
                    icon: DocumentChartBarIcon,
                    color: 'text-cyan-700'
                },
                8: {
                    icon: DocumentIcon,
                    color: 'text-cyan-700'
                },
                9: {
                    icon: ComputerDesktopIcon,
                    color: 'text-cyan-700'
                },
                10: {
                    icon: BookOpenIcon,
                    color: 'text-cyan-700'
                },
                11: {
                    icon: MegaphoneIcon,
                    color: 'text-cyan-700'
                },
                12: {
                    icon: LightBulbIcon,
                    color: 'text-cyan-700'
                },

            }

            if (assignmentIcons[assignment_id]) {
                return assignmentIcons[assignment_id].icon;
            } else {
                return DocumentIcon;
            }
        },

        validatePrice() {
            this.isPriceTouched = true;
            const pricePattern = /^\d+$/;
            this.isPriceValid = pricePattern.test(this.bidForm.price) && parseInt(this.bidForm.price) > 0;
        }
    }
}
</script>

<template>
    <div>
        <div v-if="!assignment.user_has_bid" class="border rounded-xl overflow-hidden border-gray-300  bg-gray-100">
            <div class="bg-white p-4 border-b-gray-200 border-b py-5">
                <div class="flex items-start">
                    <span :class="['text-cyan-700 inline-flex rounded-lg p-5 ring-1 ring-gray-300']">
                        <component :is="getAssignmentIcon(assignment.translation_category_id)" class="h-8 w-8"
                            aria-hidden="true" />
                    </span>
                    <div class="ml-4 mt-2">
                        <div class="flex just">
                            <span>
                                <LanguageIcon class="h-6 w-6 mr-1 text-gray-700" aria-hidden="true" />
                            </span>
                            <p class="text-base text-gray-700 leading-7 font-semibold">{{
                                assignment.from_translation_language }} {{
                                    $t('translations.translation_assignment_market.to_label') }} {{
                                    assignment.to_translation_language
                                }}
                            </p>
                            <span
                                class="inline-flex items-center content-end rounded-md bg-gray-50 ml-2 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                                {{ assignment.assignment_id }}</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">
                            {{ $t('translations.translation_assignments.created_date_label') }}:
                            <span class="font-semibold">{{ assignment.created_date }} </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="flex items-center justify-between px-4 ">
                <span
                    class="inline-flex items-center rounded-lg bg-moss-200 px-3 py-1 text-sm font-medium text-moss-700 border border-moss-400">{{
                        bidText }}</span>
                <button type="button" @click="showDetails"
                    class="rounded-md bg-gray-700 my-4 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600">
                    {{ $t('translations.translation_assignment_market.more_info_button') }}</button>

                <!-- <SBXButton variant="secondary" class="my-4 items-end">{{
                    $t('translations.translation_assignment_market.make_bid_button') }}</SBXButton> -->
            </div>
        </div>

        <div v-if="assignment.user_has_bid" class="border rounded-xl overflow-hidden border-gray-300  bg-gray-100">
            <div class="bg-white p-4 border-b-gray-200 border-b py-5">
                <div class="flex items-start">
                    <span :class="['text-cyan-700 inline-flex rounded-lg p-5 ring-1 ring-gray-300']">
                        <DocumentIcon class="h-8 w-8" aria-hidden="true" />
                    </span>
                    <div class="ml-4 mt-2">
                        <div class="flex just">
                            <span>
                                <LanguageIcon class="h-6 w-6 mr-1 text-gray-700" aria-hidden="true" />
                            </span>
                            <p class="text-base text-gray-700 leading-7 font-semibold">{{
                                assignment.from_translation_language }} {{
                                    $t('translations.translation_assignment_market.to_label') }} {{
                                    assignment.to_translation_language
                                }}
                            </p>
                            <span
                                class="inline-flex items-center content-end rounded-md bg-gray-50 ml-2 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                                {{ assignment.assignment_id }}</span>
                        </div>
                        <!-- <p class="text-sm text-gray-600 mt-1">
                            {{ $t('translations.translation_categories.title') }}:
                            <span class="font-semibold">{{ assignment.translation_category }} </span>
                        </p> -->
                    </div>
                </div>
            </div>
            <div class="flex items-center px-4 ">
                <button type="button" @click="showAssignment"
                    class="rounded-md bg-moss-500 my-4 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-moss-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-moss-600">
                    {{ $t('translations.translation_assignment_market.show_assignment_button') }}</button>
            </div>
        </div>

        <!-- SideOverLay-->
        <TransitionRoot :show=sideOverLay.value>
            <Dialog class="relative z-10" @close="sideOverLay.value = false">
                <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0"
                    enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100"
                    leave-to="opacity-0">
                    <div class="fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-hidden">
                    <div class="absolute inset-0 overflow-hidden">
                        <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                            <TransitionChild as="template"
                                enter="transform transition ease-in-out duration-500 sm:duration-700"
                                enter-from="translate-x-full" enter-to="translate-x-0"
                                leave="transform transition ease-in-out duration-500 sm:duration-700"
                                leave-from="translate-x-0" leave-to="translate-x-full">
                                <DialogPanel class="pointer-events-auto w-screen max-w-2xl ">
                                    <div class="flex h-full flex-col overflow-y-auto bg-white pb-6 shadow-xl">
                                        <div class="py-6 px-4 sm:px-6 bg-gray-600">
                                            <div class="flex items-start justify-between">
                                                <DialogTitle class="text-lg font-semibold leading-7 text-white">
                                                    {{
                                                        $t('translations.my_assignments.assignment_detail.title')
                                                    }} {{ assignment.assignment_id }}
                                                    <span
                                                        class="inline-flex align-top rounded-md bg-moss-200 mx-3 px-3 py-1 text-xs font-medium text-green-800 ring-1 ring-inset ring-green-600/20">
                                                        <CheckBadgeIcon class="h-4 w-4 text-green-700 mr-1"
                                                            aria-hidden="true" />
                                                        {{
                                                            $t('translations.my_assignments.assignment_detail.confirmed_label')
                                                        }}
                                                    </span>

                                                </DialogTitle>

                                                <div class=" ml-3 flex h-7 items-center">
                                                    <button type="button"
                                                        class="relative rounded-md bg-gray-500 text-white hover:text-gray-500 p-1 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                                        @click="sideOverLay.value = false">
                                                        <span class="absolute -inset-2.5" />
                                                        <span class="sr-only">Close panel</span>
                                                        <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="relative mt-6 flex-2 px-4 sm:px-6">
                                            <div class="grid grid-cols-1 lg:grid-cols-1 gap-4">
                                                <dl class="grid grid-cols-2">
                                                    <div class="py-5 sm:col-span-1 sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.to_language_label')
                                                            }}</dt>
                                                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                            assignment.from_translation_language }}</dd>
                                                    </div>
                                                    <div class="py-5 sm:col-span-1 sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.from_language_label')
                                                            }}</dt>
                                                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                            assignment.to_translation_language }}</dd>
                                                    </div>
                                                    <!-- <div class="border-t border-gray-200 py-5 sm:col-span-1 sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.category_label')
                                                            }}</dt>
                                                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                            assignment.translation_category }}</dd>
                                                    </div> -->
                                                    <div class="border-t border-gray-200 py-5 sm:col-span-2 sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.authorization_label')
                                                            }}</dt>
                                                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                                                            <span v-if="assignment.is_authorization_required"
                                                                class="inline-flex items-center rounded-md bg-moss-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-moss-600/20">{{
                                                                    $t('translations.global.yes') }}</span>
                                                            <span v-if="!assignment.is_authorization_required"
                                                                class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10">
                                                                {{ $t('translations.global.no') }}</span>
                                                        </dd>
                                                    </div>
                                                    <div class="col-span-1 border-t border-gray-200 py-5  sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.created_at_label')
                                                            }}</dt>
                                                        <dd class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                            assignment.created_date }}</dd>
                                                    </div>
                                                    <div class="col-span-1 border-t border-gray-200 py-5  sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.file_count_label')
                                                            }}</dt>
                                                        <dd v-if="assignment.files.length != null && assignment.files.length > 0"
                                                            class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                                assignment.files.length }}</dd>
                                                        <dd v-else class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                                                            -</dd>
                                                    </div>
                                                    <div class="col-span-1 border-t border-gray-200 py-6  sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">
                                                            {{
                                                                $t('translations.my_assignments.assignment_detail.costumer_type_label')
                                                            }}</dt>
                                                        <dd v-if="assignment.assignment_type == 'company'"
                                                            class="text-sm leading-6 text-gray-700">{{
                                                                $t('translations.my_assignments.assignment_detail.company_label')
                                                            }}</dd>
                                                        <dd v-if="assignment.assignment_type == 'personal'"
                                                            class="text-sm leading-6 text-gray-700 ">{{
                                                                $t('translations.my_assignments.assignment_detail.private_person_label')
                                                            }}
                                                        </dd>
                                                    </div>
                                                    <div class="border-t border-gray-200 py-5 sm:col-span-1 sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">{{
                                                            $t('translations.my_assignments.assignment_detail.latest_delivery_date_label')
                                                            }}</dt>

                                                        <dd v-if="assignment.last_delivery_date"
                                                            class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                                assignment.last_delivery_date }}</dd>
                                                        <dd v-else class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                                                            -</dd>
                                                    </div>

                                                    <div class="col-span-2 border-t border-gray-200 py-6 sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">
                                                            {{
                                                                $t('translations.my_assignments.assignment_detail.files_label')
                                                            }}</dt>
                                                        <dd
                                                            v-if="assignment.files.length != null && assignment.files.length > 0">
                                                            <ul class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                                                                <li class="flex items-center justify-between py-3 text-sm hover:bg-moss-50"
                                                                    v-for="file in assignment.files" :key="file.id">
                                                                    <div class="flex w-0 flex-1 pl-1 ml-2 items-center">
                                                                        <PaperClipIcon
                                                                            class="h-5 w-5 flex-shrink-0 text-gray-400"
                                                                            aria-hidden="true" />
                                                                        <div class="ml-3 flex min-w-0 flex-1 gap-2">
                                                                            <span
                                                                                class="truncate font-medium hover:text-gray-900"><a
                                                                                    :href="file.url" target="_blank">{{
                                                                                        file.original_filename }}</a></span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="ml-4 flex-shrink-0 pr-2">
                                                                        <a :href="file.url"
                                                                            class="font-bold text-moss-500 hover:text-moss-600 pr-1"
                                                                            target="_blank">Ladda
                                                                            ner</a>
                                                                    </div>
                                                                </li>
                                                            </ul>
                                                            <div v-if="assignment.files.length > 1"
                                                                class="py-2 pr-2 border-t border-dashed border-gray-300">
                                                                <a href="#"
                                                                    class="text-sm font-semibold text-moss-500 hover:text-moss-600 text-right">Ladda
                                                                    ner alla filer</a>
                                                            </div>
                                                        </dd>
                                                        <dd v-else class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                                                            -</dd>

                                                    </div>
                                                    <div class="col-span-2 border-t border-gray-200 py-6  sm:px-0">
                                                        <dt class="text-sm font-semibold leading-6 text-gray-900">
                                                            {{
                                                                $t('translations.my_assignments.assignment_detail.notes_label')
                                                            }}</dt>
                                                        <dd v-if="assignment.notes != null && assignment.notes.length != ''"
                                                            class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">{{
                                                                assignment.notes }}</dd>
                                                        <dd v-else class="mt-1 text-sm leading-6 text-gray-700 sm:mt-2">
                                                            -</dd>
                                                    </div>
                                                </dl>
                                            </div>
                                            <div class="py-4 px-4 bg-gray-50 rounded-md">
                                                <div>
                                                    <p class="text-lg font-semibold text-gray-900">{{
                                                        $t('translations.translation_assignment_market.make_bid_button')
                                                        }}</p>
                                                    <p class="text-sm text-gray-600 mt-2">{{
                                                        $t('translations.translation_assignment_market.make_bid_description')
                                                        }}</p>
                                                </div>
                                                <div class="py-5 mt-1">
                                                    <label for="price"
                                                        class="block text-sm font-medium leading-6 text-gray-900">{{
                                                            $t('translations.translation_assignment_market.price_label') }}
                                                        -
                                                        {{ $t('translations.translation_assignment_market.excl_vat')
                                                        }}</label>
                                                    <div class="relative mt-2 rounded-md shadow-sm">
                                                        <input type="text" name="price" id="price"
                                                            v-model="bidForm.price" pattern="^[1-9]\d*$"
                                                            class="block w-full rounded-md border-0 py-3 pl-1-5 pr-12 text-gray-900 font-semibold ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 invalid:border-red-500 invalid:ring-red-500 invalid:ring-inset"
                                                            placeholder="0" aria-describedby="price-currency"
                                                            required />
                                                        <div
                                                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                                            <span class="text-gray-500 sm:text-sm"
                                                                id="price-currency">{{
                                                                    $t('translations.translation_assignment_market.excl_vat')
                                                                }}</span>
                                                        </div>

                                                    </div>
                                                    <div class="text-gray-500 text-sm font-medium leading-6 mt-1 ml-1">
                                                        {{
                                                            $t('translations.translation_assignment_market.price_currency_information')
                                                        }}
                                                    </div>
                                                </div>
                                                <div class="pb-5">
                                                    <label for="delivery_date"
                                                        class="block text-sm font-medium leading-6 text-gray-900">{{
                                                            $t('translations.translation_assignment_market.delivery_date')
                                                        }}
                                                    </label>
                                                    <div class="relative mt-2 rounded-md shadow-sm">
                                                        <input type="date" name="delivery_date" id="delivery_date"
                                                            v-model="bidForm.delivery_date"
                                                            :min="new Date().toISOString().split('T')[0]"
                                                            class="block w-full rounded-md border-0 py-3 pl-1-5 pr-12 text-gray-900 font-semibold ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 invalid:border-red-500 invalid:ring-red-500 invalid:ring-inset"
                                                            placeholder="0" aria-describedby="price-currency"
                                                            required />
                                                    </div>
                                                    <div class="text-gray-500 text-sm font-medium leading-6 mt-1 ml-1">
                                                        {{
                                                            $t('translations.translation_assignment_market.devliery_date_information')
                                                        }} {{ new Date().toISOString().split('T')[0] }}
                                                    </div>
                                                </div>
                                                <button type="button" @click="makeBid" :disabled="isButtonDisabled"
                                                    class="w-full text-center gap-x-1.5 rounded-md bg-moss-500 px-3 py-4
                                                    text font-semibold text-white hover:bg-moss-600 disabled:bg-gray-200
                                                    disabled:text-gray-300 disabled:cursor-not-allowed">
                                                    {{
                                                        $t("translations.translation_assignment_market.make_bid_button")
                                                    }}
                                                </button>
                                            </div>
                                            <div class="text-gray-500 text-sm font-medium leading-6 my-2">
                                                {{
                                                    $t('translations.translation_assignment_market.customer_service_question')
                                                }}
                                                <a
                                                    href="mailto:<EMAIL>"><EMAIL></a>
                                            </div>
                                        </div>
                                    </div>
                                </DialogPanel>
                            </TransitionChild>
                        </div>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>
