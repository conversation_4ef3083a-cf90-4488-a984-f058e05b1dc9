<script>

/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';

import SBXDefaultPageLayout from '@sbxadmin/Layouts/SBXDefaultPageLayout.vue';
import SBXButton from '@sbxui/Buttons/SBXButton.vue';

import AssignmentCard from './Components/AssignmentCard.vue';
import { DocumentMagnifyingGlassIcon } from '@heroicons/vue/24/outline';



/*
|--------------------------------------------------------------------------
| Component Implementation
|--------------------------------------------------------------------------
*/

export default {
    components: {
        SBXDefaultPageLayout,
        AssignmentCard,
        SBXButton,
        DocumentMagnifyingGlassIcon

    },

    props: {
        assignments: Object,

        selectedAssignment: {
            type: Object,
            default: null
        },

        filters: Object
    },

    mounted() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_assignment_market.title");
    },

    updated() {
        this.$page.props.page_info.title_label = this.$t("translations.translation_assignment_market.title");
    },

    methods: {
        showAll() {
            router.get(this.route('translation_assignment_market', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code]));
        }
    }
}
</script>

<template>
    <SBXDefaultPageLayout>
        <div class="mt-6 mb-6">
            <div v-if="assignments.data.length > 0">
                <!-- <div class="text-left border-b border-gray-300 pb-5">
                    <p class="text-3xl leading-7 font-bold tracking-tight text-gray-800 sm:text-4xl">{{
                        $t('translations.translation_assignment_market.page_title') }}</p>

                    <p class="mt-1 text-left text-xlarge text-gray-600">{{
                        $t('translations.translation_assignment_market.page_subtitle') }}</p>
                </div> -->
                <div class="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-4">
                    <AssignmentCard v-if="!selectedAssignment" v-for="assignment in assignments.data"
                        :assignment="assignment" />
                    <AssignmentCard v-if="selectedAssignment" :assignment="selectedAssignment.data" />
                </div>

                <SBXButton v-if="selectedAssignment" @click="showAll" class="mt-4">{{
                    $t('translations.translation_assignment_market.show_all_button') }}</SBXButton>
            </div>
            <div v-else>
                <button type="button"
                    class="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    <DocumentMagnifyingGlassIcon class="mx-auto h-12 w-12 text-gray-400 stroke-1" />

                    <span class="mt-2 block text-base font-semibold text-gray-400">{{
                        $t('translations.translation_assignment_market.no_assignments') }}</span>
                </button>
            </div>
        </div>
    </SBXDefaultPageLayout>
</template>

<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
    class="w-6 h-6">
  </svg>
