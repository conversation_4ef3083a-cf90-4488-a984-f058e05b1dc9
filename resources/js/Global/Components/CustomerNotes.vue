<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue'
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import SBXButton from '@sbxui/Buttons/SBXButton.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXSelect from '@sbxui/Form/SBXSelect.vue';
  import SBXTextArea from '@sbxui/Form/SBXTextArea.vue';
  import SBXDialog from '@sbxui/Application/SBXDialog.vue';

  import CustomerNote from '@/Global/Components/CustomerNote.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default defineComponent({
    components: {
      SBXButton,
      SBXInput,
      SBXSelect,
      SBXTextArea,
      SBXDialog,
      CustomerNote
    },

    props: {
      customer: {
        type: Object,
        required: true
      }
    },

     watch: {
      customer(newValue, oldValue) {
        this.addNoteForm = useForm('post', this.route('customers.add_note', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, newValue.id]), {
          note: null,
          next_visit_status: null,
          next_visit_time: null
        });

        this.notes = newValue.notes;
      }
    },

    data() {
      return {
        notes: this.customer.notes,

        addNoteForm: useForm('post', this.route('customers.add_note', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.customer.id]), {
          note: null,
          next_visit_status: null,
          next_visit_time: null
        }),

        nextVisitTimeOptions: [
          { value: null, text: this.$t('sydfisk.customers.next_visit_time_options.no_change_label') },
          { value: 1, text: this.$t('sydfisk.customers.next_visit_time_options.one_week_label') },
          { value: 2, text: this.$t('sydfisk.customers.next_visit_time_options.two_weeks_label') },
          { value: 3, text: this.$t('sydfisk.customers.next_visit_time_options.three_weeks_label') },
          { value: 4, text: this.$t('sydfisk.customers.next_visit_time_options.four_weeks_label') },
          { value: 8, text: this.$t('sydfisk.customers.next_visit_time_options.two_months_label') },
          { value: 12, text: this.$t('sydfisk.customers.next_visit_time_options.three_months_label') },
          { value: 16, text: this.$t('sydfisk.customers.next_visit_time_options.six_months_label') }
        ],

        nextVisitStatusOptions: [
          { value: null, text: this.$t('sydfisk.customers.next_visit_status.no_change_label') },
          { value: 'unplanned', text: this.$t('sydfisk.customers.next_visit_status.unplanned_label') },
          { value: 'booked_visit', text: this.$t('sydfisk.customers.next_visit_status.booked_visit_label') },
          { value: 'come_next_time', text: this.$t('sydfisk.customers.next_visit_status.come_next_time_label') },
          { value: 'purchased_wants_visit', text: this.$t('sydfisk.customers.next_visit_status.purchased_wants_visit_label') },
          { value: 'not_home', text: this.$t('sydfisk.customers.next_visit_status.not_home_label') }
        ],

        addNoteOpen: false,
      }
    },

    methods: {
      confirmAddNoteDialog() {
        var self = this;

        this.addNoteForm.submit({
          preserveScroll: true,
          preserveState: true,
          onSuccess: page => {
            self.addNoteOpen = false;
            self.addNoteForm.note = null;
            self.addNoteForm.next_visit_time = null;
            self.addNoteForm.next_visit_status = null;

            if (page.props.hasOwnProperty('customers')) {
              var updatedCustomer = null;

              for (var i = 0; i < page.props.customers.data.length; i++) {
                let currentCustomer = page.props.customers.data[i];

                if (currentCustomer.id == self.customer.id) {
                  updatedCustomer = currentCustomer;
                  break;
                }
              }

              if (updatedCustomer) {
                self.notes = updatedCustomer.notes;
              }
            }
          }
        });
      },
    },
  });
</script>

<template>
  <div>
    <div class="flex justify-end">
      <SBXButton @click="addNoteOpen = true" class="mb-2">{{ $t('sydfisk.customers.add_note_button') }}</SBXButton>
    </div>
    <CustomerNote v-for="note in notes" :note="note" :nextVisitTimeOptions="nextVisitTimeOptions" :nextVisitStatusOptions="nextVisitStatusOptions" />

    <!-- START Add note Dialog -->
    <SBXDialog
      :isOpen="addNoteOpen"
      :title="$t('sydfisk.customers.add_note_dialog.title')"
      :cancelButtonText="$t('sydfisk.global.cancel')"
      :confirmButtonText="$t('sydfisk.customers.add_note_dialog.update_button')"
      @cancelDialog="addNoteOpen = false"
      @confirmDialog="confirmAddNoteDialog">

        <!-- START Dialog Contant -->
        <div class="w-full grid grid-cols-2 gap-2">
          <SBXTextArea v-model:model="addNoteForm.note" :label="$t('sydfisk.customers.add_note_dialog.note_label')" :rows="5" :error="addNoteForm.errors.note" class="col-span-2" />

          <SBXSelect v-model:model="addNoteForm.next_visit_status" :items="nextVisitStatusOptions" valueFieldName="value" :label="$t('sydfisk.customers.add_note_dialog.next_visit_status_label')" @change="addNoteForm.validate('next_visit_status')" :error="addNoteForm.errors.next_visit_status" />

          <SBXSelect v-model:model="addNoteForm.next_visit_time" :items="nextVisitTimeOptions" valueFieldName="value" :label="$t('sydfisk.customers.add_note_dialog.next_visit_time_label')" @change="addNoteForm.validate('next_visit_time')" :error="addNoteForm.errors.next_visit_time" />
        </div>
         <!-- END Dialog Contant -->
    </SBXDialog>
    <!-- END Add note Dialog -->
  </div>
</template>
