<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue'
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import { PencilIcon, TrashIcon } from '@heroicons/vue/24/solid';

  import SBXButton from '@sbxui/Buttons/SBXButton.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXSelect from '@sbxui/Form/SBXSelect.vue';
  import SBXTextArea from '@sbxui/Form/SBXTextArea.vue';
  import SBXDialog from '@sbxui/Application/SBXDialog.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default defineComponent({
    components: {
      PencilIcon,
      TrashIcon,
      SBXButton,
      SBXInput,
      SBXSelect,
      SBXTextArea,
      SBXDialog
    },

    props: {
      note: {
        type: Object,
        required: true
      },
    },

    watch: {
      'note': function(newVal, oldVal) {
        this.updateNoteForm.note = this.note.notes;
      },
    },

    data() {
      return {
        updateNoteForm: useForm('put', this.route('orders.update_note', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.note.id]), {
          note: this.note.notes
        }),

        deleteNoteForm: useForm('delete', this.route('orders.delete_note', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.note.id]), {
        }),

        updateNoteOpen: false,
        deleteNoteOpen: false,
      }
    },

    computed: {
    },

    methods: {
      confirmUpdateNoteDialog() {
        var self = this;

        this.updateNoteForm.submit({
          preserveScroll: true,
          preserveState: false,
          onSuccess: page => {
            self.updateNoteOpen = false;
          }
        });
      },

      confirmDeleteNoteDialog() {
        var self = this;

        this.deleteNoteForm.submit({
          preserveScroll: true,
          preserveState: false,
          onSuccess: page => {
            self.deleteNoteOpen = false;
          }
        });
      },
    }
  });
</script>

<template>
  <div class="w-full">
    <div class="p-2 mb-1 w-full flex justify-between bg-gray-300 items-center">
      <p class="text-sm font-semibold bg-gray-300">{{ note.created_at }} {{ note.created_by }}</p>
      <div v-if="$page.props.isAdmin || note.sales_person_id == $page.props.salesPersonID" class="flex">
        <PencilIcon @click="updateNoteOpen = true" class="h-6 w-6 bg-blue-700 rounded p-1 text-white hover:bg-blue-800 cursor-pointer" aria-hidden="true" />
        <TrashIcon @click="deleteNoteOpen = true" class="ml-1 h-6 w-6 bg-red-700 rounded p-1 text-white hover:bg-red-800 cursor-pointer" aria-hidden="true" />
      </div>
    </div>
    <p class="text-sm mb-4" v-html="note.notes"></p>

    <!-- START Update note Dialog -->
    <SBXDialog
      :isOpen="updateNoteOpen"
      :title="$t('sydfisk.orders.edit_note_dialog.title')"
      :cancelButtonText="$t('sydfisk.global.cancel')"
      :confirmButtonText="$t('sydfisk.orders.edit_note_dialog.update_button')"
      @cancelDialog="updateNoteOpen = false"
      @confirmDialog="confirmUpdateNoteDialog">

        <!-- START Dialog Contant -->
        <div class="w-full grid grid-cols-2 gap-2">
          <SBXTextArea v-model:model="updateNoteForm.note" :label="$t('sydfisk.orders.edit_note_dialog.note_label')" :rows="5" :error="updateNoteForm.errors.note" class="col-span-2" />
        </div>
         <!-- END Dialog Contant -->
    </SBXDialog>
    <!-- END Update note Dialog -->

    <!-- START Delete note Dialog -->
    <SBXDialog
      :isOpen="deleteNoteOpen"
      :title="$t('sydfisk.orders.delete_note_dialog.title')"
      :cancelButtonText="$t('sydfisk.global.cancel')"
      :confirmButtonText="$t('sydfisk.orders.delete_note_dialog.update_button')"
      variant="danger"
      @cancelDialog="deleteNoteOpen = false"
      @confirmDialog="confirmDeleteNoteDialog">

        <!-- START Dialog Contant -->
        <div class="w-full">
          <p>{{ $t('sydfisk.orders.delete_note_dialog.message_text') }}</p>
        </div>
         <!-- END Dialog Contant -->
    </SBXDialog>
    <!-- END Delete note Dialog -->
  </div>
</template>
