<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    props: {
      model: {
        type: Array,
        default: []
      },
    },

    mounted() {
      console.log("currentItems", this.currentItems);
    },

    watch: {
      model: {
        handler: function(newValue) {
          console.log("newValue", newValue);
          this.currentItems = newValue;
        },
        deep: true
      }
    },

    data() {
      return {
        currentItems: this.model
      }
    }
  }
</script>

<template>
  <fieldset>
    <div class="space-y-1">
      <div v-for="(item, itemIndex) in currentItems" class="relative flex items-start">
        <div class="flex h-6 items-center">
          <input v-model="currentItems[itemIndex].isSelected" @input="$emit('update:model', currentItems)" @change="$emit('change', currentItems)" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
        </div>

        <div class="ml-3 text-sm leading-6">
          <label class="font-medium text-gray-900">{{ item.value }}</label>
        </div>
      </div>
    </div>
  </fieldset>
</template>
