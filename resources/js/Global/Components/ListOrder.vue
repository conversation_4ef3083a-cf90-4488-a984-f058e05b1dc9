<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';

  import ListOrderRow from './ListOrderRow.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    components: {
      ListOrderRow
    },

    props: {
      order: Object
    },

    data() {
      return {
        // selectedSalesPersonID: this.order.salary_sales_person.id
      }
    },

    computed: {
      shownSource() {
        if (this.order.source == 'sydfisk') {
          return this.$t('sydfisk.sources.sydfisk');
        }

        if (this.order.source == 'zettle') {
          return this.$t('sydfisk.sources.zettle');
        }

        if (this.order.source == 'crm') {
          return this.$t('sydfisk.sources.crm');
        }

        return this.$t('sydfisk.sources.unknown');
      },

      shownCustomer() {
        var company = null;
        if (this.order.customer != null && !this.isBlank(this.order.customer.company)) {
          company = this.order.company;
        }

        if (!this.isBlank(this.order.company)) {
          company = this.order.company;
        }

        var firstName = null;
        if (this.order.customer != null && !this.isBlank(this.order.customer.first_name)) {
          firstName = this.order.first_name;
        }

        if (!this.isBlank(this.order.first_name)) {
          firstName = this.order.first_name;
        }

        var lastName = null;
        if (this.order.customer != null && !this.isBlank(this.order.customer.last_name)) {
          lastName = this.order.last_name;
        }

        if (!this.isBlank(this.order.last_name)) {
          lastName = this.order.last_name;
        }

        var address = null;
        if (this.order.customer != null && !this.isBlank(this.order.customer.address_1)) {
          address = this.order.address_1;
        }

        if (!this.isBlank(this.order.address_1)) {
          address = this.order.address_1;
        }

        var postalCode = null;
        if (this.order.customer != null && !this.isBlank(this.order.customer.postal_code)) {
          postalCode = this.order.postal_code;
        }

        if (!this.isBlank(this.order.postal_code)) {
          postalCode = this.order.postal_code;
        }

        var city = null;
        if (this.order.customer != null && !this.isBlank(this.order.customer.city)) {
          city = this.order.city;
        }

        if (!this.isBlank(this.order.city)) {
          city = this.order.city;
        }

        var customerText = "";
        if (!this.isBlank(company)) {
          customerText += company + ' ';
        }

        if (!this.isBlank(firstName)) {
          if (customerText.length > 0) {
            customerText += ', ';
          }

          customerText += firstName + ' ';
        }

        if (!this.isBlank(lastName)) {
          if (customerText.length > 0 && this.isBlank(firstName)) {
            customerText += ', ';
          }

          customerText += lastName;
        }

        if (!this.isBlank(address)) {
          if (customerText.length > 0) {
            customerText += ', ';
          }

          customerText += address;
        }

        if (!this.isBlank(postalCode)) {
          if (customerText.length > 0 && !this.isBlank(address)) {
            customerText += ', ';
          }

          customerText += postalCode + ' ';
        }

        if (!this.isBlank(city)) {
          customerText += city + ' ';
        }


        return customerText;

        if (this.order.source == 'sydfisk') {
          return '#' + this.order.external_order_no + ', ' + this.order.name + ', ' + this.order.address + ', ' + this.order.postal_code + ', ' + this.order.city;
        }

        return null;
      },

      // salesPersonItems() {
      //   var salesPersons = [];

      //   for (var i = 0; i < this.salesPersons.data.length; i++) {
      //     let salesPerson = this.salesPersons.data[i];

      //     salesPersons.push({
      //       value: salesPerson.id,
      //       text: salesPerson.name
      //     });
      //   }

      //   return salesPersons;
      // }
    },

    methods: {
      // updateSalesPerson() {
      //   this.$inertia.put(this.route('salary_reports.order.sales_person.update', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.salaryReportID, this.order.id, this.selectedSalesPersonID]), {}, {
      //       preserveScroll: true
      //   });
      // }

      isBlank(str) {
        return (!str || /^\s*$/.test(str));
      },

      showOrder() {
        console.log("showOrder");

        this.$inertia.get(this.route('orders.show', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.order.id]));
      }
    }
  }
</script>

<template>
  <div class="mb-4">
    <div class="w-full flex items-start bg-gray-300 py-1 px-2">
      <div class="flex flex-col w-30 mr-12">
        <span @click="showOrder" class="text-sm font-bold">{{ shownSource }} #{{ order.external_order_no }}</span>
        <span @click="showOrder" class="text-xs font-normal">{{ order.ordered_at_datetext }}, {{ order.ordered_at_timetext }}</span>
      </div>

      <span class="text-sm font-semibold grow">{{ shownCustomer }}</span>

      <div class="flex flex-col mr-4">
        <span class="text-sm font-bold text-right">{{ order.total_amount }}</span>
        <span class="text-xs font-normal text-right">{{ order.total_discount }}</span>
      </div>

      <!-- <select v-model="selectedSalesPersonID" @change="updateSalesPerson" class="block w-48 pl-3 pr-10 py-1 text-sm border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-gray-100">
        <option v-for="salesPerson in salesPersonItems" :value="salesPerson.value">
          {{ salesPerson.text }}
        </option>
      </select> -->
    </div>

    <div class="pb-1 bg-gray-100">
      <ListOrderRow v-for="row in order.rows" :row="row" />
    </div>
  </div>
</template>
