<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue'
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';
  import { useForm } from 'laravel-precognition-vue-inertia';

  import SBXButton from '@sbxui/Buttons/SBXButton.vue';
  import SBXInput from '@sbxui/Form/SBXInput.vue';
  import SBXTextArea from '@sbxui/Form/SBXTextArea.vue';
  import SBXDialog from '@sbxui/Application/SBXDialog.vue';

  import OrderNote from '@/Global/Components/OrderNote.vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default defineComponent({
    components: {
      SBXButton,
      SBXInput,
      SBXTextArea,
      SBXDialog,
      OrderNote
    },

    props: {
      order: {
        type: Object,
        required: true
      }
    },

    data() {
      return {
        addNoteForm: useForm('post', this.route('orders.add_note', [this.$page.props.locale.selected_market_code, this.$page.props.locale.selected_language_code, this.order.id]), {
          note: null
        }),

        addNoteOpen: false,
      }
    },

    methods: {
      confirmAddNoteDialog() {
        var self = this;

        this.addNoteForm.submit({
          preserveScroll: true,
          preserveState: false,
          onSuccess: page => {
            self.addNoteOpen = false;
            self.addNoteForm.note = null;
          }
        });
      },
    },
  });
</script>

<template>
  <div>
    <div class="flex justify-end">
      <SBXButton @click="addNoteOpen = true" class="mb-2">{{ $t('sydfisk.orders.add_note_button') }}</SBXButton>
    </div>
    <OrderNote v-for="note in order.notes" :note="note" />

    <!-- START Add note Dialog -->
    <SBXDialog
      :isOpen="addNoteOpen"
      :title="$t('sydfisk.orders.add_note_dialog.title')"
      :cancelButtonText="$t('sydfisk.global.cancel')"
      :confirmButtonText="$t('sydfisk.orders.add_note_dialog.update_button')"
      @cancelDialog="addNoteOpen = false"
      @confirmDialog="confirmAddNoteDialog">

        <!-- START Dialog Contant -->
        <div class="w-full grid grid-cols-2 gap-2">
          <SBXTextArea v-model:model="addNoteForm.note" :label="$t('sydfisk.orders.add_note_dialog.note_label')" :rows="5" :error="addNoteForm.errors.note" class="col-span-2" />
        </div>
         <!-- END Dialog Contant -->
    </SBXDialog>
    <!-- END Add note Dialog -->
  </div>
</template>
