<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { ref } from 'vue';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    props: {
      step: Number,
      label: String
    }
  }
</script>

<template>
  <div class="flex items-center">
    <div class="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 border border-gray-900 rounded-full">
      <span class="text-base sm:text-xl text-center text-gray-900 font-semibold">{{ step }}</span>
    </div>

    <span class="flex-1 h-px mx-4 border-none bg-gray-900"></span>
    <p class="text-sm sm:text-base text-gray-900 font-semibold">{{ label }}</p>
    <span class="flex-1 h-px mx-4 border-none bg-gray-900"></span>
  </div>
</template>
