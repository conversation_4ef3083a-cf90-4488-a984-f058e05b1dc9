<script>

  /*
  |--------------------------------------------------------------------------
  | Dependency Definitions
  |--------------------------------------------------------------------------
  */

  import { defineComponent } from 'vue';
  import { Head } from '@inertiajs/vue3';
  import { router } from '@inertiajs/vue3';



  /*
  |--------------------------------------------------------------------------
  | Component Implementation
  |--------------------------------------------------------------------------
  */

  export default {
    props: {
      row: Object
    }
  }
</script>

<template>
  <div class="w-full flex items-center bg-gray-100 pl-2">
    <span class="text-xs w-12">{{ row.quantity }} {{ row.unit }}</span>
    <span v-if="row.zettle_product_id != null" class="text-xs">{{ row.zettle_product.name }} {{ row.zettle_product_variation.name }} {{ row.discount_text }}</span>
    <span v-if="row.sydfisk_product_id != null && row.sydfisk_product_variation_id == null" class="text-xs">{{ row.sydfisk_product.name }} {{ row.discount_text }}</span>
    <span v-if="row.sydfisk_product_variation_id != null" class="text-xs">{{ row.sydfisk_product_variation.name }} {{ row.discount_text }}</span>
    <span class="text-xs text-right mr-6 grow">{{ row.row_discounted_total }} kr</span>
    <!-- <div class="w-48 flex items-center">
      <span class="text-xs">{{ row.commission_type_value }}</span>
      <span class="text-xs text-right mr-4 grow">{{ row.commission }} kr</span>
    </div> -->
  </div>
</template>
