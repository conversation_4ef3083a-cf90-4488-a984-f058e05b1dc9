/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import "./bootstrap";
import "../../node_modules/vue-multiselect/dist/vue-multiselect.css";
import "../css/app.css";

import { createApp, h } from "vue";
import { createInertiaApp } from "@inertiajs/vue3";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../vendor/tightenco/ziggy/dist/vue.m";
import { createStore } from "vuex";
import { createI18n } from "vue-i18n";
import { createGtm, useGtm } from "@gtm-support/vue-gtm";
import VueCountdown from "@chenfengyuan/vue-countdown";

import AdminLayout from "@/Layouts/Admin.vue";

import sv from "@/Locales/sv/locale_sv";

/*
|--------------------------------------------------------------------------
| Constant Definitions
|--------------------------------------------------------------------------
*/

const store = createStore({
    modules: {},
});

const i18n = createI18n({
    locale: "sv",
    fallbackLocale: "sv",
    messages: { sv },
});

const gtm = createGtm({
    id: "GTM-W93HJ5KS",
    enabled: true,
    debug: false,
});

/*
|--------------------------------------------------------------------------
| Application Setup
|--------------------------------------------------------------------------
*/

createInertiaApp({
    title: (title) => `${title}`,

    resolve: (name) => {
        var page = null;

        if (name.startsWith("cms::")) {
            page = resolvePageComponent(
                `../../vendor/softbox/sbxcms/resources/js/Pages/${
                    name.split("::")[1]
                }.vue`,
                import.meta.glob(
                    "../../vendor/softbox/sbxcms/resources/js/Pages/**/*.vue"
                )
            );
        } else if (name.startsWith("webshop::")) {
            page = resolvePageComponent(
                `../../vendor/softbox/sbxwebshop/resources/js/Pages/${
                    name.split("::")[1]
                }.vue`,
                import.meta.glob(
                    "../../vendor/softbox/sbxwebshop/resources/js/Pages/**/*.vue"
                )
            );
        } else if (name.startsWith("admin::")) {
            page = resolvePageComponent(
                `../../vendor/softbox/sbxadmin/resources/js/Pages/${
                    name.split("::")[1]
                }.vue`,
                import.meta.glob(
                    "../../vendor/softbox/sbxadmin/resources/js/Pages/**/*.vue"
                )
            );
        } else if (name.startsWith("app::")) {
            page = resolvePageComponent(
                `./SBX/App/${name.split("::")[1]}.vue`,
                import.meta.glob("./SBX/App/**/*.vue")
            );
        } else {
            page = resolvePageComponent(
                `./Pages/${name}.vue`,
                import.meta.glob("./Pages/**/*.vue")
            );
        }

        page.then((module) => {
            if (module.default.layout !== null) {
                module.default.layout = module.default.layout || AdminLayout;
            }
        });

        return page;
    },

    setup({ el, App, props, plugin }) {
        return createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(store)
            .use(i18n)
            .use(ZiggyVue, Ziggy)
            .use(gtm)
            .mixin({ components: { VueCountdown } })
            .mount(el);
    },

    progress: {
        color: "#006AAE",
    },
});
