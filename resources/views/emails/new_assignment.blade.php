@extends('emails.layouts.template')

@section('content')
    @include('emails.partials.text_section', [
        'text' =>
            '<br><br><strong>' .
            __('oversattare.new_assignment_email_greeting') .
            ' ' .
            $user->first_name .
            '!</strong><br><br>' .
            __('oversattare.new_assignment_email_intro'),
    ])

    @include('emails.partials.title_section', [
        'title' => __('oversattare.new_assignment_email_translation_title'),
    ])

    @include('emails.partials.list_row', [
        'column1' => __('oversattare.new_assignment_email_from_language_label'),
        'column2' => $assignment->fromLanguage->localizations()->where('language_code', 'sv')->first()->name,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.new_assignment_email_to_language_label'),
        'column2' => $assignment->toLanguage->localizations()->where('language_code', 'sv')->first()->name,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.new_assignment_email_assignment_id_label'),
        'column2' => $assignment->assignment_id,
    ])

    @include('emails.partials.text_section', [
        'text' => __('oversattare.new_assignment_email_button_relevant'),
    ])

    @include('emails.partials.button', [
        'label' => __('oversattare.new_assignment_email_assignment_button'),
        'link' => $assignment->assignmentLink,
    ])

    @include('emails.partials.text_section', [
        'text' => __('oversattare.new_assignment_email_relevant_section'),
    ])

    @include('emails.partials.footer')
@endsection
