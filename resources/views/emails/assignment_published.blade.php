@extends('emails.layouts.template')

@section('content')
    @include('emails.partials.title_section', [
        'title' => __('oversattare.assignment_published_email_title'),
    ])

    @include('emails.partials.text_section', [
        'text' =>
            __('oversattare.assignment_published_email_greeting') .
            ' ' .
            $assignment->first_name .
            ',<br><br>' .
            __('oversattare.assignment_published_email_intro_1'),
    ])

    {{-- @include('emails.partials.list_row', [
        'column1' => __('oversattare.assignment_published_email_category_label'),
        'column2' => $assignment->category->localizations()->where('language_code', 'sv')->first()->name,
    ])
    @include('emails.partials.list_row', ['column1' => __('oversattare.new_assignment_email_from_language_label'), 'column2' => $assignment->fromLanguage->localizations()->where('language_code', 'sv')->first()->name])
  @include('emails.partials.list_row', ['column1' => __('oversattare.new_assignment_email_to_language_label'), 'column2' => $assignment->toLanguage->localizations()->where('language_code', 'sv')->first()->name])
  @include('emails.partials.list_row', ['column1' => __('oversattare.new_assignment_email_authorized_label'), 'column2' => $assignment->authorizedText])
  @include('emails.partials.list_row', ['column1' => __('oversattare.new_assignment_email_assignment_id_label'), 'column2' => $assignment->assignment_id]) --}}


    @include('emails.partials.text_section', [
        'text' => __('oversattare.new_assignment_email_info_section'),
    ])

    @include('emails.partials.footer')
@endsection
