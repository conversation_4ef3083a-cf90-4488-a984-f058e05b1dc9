@extends('emails.layouts.template')

@section('content')
    @include('emails.partials.text_section', [
        'text' => '<br><br><strong>' . __('oversattare.won_assignment_email_greeting') . '!',
    ])

    @include('emails.partials.text_section', [
        'text' => __('oversattare.won_assignment_email_intro_1'),
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.won_assignment_email_assignment_id_label'),
        'column2' => $assignment->assignment_id,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.won_assignment_email_from_language_label'),
        'column2' => $assignment->fromLanguage->localizations()->where('language_code', 'sv')->first()->name,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.won_assignment_email_to_language_label'),
        'column2' => $assignment->toLanguage->localizations()->where('language_code', 'sv')->first()->name,
    ])

    @include('emails.partials.list_row', [
        'column1' => __('oversattare.won_assignment_email_delivery_date'),
        'column2' => $assignment->last_delivery,
    ])

    @include('emails.partials.text_section', ['text' => __('oversattare.won_assignment_email_intro_2')])

    @include('emails.partials.button', [
        'label' => __('oversattare.won_assignment_email_button_label'),
        'link' => $assignment->assignmentLink,
    ])


    @include('emails.partials.footer')
@endsection
