@extends('emails.layouts.template')

@section('content')
    @include('emails.partials.title_section', [
        'title' => __('oversattare.assignment_premarket_offer_email_title'),
    ])

    @include('emails.partials.text_section', [
        'text' =>
            __('oversattare.assignment_premarket_offer_email_greeting') .
            ' ' .
            $assignment->first_name .
            ',<br><br>' .
            __('oversattare.assignment_premarket_offer_email_intro_1'),
    ])

    @include('emails.partials.list_row', [
        'column1' => __('oversattare.assignment_premarket_offer_email_from_language_label'),
        'column2' => $assignment->fromLanguage->localizations()->where('language_code', 'sv')->first()->name,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.assignment_premarket_offer_email_to_language_label'),
        'column2' => $assignment->toLanguage->localizations()->where('language_code', 'sv')->first()->name,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.assignment_premarket_offer_email_authorized_label'),
        'column2' => $assignment->authorizedText,
    ])
    @include('emails.partials.list_row', [
        'column1' => __('oversattare.assignment_premarket_offer_email_stamp_label'),
        'column2' => $assignment->authorizedText,
    ])

    @include('emails.partials.list_row', [
        'column1' => __('oversattare.assignment_premarket_offer_email_delivery_date_label'),
        'column2' => $assignment->last_delivery,
    ])


    @include('emails.partials.text_section', [
        'text' =>
            '<strong>Uppskattat pris: </strong>' .
            $assignment->getPreMarketBidWithTax() .
            ' SEK inkl. moms<br/><br/>' .
            __('oversattare.assignment_premarket_offer_email_info_section'),
    ])
    @include('emails.partials.green_button', [
        'label' => 'Jag vill gå vidare',
        'link' => $assignment->assignmentPreMarketBidCustomerLink . '/accept',
    ])
    @include('emails.partials.red_button', [
        'label' => 'Jag avböjer',
        'link' => $assignment->assignmentPreMarketBidCustomerLink . '/decline',
    ])

    @include('emails.partials.text_section', [
        'text' => __('oversattare.assignment_premarket_offer_email_outro'),
    ])


    @include('emails.partials.footer')
@endsection
