/*
|--------------------------------------------------------------------------
| Dependency Definitions
|--------------------------------------------------------------------------
*/

import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import vue from "@vitejs/plugin-vue";

/*
|--------------------------------------------------------------------------
| Vite Setup
|--------------------------------------------------------------------------
*/

export default defineConfig({
    plugins: [
        laravel({
            input: ["resources/js/app.js", "resources/css/app.css"],
            ssr: "resources/js/ssr.js",
            refresh: true,
            detectTls: "mmaj-translation-service.test",
        }),

        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],

    resolve: {
        preserveSymlinks: true,
        alias: {
            "@": "/resources/js",
            "@sbx": "/resources/js/SBX",
            "@sbxadmin": "/resources/js/SBX/Admin",
            "@sbxcms": "/resources/js/SBX/CMS",
            "@sbxui": "/resources/js/SBX/UI",
            "@sbxmedialibrary": "/resources/js/SBX/MediaLibrary",
            "@sbxwebshop": "/resources/js/SBX/Webshop",
            "@sbxmailchimp": "/resources/js/SBX/Mailchimp",
        },
    },
});
