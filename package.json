{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^22.13.11", "@vitejs/plugin-vue": "^5.0.0", "@vue/server-renderer": "^3.3.13", "autoprefixer": "^10.4.7", "axios": "^1.6.8", "laravel-vite-plugin": "^1.0", "lodash": "^4.17.19", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^5.0", "vue": "^3.5.12"}, "dependencies": {"@chenfengyuan/vue-countdown": "^2.1.2", "@gtm-support/vue-gtm": "^3.0.1", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.0.10", "@inertiajs/vue3": "^1.0.14", "@popperjs/core": "^2.11.8", "@sipec/vue3-tags-input": "^3.0.4", "@tiptap/starter-kit": "^2.0.0-beta.199", "@tiptap/vue-3": "^2.0.0-beta.199", "@vueform/slider": "^2.1.9", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "filepond": "^4.30.4", "filepond-plugin-file-validate-size": "^2.2.6", "filepond-plugin-file-validate-type": "^1.2.7", "filepond-plugin-image-crop": "^2.0.6", "filepond-plugin-image-preview": "^4.6.11", "filepond-plugin-image-validate-size": "^1.2.7", "laravel-precognition-vue-inertia": "^0.5.2", "lucide-vue-next": "^0.483.0", "sweetalert2": "^11.4.32", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "v-calendar": "^3.0.3", "vue-filepond": "^7.0.3", "vue-i18n": "^9.2.2", "vue-multiselect": "^3.0.0", "vue3-popper": "^1.5.0", "vuedraggable": "^4.1.0", "vuex": "^4.0.2"}}