{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1.0", "barryvdh/laravel-dompdf": "^2.0", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^0.6.3", "intervention/image": "^2.7", "laravel/framework": "^10.0", "laravel/horizon": "^5.10", "laravel/jetstream": "^4.0", "laravel/pail": "^1.0", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.7", "predis/predis": "^2.0", "pusher/pusher-php-server": "^7.2", "softbox/sbxadmin": "dev-oversattare-changes", "softbox/sbxui": "dev-master", "softbox/sbxwebshop": "dev-master", "softbox/sbxmailgun": "dev-main", "softbox/sbxmedialibrary": "dev-main", "softbox/sbxhellosms": "dev-main", "sveaekonomi/checkout": "^1.5", "symfony/http-client": "^6.3", "symfony/mailgun-mailer": "^6.3", "tightenco/ziggy": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan vendor:publish --tag=sbxadmin --force", "@php artisan vendor:publish --tag=sbxui --force", "@php artisan vendor:publish --tag=sbxwebshopauto --force", "@php artisan vendor:publish --tag=sbxmedialibrary --force"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"sbxui": {"type": "path", "url": "../../../Softbox/SBX/UI"}, "sbxadmin": {"type": "path", "url": "../../../Softbox/SBX/Admin"}, "softbox/sbxwebshop": {"type": "path", "url": "../../../Softbox/SBX/Webshop"}, "softbox/sbxmailgun": {"type": "path", "url": "../../../Softbox/SBX/Mailgun"}, "softbox/sbxmedialibrary": {"type": "path", "url": "../../../Softbox/SBX/MediaLibrary"}, "softbox/sbxhellosms": {"type": "path", "url": "../../../Softbox/SBX/HelloSMS"}}}